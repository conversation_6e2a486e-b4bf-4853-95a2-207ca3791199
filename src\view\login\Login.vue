<template>
  <div class="login-container" v-show="logoGetReady">
    <div class="white-trans-bg"></div>
    <div class="login-container-wrapper">
      <img src="@/asset/image/login/login-logo.svg" width="480px" />
      <section class="form-container">
        <div class="company-logo">
          <!-- <img v-if="!systemSetting.systemLogo" src="@/asset/image/login/logo.png" height="40px" /> -->
          <div class="system-logo-container">
            <!-- <img :src="systemSetting.systemLogo" height="40px" /> -->
            <span class="system-name">{{ systemSetting.systemName }}</span>
          </div>
        </div>
        <div v-if="isElectricty" class="login-form-subtitle">{{ $t('login.electricity') }}</div>
        <el-tabs v-model="loginType" stretch @tab-click="handleClickTabs">
          <el-tab-pane :label="$t('login.tab.account')" name="account">
            <el-form ref="loginForm" :model="form.model" :rules="form.rule" size="medium" class="login-form">
              <el-form-item prop="username" class="bg-white">
                <i class="login-form-icon soc-icon-user"></i>
                <el-input
                  ref="usernameInputDom"
                  v-model="form.model.username"
                  name="username"
                  size="medium"
                  auto-complete="on"
                  :placeholder="$t('login.placeholder.username')"
                ></el-input>
              </el-form-item>
              <el-form-item prop="password" class="bg-white">
                <i class="login-form-icon" :class="password.class" @click="clickPasswordLock"></i>
                <el-input
                  ref="passwordInputDom"
                  v-model="form.model.password"
                  :type="password.type"
                  name="password"
                  auto-complete="on"
                  maxlength="64"
                  @keydown.enter.native.prevent="clickLogin"
                  :placeholder="$t('login.placeholder.password')"
                ></el-input>
              </el-form-item>
              <el-form-item v-if="captcha.open" prop="captcha" class="bg-white">
                <i class="login-form-icon soc-icon-vercode"></i>
                <el-input v-model="form.model.captcha" maxlength="4" :placeholder="$t('login.placeholder.captcha')"></el-input>
                <img :src="captcha.src" :title="$t('login.captcha.switch')" @click="getCaptcha" />
              </el-form-item>
            </el-form>
            <section v-if="exception.login && loginType === 'account'" class="error-info">
              <el-alert :type="exception.type">
                <span v-html="exception.login"></span>
                <el-upload
                  v-if="exception.code === code.license.admin"
                  ref="uploadLicense"
                  action="#"
                  :headers="upload.header"
                  auto-upload
                  :show-file-list="false"
                  accept=".lic, .license"
                  :file-list="upload.files"
                  :before-upload="beforeUploadValidate"
                  :on-change="onUploadFileChange"
                  :http-request="submitUploadFile"
                  class="upload-license"
                >
                  <el-tooltip
                    v-if="exception.tip && exception.tip !== ''"
                    :content="$t('login.license.sequence') + ':' + exception.tip"
                    effect="light"
                    placement="top"
                  >
                    <el-button class="upload-license-button" @click="clickUploadLicense">
                      {{ $t('button.upload') }}
                    </el-button>
                  </el-tooltip>
                  <el-button v-else class="upload-license-button" @click="clickUploadLicense">
                    {{ $t('button.upload') }}
                  </el-button>
                </el-upload>
              </el-alert>
            </section>
          </el-tab-pane>

          <el-tab-pane :label="$t('login.tab.mail')" name="mail">
            <el-form ref="mailFormDom" :model="mail" size="medium" class="login-form">
              <el-form-item prop="mail" class="bg-white">
                <i class="login-form-icon soc-icon-email"></i>
                <el-input
                  ref="mailInputDom"
                  v-model="mail.value"
                  class="login-form-input"
                  name="mail"
                  size="medium"
                  auto-complete="on"
                  :placeholder="$t('login.placeholder.mail')"
                  @keydown.enter.native.prevent="keydownMailInput"
                ></el-input>
              </el-form-item>
              <el-form-item prop="mailCaptcha" class="bg-white">
                <div style="display: flex;align-items: center;width: 100%;height: 37px;">
                  <i class="login-form-icon soc-icon-captcha"></i>
                  <el-input
                    ref="mailCaptchaInputDom"
                    v-model="mail.captcha"
                    name="mailCaptcha"
                    class="login-form-input"
                    :placeholder="$t('login.placeholder.mailCaptcha')"
                    @keydown.enter.native.prevent="clickLogin"
                  ></el-input>
                  <el-button
                    :loading="effect.mailCaptchaLoading"
                    :disabled="effect.mailCaptchaDisabled"
                    type="primary"
                    @click="clickGetEmailCaptcha"
                    style="width: 240px;height: 100%;border-top-left-radius: 0px;border-bottom-left-radius: 0px;"
                  >
                    <span v-if="effect.mailCaptchaLoading">
                      {{ $t('login.mail.getAgainCaptcha', [mail.seconds]) }}
                    </span>
                    <span v-else>{{ $t('login.mail.getCaptcha') }}</span>
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
            <section v-if="exception.login && loginType === 'mail'" class="error-info">
              <el-alert :type="exception.type">
                <span v-html="exception.login"></span>
                <el-upload
                  v-if="exception.code === code.license.admin"
                  ref="uploadLicense"
                  action="#"
                  :headers="upload.header"
                  auto-upload
                  :show-file-list="false"
                  accept=".lic, .license"
                  :file-list="upload.files"
                  :before-upload="beforeUploadValidate"
                  :on-change="onUploadFileChange"
                  :http-request="submitUploadFile"
                  class="upload-license"
                >
                  <el-tooltip
                    v-if="exception.tip && exception.tip !== ''"
                    :content="$t('login.license.sequence') + ':' + exception.tip"
                    effect="light"
                    placement="top"
                  >
                    <el-button class="upload-license-button" @click="clickUploadLicense">
                      {{ $t('button.upload') }}
                    </el-button>
                  </el-tooltip>
                  <el-button v-else class="upload-license-button" @click="clickUploadLicense">
                    {{ $t('button.upload') }}
                  </el-button>
                </el-upload>
              </el-alert>
            </section>
          </el-tab-pane>
        </el-tabs>
        <div class="user-privacy">
          <el-checkbox v-model="isAggree">
            {{ $t('login.privacy.agreePrefix') }}
            <a class="user-note" @click.stop="openPrivacy">{{ $t('login.privacy.protocol') }}</a>
          </el-checkbox>
        </div>
        <el-button class="loginBtn" type="primary" :loading="loading" :disabled="license.remain <= 0" @click.native.prevent="clickLogin">
          {{ $t('login.text') }}
        </el-button>
      </section>
    </div>
    <complete-user :visible.sync="exception.visible.completeUser" @on-submit="clickSubmitExceptionLogin"></complete-user>
    <complete-password :visible.sync="exception.visible.updatePassword" @on-submit="clickSubmitExceptionLogin"></complete-password>
    <complete-password
      :visible.sync="exception.visible.resetPassword"
      :has-old-password="false"
      @on-submit="clickSubmitExceptionLogin"
    ></complete-password>
    <custom-dialog :visible="privacyVisible" :title="$t('login.privacy.protocol')" width="80%" @on-close="privacyClose" @on-submit="privacyOk">
      <iframe src="/static/privacy.html" frameborder="0" width="100%" height="100%" style="min-height: 400px;"></iframe>
    </custom-dialog>
  </div>
</template>
<script>
import { JSEncrypt } from 'jsencrypt'
import CompleteUser from './component/LoginCompleteUser'
import CompletePassword from './component/LoginCompletePassword'
import CustomDialog from '@comp/CustomDialog'
import loginCode from '@asset/js/code/login'
import { validateEmail } from '@util/validate'
import { validateCaptcha } from '@util/validate'
import { isEmpty } from '@util/common'
// import AESGCM from "@util/aes-gcm";
import { isNotEmpty } from '@util/is'
import { prompt } from '@util/prompt'
import { getCaptchaData, uploadLicenseData, queryCaptchaMailData } from '@api/login/login-api'
import { getSystemNameAndLogo } from '@api/layout/layout-api'

export default {
  components: {
    CompleteUser,
    CompletePassword,
    CustomDialog,
  },
  data() {
    const validatorCaptcha = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.captcha.empty')))
      } else if (!validateCaptcha(value)) {
        callback(new Error(this.$t('validate.captcha.rule')))
      } else {
        callback()
      }
    }
    return {
      logoGetReady: false,
      loginType: 'account',
      mail: {
        value: '',
        captcha: '',
        timer: null,
        seconds: -1,
      },
      effect: {
        mailCaptchaLoading: false,
        mailCaptchaDisabled: true,
      },
      form: {
        model: {
          username: '',
          password: '',
          captcha: '',
        },
        rule: {
          captcha: [{ validator: validatorCaptcha, trigger: 'blur' }],
        },
      },
      password: {
        type: 'password',
        class: 'soc-icon-lock',
      },
      captcha: {
        open: false,
        src: '',
      },
      exception: {
        login: null,
        code: 0,
        tip: '',
        type: 'warning',
        visible: {
          completeUser: false,
          resetPassword: false,
          updatePassword: false,
        },
      },
      loading: false,
      router: {
        redirect: null,
        query: {},
      },
      license: {
        remain: 365,
        overdue: 7,
      },
      upload: {
        header: {
          'Content-Type': 'multipart/form-data',
        },
        files: [],
      },
      isAggree: false,
      privacyVisible: false,
      systemSetting: {
        systemLogo: '',
        systemName: '',
      },
    }
  },
  computed: {
    code() {
      return loginCode
    },
    isElectricty() {
      return !!window.GLOBAL_CONFIG.LOGIN_IS_ELECTRICTY
    },
  },
  watch: {
    $route: {
      handler(route) {
        const query = route.query
        if (query) {
          this.router.redirect = query.redirect
          this.router.query = this.getRouterQuery(query)
        }
      },
      immediate: true,
    },
    'form.username'() {
      this.resetCaptcha()
    },
    'form.password'() {
      this.resetCaptcha()
    },
    'mail.value'(value) {
      this.resetCaptcha()
      this.effect.mailCaptchaDisabled = !validateEmail(value)
    },
    'mail.captcha'() {
      this.resetCaptcha()
    },
    'exception.login'(newVal) {
      switch (newVal) {
        case this.$t('login.account.toImproved', ['login-complete-account']):
          this.addExceptionEvent('login-complete-account', 'completeUser')
          break
        case this.$t('login.password.overdue', ['login-complete-password']):
          this.addExceptionEvent('login-complete-password', 'updatePassword')
          break
        case this.$t('login.password.beReset', ['login-reset-password']):
          this.addExceptionEvent('login-reset-password', 'resetPassword')
          break
        default:
          break
      }
    },
  },
  mounted() {
    document.querySelector('title').innerText = '盛邦安全'
    this.preloadLoginData()
  },
  methods: {
    getLogo() {
      getSystemNameAndLogo().then((res) => {
        if (res) {
          this.systemSetting.systemLogo = res.systemLogo
          this.systemSetting.systemName = res.systemName
          document.querySelector('title').innerText = res.systemName
        }
        this.logoGetReady = true

        // 设置系统语言
        if (res.localeLanguage) {
          const locale = res.localeLanguage
          if (this.$i18n.locale !== locale) {
            this.$i18n.locale = locale
          }
        }
      })
    },
    handleClickTabs(tab) {
      this.exception.login = false
      if (tab.name === 'account') {
        this.$nextTick(() => {
          this.$refs.usernameInputDom.focus()
        })
      } else {
        this.$nextTick(() => {
          this.$refs.mailInputDom.focus()
        })
      }
    },
    clickGetEmailCaptcha() {
      const value = this.mail.value
      if (!isNotEmpty(value)) {
        prompt({
          i18nCode: 'login.validate.emailName',
          type: 'warning',
        })
      } else if (!validateEmail(value)) {
        prompt({
          i18nCode: 'validate.comm.email',
          type: 'warning',
        })
      } else {
        this.getCaptchaMail(this.mail.value)
      }
    },
    getCaptchaMail(mailValue) {
      queryCaptchaMailData(mailValue).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'login.validate.success',
              type: 'success',
              print: true,
            },
            () => {
              this.countdownGetCaptcha()
            }
          )
        } else if (res === 2) {
          prompt(
            {
              i18nCode: 'login.validate.invalid',
              type: 'warning',
              print: true,
            },
            () => {
              this.effect.mailCaptchaLoading = false
              this.resetCaptcha()
            }
          )
        } else if (res === 3) {
          prompt(
            {
              i18nCode: 'login.validate.failed',
              type: 'error',
              print: true,
            },
            () => {
              this.effect.mailCaptchaLoading = false
              this.resetCaptcha()
            }
          )
        } else if (res === 15) {
          prompt(
            {
              i18nCode: 'login.mail.noExist',
              type: 'error',
              print: true,
            },
            () => {
              this.effect.mailCaptchaLoading = false
              this.resetCaptcha()
            }
          )
        } else if (res === 16) {
          prompt(
            {
              i18nCode: 'login.mail.servicedown',
              type: 'error',
              print: true,
            },
            () => {
              this.effect.mailCaptchaLoading = false
              this.resetCaptcha()
            }
          )
        }
      })
    },
    countdownGetCaptcha() {
      const COUNTDOWN_SECOND = 60
      if (!this.mail.timer) {
        this.mail.seconds = COUNTDOWN_SECOND
        this.effect.mailCaptchaLoading = true
        this.mail.timer = setInterval(() => {
          if (this.mail.seconds > 1 && this.mail.seconds <= COUNTDOWN_SECOND) {
            this.mail.seconds--
          } else {
            this.clearCountdownTimer()
          }
        }, 1000)
      }
    },
    clearCountdownTimer() {
      clearTimeout(this.mail.timer)
      this.mail.timer = null
      this.effect.mailCaptchaLoading = false
    },
    resetCaptcha() {
      if (this.captcha.enable) {
        this.captcha.success = false
        this.$refs.slideCaptchaDom.reset()
      }
    },
    keydownMailInput() {
      const formExistEmpty = this.mail.value.isNotEmpty() && this.mail.captcha.isNotEmpty()
      if (formExistEmpty) {
        this.clickLogin()
        return
      }
      this.$refs.mailCaptchaInputDom.focus()
    },
    preloadLoginData() {
      this.$store.dispatch('user/registry').then((res) => {
        this.getCaptcha()
        this.initLoginState()
        this.getLogo()
      })
    },
    initLoginState() {
      this.$store.dispatch('user/saveMode', 'offline')
    },
    clickLogin() {
      if (!this.isAggree) {
        this.$notify({
          type: 'warning',
          title: this.$t('login.privacy.pleaseread'),
          offset: 70,
        })
        return
      }
      if (this.loginType === 'account') {
        this.accountLogin()
      } else {
        this.emailLogin()
      }
    },
    accountLogin() {
      if (this.loginType === 'account') {
        const formExistEmpty = !this.form.model.username.isNotEmpty() || !this.form.model.password.isNotEmpty()
        this.captcha.enable ? this.enableCaptcha(formExistEmpty) : this.disableCaptcha(formExistEmpty)
      }
    },
    emailLogin() {
      const name = this.mail.value
      const password = this.mail.captcha
      if (name === '' || !name) {
        prompt({
          i18nCode: 'login.validate.emailName',
          type: 'warning',
        })
      } else if (!validateEmail(name)) {
        prompt({
          i18nCode: 'validate.comm.email',
          type: 'warning',
        })
      } else if (password === '' || !password) {
        prompt({
          i18nCode: 'validate.captcha.empty',
          type: 'warning',
        })
      } else {
        this.captcha.enable ? this.enableCaptcha(false) : this.disableCaptcha(false)
      }
    },
    enableCaptcha(empty) {
      this.exception.login = null
      if (empty) {
        setTimeout(() => {
          this.exception.login = this.$t('login.account.empty')
        }, 500)
      }
      if (this.captcha.success) {
        this.exception.login = false
        this.loginValidate()
      } else {
        this.getCaptcha()
        this.showSlideCaptcha()
      }
    },
    disableCaptcha(empty) {
      this.exception.login = null
      if (empty) {
        this.exception.type = 'warning'
        setTimeout(() => {
          this.exception.login = this.$t('login.account.empty')
        }, 500)
      } else {
        this.exception.login = false
        this.loginValidate()
      }
    },

    loginValidate() {
      this.accountLoginValidate()
      this.mailLoginValidate()
    },
    accountLoginValidate() {
      if (this.loginType === 'account') {
        this.$refs.loginForm.validate((valid) => {
          if (valid) {
            const encryptor = new JSEncrypt()
            encryptor.setPublicKey(this.$store.getters.publicKey)
            const params = {
              account: this.form.model.username,
              pasw: encryptor.encrypt(this.form.model.password),
            }
            if (this.form.model.captcha) {
              Object.assign(params, {
                captcha: this.form.model.captcha,
              })
            }
            this.login(params)
          } else {
            this.validateFail()
          }
        })
      }
    },
    mailLoginValidate() {
      if (this.loginType === 'mail') {
        this.$refs.mailFormDom.validate((valid) => {
          if (valid) {
            this.login({
              mail: this.mail.value,
              mailCaptcha: this.mail.captcha,
              captcha: this.captcha.code,
            })
          } else {
            this.validateFail()
          }
        })
      }
    },
    validateFail() {
      prompt(
        {
          i18nCode: 'validate.form.warning',
          type: 'warning',
          print: true,
        },
        () => {
          this.resetCaptcha()
        }
      )
    },
    login(formObj) {
      const loginUrl = this.loginType === 'account' ? 'user/login' : 'user/loginMail'
      this.$store
        .dispatch(loginUrl, formObj)
        .then((res) => {
          if (res) {
            this.exception.code = res.result
            this.exception.tip = res.message
            if (res !== this.code.success) {
              this.captcha.open ? this.getCaptcha() : false
            }
            switch (this.exception.code) {
              // 登录成功
              case this.code.success:
                this.loginSuccess()
                break
              // 系统不存在该用户、用户名错误
              case this.code.account.illegalUser:
              case this.code.account.invalid:
                this.exception.login = this.$t('login.account.invalid')
                this.exception.type = 'error'
                break
              // 账号已经过期
              case this.code.account.overdue:
                this.exception.login = this.$t('login.account.overdue')
                this.exception.type = 'warning'
                break
              // 账号非法时间登录
              case this.code.account.illegalTime:
                this.exception.login = this.$t('login.account.illegalTime')
                this.exception.type = 'warning'
                break
              // 账号被锁定
              case this.code.account.lock:
                this.exception.login = this.$t('login.account.lock')
                this.exception.type = 'error'
                break
              // 账号首次登录,完善用户信息
              case this.code.account.toImproved:
                this.exception.login = this.$t('login.account.toImproved', ['login-complete-account'])
                this.exception.type = 'warning'
                break
              // 账号未授权任何菜单
              case this.code.account.unauth:
                this.exception.login = this.$t('login.account.unauth')
                this.exception.type = 'warning'
                break
              // 密码错误
              case this.code.password.invalid:
                this.exception.login = this.$t('login.password.invalid')
                this.exception.type = 'error'
                break
              // 账号密码过期
              case this.code.password.overdue:
                this.exception.login = this.$t('login.password.overdue', ['login-complete-password'])
                this.exception.type = 'warning'
                break
              // 密码被重置
              case this.code.password.beReset:
                this.exception.login = this.$t('login.password.beReset', ['login-reset-password'])
                this.exception.type = 'warning'
                break
              // 验证码错误
              case this.code.captcha.invalid:
                this.exception.login = this.$t('login.captcha.invalid')
                this.exception.type = 'error'
                break
              // session已失效或者未登录
              case this.code.session.invalid:
                this.exception.login = this.$t('login.session.invalid')
                this.exception.type = 'warning'
                break
              // license已过期，联系管理员
              case this.code.license.overdue:
                this.exception.login = this.$t('login.license.overdue')
                this.exception.type = 'warning'
                break
              // license已过期重新上传
              case this.code.license.admin:
                this.exception.login = this.$t('login.license.admin', [''])
                this.exception.type = 'error'
                break
              case 14:
                this.exception.login = this.$t('login.mail.invalid')
                this.exception.type = 'error'
                break
              case 15:
                this.exception.login = this.$t('login.mail.noExist')
                this.exception.type = 'error'
                break
              case 16:
                this.exception.login = this.$t('login.mail.servicedown')
                this.exception.type = 'error'
                break
              default:
                console.error('err-question: no find return value')
                break
            }
          }
        })
        .catch((e) => {
          this.captcha.open ? this.getCaptcha() : false
          console.error(e)
        })
    },
    loginSuccess() {
      this.loginSuccessAddNotice()
      this.loginSuccessAddState()
    },
    loginSuccessAddNotice() {
      const now = new Date(),
        hour = now.getHours(),
        notify = (dateString) => {
          if (document.getElementsByClassName('el-notification').length === 0) {
            this.$notify({
              type: 'success',
              title: this.$t('login.success.welcome', [this.form.model.username]),
              message: this.$t(`login.success.${dateString}`),
              offset: 70,
            })
          }
        }

      if (hour >= 0 && hour < 6) {
        notify('night')
      }

      if (hour >= 6 && hour < 12) {
        notify('morning')
      }

      if (hour >= 12 && hour < 13) {
        notify('noon')
      }

      if (hour >= 13 && hour < 18) {
        notify('afternoon')
      }

      if (hour >= 18 && hour < 24) {
        notify('evening')
      }
    },
    loginSuccessAddState() {
      this.$store.dispatch('user/saveMode', 'online')
      if (this.router.redirect === '/login') {
        this.router.redirect = null
      }
      this.$router.replace({
        path: this.router.redirect || '/layout',
        query: this.router.query,
      })
    },
    clickPasswordLock() {
      this.password.type = this.password.type === 'password' ? 'text' : 'password'
      this.password.class = this.password.type === 'password' ? 'soc-icon-lock' : 'soc-icon-unlock'
    },
    clickSubmitExceptionLogin() {
      this.exception.login = false
      this.preloadLoginData()
    },
    clickUploadLicense() {
      this.upload.files = []
      this.$refs.uploadLicense.submit()
    },
    beforeUploadValidate(file) {
      if (this.upload.files.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isLic = suffix === 'lic'
        const isLicense = suffix === 'license'
        if (!isLic && !isLicense) {
          prompt({
            i18nCode: 'validate.upload.license',
            type: 'warning',
          })
        }
        return isLic || isLicense
      }
    },
    onUploadFileChange(file) {
      this.upload.files.push(file)
    },
    submitUploadFile(param) {
      if (param.file && this.upload.files.length > 0) {
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.uploadLicense(formData)
      }
    },
    addExceptionEvent(className, visibleName) {
      this.$nextTick(() => {
        if (this.exception.login) {
          document.querySelector(`.${className}`).addEventListener('click', () => {
            this.exception.visible[visibleName] = true
          })
        }
      })
    },
    getRouterQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    },
    getCaptcha() {
      getCaptchaData().then((res) => {
        if (res) {
          this.captcha.open = true
          this.captcha.src = 'data:image/jpeg;base64,' + res
        } else {
          this.captcha.open = false
        }
      })
    },
    uploadLicense(formData) {
      uploadLicenseData(formData).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.upload.success',
            type: 'success',
          })
          this.exception.login = false
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.upload.format',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.upload.error',
            type: 'error',
          })
        }
      })
    },
    privacyClose() {
      this.privacyVisible = false
    },
    privacyOk() {
      this.privacyVisible = false
    },
    openPrivacy(e) {
      this.privacyVisible = true
    },
  },
}
</script>
<style lang="scss" scoped>
.login-container {
  position: relative;
  top: 50%;
  height: 500px;
  margin-top: -250px;
  display: flex;
  justify-content: space-between;
  ::v-deep .el-tabs__item.is-active {
    color: #f78989;
  }
  ::v-deep .el-tabs__active-bar {
    background-color: #f78989;
  }
  ::v-deep .el-tabs__item:hover {
    color: #e23938;
  }
  .white-trans-bg {
    background: #e3f1ff;
    opacity: 0.3;
    width: 100%;
    height: 100%;
    position: absolute;
    z-index: -1;
  }
  .loginBtn {
    width: 100%;
    margin: 12px auto 12px;
    border-radius: 4px;
    background: #e23938;
    border-color: #e23938 !important;
    color: #fff;
    &:hover,
    &:focus {
      background: #f78989;
      border-color: #f78989 !important;
    }
  }
  .login-container-wrapper {
    display: flex;
    width: 100%;
    margin-right: 2%;
    justify-content: space-between;
    img {
      margin-left: 3%;
    }
    .form-container {
      width: 500px;
      padding: 48px;
      .company-logo {
        place-items: center;
        display: grid;
        padding-bottom: 24px;
        padding-top: 24px;
        .system-logo-container {
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 100%;
          .system-name {
            font-size: 18px;
            font-weight: bold;
            margin-left: 10px;
            color: $WT;
          }
        }
      }
      .login-form-title {
        font-weight: 700;
        font-size: 18px;
        text-align: center;
        color: #000;
      }
      .login-form-subtitle {
        margin-bottom: 8px;
        font-weight: 700;
        font-size: 14px;
        text-align: center;
        color: #999;
      }
      .bg-white {
        background: #fff;
        border-radius: 4px;
      }
      .login-form {
        .el-form-item {
          position: relative;
          margin: 30px 0 0 0;
          text-align: center;

          .el-input {
            ::v-deep .el-input__inner {
              text-indent: 50px;
              font-size: 14px;
              @include theme('color', login-form-text-color);
              @include theme('border-color', login-form-border-color);
            }
          }

          button {
            width: 100%;
            margin: 20px auto;
            border-radius: 4px;
            background: #e23938;
            border-color: #e23938;
            color: #fff;
          }

          i.login-form-icon {
            position: absolute;
            top: 8px;
            z-index: 1;
            width: 50px;
            font-size: 20px;
            border-right: 1px solid;
            @include theme('color', login-form-text-color);
            @include theme('border-color', login-form-border-color);
          }

          i.login-form-icon.soc-icon-lock,
          i.login-form-icon.soc-icon-unlock {
            font-size: 22px;
            cursor: pointer;
          }

          img {
            top: 6px;
            position: absolute;
            right: 7px;
            cursor: pointer;
          }
        }
      }

      .error-info {
        padding: 10px 0;
        text-align: center;
        animation: shake 1s;

        .el-alert {
          ::v-deep .el-alert__content {
            margin: 0 auto;

            .el-alert__description {
              margin: 0;
            }

            .upload-license {
              display: inline-block;

              &-button {
                padding: 0;
                border: none;
                background-color: $CR;

                span {
                  color: #1873d7;

                  &:hover,
                  &:focus {
                    @include theme('color', font-color);
                  }
                }
              }
            }
          }
        }
      }

      ::v-deep b {
        cursor: pointer;
        color: #1873d7;

        &:hover,
        &:focus {
          @include theme('color', font-color);
        }
      }
    }
  }

  .user-privacy {
    margin-top: 24px;
    ::v-deep .el-checkbox {
      color: #bbb;
    }
  }
}
</style>
