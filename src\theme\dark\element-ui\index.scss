$white: #fff;
$black: #000;
$gray: #666;
$transparent: transparent;
$base-border-color: rgba(67, 67, 67, 0.65);
$base-font-border-color: rgba(255, 255, 255, 0.65);
$base-empty-color: #999;
$base-disabled-color: #bbb;
$base-mask-color: #14141496;
$danger-color: rgba(245, 108, 108, 0.35);
$waring-color: rgba(230, 162, 60, 0.35);
$success-color: rgba(103, 194, 58, 0.35);
$success-hover-color: rgba(103, 194, 58, 0.65);

$border: rgba(255, 255, 255, 0.65);
$border-color: #434343;
$border-disabled-color: rgba(67, 67, 67, 0.65);
$border-hover-color: rgba(23, 125, 220, 0.65);
$border-active-color: rgba(23, 125, 220, 0.65);
$border-focus-color: rgba(23, 125, 220, 0.65);
$border-dialog-color: #303030;
$border-active-bottom-color: rgb(64, 158, 255);
$border-gray-color: #7b7f8694;
$border-tabs-color: #dfe4ed;
$border-tab-active-color: #d1dbe5;
$border-success-light-color: #274916;
$border-checkbox-color: rgba(255, 255, 255, 0.65);
$border-tooltip-arrow-color: rgba(255, 255, 255, 0.2);

$font-color: rgba(255, 255, 255, 0.65);
$font-title-color: rgba(255, 255, 255, 0.85);
$font-hover-color: #177ddc;
$font-focus-color: #177ddc;
$font-disabled-color: rgba(255, 255, 255, 0.3);
$font-active-color: #165996;
$font-dropdown-btn: rgba(64, 158, 255, 0.2);
$font-item-color: rgba(144, 147, 153, 0.35);
$font-item-hover-color: rgba(144, 147, 153, 0.65);
$font-btn-color: #333;
$font-primary: #66b1ff;
$font-breadcrumb-color: rgba(255, 255, 255, 0.45);
$font-success-color: #6abe39;
$font-group-color: #b8b9b9;
$font-placeholder-color: rgba(125, 125, 125, 0.5);

$bg-color: #141414;
$bg-disabled-color: #201f1f;
$bg-select-color: #1f1f1f;
$bg-select-hover-color: rgba(255, 255, 255, 0.08);
$bg-dialog-color: #1f1f1f;
$bg-hover-color: #111b26;
$bg-btn-color: rgba(255, 255, 255, 0.5);
$bg-box-shadow-color: rgba(0, 0, 0, 0.1);
$bg-shadow-color: rgba(0, 0, 0, 0.04);
$bg-dropdown-before-color: rgba(220, 223, 230, 0.5);
$bg-inner-color: #f2f6fc;
$bg-active-color: #e6f1fe;
$bg-list-item-color: rgb(236, 245, 255);
$bg-tag-color: transparent;
$bg-shadow-dark-color: rgba(0, 0, 0, 0.6);
$bg-scrollbar-color: rgba(144, 147, 153, 0.3);
$bg-scrollbar-hover-color: rgba(144, 147, 153, 0.5);
$bg-calendar-color: #f2f8fe;
$bg-dragover-color: rgba(32, 159, 255, 0.06);
$bg-box-shadow: rgba(0, 0, 0, 0.2);
$bg-required-color: #ff4d51;
$bg-success-light-color: #162312;
$bg-checkbox-color: transparent;

html[data-theme='dark'] {
  .el-pagination {
    button {
      &:hover {
        color: $font-hover-color;
      }

      &:disabled {
        color: $font-disabled-color;
        background-color: $bg-disabled-color;

        &:active {
          transform: scale(1);
        }
      }
    }

    .btn-next,
    .btn-prev {
      background-color: $transparent;
      border: 1px solid $border-color;

      i {
        color: $font-color;
      }
    }

    .el-pager {
      .disabled {
        color: $font-disabled-color;
      }

      li {
        color: $font-color;
        border: 1px solid $border-color;
        background: $transparent;

        &:hover {
          color: $font-hover-color;
        }

        &.active {
          color: $font-hover-color;
        }

        &.btn-quicknext.disabled,
        &.btn-quickprev.disabled {
          color: $font-disabled-color;
        }
      }
    }

    &__total,
    &__sizes,
    &__jump {
      color: $font-color;
    }

    &__sizes {
      .el-input {
        &__inner {
          &:hover {
            border-color: $font-hover-color;
          }
        }
      }
    }

    &.is-background {
      .btn-next,
      .btn-prev,
      .el-pager li {
        border: 1px solid $border-color;
        background-color: $transparent;
        color: $font-color;

        &:hover,
        &:focus {
          border-color: $border-hover-color;
          background-color: $transparent;
          color: $font-active-color;

          i {
            color: $font-active-color;
          }
        }

        &:disabled {
          border: 1px solid $border-disabled-color;
          background-color: $bg-disabled-color;
          color: $font-disabled-color;

          &:active {
            transform: scale(1);
          }
        }
      }

      .el-pager {
        li {
          color: $font-color;
          border: 1px solid $border-color;
          background: $transparent;

          &:not(.disabled):hover {
            border-color: $border-hover-color;
            background-color: $transparent;
            color: $font-hover-color;
          }

          &:not(.disabled).active {
            border-color: $border-active-color;
            background-color: $transparent;
            color: $font-active-color;
          }
        }
      }
    }
  }

  .el-dialog {
    background-color: $bg-color;

    &__header {
      border-bottom: 1px solid $border-dialog-color;
      box-sizing: border-box;

      .el-dialog__title {
        color: $font-title-color;
      }
    }

    &__body {
      color: $font-color;
    }

    &__footer {
      border-top: 1px solid $border-dialog-color;
    }
  }

  .el-dialog__headerbtn {
    &:focus,
    &:hover {
      .el-dialog__close {
        color: $font-hover-color;
      }
    }

    .el-dialog__close {
      color: $font-color;
    }
  }

  .el-autocomplete-suggestion {
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    border: 1px solid $base-border-color;
    background-color: $bg-color;

    &.is-loading {
      li {
        color: $base-empty-color;
      }

      li:hover {
        background-color: $white;
      }
    }

    li {
      color: $base-font-border-color;
    }

    li.highlighted,
    li:hover {
      background-color: $bg-hover-color;
    }

    li.divider {
      border-top: 1px solid $black;
    }
  }

  .el-menu--collapse {
    .el-submenu {
      .el-menu {
        z-index: 10;
        -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
        border: 1px solid $base-border-color;
        box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
      }
    }

    & > .el-menu-item {
      &.is-active {
        i {
          color: inherit;
        }
      }
    }
  }

  .el-dropdown {
    margin-left: 10px;
    color: $base-font-border-color;

    .el-dropdown__caret-button::before {
      background: $transparent;
    }

    .el-dropdown__caret-button.el-button--default::before {
      background: $transparent;
    }
  }

  .el-dropdown-menu {
    background-color: $bg-color;
    border: 1px solid $bg-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    z-index: 10;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-dropdown-menu__item {
    color: $base-font-border-color;

    &:focus,
    &:not(.is-disabled):hover {
      background-color: $bg-hover-color;
      color: $font-hover-color;
    }

    &.is-disabled {
      color: $base-disabled-color;
    }
  }

  .el-dropdown-menu__item--divided {
    border-top: 1px solid $bg-hover-color;

    &:before {
      background-color: $bg-hover-color;
    }
  }

  .el-menu {
    border-right: solid 1px $transparent;
    background-color: $transparent;

    &.el-menu--horizontal {
      border-bottom: solid 1px $base-border-color;
    }
  }

  .el-menu--horizontal {
    .el-menu {
      .el-menu-item,
      .el-submenu__title {
        background-color: $bg-color;
        color: $font-color;

        &:hover,
        &:focus {
          color: $font-focus-color;
          background-color: $bg-hover-color;
        }
      }

      .el-menu-item {
        &.is-active {
          background-color: $bg-hover-color;
          color: $font-active-color;

          & > .el-submenu__title {
            color: $base-font-border-color;
          }
        }
      }

      .el-submenu {
        &.is-active {
          & > .el-submenu__title {
            color: $font-active-color;
            background-color: $bg-hover-color;
          }
        }
      }
    }

    .el-menu-item {
      &:not(.is-disabled) {
        &:hover,
        &:focus {
          color: $font-focus-color;
          background-color: $bg-hover-color;
        }
      }
    }

    & > .el-menu-item {
      color: $font-color;

      &.is-active {
        border-bottom: 2px solid $border-active-bottom-color;
        color: $font-active-color;
        background-color: $bg-hover-color;
      }

      &.is-disabled {
        color: $white;
      }
    }

    & > .el-menu-item:not(.is-disabled):focus,
    & > .el-menu-item:not(.is-disabled):hover {
      color: $font-focus-color;
      background-color: $bg-hover-color;
    }

    & > .el-submenu {
      .el-submenu__title {
        border-bottom: 2px solid transparent;
        color: $font-color;
      }

      .el-submenu__title:hover {
        background-color: $bg-hover-color;
        color: $font-focus-color;
      }

      &.is-active {
        .el-submenu__title {
          border-bottom: 2px solid $border-active-bottom-color;
          color: $font-focus-color;
          background-color: $bg-hover-color;
        }
      }

      &:hover {
        background-color: $bg-hover-color;
        color: $font-focus-color;
      }
    }
  }

  .el-menu--popup {
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-menu-item {
    color: $font-color;

    &:focus,
    &:hover {
      color: $font-hover-color;
      background-color: $bg-hover-color;
    }

    &.is-active {
      color: $font-hover-color;
      background-color: $bg-hover-color;

      i {
        color: inherit;
      }
    }

    i {
      color: $font-color;
    }
  }

  .el-submenu__title {
    color: $font-color;
    -webkit-transition: border-color 0.3s, background-color 0.3s, color 0.3s;
    transition: border-color 0.3s, background-color 0.3s, color 0.3s;

    i {
      color: $font-color;
    }

    &:focus,
    &:hover {
      color: $font-hover-color;
      background-color: $bg-hover-color;
    }
  }

  .el-submenu {
    &.is-active {
      .el-submenu__title {
        border-bottom-color: $border-active-bottom-color;
      }
    }
  }

  .el-menu-item-group__title {
    color: $font-item-color;
  }

  .el-radio-button__inner {
    background: $white;
    border: 1px solid $border-gray-color;
    color: $base-font-border-color;

    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .el-radio-button {
    &:first-child {
      .el-radio-button__inner {
        border-left: 1px solid $border-gray-color;
      }
    }

    &:focus:not(.is-focus):not(:active):not(.is-disabled) {
      -webkit-box-shadow: 0 0 2px 2px $border-active-bottom-color;
      box-shadow: 0 0 2px 2px $border-active-bottom-color;
    }
  }

  .el-radio-button__orig-radio {
    &:disabled {
      & + .el-radio-button__inner {
        color: $base-font-border-color;
        background-color: $white;
        border-color: $border-color;

        &:active {
          transform: scale(1);
        }
      }

      &:checked {
        & + .el-radio-button__inner {
          background-color: $bg-inner-color;

          &:active {
            transform: scale(1);
          }
        }
      }
    }

    &:checked {
      & + .el-radio-button__inner {
        color: $white;
        background-color: $border-active-bottom-color;
        border-color: $border-active-bottom-color;
        -webkit-box-shadow: -1px 0 0 0 $border-active-bottom-color;
        box-shadow: -1px 0 0 0 $border-active-bottom-color;
      }
    }
  }

  .el-switch__label {
    color: $base-font-border-color;

    &.is-active {
      color: $border-active-bottom-color;
    }
  }

  .el-switch__core {
    border: 1px solid $border-gray-color;
    background: $border-gray-color;

    &:after {
      background-color: $white;
    }
  }

  .el-switch {
    &.is-checked {
      .el-switch__core {
        border-color: $border-active-bottom-color;
        background-color: $border-active-bottom-color;
      }
    }
  }

  .el-select-dropdown {
    border: 1px solid $transparent;
    background-color: $bg-select-color;
    color: $font-color;

    &.is-multiple {
      .el-select-dropdown__item {
        &.selected {
          color: $border-active-bottom-color;
          background-color: $bg-hover-color;

          &.hover {
            background-color: $bg-hover-color;
          }
        }
      }
    }
  }

  .el-select-dropdown__empty {
    color: $base-empty-color;
  }

  .el-select-dropdown__item {
    color: $font-color;

    &.is-disabled {
      color: $base-font-border-color;

      &:hover {
        background-color: $bg-hover-color;
      }
    }

    &.hover,
    &:hover {
      background-color: $bg-hover-color;
    }

    &.selected {
      color: $font-hover-color;
      background-color: $bg-hover-color;
    }
  }

  .el-select-group__wrap {
    &:not(:last-of-type)::after {
      background: $base-border-color;
    }
  }

  .el-select-group__title {
    color: $font-item-color;
  }

  .el-select {
    &:hover {
      .el-input__inner {
        border-color: $base-font-border-color;
      }
    }

    .el-input__inner {
      &:focus {
        border-color: $border-active-bottom-color;
      }
    }

    .el-input {
      .el-select__caret {
        color: $base-font-border-color;

        &.is-show-close {
          color: $base-font-border-color;

          &:hover {
            color: $font-item-color;
          }
        }
      }

      &.is-disabled {
        .el-input__inner {
          &:hover {
            border-color: $base-font-border-color;
          }
        }
      }

      &.is-focus {
        .el-input__inner {
          border-color: $border-active-bottom-color;
        }
      }
    }

    .el-tag {
      border-color: transparent;
      background-color: $bg-tag-color;

      &.el-tag--info {
        max-width: 75%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

    .el-tag__close {
      &.el-icon-close {
        background-color: $base-font-border-color;
        color: $white;

        &:hover {
          background-color: $font-item-color;
        }
      }
    }
  }

  .el-select__input {
    color: $font-color;
    background-color: transparent;

    &.is-mini {
      height: 14px;
    }
  }

  .el-select__close {
    color: $base-font-border-color;

    &:hover {
      color: $font-item-color;
    }
  }

  .el-table__expanded-cell {
    background-color: $transparent;

    &:hover {
      background-color: $transparent !important;
    }
  }

  .el-table__empty-text {
    color: $font-item-color;
  }

  .el-table__expand-icon {
    color: $gray;
  }

  .el-table__placeholder {
    display: inline-block;
    width: 20px;
  }

  .el-table {
    color: $font-color;
    background-color: $transparent;

    &::before {
      background-color: $bg-color;
    }

    thead {
      color: $font-hover-color;
      font-weight: 500;

      &.is-group {
        th {
          background: $transparent;
        }
      }
    }

    th {
      background-color: $transparent;

      &.is-leaf {
        border-bottom: 1px solid $base-border-color;
      }

      & > .cell.highlight {
        color: $border-active-bottom-color;
      }

      &.required {
        & > div {
          &::before {
            background: $bg-required-color;
          }
        }
      }
    }

    td {
      border-bottom: 1px solid $base-border-color;
    }

    tr {
      background-color: $transparent;
    }

    .sort-caret.ascending {
      border-bottom-color: $base-font-border-color;
      top: 5px;
    }

    .sort-caret.descending {
      border-top-color: $base-font-border-color;
      bottom: 7px;
    }

    .ascending {
      .sort-caret {
        &.ascending {
          border-bottom-color: $border-active-bottom-color;
        }
      }
    }

    .descending {
      .sort-caret {
        &.descending {
          border-top-color: $border-active-bottom-color;
        }
      }
    }
  }

  .el-table--border {
    border: 1px solid $border-color;

    &::after {
      background-color: $bg-color;
    }

    td,
    th {
      border-right: 1px solid $border-color;
    }

    th {
      border-bottom: 1px solid $bg-color;

      &.gutter {
        &:last-of-type {
          border-bottom: 1px solid $border-color;
          border-bottom-width: 1px;
        }
      }
    }
  }

  .el-table--group {
    border: 1px solid $border-color;

    &::after {
      background-color: $bg-color;
    }
  }

  .el-table__body-wrapper {
    .el-table--border {
      &.is-scrolling-right {
        & ~ .el-table__fixed-right {
          border-left: 1px solid $bg-color;
        }
      }

      &.is-scrolling-left {
        & ~ .el-table__fixed {
          border-right: 1px solid $border-color;
        }
      }
    }
  }

  .el-table__fixed-right-patch {
    border-bottom: 1px solid $bg-color;
    background-color: $bg-color;
  }

  .el-table__fixed,
  .el-table__fixed-right {
    -webkit-box-shadow: 0 0 10px $bg-box-shadow-color;
    box-shadow: 0 0 10px $bg-box-shadow-color;
    background-color: $bg-color;

    &::before {
      background-color: $bg-color;
    }
  }

  .el-table__fixed-footer-wrapper {
    tbody {
      td {
        border-top: 1px solid $border-color;
        background-color: $bg-hover-color;
        color: $base-font-border-color;
      }
    }
  }

  .el-table__footer-wrapper {
    td {
      border-top: 1px solid $border-color;
    }

    tbody {
      td {
        background-color: $bg-hover-color;
        color: $base-font-border-color;
      }
    }
  }

  .el-table__header-wrapper {
    tbody {
      td {
        background-color: $bg-hover-color;
        color: $base-font-border-color;
      }
    }
  }

  .el-picker-panel,
  .el-table-filter {
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  tr {
    &.el-table__row {
      &:hover {
        background-color: $bg-hover-color;
      }
    }
  }

  .el-table--striped {
    .el-table__body {
      tr {
        &.el-table__row--striped {
          td {
            background: $bg-hover-color;
          }

          &.current-row {
            td {
              background-color: $bg-hover-color;
            }
          }
        }
      }
    }
  }

  .el-table__body {
    tr {
      &.hover-row {
        & > td {
          background-color: $bg-hover-color;
        }

        &.current-row {
          & > td {
            background-color: $bg-hover-color;
          }
        }

        &.el-table__row--striped {
          & > td {
            background-color: $bg-hover-color;
          }

          &.current-row {
            & > td {
              background-color: $bg-hover-color;
            }
          }
        }
      }

      &.current-row {
        & > td {
          background-color: $bg-hover-color;
        }
      }
    }
  }

  .el-table__column-resize-proxy {
    border-left: 1px solid $border-color;
  }

  .el-table__column-filter-trigger {
    i {
      color: $font-item-color;
    }
  }

  .el-table--enable-row-transition {
    .el-table__body {
      td {
        -webkit-transition: background-color 0.25s ease;
        transition: background-color 0.25s ease;
      }
    }
  }

  .el-table--enable-row-hover {
    .el-table__body {
      tr {
        &:hover {
          & > td {
            background-color: $bg-hover-color;
          }
        }
      }
    }
  }

  .el-table-filter {
    border: 1px solid $border-color;
    background-color: $white;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-table-filter__list-item {
    &:hover {
      background-color: $bg-list-item-color;
      color: $font-primary;
    }

    &.is-active {
      background-color: $border-active-bottom-color;
      color: $white;
    }
  }

  .el-table-filter__bottom {
    border-top: 1px solid $border-color;

    button {
      color: $base-font-border-color;

      &:hover {
        color: $border-active-bottom-color;
      }

      &.is-disabled {
        color: $base-font-border-color;
        cursor: not-allowed;
      }
    }
  }

  .el-date-table {
    th {
      color: $base-font-border-color;
      border-bottom: solid 1px $border-color;
    }

    td {
      &.in-range {
        div {
          background-color: $bg-hover-color;

          &:hover {
            background-color: $bg-hover-color;
          }
        }
      }

      &.next-month,
      &.prev-month {
        color: $base-font-border-color;
      }

      &.today {
        position: relative;

        .cell {
          color: $border-active-bottom-color;
        }

        span {
          color: $border-active-bottom-color;
          font-weight: 700;
        }

        &.end-date {
          span {
            color: $white;
          }
        }

        &.start-date {
          span {
            color: $white;
          }
        }
      }

      &.available {
        &:hover {
          color: $border-active-bottom-color;
        }
      }

      &.current {
        &:not(.disabled) {
          span {
            color: $white;
            background-color: $border-active-bottom-color;
          }
        }
      }

      &.end-date,
      &.start-date {
        div {
          color: $white;
        }

        span {
          background-color: $border-active-bottom-color;
        }
      }

      &.disabled {
        div {
          background-color: $bg-disabled-color;
          color: $base-font-border-color;
        }
      }

      &.selected {
        div {
          background-color: $bg-inner-color;

          &:hover {
            background-color: $bg-inner-color;
          }
        }

        span {
          background-color: $border-active-bottom-color;
          color: $white;
        }
      }

      &.week {
        color: $base-font-border-color;
      }
    }

    &.is-week-mode {
      .el-date-table__row {
        &.current {
          div {
            background-color: $bg-hover-color;

            &:hover {
              background-color: $bg-hover-color;
            }
          }
        }

        &:hover {
          td {
            &.available {
              &:hover {
                color: $base-font-border-color;
              }
            }
          }

          div {
            background-color: $bg-hover-color;

            &:hover {
              background-color: $bg-hover-color;
            }
          }
        }
      }
    }
  }

  .el-month-table {
    td {
      &.today {
        &.end-date,
        &.start-date {
          .cell {
            color: $white;
          }
        }
      }

      &.disabled {
        .cell {
          background-color: $bg-hover-color;
          color: $base-font-border-color;

          &:hover {
            color: $base-font-border-color;
          }
        }
      }

      &.in-range {
        div {
          background-color: $bg-inner-color;

          &:hover {
            background-color: $bg-inner-color;
          }
        }
      }

      &.end-date,
      &.start-date {
        div {
          color: $white;
        }

        .cell {
          color: $white;
          background-color: $border-active-bottom-color;
        }
      }

      &.current {
        &:not(.disabled) {
          .cell {
            color: $border-active-bottom-color;
          }
        }
      }

      .cell {
        color: $base-font-border-color;

        &:hover {
          color: $border-active-bottom-color;
        }
      }
    }
  }

  .el-year-table {
    .el-icon {
      color: $base-font-border-color;
    }

    td {
      &.today {
        .cell {
          color: $border-active-bottom-color;
        }
      }

      &.disabled {
        .cell {
          background-color: $bg-hover-color;
          color: $base-font-border-color;

          &:hover {
            color: $base-font-border-color;
          }
        }
      }

      &.current {
        &:not(.disabled) {
          .cell {
            color: $border-active-bottom-color;
          }
        }
      }

      .cell {
        color: $base-font-border-color;

        &:hover {
          color: $border-active-bottom-color;
        }
      }
    }
  }

  .el-date-range-picker__content {
    &.is-left {
      border-right: 1px solid $base-font-border-color;
    }
  }

  .el-date-range-picker__time-header {
    border-bottom: 1px solid $base-font-border-color;

    & > .el-icon-arrow-right {
      color: $base-font-border-color;
    }
  }

  .el-date-range-picker__time-picker-wrap {
    .el-picker-panel {
      background: $bg-color;
    }
  }

  .el-date-picker__time-header {
    border-bottom: 1px solid $base-font-border-color;
  }

  .el-date-picker__header--bordered {
    border-bottom: solid 1px $border-color;
  }

  .el-date-picker__header-label {
    color: $base-font-border-color;

    &.active,
    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .time-select-item {
    &.selected {
      &:not(.disabled) {
        color: $border-active-bottom-color;
        font-weight: 700;
      }
    }

    &.disabled {
      color: $base-border-color;
    }

    &:hover {
      background-color: $bg-hover-color;
    }
  }

  .el-date-editor {
    .el-range__icon {
      color: $base-font-border-color;
    }

    .el-range-input {
      color: $base-font-border-color;
    }

    .el-range-input {
      &::-webkit-input-placeholder {
        color: $font-placeholder-color;
      }

      &:-ms-input-placeholder {
        color: $font-placeholder-color;
      }

      &::-ms-input-placeholder {
        color: $font-placeholder-color;
      }

      &::placeholder {
        color: $font-placeholder-color;
      }
    }

    .el-range-separator {
      color: $base-font-border-color;
    }

    .el-range__close-icon {
      color: $base-font-border-color;
    }
  }

  .el-range-editor {
    &.is-active {
      border-color: $border-active-bottom-color;

      &:hover {
        border-color: $border-active-bottom-color;
      }
    }

    &.is-disabled {
      background-color: $bg-disabled-color;
      border-color: $base-border-color;
      color: $base-font-border-color;

      &:hover,
      &:focus {
        border-color: $base-border-color;
      }

      input {
        background-color: $bg-disabled-color;
        color: $base-font-border-color;

        &::-webkit-input-placeholder {
          color: $font-placeholder-color;
        }

        &:-ms-input-placeholder {
          color: $font-placeholder-color;
        }

        &::-ms-input-placeholder {
          color: $font-placeholder-color;
        }

        &::placeholder {
          color: $font-placeholder-color;
        }
      }

      .el-range-separator {
        color: $base-font-border-color;
      }
    }
  }

  .el-picker-panel,
  .el-time-panel {
    color: $white;
    border: 1px solid $base-font-border-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    background: $bg-color;
  }

  .el-picker-panel__footer {
    border-top: 1px solid $bg-color;
    background-color: $bg-color;
  }

  .el-picker-panel__shortcut {
    background-color: transparent;
    color: $base-font-border-color;

    &:hover {
      color: $border-active-bottom-color;
    }

    &.active {
      background-color: $bg-active-color;
      color: $border-active-bottom-color;
    }
  }

  .el-picker-panel__btn {
    border: 1px solid $border-color;
    color: $font-btn-color;
    background-color: transparent;

    &[disabled] {
      color: $font-disabled-color;
    }
  }

  .el-picker-panel__icon-btn {
    color: $base-font-border-color;

    &:hover {
      color: $border-active-bottom-color;
    }

    &.is-disabled {
      color: $base-disabled-color;
    }
  }

  .el-picker-panel [slot='sidebar'],
  .el-picker-panel__sidebar {
    border-right: 1px solid $base-font-border-color;
    background-color: $transparent;
  }

  .el-time-spinner__wrapper {
    &.is-arrow {
      .el-time-spinner__item {
        &:hover {
          &:not(.disabled):not(.active) {
            background: $white;
            cursor: default;
          }
        }
      }
    }
  }

  .el-time-spinner__arrow {
    color: $font-item-color;

    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .el-time-spinner__item {
    color: $base-font-border-color;

    &:hover {
      &:not(.disabled) {
        &:not(.active) {
          background: $bg-hover-color;
        }
      }
    }

    &.active {
      &:not(.disabled) {
        color: $base-font-border-color;
      }
    }

    &.disabled {
      color: $base-font-border-color;
    }
  }

  .el-time-panel {
    border: 1px solid $base-border-color;
    background-color: $bg-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-time-panel__content {
    &::after,
    &::before {
      border-top: 1px solid $base-border-color;
      border-bottom: 1px solid $base-border-color;
    }
  }

  .el-time-panel__footer {
    border-top: 1px solid $base-font-border-color;
  }

  .el-time-panel__btn {
    background-color: $transparent;
    color: $base-font-border-color;

    &.confirm {
      color: $border-active-bottom-color;
    }
  }

  .el-time-range-picker__body {
    border: 1px solid $base-border-color;
  }

  .el-popover {
    background: $bg-dialog-color;
    border: 1px solid $bg-dialog-color;
    color: $font-title-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-popover__title {
    color: $base-font-border-color;
  }

  .v-modal {
    background: $black;
  }

  .el-message-box {
    background-color: $bg-color;
    border: 1px solid $bg-color;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-message-box__title {
    color: $base-font-border-color;
  }

  .el-form-item {
    &.is-error {
      .el-input__inner {
        border-color: $danger-color;

        &:focus {
          border-color: $danger-color;
        }
      }

      .el-textarea__inner {
        border-color: $danger-color;

        &:focus {
          border-color: $danger-color;
        }
      }

      .el-input__validateIcon {
        color: $danger-color;
      }
    }

    &.is-required {
      &:not(.is-no-asterisk) {
        &.el-form-item__label-wrap {
          & > .el-form-item__label {
            &:before {
              color: $danger-color;
            }
          }
        }

        & > .el-form-item__label {
          &:before {
            color: $danger-color;
          }
        }
      }
    }
  }

  .el-message-box__input {
    input {
      &.invalid {
        border-color: $danger-color;

        &:focus {
          border-color: $danger-color;
        }
      }
    }
  }

  .el-message-box__headerbtn {
    .el-message-box__close {
      color: $font-item-color;
    }

    &:focus,
    &:hover {
      .el-message-box__close {
        color: $border-active-bottom-color;
      }
    }
  }

  .el-message-box__content {
    color: $base-font-border-color;
  }

  .el-message-box__status {
    &.el-icon-success {
      color: $success-color;
    }

    &.el-icon-info {
      color: $font-item-color;
    }

    &.el-icon-warning {
      color: $waring-color;
    }

    &.el-icon-error {
      color: $danger-color;
    }
  }

  .el-message-box__errormsg {
    color: $danger-color;
  }

  .el-breadcrumb__separator {
    color: $base-font-border-color;
  }

  .el-breadcrumb__inner {
    color: $font-breadcrumb-color;

    &.is-link,
    a {
      color: $font-breadcrumb-color;

      &:hover {
        color: $border-active-bottom-color;
      }
    }
  }

  .el-breadcrumb__item {
    &:last-child {
      .el-breadcrumb__inner {
        color: $base-font-border-color;

        &:hover {
          color: $base-font-border-color;
        }

        a {
          color: $base-font-border-color;

          &:hover {
            color: $base-font-border-color;
          }
        }
      }
    }
  }

  .el-form-item__label {
    color: $font-hover-color;
  }

  .el-form-item__error {
    color: $danger-color;
  }

  .el-tabs__active-bar {
    background-color: $border-active-bottom-color;
  }

  .el-tabs__new-tab {
    border: 1px solid $border-color;
    color: $font-color;

    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .el-tabs__nav-wrap {
    &::after {
      background-color: $base-border-color;
    }
  }

  .el-tabs__nav-next,
  .el-tabs__nav-prev {
    color: $font-item-color;
  }

  .el-tabs__item {
    color: $base-font-border-color;

    .el-icon-close {
      &:hover {
        background-color: $base-font-border-color;
        color: $white;
      }
    }

    &:focus {
      &.is-active {
        &.is-focus {
          &:not(:active) {
            -webkit-box-shadow: 0 0 2px 2px $border-active-bottom-color inset;
            box-shadow: 0 0 2px 2px $border-active-bottom-color inset;
          }
        }
      }
    }

    &:hover,
    &.is-active {
      color: $border-active-bottom-color;
    }

    &.is-disabled {
      color: $base-font-border-color;
    }
  }

  .el-tabs--card {
    & > .el-tabs__header {
      border-bottom: 1px solid $base-border-color;

      .el-tabs__nav {
        border: 1px solid $base-border-color;
      }

      .el-tabs__item {
        border-bottom: 1px solid transparent;
        border-left: 1px solid $base-border-color;

        &.is-active {
          border-bottom-color: $border-focus-color;
        }
      }
    }
  }

  .infinite-list {
    color: $font-title-color;

    .infinite-list-item {
      color: $font-title-color;
    }
  }

  el-tabs {
    .el-tabs__content {
      color: $font-title-color;
    }
  }

  .el-tabs--border-card {
    background: $bg-color;
    border: 1px solid $border-gray-color;
    -webkit-box-shadow: 0 2px 4px 0 $bg-box-shadow-color, 0 0 6px 0 $bg-shadow-color;
    box-shadow: 0 2px 4px 0 $bg-box-shadow-color, 0 0 6px 0 $bg-shadow-color;

    & > .el-tabs__content {
      padding: 15px;
    }

    & > .el-tabs__header {
      border-bottom: 1px solid $base-border-color;
      color: rgba($white, 0.65);
      background-color: rgba($white, 0.04);
    }

    & > .el-tabs__header {
      .el-tabs__item {
        border: 1px solid $transparent;
        color: rgba($white, 0.65);
        background-color: rgba($white, 0.04);

        &.is-active {
          color: $border-active-bottom-color;
          background-color: $bg-color;
          border-right-color: $border-gray-color;
          border-left-color: $border-gray-color;
        }

        &:not(.is-disabled) {
          &:hover {
            color: $border-active-bottom-color;
          }
        }

        &.is-disabled {
          color: $base-font-border-color;
        }
      }
    }
  }

  .el-tabs--bottom {
    &.el-tabs--border-card {
      .el-tabs__header {
        &.is-bottom {
          border-top: 1px solid $border-gray-color;
        }
      }
    }
  }

  .el-tabs--left {
    &.el-tabs--card {
      .el-tabs__nav {
        border-bottom: 1px solid $base-border-color;
      }

      .el-tabs__item {
        &.is-left {
          border-right: 1px solid $base-border-color;
          border-top: 1px solid $base-border-color;

          &:first-child {
            border-right: 1px solid $base-border-color;
          }

          &.is-active {
            border: 1px solid $base-border-color;
            border-right-color: $white;
          }
        }
      }
    }

    &.el-tabs--border-card {
      .el-tabs__header {
        &.is-left {
          border-right: 1px solid $border-tabs-color;
        }
      }

      .el-tabs__item {
        &.is-left {
          border: 1px solid $transparent;

          &.is-active {
            border-color: $border-tab-active-color transparent;
          }
        }
      }
    }
  }

  .el-tabs--right {
    &.el-tabs--card {
      .el-tabs__item {
        &.is-right {
          border-top: 1px solid $base-border-color;

          &:first-child {
            border-left: 1px solid $base-border-color;
          }

          &.is-active {
            border: 1px solid $base-border-color;
            border-left-color: $white;
          }
        }
      }

      .el-tabs__nav {
        border-bottom: 1px solid $base-border-color;
      }
    }

    &.el-tabs--border-card {
      .el-tabs__header {
        &.is-right {
          border-left: 1px solid $border-tabs-color;
        }
      }

      .el-tabs__item {
        &.is-right {
          &.is-active {
            border-color: $border-tab-active-color transparent;
          }
        }
      }
    }
  }

  .el-tree {
    padding: 5px;
    height: 100%;
    min-width: 180px;
    overflow: scroll;
    color: $base-font-border-color;
    background-color: $transparent;
  }

  .el-tree__empty-text {
    color: $font-item-color;
  }

  .el-tree__drop-indicator {
    background-color: $border-active-bottom-color;
  }

  .el-tree-node {
    &:focus {
      & > .el-tree-node__content {
        background-color: $bg-hover-color;
        color: $font-hover-color;
      }
    }

    &.is-drop-inner {
      & > .el-tree-node__content {
        .el-tree-node__label {
          background-color: $bg-hover-color;
          color: $base-font-border-color;
        }
      }
    }
  }

  .el-tree-node__content {
    border-bottom: 1px dashed $border-color;
    &:hover {
      background-color: $bg-select-hover-color;
      color: $font-hover-color;
    }
  }

  .el-tree-node__expand-icon {
    color: $base-font-border-color;

    &.is-leaf {
      color: transparent;
    }
  }

  .el-tree-node__loading-icon {
    color: $base-font-border-color;
  }

  .el-tree-node {
    & > .el-tree-node__children {
      background-color: transparent;
    }
  }

  .el-tree-node__children {
    .is-current {
      .el-tree-node__content {
        border-right: 2px solid $font-hover-color;
        background-color: $bg-hover-color;
        color: $font-hover-color;
      }
    }
  }

  .el-tree--highlight-current {
    .el-tree-node {
      &.is-current {
        & > .el-tree-node__content {
          background-color: $bg-hover-color;
        }
      }
    }
  }

  .el-alert {
    background-color: $white;

    &.is-light {
      .el-alert__closebtn {
        color: $base-font-border-color;
      }
    }

    &.is-dark {
      .el-alert__closebtn,
      .el-alert__description {
        color: $white;
      }
    }
  }

  .el-alert--success {
    &.is-light {
      background-color: $bg-success-light-color;
      border: 1px solid $border-success-light-color;
      color: #49aa19;

      .el-alert__description {
        color: #49aa19;
      }
    }

    &.is-dark {
      background-color: $bg-success-light-color;
      border: 1px solid $border-success-light-color;
      color: #49aa19;
    }
  }

  .el-alert--info {
    &.is-light {
      background-color: $bg-hover-color;
      color: $font-focus-color;
      border: 1px solid #153450;
    }

    .el-alert__description {
      color: $font-focus-color;
    }

    &.is-dark {
      background-color: $bg-hover-color;
      border: 1px solid #153450;
      color: $font-focus-color;
    }
  }

  .el-alert--warning {
    &.is-light {
      background-color: #2b2111;
      color: #d87a16;
      border: 1px solid #594214;

      .el-alert__description {
        color: #d87a16;
      }
    }

    &.is-dark {
      background-color: #2b2111;
      color: #d87a16;
      border: 1px solid #594214;
    }
  }

  .el-alert--error {
    &.is-light {
      background-color: #2a1215;
      color: #d32029;
      border: 1px solid #58181c;

      .el-alert__description {
        color: #d32029;
      }
    }

    &.is-dark {
      background-color: #2a1215;
      color: #d32029;
      border: 1px solid #58181c;
    }
  }

  .el-notification {
    border-radius: 2px;
    border: 1px solid $bg-dialog-color;
    background-color: $bg-dialog-color;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-notification__title {
    color: $font-title-color;
  }

  .el-notification__content {
    color: $font-color;
  }

  .el-notification__closeBtn {
    color: $font-color;

    &:hover {
      color: $font-title-color;
    }
  }

  .el-notification {
    .el-icon-success {
      color: #49aa19;
    }

    .el-icon-error {
      color: #d32029;
    }

    .el-icon-info {
      color: $font-focus-color;
    }

    .el-icon-warning {
      color: #d87a16;
    }
  }

  .el-input-number__decrease,
  .el-input-number__increase {
    background: $transparent;
    color: $base-font-border-color;

    &:hover {
      color: $border-active-bottom-color;
    }

    &:hover {
      &:not(.is-disabled) {
        & ~ .el-input {
          .el-input__inner {
            &:not(.is-disabled) {
              border-color: $border-active-bottom-color;
            }
          }
        }
      }
    }

    &.is-disabled {
      color: $base-font-border-color;
    }
  }

  .el-input-number__increase {
    border-left: 1px solid $base-border-color;
  }

  .el-input-number__decrease {
    border-right: 1px solid $base-border-color;
  }

  .el-input-number {
    &.is-controls-right {
      .el-input-number__increase {
        border-bottom: 1px solid $border-gray-color;
      }

      .el-input-number__decrease {
        border-left: 1px solid $border-gray-color;
      }
    }

    &.is-disabled {
      .el-input-number__decrease,
      .el-input-number__increase {
        border-color: $base-border-color;
        color: $base-font-border-color;

        &:hover {
          color: $base-font-border-color;
        }
      }
    }
  }

  .el-tooltip__popper {
    &.is-dark {
      background: $bg-dialog-color;
      border: 1px solid $bg-dialog-color;
      color: $font-color;
      -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
      box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    }

    &.is-light {
      background: $bg-dialog-color;
      border: 1px solid $bg-dialog-color;
      color: $base-font-border-color;
    }

    &.is-light[x-placement^='left'] {
      .popper__arrow {
        border-left-color: $bg-dialog-color;

        &::after {
          border-left-color: $bg-dialog-color;
        }
      }
    }

    &.is-light[x-placement^='top'] {
      .popper__arrow {
        border-top-color: $bg-dialog-color;

        &::after {
          border-top-color: $bg-dialog-color;
        }
      }
    }

    &.is-light[x-placement^='bottom'] {
      .popper__arrow {
        border-bottom-color: $bg-dialog-color;

        &::after {
          border-bottom-color: $bg-dialog-color;
        }
      }
    }

    &.is-light[x-placement^='right'] {
      .popper__arrow {
        border-right-color: $bg-dialog-color;

        &::after {
          border-right-color: $bg-dialog-color;
        }
      }
    }

    .popper__arrow {
      border-color: transparent;

      &::after {
        border-color: transparent;
      }
    }
  }

  .el-tooltip__popper[x-placement^='top'] {
    .popper__arrow {
      border-top-color: $bg-dialog-color;

      &::after {
        border-top-color: $bg-dialog-color;
      }
    }
  }

  .el-tooltip__popper[x-placement^='bottom'] {
    .popper__arrow {
      border-bottom-color: $bg-dialog-color;

      &::after {
        border-bottom-color: $border-tooltip-arrow-color;
      }
    }
  }

  .el-tooltip__popper[x-placement^='right'] {
    .popper__arrow {
      border-right-color: $bg-dialog-color;

      &::after {
        border-right-color: $bg-dialog-color;
      }
    }
  }

  .el-tooltip__popper[x-placement^='left'] {
    .popper__arrow {
      border-left-color: $bg-dialog-color;

      &::after {
        border-left-color: $bg-dialog-color;
      }
    }
  }

  .el-slider__runway {
    background-color: $base-border-color;

    &.disabled {
      .el-slider__bar {
        background-color: $base-font-border-color;
      }

      .el-slider__button {
        border-color: $base-font-border-color;
      }
    }
  }

  .el-slider__bar {
    background-color: $border-active-bottom-color;
  }

  .el-slider__button-wrapper {
    background-color: transparent;
    outline: none;
  }

  .el-slider__button {
    border: 2px solid $border-active-bottom-color;
    background-color: $white;
  }

  .el-slider__stop {
    background-color: $white;
  }

  .el-slider__marks-text {
    color: $font-item-color;
  }

  .el-slider {
    &.is-vertical {
      &.el-slider--with-input {
        .el-slider__input {
          &:active {
            .el-input-number__decrease,
            .el-input-number__increase {
              border-color: $border-active-bottom-color;
            }
          }

          &:hover {
            .el-input-number__decrease,
            .el-input-number__increase {
              border-color: $base-font-border-color;
            }
          }

          .el-input-number__decrease,
          .el-input-number__increase {
            border: 1px solid $border-gray-color;
            -webkit-transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
            transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
          }
        }
      }
    }
  }

  .el-loading-mask {
    background-color: $base-mask-color;
  }

  .el-loading-spinner {
    .el-loading-text {
      color: $border-active-bottom-color;
    }

    .path {
      stroke: $border-active-bottom-color;
    }

    i {
      color: $border-active-bottom-color;
    }
  }

  .el-upload__tip {
    color: $base-font-border-color;
  }

  .el-upload--picture-card {
    background-color: #fbfdff;
    border: 1px dashed #c0ccda;

    i {
      color: #8c939d;
    }

    &:hover {
      border-color: $border-active-bottom-color;
      color: $border-active-bottom-color;
    }
  }

  .el-upload {
    &:focus {
      border-color: $border-active-bottom-color;
      color: $border-active-bottom-color;

      .el-upload-dragger {
        border-color: $border-active-bottom-color;
      }
    }
  }

  .el-upload-dragger {
    background-color: $white;
    border: 1px dashed #d9d9d9;

    .el-icon-upload {
      color: $base-font-border-color;
    }

    .el-upload__text {
      color: $base-font-border-color;

      em {
        color: $border-active-bottom-color;
      }
    }

    &.is-dragover {
      background-color: $bg-dragover-color;
      border: 2px dashed $border-active-bottom-color;
    }

    & ~ .el-upload__files {
      border-top: 1px solid $border-gray-color;
    }

    &:hover {
      border-color: $border-active-bottom-color;
    }
  }

  .el-upload-list__item {
    color: $base-font-border-color;

    .el-icon-upload-success {
      color: $success-color;
    }

    .el-icon-close {
      color: $base-font-border-color;
    }

    .el-icon-close-tip {
      color: $border-active-bottom-color;
    }

    &:hover {
      background-color: $bg-hover-color;
    }

    &.is-success {
      .el-upload-list__item-name {
        &:focus,
        &:hover {
          color: $border-active-bottom-color;
          cursor: pointer;
        }
      }
    }
  }

  .el-upload-list__item-name {
    color: $base-font-border-color;

    [class^='el-icon'] {
      color: $font-item-color;
    }
  }

  .el-upload-list__item-delete {
    color: $base-font-border-color;

    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .el-upload-list--picture-card {
    .el-upload-list__item {
      background-color: $white;
      border: 1px solid #c0ccda;

      .el-icon-check,
      .el-icon-circle-check {
        color: $white;
      }
    }

    .el-upload-list__item-status-label {
      background: #13ce66;
      -webkit-box-shadow: 0 0 1pc 1px $bg-box-shadow;
      box-shadow: 0 0 1pc 1px $bg-box-shadow;
    }

    .el-upload-list__item-actions {
      color: $white;
      background-color: rgba(0, 0, 0, 0.5);

      .el-upload-list__item-delete {
        color: inherit;
      }
    }
  }

  .el-upload-list--picture {
    .el-upload-list__item {
      background-color: $white;
      border: 1px solid #c0ccda;

      .el-icon-check,
      .el-icon-circle-check {
        color: $white;
      }
    }

    .el-upload-list__item-thumbnail {
      background-color: $white;
    }

    .el-upload-list__item-status-label {
      background: #13ce66;
      -webkit-box-shadow: 0 1px 1px #ccc;
      box-shadow: 0 1px 1px #ccc;
    }
  }

  .el-upload-cover__label {
    background: #13ce66;
    -webkit-box-shadow: 0 0 1pc 1px $bg-box-shadow;
    box-shadow: 0 0 1pc 1px $bg-box-shadow;

    i {
      color: $white;
    }
  }

  .el-upload-cover__interact {
    background-color: rgba(0, 0, 0, 0.72);

    .btn {
      color: $white;

      i {
        color: $white;
      }
    }
  }

  .el-upload-cover__title {
    background-color: $white;
    color: $base-font-border-color;
  }

  .el-progress__text {
    color: $base-font-border-color;
  }

  .el-progress {
    &.is-success {
      .el-progress-bar__inner {
        background-color: #49aa19;
      }

      .el-progress__text {
        color: $success-color;
      }
    }

    &.is-warning {
      .el-progress-bar__inner {
        background-color: #e6a23c;
      }

      .el-progress__text {
        color: $waring-color;
      }
    }

    &.is-exception {
      .el-progress-bar__inner {
        background-color: #a61d24;
      }

      .el-progress__text {
        color: $danger-color;
      }
    }
  }

  .el-progress-bar__outer {
    background-color: $border-color;
  }

  .el-progress-bar__inner {
    background-color: $font-focus-color;
  }

  .el-progress-bar__innerText {
    color: $white;
  }

  .el-spinner-inner {
    .path {
      stroke: #ececec;
    }
  }

  .el-message {
    border-color: $border-color;
    background-color: $bg-color;

    .el-icon-error {
      color: #a61d24;
    }

    .el-icon-success {
      color: #49aa19;
    }

    .el-icon-info {
      color: $font-color;
    }

    .el-icon-warning {
      color: #d89614;
    }
  }

  .el-message--info {
    .el-message__content {
      color: $font-color;
    }
  }

  .el-message--success {
    .el-message__content {
      color: $font-color;
    }
  }

  .el-message--warning {
    background-color: $bg-color;

    .el-message__content {
      color: $font-color;
    }
  }

  .el-message--error {
    background-color: $bg-color;

    .el-message__content {
      color: $font-color;
    }
  }

  .el-message__closeBtn {
    color: $base-font-border-color;

    &:hover {
      color: $white;
    }
  }

  .el-badge__content {
    background-color: #d32029;
    color: $white;
    border: 1px solid #d32029;
  }

  .el-badge__content--primary {
    background-color: $font-focus-color;
    border: 1px solid $font-focus-color;
  }

  .el-badge__content--success {
    background-color: #49aa19;
    border: 1px solid #49aa19;
  }

  .el-badge__content--warning {
    background-color: #d87a16;
    border: 1px solid #d87a16;
  }

  .el-badge__content--info {
    background-color: $font-item-color;
  }

  .el-badge__content--danger {
    background-color: $danger-color;
  }

  .el-card {
    border: 1px solid $border-dialog-color;
    background-color: $bg-color;
    color: rgba(255, 255, 255, 0.65);
  }

  .el-card__header {
    color: $font-title-color;
    border-bottom: 1px solid $border-color;
  }

  .el-rate__icon {
    color: $base-font-border-color;
  }

  .el-steps--simple {
    background: $bg-hover-color;
  }

  .el-step {
    &__head {
      &.is-process {
        color: $base-font-border-color;
        border-color: $base-font-border-color;
      }

      &.is-wait {
        color: $base-font-border-color;
        border-color: $base-font-border-color;
      }

      &.is-success {
        color: $font-focus-color;
        border-color: $font-focus-color;
      }

      &.is-error {
        color: #a61d24;
        border-color: #a61d24;
      }

      .is-finish {
        color: $font-focus-color;
        border-color: $font-focus-color;
      }
    }

    &__icon {
      background: $font-focus-color;

      &.is-text {
        border-color: $font-focus-color;
      }

      &-inner {
        color: inherit;
      }
    }

    &__line {
      border-color: inherit;
      background-color: $font-focus-color;

      &-inner {
        border-color: inherit;
      }
    }

    &__title {
      &.is-process {
        color: $font-title-color;
      }

      &.is-wait {
        color: rgba(255, 255, 255, 0.45);
      }

      &.is-success {
        color: $font-color;
      }

      &.is-error {
        color: #a61d24;
      }

      &.is-finish {
        color: $font-color;
      }
    }

    .is-success {
      .el-step__icon {
        background-color: $bg-color;
      }
    }

    .is-process {
      .el-step__icon {
        background-color: $transparent;
        color: $white;
      }

      .el-step__line {
        background-color: rgba(255, 255, 255, 0.3);
      }
    }

    .is-wait {
      .el-step__icon {
        background-color: $bg-color;
        color: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.3);
      }
    }

    .is-error {
      .el-step__icon {
        background-color: $bg-color;
      }
    }

    .is-finish {
      .el-step__icon {
        background-color: $bg-color;
      }
    }

    &__description {
      &.is-process {
        color: $font-color;
      }

      &.is-wait {
        color: rgba(255, 255, 255, 0.45);
      }

      &.is-success {
        color: rgba(255, 255, 255, 0.45);
      }

      &.is-error {
        color: #a61d24;
      }

      &.is-finish {
        color: rgba(255, 255, 255, 0.45);
      }
    }

    &.is-simple {
      .el-step__arrow {
        &::after,
        &::before {
          background: $base-font-border-color;
        }
      }
    }
  }

  .el-carousel__arrow {
    background-color: rgba(31, 45, 61, 0.11);
    color: $white;

    &:hover {
      background-color: rgba(102, 118, 134, 0.23);
    }
  }

  .el-carousel__indicators--outside {
    button {
      background-color: $base-font-border-color;
    }
  }

  .el-carousel__indicator {
    background-color: transparent;
  }

  .el-carousel__button {
    background-color: $white;
  }

  .el-carousel__mask {
    background-color: $white;
  }

  .el-collapse {
    border-top: 1px solid $border-color;
    border-bottom: 1px solid $border-color;
  }

  .el-collapse-item {
    &.is-disabled {
      .el-collapse-item__header {
        color: $base-disabled-color;
      }
    }
  }

  //.el-collapse-item__header.is-active {
  //    border-bottom-color: transparent !important;
  //}

  .el-collapse-item__header {
    color: $font-title-color;
    background-color: transparent;
    border-bottom: 1px solid $border-color;
    -webkit-transition: border-bottom-color 0.3s;
    transition: border-bottom-color 0.3s;
    padding: 12px 16px;

    &.focusing {
      &:focus {
        &:not(:hover) {
          color: $border-active-bottom-color;
        }
      }
    }
    &.is-active {
      border-bottom-color: transparent !important;
    }
  }

  .el-collapse-item__wrap {
    background-color: $bg-color;
    border-bottom: 1px solid $border-color;
    padding: 16px;
  }

  .el-collapse-item__content {
    color: rgba(255, 255, 255, 0.65);
    margin-top: 8px;
  }

  .el-popper {
    .popper__arrow {
      border-color: $transparent;

      &::after {
        border-color: $transparent;
      }
    }
  }

  .el-popper[x-placement^='top'] {
    .popper__arrow {
      border-top-color: $bg-dialog-color;

      &::after {
        border-top-color: $bg-dialog-color;
      }
    }
  }

  .el-popper[x-placement^='bottom'] {
    .popper__arrow {
      border-bottom-color: $bg-dialog-color;

      &::after {
        border-bottom-color: $border-tooltip-arrow-color;
      }
    }
  }

  .el-popper[x-placement^='right'] {
    .popper__arrow {
      border-right-color: $bg-dialog-color;

      &::after {
        border-right-color: $bg-dialog-color;
      }
    }
  }

  .el-popper[x-placement^='left'] {
    .popper__arrow {
      border-left-color: $bg-dialog-color;

      &::after {
        border-left-color: $bg-dialog-color;
      }
    }
  }

  .el-tag {
    background-color: rgba(255, 255, 255, 0.04);
    border: 1px solid $base-border-color;
    color: $font-color;

    &.is-hit {
      border-color: $border-active-bottom-color;
    }

    .el-tag__close {
      color: $border-active-bottom-color;
    }

    .el-tag__close {
      &:hover {
        color: $white;
        background-color: $border-active-bottom-color;
      }
    }

    &.el-tag--info {
      background-color: rgba(255, 255, 255, 0.04);
      border: 1px solid $base-border-color;
      color: $font-color;

      &.is-hit {
        border-color: $font-item-color;
      }

      .el-tag__close {
        color: $white;

        &:hover {
          color: $white;
          background-color: $font-item-color;
        }
      }
    }

    &.el-tag--success {
      background-color: $bg-success-light-color;
      border-color: $border-success-light-color;
      color: $font-success-color;

      &.is-hit {
        border-color: $success-color;
      }

      .el-tag__close {
        color: $success-color;

        &:hover {
          color: $white;
          background-color: $success-color;
        }
      }
    }

    &.el-tag--warning {
      background-color: #2b1d11;
      border-color: #593815;
      color: #e89a3c;

      &.is-hit {
        border-color: $waring-color;
      }

      .el-tag__close {
        color: $waring-color;

        &:hover {
          color: $white;
          background-color: $waring-color;
        }
      }
    }

    &.el-tag--danger {
      background-color: #2a1215;
      border-color: #58181c;
      color: #e84749;

      &.is-hit {
        border-color: $danger-color;
      }

      .el-tag__close {
        color: $danger-color;

        &:hover {
          color: $white;
          background-color: $danger-color;
        }
      }
    }
  }

  .el-tag--dark {
    background-color: $border-active-bottom-color;
    border-color: $border-active-bottom-color;
    color: $white;

    &.is-hit {
      border-color: $border-active-bottom-color;
    }

    .el-tag__close {
      color: $white;

      &:hover {
        color: $white;
        background-color: $font-primary;
      }
    }

    &.el-tag--info {
      background-color: $font-item-color;
      border-color: $font-item-color;
      color: $white;

      &.is-hit {
        border-color: $font-item-color;
      }

      .el-tag__close {
        color: $white;

        &:hover {
          color: $white;
          background-color: #a6a9ad;
        }
      }
    }

    &.el-tag--success {
      background-color: $success-color;
      border-color: $success-color;
      color: $white;

      &.is-hit {
        border-color: $success-color;
      }

      .el-tag__close {
        color: $white;

        &:hover {
          color: $white;
          background-color: #85ce61;
        }
      }
    }

    &.el-tag--warning {
      background-color: $waring-color;
      border-color: $waring-color;
      color: $white;

      &.is-hit {
        border-color: $waring-color;
      }

      .el-tag__close {
        color: $white;

        &:hover {
          color: $white;
          background-color: #ebb563;
        }
      }
    }

    &.el-tag--danger {
      background-color: $danger-color;
      border-color: $danger-color;
      color: $white;

      &.is-hit {
        border-color: $danger-color;
      }

      .el-tag__close {
        color: $white;

        &:hover {
          color: $white;
          background-color: #f78989;
        }
      }
    }
  }

  .el-tag--plain {
    background-color: $white;
    border-color: #b3d8ff;
    color: $border-active-bottom-color;

    .el-tag__close {
      color: $border-active-bottom-color;

      &:hover {
        color: $white;
        background-color: $border-active-bottom-color;
      }
    }

    &.is-hit {
      border-color: $border-active-bottom-color;
    }

    &.el-tag--info {
      background-color: $white;
      border-color: #d3d4d6;
      color: $font-item-color;

      &.is-hit {
        border-color: $font-item-color;
      }

      .el-tag__close {
        color: $font-item-color;

        &:hover {
          color: $white;
          background-color: $font-item-color;
        }
      }
    }

    &.el-tag--success {
      background-color: $white;
      border-color: #c2e7b0;
      color: $success-color;

      &.is-hit {
        border-color: $success-color;
      }

      .el-tag__close {
        color: $success-color;

        &:hover {
          color: $white;
          background-color: $success-color;
        }
      }
    }

    &.el-tag--warning {
      background-color: $white;
      border-color: #f5dab1;
      color: $waring-color;

      &.is-hit {
        border-color: $waring-color;
      }

      .el-tag__close {
        color: $waring-color;

        &:hover {
          color: $white;
          background-color: $waring-color;
        }
      }
    }

    &.el-tag--danger {
      background-color: $white;
      border-color: #fbc4c4;
      color: $danger-color;

      &.is-hit {
        border-color: $danger-color;
      }

      .el-tag__close {
        color: $danger-color;

        &:hover {
          color: $white;
          background-color: $danger-color;
        }
      }
    }
  }

  .el-cascader {
    &:not(.is-disabled):hover {
      .el-input__inner {
        border-color: $base-font-border-color;
      }
    }

    .el-input {
      .el-input__inner {
        &:focus {
          border-color: $border-active-bottom-color;
        }
      }

      &.is-focus {
        .el-input__inner {
          border-color: $border-active-bottom-color;
        }
      }

      .el-icon-circle-close {
        &:hover {
          color: $font-item-color;
        }
      }
    }

    &.is-disabled {
      .el-cascader__label {
        color: $base-font-border-color;
      }
    }
  }

  .el-cascader__dropdown {
    background: $bg-select-color;
    border: 1px solid $bg-select-color;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-cascader__tags {
    .el-tag {
      background: $bg-tag-color;

      &:not(.is-hit) {
        border-color: transparent;
      }

      .el-icon-close {
        background-color: $base-font-border-color;
        color: $white;

        &:hover {
          background-color: $font-item-color;
        }
      }
    }
  }

  .el-cascader__suggestion-list {
    color: $base-font-border-color;
  }

  .el-cascader__suggestion-item {
    &:focus,
    &:hover {
      background: $bg-hover-color;
    }

    &.is-checked {
      color: $border-active-bottom-color;
      font-weight: 700;
    }
  }

  .el-cascader__empty-text {
    color: $base-font-border-color;
  }

  .el-cascader__search-input {
    color: $base-font-border-color;

    &::-webkit-input-placeholder {
      color: $font-placeholder-color;
    }

    &:-ms-input-placeholder {
      color: $font-placeholder-color;
    }

    &::-ms-input-placeholder {
      color: $font-placeholder-color;
    }

    &::placeholder {
      color: $font-placeholder-color;
    }
  }

  .el-color-predefine__color-selector {
    &.selected {
      -webkit-box-shadow: 0 0 3px 2px $border-active-bottom-color;
      box-shadow: 0 0 3px 2px $border-active-bottom-color;
    }

    &.is-alpha {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
    }
  }

  .el-color-hue-slider__bar {
    position: relative;
    background: -webkit-gradient(
      linear,
      left top,
      right top,
      from(red),
      color-stop(17%, #ff0),
      color-stop(33%, #0f0),
      color-stop(50%, #0ff),
      color-stop(67%, #00f),
      color-stop(83%, #f0f),
      to(red)
    );
    background: linear-gradient(to right, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red 100%);
    height: 100%;
  }

  .el-color-hue-slider__thumb {
    background: $white;
    border: 1px solid #f0f0f0;
    -webkit-box-shadow: 0 0 2px $bg-shadow-dark-color;
    box-shadow: 0 0 2px $bg-shadow-dark-color;
  }

  .el-color-hue-slider {
    background-color: red;

    &.is-vertical {
      .el-color-hue-slider__bar {
        background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(red),
          color-stop(17%, #ff0),
          color-stop(33%, #0f0),
          color-stop(50%, #0ff),
          color-stop(67%, #00f),
          color-stop(83%, #f0f),
          to(red)
        );
        background: linear-gradient(to bottom, red 0, #ff0 17%, #0f0 33%, #0ff 50%, #00f 67%, #f0f 83%, red 100%);
      }
    }
  }

  .el-color-svpanel__white {
    background: -webkit-gradient(linear, left top, right top, from($white), to(rgba(255, 255, 255, 0)));
    background: linear-gradient(to right, $white, rgba(255, 255, 255, 0));
  }

  .el-color-svpanel__black {
    background: -webkit-gradient(linear, left bottom, left top, from($black), to(rgba(0, 0, 0, 0)));
    background: linear-gradient(to top, $black, rgba(0, 0, 0, 0));
  }

  .el-color-svpanel__cursor {
    & > div {
      -webkit-box-shadow: 0 0 0 1.5px $white, inset 0 0 1px 1px rgba(0, 0, 0, 0.3), 0 0 1px 2px rgba(0, 0, 0, 0.4);
      box-shadow: 0 0 0 1.5px $white, inset 0 0 1px 1px rgba(0, 0, 0, 0.3), 0 0 1px 2px rgba(0, 0, 0, 0.4);
    }
  }

  .el-color-alpha-slider {
    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);

    &.is-vertical {
      .el-color-alpha-slider__bar {
        background: -webkit-gradient(linear, left top, left bottom, from(rgba(255, 255, 255, 0)), to(white));
        background: linear-gradient(to bottom, rgba(255, 255, 255, 0) 0, $white 100%);
      }
    }
  }

  .el-color-alpha-slider__bar {
    background: -webkit-gradient(linear, left top, right top, from(rgba(255, 255, 255, 0)), to(white));
    background: linear-gradient(to right, rgba(255, 255, 255, 0) 0, $white 100%);
  }

  .el-color-alpha-slider__thumb {
    background: $white;
    border: 1px solid #f0f0f0;
    -webkit-box-shadow: 0 0 2px $bg-shadow-dark-color;
    box-shadow: 0 0 2px $bg-shadow-dark-color;
  }

  .el-color-dropdown__value {
    color: $black;
  }

  .el-color-dropdown__btn {
    border: 1px solid #dcdcdc;
    color: $font-btn-color;
    background-color: transparent;

    &:hover {
      color: $border-active-bottom-color;
      border-color: $border-active-bottom-color;
    }
  }

  .el-color-dropdown__btn[disabled] {
    color: #ccc;
  }

  .el-color-dropdown__link-btn {
    color: $border-active-bottom-color;

    &:hover {
      color: $font-dropdown-btn;
    }
  }

  .el-color-picker__mask {
    background-color: $base-mask-color;
  }

  .el-color-picker__trigger {
    border: 1px solid $base-border-color;
  }

  .el-color-picker__color {
    border: 1px solid $base-empty-color;

    &.is-alpha {
      background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAwAAAAMCAIAAADZF8uwAAAAGUlEQVQYV2M4gwH+YwCGIasIUwhT25BVBADtzYNYrHvv4gAAAABJRU5ErkJggg==);
    }
  }

  .el-color-picker__empty {
    color: $base-empty-color;
  }

  .el-color-picker__icon {
    color: $white;
  }

  .el-color-picker__panel {
    background-color: $bg-color;
    border: 1px solid $border-color;
    -webkit-box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
    box-shadow: 0 2px 12px 0 $bg-box-shadow-color;
  }

  .el-textarea__inner {
    color: $base-font-border-color;
    background-color: $transparent;
    border: 1px solid $base-border-color;

    &::-webkit-input-placeholder {
      color: $font-placeholder-color;
    }

    &:-ms-input-placeholder {
      color: $font-placeholder-color;
    }

    &::-ms-input-placeholder {
      color: $font-placeholder-color;
    }

    &::placeholder {
      color: $font-placeholder-color;
    }

    &:hover {
      border-color: $base-font-border-color;
    }

    &:focus {
      border-color: $border-active-bottom-color;
    }
  }

  .el-textarea {
    .el-input__count {
      color: $font-item-color;
      background: $white;
    }

    &.is-disabled {
      .el-textarea__inner {
        background-color: $bg-disabled-color;
        border-color: $base-border-color;
        color: $base-font-border-color;

        &::placeholder {
          color: $font-placeholder-color;
        }

        &::-ms-input-placeholder {
          color: $font-placeholder-color;
        }

        &::-webkit-input-placeholder {
          color: $font-placeholder-color;
        }
      }
    }

    &.is-exceed {
      .el-textarea__inner {
        border-color: $danger-color;
      }

      .el-input__count {
        color: $danger-color;
      }
    }
  }

  .el-input__prefix,
  .el-input__suffix {
    color: $base-font-border-color;
  }

  .el-input__inner {
    background-color: $transparent;
    border: 1px solid $border-color;
    color: $font-color;

    &::-webkit-input-placeholder {
      color: $font-placeholder-color;
    }

    &:-ms-input-placeholder {
      color: $font-placeholder-color;
    }

    &::-ms-input-placeholder {
      color: $font-placeholder-color;
    }

    &::placeholder {
      color: $font-placeholder-color;
    }

    &:hover {
      border-color: $base-font-border-color;
    }

    &:focus {
      border-color: $border-active-bottom-color;
      outline: 0;
    }
  }

  .el-input {
    .el-input__count {
      color: $font-item-color;

      .el-input__count-inner {
        background: transparent;
      }
    }

    .el-input__clear {
      color: $base-font-border-color;

      &:hover {
        color: $font-item-color;
      }
    }

    &::-webkit-scrollbar-track-piece {
      background: $white;
    }

    &::-webkit-scrollbar-track {
      background: $white;
    }

    &::-webkit-scrollbar-corner {
      background: $white;
    }

    &::-webkit-scrollbar-thumb {
      background: #b4bccc;
    }

    &.is-exceed {
      .el-input__inner {
        border-color: $danger-color;
      }

      .el-input__suffix {
        .el-input__count {
          color: $danger-color;
        }
      }
    }

    &.is-disabled {
      .el-input__inner {
        background-color: $transparent;
        border: 1px solid $base-border-color;
        color: $base-font-border-color;
        cursor: not-allowed;

        &::placeholder {
          color: $font-placeholder-color;
        }

        &::-ms-input-placeholder {
          color: $font-placeholder-color;
        }

        &:-ms-input-placeholder {
          color: $font-placeholder-color;
        }

        &::-webkit-input-placeholder {
          color: $font-placeholder-color;
        }
      }
    }

    &.is-active {
      .el-input__inner {
        border-color: $border-active-bottom-color;
        outline: 0;
      }
    }
  }

  .el-input-group__append,
  .el-input-group__prepend {
    background-color: transparent;
    color: $font-group-color;
    border: 1px solid $border-color;
    button {
      &.el-button {
        border-color: transparent;
        background-color: transparent;
        color: inherit;
      }
    }

    div {
      &.el-select {
        .el-input__inner {
          border-color: transparent;
          background-color: transparent;
          color: inherit;
        }

        &:hover {
          .el-input__inner {
            border-color: transparent;
            background-color: transparent;
            color: inherit;
          }
        }
      }
    }
  }

  .el-input-group--prepend,
  .el-input-group--append {
    .el-select {
      .el-input {
        &.is-focus {
          .el-input__inner {
            border-color: transparent;
          }
        }
      }
    }
  }

  .el-transfer__button {
    color: $white;
    background-color: $border-active-bottom-color;
    font-size: 0;

    &.is-disabled {
      border: 1px solid $border-gray-color;
      background-color: $bg-hover-color;
      color: $base-font-border-color;

      &:hover {
        border: 1px solid $border-gray-color;
        background-color: $bg-hover-color;
        color: $base-font-border-color;
      }
    }
  }

  .el-transfer-panel__item {
    &.el-checkbox {
      color: $base-font-border-color;
    }

    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .el-transfer-panel {
    border: 1px solid $base-border-color;
    background: $bg-color;

    .el-transfer-panel__header {
      background: $bg-color;
      border-bottom: 1px solid $base-border-color;
      color: $black;

      .el-checkbox {
        .el-checkbox__label {
          color: $base-font-border-color;

          span {
            color: $font-item-color;
          }
        }
      }
    }

    .el-transfer-panel__footer {
      background: $white;
      border-top: 1px solid $border-color;

      .el-checkbox {
        color: $base-font-border-color;
      }
    }

    .el-transfer-panel__empty {
      color: $font-item-color;
    }
  }

  .el-timeline-item__tail {
    border-left: 2px solid $base-border-color;
  }

  .el-timeline-item__icon {
    color: $white;
  }

  .el-timeline-item__node {
    background-color: $base-border-color;
  }

  .el-timeline-item__node--primary {
    background-color: $border-active-bottom-color;
  }

  .el-timeline-item__node--success {
    background-color: $success-color;
  }

  .el-timeline-item__node--warning {
    background-color: $waring-color;
  }

  .el-timeline-item__node--danger {
    background-color: $danger-color;
  }

  .el-timeline-item__node--info {
    background-color: $font-item-color;
  }

  .el-timeline-item__content {
    color: $base-font-border-color;
  }

  .el-timeline-item__timestamp {
    color: $font-item-color;
  }

  .el-link {
    &.is-underline {
      &:hover {
        &:after {
          border-bottom: 1px solid $border-active-bottom-color;
        }
      }
    }

    &.el-link--info {
      color: $font-item-color;

      &:hover {
        color: #a6a9ad;
      }

      &:after {
        border-color: $font-item-color;
      }

      &.is-disabled {
        color: #c8c9cc;
      }

      &.is-underline {
        &:hover {
          &:after {
            border-color: $font-item-color;
          }
        }
      }
    }

    &.el-link--warning {
      color: $waring-color;

      &:hover {
        color: #ebb563;
      }

      &:after {
        border-color: $waring-color;
      }

      &.is-disabled {
        color: #f3d19e;
      }

      &.is-underline {
        &:hover {
          &:after {
            border-color: $waring-color;
          }
        }
      }
    }

    &.el-link--success {
      color: $success-color;

      &:hover {
        color: #85ce61;
      }

      &:after {
        border-color: $success-color;
      }

      &.is-disabled {
        color: #b3e19d;
      }

      &.is-underline {
        &:hover {
          &:after {
            border-color: $success-color;
          }
        }
      }
    }

    &.el-link--danger {
      color: $danger-color;

      &:hover {
        color: #f78989;
      }

      &:after {
        border-color: $danger-color;
      }

      &.is-disabled {
        color: #fab6b6;
      }

      &.is-underline {
        &:hover {
          &:after {
            border-color: $danger-color;
          }
        }
      }
    }

    &.el-link--primary {
      color: $border-active-bottom-color;

      &:hover {
        color: $font-primary;
      }

      &.is-disabled {
        color: #a0cfff;
      }

      &:after {
        border-color: $border-active-bottom-color;
      }

      &.is-underline {
        &:hover {
          &:after {
            border-color: $border-active-bottom-color;
          }
        }
      }
    }

    &.el-link--default {
      color: $base-font-border-color;

      &:hover {
        color: $border-active-bottom-color;
      }

      &:after {
        border-color: $border-active-bottom-color;
      }

      &.is-disabled {
        color: $base-font-border-color;
      }

      &.is-underline {
        &:hover {
          &:after {
            border-color: $border-active-bottom-color;
          }
        }
      }
    }
  }

  .el-divider {
    background-color: rgba(255, 255, 255, 0.2);
  }

  .el-divider__text {
    background-color: $bg-color;
    color: $base-font-border-color;
  }

  .el-image__placeholder {
    background: $bg-hover-color;
  }

  .el-image__error {
    color: $base-font-border-color;
    background: $bg-hover-color;
  }

  .el-image-viewer__close {
    color: $white;
    background-color: $base-font-border-color;
  }

  .el-image-viewer__actions {
    background-color: $base-font-border-color;
    border-color: $white;
  }

  .el-image-viewer__actions__inner {
    color: $white;
  }

  .el-image-viewer__next,
  .el-image-viewer__prev {
    color: $white;
    background-color: $base-font-border-color;
    border-color: $white;
  }

  .el-image-viewer__mask {
    background: $black;
  }

  .el-button {
    background-color: $transparent;
    border: 1px solid $border-color;
    color: $font-color;

    &:focus,
    &.is-plain:focus {
      background-color: $transparent;
      border-color: $border-focus-color;
      color: $font-focus-color;
    }

    &:hover,
    &.is-plain:hover {
      background-color: $transparent;
      border-color: $border-hover-color;
      color: $font-hover-color;
    }

    &:active,
    &.is-plain:active,
    &.is-active {
      background-color: $transparent;
      border-color: $border-active-color;
      color: $font-active-color;
    }

    &.is-disabled {
      &:active {
        transform: scale(1);
      }

      background-color: $bg-disabled-color;
      border-color: $border-disabled-color;
      color: $font-disabled-color;

      &:focus,
      &:hover {
        background-color: $bg-disabled-color;
        border-color: $border-disabled-color;
        color: $font-disabled-color;
      }

      &.is-plain,
      &.is-plain:focus,
      &.is-plain:hover {
        background-color: $bg-disabled-color;
        border-color: $border-disabled-color;
        color: $font-disabled-color;
      }

      &.el-button--text {
        background-color: $transparent;
      }
    }

    &.is-loading {
      color: $font-disabled-color;

      &:before {
        background-color: $transparent;
      }
    }
  }

  .el-button--primary {
    color: $white;
    background-color: transparent;
    //border-color: rgba(64, 158, 255, 0.35);

    &:focus,
    &:hover {
      //background: rgba(64, 158, 255, 0.65);
      border-color: rgba(64, 158, 255, 0.65);
      color: $white;
    }

    &.is-active,
    &:active {
      //background: rgba(64, 158, 255, 0.65);
      border-color: rgba(64, 158, 255, 0.65);
      color: $white;
    }

    &.is-disabled {
      color: $base-font-border-color;
      background-color: $bg-disabled-color;
      border-color: $base-border-color;

      &:active,
      &:focus,
      &:hover {
        color: $base-font-border-color;
        background-color: $bg-disabled-color;
        border-color: $base-border-color;
      }
    }

    &.is-plain {
      color: $white;
      background: rgba(240, 249, 235, 0.35);
      border-color: rgba(240, 249, 235, 0.35);

      &:focus,
      &:hover {
        background: rgba(64, 158, 255, 0.65);
        border-color: rgba(64, 158, 255, 0.65);
        color: $white;
      }

      &:active {
        background: rgba(58, 142, 230, 0.65);
        border-color: rgba(58, 142, 230, 0.65);
        color: $white;
        outline: 0;
      }

      &.is-disabled {
        color: rgb(140, 197, 255);
        background-color: $bg-list-item-color;
        border-color: rgba(217, 236, 255, 0.65);

        &:active,
        &:focus,
        &:hover {
          color: rgb(140, 197, 255);
          background-color: $bg-list-item-color;
          border-color: rgba(217, 236, 255, 0.65);
        }
      }
    }
  }

  .el-button--success {
    color: $white;
    background-color: $success-color;
    border-color: $success-color;

    &:focus,
    &:hover {
      background: $success-hover-color;
      border-color: $success-hover-color;
      color: $white;
    }

    &.is-active,
    &:active {
      background: $success-hover-color;
      border-color: $success-hover-color;
      color: $white;
    }

    &.is-disabled {
      color: $white;
      background-color: rgba(179, 225, 157, 0.65);
      border-color: rgba(179, 225, 157, 0.65);

      &:active,
      &:focus,
      &:hover {
        color: $white;
        background-color: rgba(179, 225, 157, 0.65);
        border-color: rgba(179, 225, 157, 0.65);
      }
    }

    &.is-plain {
      color: rgba(103, 194, 58, 1);
      background: rgba(240, 249, 235, 0.35);
      border-color: rgba(194, 231, 176, 0.35);

      &:focus,
      &:hover {
        background: rgba(103, 194, 58, 0.65);
        border-color: rgba(103, 194, 58, 0.65);
        color: $white;
      }

      &:active {
        background: rgba(93, 175, 52, 0.65);
        border-color: rgba(93, 175, 52, 0.65);
        color: $white;
        outline: 0;
      }

      &.is-disabled {
        color: #a4da89;
        background-color: rgba(240, 249, 235, 0.65);
        border-color: rgba(225, 243, 216, 0.65);

        &:active,
        &:focus,
        &:hover {
          color: #a4da89;
          background-color: rgba(240, 249, 235, 0.65);
          border-color: rgba(225, 243, 216, 0.65);
        }
      }
    }
  }

  .el-button--warning {
    color: $white;
    background-color: $waring-color;
    border-color: $waring-color;

    &:focus,
    &:hover {
      background: rgba(235, 181, 99, 0.65);
      border-color: rgba(235, 181, 99, 0.65);
      color: $white;
    }

    &:active {
      outline: 0;
    }

    &.is-active,
    &:active {
      background: rgba(207, 146, 54, 0.65);
      border-color: rgba(207, 146, 54, 0.65);
      color: $white;
    }

    &.is-disabled {
      color: $white;
      background-color: rgba(243, 209, 158, 0.65);
      border-color: rgba(243, 209, 158, 0.65);

      &:active,
      &:focus,
      &:hover {
        color: $white;
        background-color: rgba(243, 209, 158, 0.65);
        border-color: rgba(243, 209, 158, 0.65);
      }
    }

    &.is-plain {
      color: rgba(230, 162, 60, 1);
      background: rgba(253, 246, 236, 0.35);
      border-color: rgba(245, 218, 177, 0.35);

      &:focus,
      &:hover {
        background: rgba(230, 162, 60, 0.65);
        border-color: rgba(230, 162, 60, 0.65);
        color: $white;
      }

      &:active {
        background: rgba(207, 146, 54, 0.65);
        border-color: rgba(207, 146, 54, 0.65);
        color: $white;
        outline: 0;
      }

      &.is-disabled {
        color: #f0c78a;
        background-color: rgba(253, 246, 236, 0.65);
        border-color: rgba(250, 236, 216, 0.65);

        &:active,
        &:focus,
        &:hover {
          color: #f0c78a;
          background-color: rgba(253, 246, 236, 0.65);
          border-color: rgba(250, 236, 216, 0.65);
        }
      }
    }
  }

  .el-button--danger {
    color: $white;
    background-color: $danger-color;
    border-color: $danger-color;

    &:focus,
    &:hover {
      background: rgba(245, 108, 108, 0.65);
      border-color: rgba(245, 108, 108, 0.65);
      color: $white;
    }

    &.is-active,
    &:active {
      background: rgba(221, 97, 97, 0.65);
      border-color: rgba(221, 97, 97, 0.65);
      color: $white;
    }

    &.is-disabled {
      color: $white;
      background-color: rgba(250, 182, 182, 0.65);
      border-color: rgba(250, 182, 182, 0.65);

      &:active,
      &:focus,
      &:hover {
        color: $white;
        background-color: rgba(250, 182, 182, 0.65);
        border-color: rgba(250, 182, 182, 0.65);
      }
    }

    &.is-plain {
      color: rgba(245, 108, 108, 1);
      background: rgba(254, 240, 240, 0.35);
      border-color: rgba(251, 196, 196, 0.35);

      &:hover,
      &:focus {
        background: rgba(245, 108, 108, 0.65);
        border-color: rgba(245, 108, 108, 0.65);
        color: $white;
      }

      &:active {
        background: rgba(221, 97, 97, 0.65);
        border-color: rgba(221, 97, 97, 0.65);
        color: $white;
      }

      &.is-disabled {
        color: #f9a7a7;
        background-color: rgba(254, 240, 240, 0.65);
        border-color: rgba(253, 226, 226, 0.65);

        &:active,
        &:focus,
        &:hover {
          color: #f9a7a7;
          background-color: rgba(254, 240, 240, 0.65);
          border-color: rgba(253, 226, 226, 0.65);
        }
      }
    }
  }

  .el-button--info {
    color: $white;
    background-color: $font-item-color;
    border-color: $font-item-color;

    &:focus,
    &:hover {
      background: $font-item-hover-color;
      border-color: $font-item-hover-color;
      color: #409eff;
    }

    &.is-active,
    &:active {
      background: $font-item-hover-color;
      border-color: $font-item-hover-color;
      color: #409eff;
    }

    &.is-disabled {
      color: $white;
      background-color: rgba(200, 201, 204, 0.65);
      border-color: rgba(200, 201, 204, 0.65);

      &:active,
      &:focus,
      &:hover {
        color: $white;
        background-color: rgba(200, 201, 204, 0.65);
        border-color: rgba(200, 201, 204, 0.65);
      }
    }

    &.is-plain {
      color: $white;
      background: rgba(244, 244, 245, 0.35);
      border-color: rgba(211, 212, 214, 0.35);

      &:focus,
      &:hover {
        background: rgba(144, 147, 153, 0.65);
        border-color: rgba(144, 147, 153, 0.65);
        color: #409eff;
      }

      &:active {
        background: rgba(130, 132, 138, 0.65);
        border-color: rgba(130, 132, 138, 0.65);
        color: #409eff;
      }

      &.is-disabled {
        color: #409eff;
        background-color: rgba(244, 244, 245, 0.65);
        border-color: rgba(233, 233, 235, 0.65);

        &:active,
        &:focus,
        &:hover {
          color: #409eff;
          background-color: rgba(244, 244, 245, 0.65);
          border-color: rgba(233, 233, 235, 0.65);
        }
      }
    }
  }

  .el-button--text {
    color: $border-active-bottom-color;
    border-color: $transparent;

    &.is-disabled {
      border-color: $transparent;

      &:hover,
      &:focus {
        border-color: $transparent;
      }
    }

    &:focus,
    &:hover {
      color: $font-primary;
      border-color: $transparent;
      background-color: $transparent;
    }

    &:active {
      color: #3a8ee6;
      background-color: $transparent;
      border-color: $transparent;
    }
  }

  .el-button-group {
    .el-button--info {
      &:first-child {
        border-right-color: $bg-btn-color;
      }

      &:not(:first-child) {
        &:not(:last-child) {
          border-left-color: $bg-btn-color;
          border-right-color: $bg-btn-color;
        }
      }

      &:last-child {
        border-left-color: $bg-btn-color;
      }
    }

    .el-button--danger {
      &:first-child {
        border-right-color: $bg-btn-color;
      }

      &:last-child {
        border-left-color: $bg-btn-color;
      }

      &:not(:first-child) {
        &:not(:last-child) {
          border-left-color: $bg-btn-color;
          border-right-color: $bg-btn-color;
        }
      }
    }

    .el-button--warning {
      &:first-child {
        border-right-color: $bg-btn-color;
      }

      &:last-child {
        border-left-color: $bg-btn-color;
      }

      &:not(:first-child):not(:last-child) {
        border-left-color: $bg-btn-color;
        border-right-color: $bg-btn-color;
      }
    }

    .el-button--success {
      &:first-child {
        border-right-color: $bg-btn-color;
      }

      &:last-child {
        border-left-color: $bg-btn-color;
      }

      &:not(:first-child):not(:last-child) {
        border-left-color: $bg-btn-color;
        border-right-color: $bg-btn-color;
      }
    }

    .el-button--primary {
      &:first-child {
        border-right-color: $bg-btn-color;
      }

      &:last-child {
        border-left-color: $transparent;
      }

      &:not(:first-child) {
        &:not(:last-child) {
          border-left-color: $bg-btn-color;
          border-right-color: $bg-btn-color;
        }
      }
    }

    & > .el-dropdown {
      & > .el-button {
        border-left-color: $bg-btn-color;
      }
    }
  }

  .el-calendar {
    background-color: $bg-color;
  }

  .el-calendar__header {
    border-bottom: 1px solid $border-dialog-color;
  }

  .el-calendar__title {
    color: $black;
  }

  .el-calendar-table {
    thead {
      th {
        color: $font-hover-color;
      }
    }

    &:not(.is-range) {
      td.next,
      td.prev {
        color: $base-font-border-color;
      }
    }

    td {
      border-bottom: 1px solid $border-dialog-color;
      border-right: 1px solid $border-dialog-color;

      &.is-selected {
        background-color: $bg-hover-color;
        color: $font-focus-color;
      }

      &.is-today {
        color: $white;
        border-bottom: 2px solid $border-active-bottom-color;
      }
    }

    tr {
      &:first-child {
        td {
          border-top: 1px solid $border-color;
        }
      }

      td {
        &:first-child {
          border-left: 1px solid $border-color;
        }
      }
    }

    .el-calendar-day {
      &:hover {
        background-color: rgba(255, 255, 255, 0.08);
      }
    }
  }

  .el-backtop {
    color: $white;
    border-bottom: 2px solid $border-active-bottom-color;
    background-color: $white;

    &:hover {
      background-color: $bg-inner-color;
    }
  }

  .el-page-header__left {
    color: rgba(255, 255, 255, 0.45);

    &:hover {
      color: $font-color;
    }

    &::after {
      background-color: $border-gray-color;
    }
  }

  .el-page-header__content {
    color: $font-title-color;
  }

  .el-checkbox {
    color: $base-font-border-color;

    &.is-bordered {
      border: 1px solid $border-gray-color;

      &.is-checked {
        border-color: $border-active-bottom-color;
      }

      &.is-disabled {
        border-color: $border-color;
      }
    }
  }

  .el-checkbox__input {
    &.is-disabled {
      .el-checkbox__inner {
        background-color: #edf2fc;
        border-color: $border-gray-color;
        cursor: not-allowed;
      }

      .el-checkbox__inner::after {
        cursor: not-allowed;
        border-color: $base-font-border-color;
      }

      &.is-checked {
        .el-checkbox__inner {
          background-color: $bg-inner-color;
          border-color: $border-gray-color;
        }

        .el-checkbox__inner::after {
          border-color: $base-font-border-color;
        }
      }

      &.is-indeterminate {
        .el-checkbox__inner {
          background-color: $bg-inner-color;
          border-color: $border-gray-color;

          &::before {
            background-color: $base-font-border-color;
            border-color: $base-font-border-color;
          }
        }
      }

      & + span {
        &.el-checkbox__label {
          color: $base-font-border-color;
        }
      }
    }

    &.is-checked {
      .el-checkbox__inner {
        background-color: $border-active-bottom-color;
        border-color: $border-active-bottom-color;
      }

      & + .el-checkbox__label {
        color: $border-active-bottom-color;
      }
    }

    &.is-focus {
      .el-checkbox__inner {
        border-color: $border-active-bottom-color;
      }
    }

    &.is-indeterminate {
      .el-checkbox__inner {
        background-color: $border-active-bottom-color;
        border-color: $border-active-bottom-color;
      }

      .el-checkbox__inner::before {
        background-color: $white;
      }
    }
  }

  .el-checkbox__inner {
    border: 1px solid $border-gray-color;
    background-color: $bg-checkbox-color;
    -webkit-transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);
    transition: border-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46), background-color 0.25s cubic-bezier(0.71, -0.46, 0.29, 1.46);

    &:hover {
      border-color: $border-active-bottom-color;
    }
  }

  .el-checkbox-button__inner {
    background: $white;
    border: 1px solid $border-gray-color;
    color: $base-font-border-color;

    &:hover {
      color: $border-active-bottom-color;
    }
  }

  .el-checkbox-button {
    &.is-checked {
      &:first-child {
        .el-checkbox-button__inner {
          border-left-color: $border-active-bottom-color;
        }
      }

      .el-checkbox-button__inner {
        color: $white;
        background-color: $border-active-bottom-color;
        border-color: $border-active-bottom-color;
        -webkit-box-shadow: -1px 0 0 0 #8cc5ff;
        box-shadow: -1px 0 0 0 #8cc5ff;
      }
    }

    &:first-child {
      .el-checkbox-button__inner {
        border-left: 1px solid $border-gray-color;
      }
    }

    &.is-focus {
      .el-checkbox-button__inner {
        border-color: $border-active-bottom-color;
      }
    }

    &.is-disabled {
      .el-checkbox-button__inner {
        color: $base-font-border-color;
        background-color: $white;
        border-color: $border-color;
      }

      &:first-child {
        .el-checkbox-button__inner {
          border-left-color: $border-color;
        }
      }
    }
  }

  .el-radio__input {
    &.is-focus {
      .el-radio__inner {
        border-color: $border-active-bottom-color;
      }
    }

    &.is-checked {
      .el-radio__inner {
        border-color: $border-active-bottom-color;
        background: $border-active-bottom-color;
      }

      & + .el-radio__label {
        color: $font-color;
      }
    }

    &.is-disabled {
      .el-radio__inner {
        background-color: $bg-hover-color;
        border-color: $base-border-color;
      }

      .el-radio__inner::after {
        cursor: not-allowed;
        background-color: $bg-hover-color;
      }

      &.is-checked {
        .el-radio__inner {
          background-color: $bg-btn-color;
          border-color: $base-border-color;
        }

        .el-radio__inner::after {
          background-color: $base-font-border-color;
        }
      }

      & + span {
        &.el-radio__label {
          color: $font-color;
          cursor: not-allowed;
        }
      }
    }
  }

  .el-radio__inner {
    border: 1px solid $border-gray-color;
    background-color: $transparent;

    &:hover {
      border-color: $border-active-bottom-color;
    }

    &::after {
      background-color: $white;
    }
  }

  .el-radio {
    color: $font-color;

    &:focus {
      &:not(.is-focus):not(:active):not(.is-disabled) {
        .el-radio__inner {
          -webkit-box-shadow: 0 0 2px 2px $border-active-bottom-color;
          box-shadow: 0 0 2px 2px $border-active-bottom-color;
        }
      }
    }

    &.is-bordered {
      border: 1px solid $border-gray-color;

      &.is-disabled {
        cursor: not-allowed;
        border-color: $border-color;
      }

      &.is-checked {
        border-color: $border-active-bottom-color;
      }
    }
  }

  .el-scrollbar__thumb {
    background-color: $bg-scrollbar-color;

    &:hover {
      background-color: $bg-scrollbar-hover-color;
    }
  }

  .el-cascader-panel.is-bordered {
    border: 1px solid $base-border-color;
  }

  .el-cascader-menu {
    color: $base-font-border-color;
    border-right: solid 1px $base-border-color;

    &__empty-text {
      color: $base-font-border-color;
    }
  }

  .el-cascader-node {
    &.is-disabled {
      color: $base-font-border-color;
    }

    &.is-selectable {
      &.in-active-path {
        color: $base-font-border-color;
      }
    }

    &.in-active-path,
    &.is-active,
    &.is-selectable.in-checked-path {
      color: $border-active-bottom-color;
    }

    &:not(.is-disabled) {
      &:focus,
      &:hover {
        background: $bg-hover-color;
      }
    }
  }

  .el-avatar {
    color: $white;
    background: $base-font-border-color;
  }

  .el-drawer {
    background-color: $bg-color;
    -webkit-box-shadow: 0 8px 10px -5px $bg-box-shadow, 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
    box-shadow: 0 8px 10px -5px $bg-box-shadow, 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
  }

  .el-drawer__header {
    position: relative;
    padding: 16px 24px;
    color: $font-color;
    background: $bg-dialog-color;
    border-bottom: 1px solid $border-dialog-color;
    border-radius: 2px 2px 0 0;
  }

  .el-drawer__body {
    flex-grow: 1;
    padding: 24px;
    overflow: auto;
    font-size: 14px;
    line-height: 1.5715;
    word-wrap: break-word;
    color: $font-title-color;
  }

  .range-picker {
    background-color: $bg-color;
    border-color: $border-color;
  }
}
