<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="form.rule" label-width="10%">
      <el-form-item>
        <el-checkbox v-model="checkbox.all" :indeterminate="checkbox.indeterminate" @change="changeCheckAllColumn">
          {{ $t('tip.select.all') }}
        </el-checkbox>
      </el-form-item>
      <el-checkbox-group v-model="checkbox.checked" @change="changeCheckColumn">
        <el-row v-for="(row, rowIndex) in groupTree" :key="rowIndex">
          <el-form-item :label="row.group">
            <template v-if="row.children.length > 0">
              <el-col v-for="(col, colIndex) in row.children" :key="colIndex" :span="4">
                <el-checkbox :label="col">
                  {{ col.label }}
                </el-checkbox>
              </el-col>
            </template>
          </el-form-item>
        </el-row>
      </el-checkbox-group>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { classify } from '@util/format'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '60%',
    },
    formData: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      checkbox: {
        indeterminate: true,
        all: false,
        checked: [],
      },
      dialogVisible: this.visible,
      form: {
        model: {
          column: [],
        },
        rule: {},
      },
    }
  },
  computed: {
    groupTree() {
      return classify(this.form.model.column, 'group')
    },
  },
  watch: {
    formData(data) {
      this.handleCustomColumn(data)
    },
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
    'checkbox.checked'(arr) {
      this.checkbox.all = arr.length === this.form.model.column.length
    },
  },
  mounted() {
    this.handleCustomColumn()
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.handleCustomColumn()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      if (this.checkbox.checked.length === 0) {
        prompt({
          i18nCode: 'tip.select.empty',
          type: 'warning',
        })
      } else {
        this.handleCustomColumnChecked()
        this.$emit('on-submit', this.form.model.column)
        this.clickCancelDialog()
      }
      this.$refs.dialogTemplate.end()
    },
    changeCheckAllColumn(value) {
      this.checkbox.checked = value ? this.form.model.column : []
      this.checkbox.indeterminate = false
    },
    changeCheckColumn(value) {
      this.checkbox.all = value.length === this.form.model.column.length
      this.checkbox.indeterminate = value.length > 0 && value.length < this.form.model.column.length
    },
    handleCustomColumn(data = this.formData) {
      this.form.model.column = data
      this.checkbox.checked = data.filter((item) => item.check === true)
    },
    handleCustomColumnChecked() {
      this.form.model.column.forEach((col) => {
        col.check = false
        this.checkbox.checked.forEach((item) => {
          if (col.key === item.key) {
            col.check = true
          }
        })
      })
    },
  },
}
</script>
