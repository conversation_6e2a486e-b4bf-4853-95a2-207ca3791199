import request from '@util/request'

// 查询列表
export function queryTableData(obj) {
  return request({
    url: '/strategy/forward/strategies',
    method: 'get',
    params: obj || {},
  })
}

// 查询转发方式
export function queryType() {
  return request({
    url: '/strategy/forward/combo/forward-types',
    method: 'get',
  })
}

// 添加
export function addData(obj) {
  return request({
    url: '/strategy/forward/strategy',
    method: 'post',
    data: obj || {},
  })
}

// 编辑
export function updateData(obj) {
  return request({
    url: '/strategy/forward/strategy',
    method: 'put',
    data: obj || {},
  })
}

// (批量)删除资产
export function deleteData(ids) {
  return request({
    url: `/strategy/forward/strategy/${ids}`,
    method: 'delete',
  })
}
