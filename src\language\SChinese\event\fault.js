export default {
  fault: {
    title: '故障事件',
    faultId: '事件ID',
    faultName: '事件名称',
    currentStatus: '当前状态',
    faultStatus: '状态',
    faultClass: '事件类型',
    faultClassName: '事件类型',
    faultLevel: '事件等级',
    edName: '资产名称',
    monitorName: '监控器名称',
    domaName: '隶属区域',
    enterDate: '发生时间',
    updateDate: '更新时间',
    recoveryDate: '恢复时间',
    faultModule: '事件描述',
    faultSolution: '解决方案',
    basicDetail: '基本详情',
    faultDetail: '历史故障',
    handleSuggest: '处置意见',
    handleState: '处置状态',
    state: {
      normal: '正常',
      abnormal: '异常',
      handled: '已处置',
      unhandle: '未处置',
    },
  },
}
