export default {
  table: {
    header: 'Alarm List',
    dialog: {
      colTitle: 'Alarm List Custom Columns',
      option: 'Options',
      detailTitle: 'Alarm List Details',
      reasonTitle: 'Alarm List Confirmation',
    },
    label: {
      name: 'Audit Name',
      level: 'Event Level',
      auditTypeName: 'Audit Type',
      alarmStrategyName: 'Alarm Strategy',
      auditUser: 'Auditor',
      state: 'Alarm Status',
      total: 'Total Events',
      createTime: 'Creation Time',
      updateTime: 'Update Time',
      reason: 'Confirmation Reason',
      handel: 'Operation',
    },
    detail: {
      detailOriginalColumn: {
        type2Name: 'Original Log Name',
        eventName: 'Event Type',
        eventCategoryName: 'Event Category',
        level: 'Event Level',
        srcIp: 'Source IP',
        dstIp: 'Destination IP',
        dateTime: 'Time',
        raw: 'Log Original Text',
      },
      detailSafeColumn: {
        type2Name: 'Security Event Name',
        alarmTypeName: 'Security Event Type',
        alarmCategoryName: 'Security Event Category',
        level: 'Event Level',
        count: 'Aggregation Count',
        aggrStartDate: 'Aggregation Start Time',
        deviceTypeName: 'Log Source Device',
      },
      detailRelevanceColumn: {
        policyName: 'Policy Name',
        eventTypeName: 'Related Event Name',
        level: 'Event Level',
        createDate: 'Creation Time',
        updateDate: 'Update Time',
        count: 'Count',
      },
      detailThreatColumn: {
        eventTypeName: 'Threat Event Type',
        eventLevel: 'Event Level',
        eventDesc: 'Event Description',
        receiveTime: 'Receive Time',
        eventTime: 'Alarm Time',
      },
    },
    state: {
      done: 'Confirmed',
      pending: 'Pending',
    },
    panel: {
      detail: 'Detail Information',
      source: 'Event Tracing',
      original: 'Original Log',
    },
  },
}
