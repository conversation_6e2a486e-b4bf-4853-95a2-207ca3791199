<template>
  <div class="headerdiv">
    <div class="logodiv">
      <img class="logo" :src="systemSetting.systemLogo" />
      <div class="logoName">{{ systemSetting.systemName }}</div>
    </div>
    <div class="namediv">
      <div class="textdiv">平台运维概览</div>
    </div>
    <div class="datediv">
      <span>{{ time }}</span>
      <span>{{ date }}</span>
      <span v-if="week">{{ $t(`visualization.threatdeal.${week}`) }}</span>
      <div class="paramsChooser">
        <!-- <i class="el-icon-date icon" style="right: 30px"></i> -->
        <i class="el-icon-timer icon" style="right: 0"></i>
        <!-- <label class="pointer">
          <el-date-picker
            style="width: 1px; height: 32px; position: absolute; top: 0; z-index: -1"
            :editable="false"
            :clearable="false"
            v-model="currentDate"
            type="date"
            placeholder="请选择日期"
            :picker-options="pickerOptions"
          ></el-date-picker>
        </label> -->
        <label class="pointer">
          <el-select style="width: 1px; height: 1px; position: absolute; top: 0; z-index: -1" v-model="refreshDura">
            <el-option v-for="item in [0, 1, 3, 5, 10]" :key="item" :label="item === 0 ? '手动' : item + '分钟'" :value="item"></el-option>
          </el-select>
        </label>
      </div>
      <fullscreen></fullscreen>
    </div>
  </div>
</template>
<script>
import fullscreen from './fullscreen.vue'
import { getSystemNameAndLogo } from '@api/layout/layout-api'

export default {
  components: { fullscreen },
  data() {
    return {
      refreshDura: 0,
      currentDate: new Date(),
      date: '',
      time: '',
      week: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
      },
      systemSetting: {
        systemLogo: '',
        systemName: '',
      },
    }
  },
  watch: {
    date() {
      this.$emit('change-date', this.date)
    },
    refreshDura() {
      this.$emit('change-refresh-dura', this.refreshDura)
    },
  },
  mounted() {
    this.refreshDura = 5
    this.getLogo()
    setInterval(() => {
      this.getDate()
    }, 1000)
  },
  methods: {
    getLogo() {
      getSystemNameAndLogo().then((res) => {
        if (res) {
          this.systemSetting.systemLogo = res.systemLogo
          this.systemSetting.systemName = res.systemName
        }
      })
    },
    fitzero(num) {
      return num.toString().padStart(2, '0')
    },
    getDate() {
      var today = this.currentDate
      var year = today.getFullYear()
      var month = today.getMonth() + 1
      var day = today.getDate()
      var daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
      var dayOfWeek = daysOfWeek[today.getDay()]

      const time = new Date()
      var hour = time.getHours()
      var minute = time.getMinutes()
      var second = time.getSeconds()
      this.date = year + '-' + this.fitzero(month) + '-' + this.fitzero(day)
      this.time = this.fitzero(hour) + ':' + this.fitzero(minute) + ':' + this.fitzero(second)
      this.week = dayOfWeek
    },
  },
}
</script>
<style scoped lang="scss">
* {
  box-sizing: border-box;
  --font-color: #66b1ff;
}

.logodiv {
  display: flex;
  position: relative;
  top: 24px;
  padding-left: 30px;
  color: var(--font-color);
  font-size: 20px;
  font-weight: 500;
  gap: 10px;
  color: transparent;
  background-clip: text;
  background-image: linear-gradient(180deg, rgb(255, 255, 255), var(--font-color));
}
.logo {
  // width: 28px;
  height: 28px;
  filter: brightness(0) invert(1);
}
.logoName {
  font-weight: 700;
  font-size: 14px;
  height: 28px;
  line-height: 28px;
}
.headerdiv {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 3fr 1fr;
  background-image: url('~@asset/image/visualization/mantance/headerbg.png');
  height: 84px;
  background-repeat: no-repeat;
  background-size: cover;
  background-position-x: center;
  z-index: 5;
}
.namediv {
  display: flex;
  align-items: center;
  justify-content: center;
}
.namebg {
  grid-area: 1 / 2;
}
.textdiv {
  grid-area: 1 / 2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 400;
  letter-spacing: 5px;
  font-size: 28px;
  color: transparent;
  background-clip: text;
  background-image: linear-gradient(to bottom, #f5f7fa, rgb(30, 234, 255));
  position: relative;
  top: -8px;
}
.datediv {
  display: flex;
  position: relative;
  align-items: center;
  top: 24px;
  justify-content: flex-end;
  padding-right: 30px;
  gap: 10px;
  color: var(--font-color);
  font-size: 16px;
  cursor: pointer;
  height: 32px;
  .paramsChooser {
    position: relative;
    color: #66b1ff;
    font-size: 14px;
    ::v-deep .el-input__prefix {
      display: none;
    }
  }
  .pointer {
    width: 30px;
    height: 20px;
    overflow: hidden;
    position: relative;
    display: inline-block;
    cursor: pointer;
    ::v-deep .el-input__inner {
      border: none !important;
    }
  }
  .icon {
    position: absolute;
    color: #66b1ff;
    top: 5px;
    pointer-events: none;
  }
}
.datediv span:first-child {
  font-weight: bold;
}
</style>
