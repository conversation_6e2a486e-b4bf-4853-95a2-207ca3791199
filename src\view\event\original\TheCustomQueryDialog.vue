<!--
 * @Description: 原始日志 - 自定义查询弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-02-15
 * @Editor:
 * @EditDate: 2022-02-15
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.customQuery', [title])"
    width="30%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formTemplate" :model="model" :rules="rules" label-width="100px">
      <el-row>
        <el-form-item prop="customQueryName" :label="$t('event.original.label.customName')">
          <el-input v-model.trim="model.customQueryName" maxlength="255"></el-input>
        </el-form-item>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    customName: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      rules: {
        customQueryName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
      },
      model: {
        customQueryName: '',
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.model.customQueryName = this.customName
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$emit('on-submit', this.model)
          this.clickCancel()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
  },
}
</script>
