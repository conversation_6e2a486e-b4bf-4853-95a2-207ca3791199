<!--
 * @Description: 威胁事件 - 底部翻页
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <footer class="table-footer infinite-scroll">
    <section class="infinite-scroll-nomore">
      <span v-show="nomore">{{ $t('validate.data.nomore') }}</span>
    </section>
    <section class="infinite-scroll-total">
      <b>{{ $t('tip.total') + ':' }}</b>
      <i v-if="totalLoading" class="el-icon-loading"></i>
      <span v-else>
        {{ total }}
      </span>
    </section>
  </footer>
</template>

<script>
export default {
  props: {
    total: {
      required: true,
      type: Number,
    },
    totalLoading: {
      required: true,
      type: Boolean,
    },
    nomore: {
      required: true,
      type: Boolean,
    },
  },
}
</script>
