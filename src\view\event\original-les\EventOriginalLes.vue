<!--
 * @Description: IP关联
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2021-10-8
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-extend">
        <el-row :gutter="24">
          <el-col :span="10">
            <range-picker
              v-model="query.form.model.srcIP"
              type="ip"
              :start-placeholder="$t('event.originalLes.placeholder.srcStartIP')"
              :end-placeholder="$t('event.originalLes.placeholder.srcEndIP')"
              @change="inputQuery"
            ></range-picker>
          </el-col>
          <el-col :span="10">
            <range-picker
              v-model="query.form.model.dstIP"
              type="ip"
              :start-placeholder="$t('event.originalLes.placeholder.dstStartIP')"
              :end-placeholder="$t('event.originalLes.placeholder.dstEndIP')"
              @change="inputQuery"
            ></range-picker>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="10">
            <el-date-picker
              v-model="query.form.model.queryTime"
              type="datetimerange"
              value-format="yyyy-MM-dd HH:mm:ss"
              format="yyyy-MM-dd HH:mm:ss"
              :clearable="false"
              :popper-class="'no-clearable'"
              :picker-options="query.form.pickerOptions"
              :range-separator="$t('time.option.until')"
              :start-placeholder="$t('time.option.startTime')"
              :end-placeholder="$t('time.option.endTime')"
              class="search-input-datepicker"
              @change="inputQuery"
            ></el-date-picker>
          </el-col>
          <el-col :span="5">
            <!--                        <el-input
                            v-model.trim="query.form.model.logName"
                            class="width-max"
                            clearable
                            :placeholder="$t('event.originalLes.logName')"
                            @change="inputQuery">
                        </el-input>-->
            <el-select v-model="query.form.model.type" clearable filterable :placeholder="$t('event.originalLes.logName')" @change="inputQuery">
              <el-option v-for="item in eventTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-col>
          <el-col :span="4" align="right" :offset="5">
            <el-button v-has="'query'" @click="inputQuery">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="resetQuery">
              {{ $t('button.reset.default') }}
            </el-button>
          </el-col>
        </el-row>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header" style="width: 100%;right: 16px;">
        <h2 class="table-body-title">
          {{ $t('event.originalLes.originalLes') }}
        </h2>
        <el-button
          v-has="'download'"
          :disabled="!option.categories || !(option.categories.toString().length > 0)"
          @click="checkOut"
          style="height:32px;margin-right: 12px;"
        >
          {{ $t('button.export.default') }}
        </el-button>
      </header>
      <main v-loading="query.loading" class="table-body-main">
        <section class="main">
          <template>
            <graph-chart :graph-data="option" mouse-event @on-click="onChartClick"></graph-chart>
          </template>
        </section>
      </main>
    </main>
  </div>
</template>
<script>
import GraphChart from '@comp/ChartFactory/common/GraphChart'
import RangePicker from '@comp/RangePicker'
import { parseTime } from '@util/format'
import { debounce } from '@util/effect'
import { isEmpty } from '@util/common'

import { queryLesData, queryEventTypeData } from '@api/event/original-les-api'
export default {
  name: 'EventOriginalLes',
  components: {
    GraphChart,
    RangePicker,
  },
  data() {
    return {
      pageData: '',
      htmlTitle: this.$t('event.originalLes.originalLes'),
      option: {
        categories: [],
        links: [],
        nodes: [],
      },
      eventTypeOption: [],
      query: {
        loading: false,
        form: {
          model: {
            type: '', // 原始日志名称
            srcIP: ['', ''],
            dstIP: ['', ''],
            queryTime: '', // 查询时间，使用逗号分隔开始结束时间
          },
          pickerOptions: {
            shortcuts: [
              {
                text: this.$t('time.option.week'),
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
                  picker.$emit('pick', [start, end])
                },
              },
              {
                text: this.$t('time.option.month'),
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
                  picker.$emit('pick', [start, end])
                },
              },
              {
                text: this.$t('time.option.quarter'),
                onClick(picker) {
                  const end = new Date()
                  const start = new Date()
                  start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
                  picker.$emit('pick', [start, end])
                },
              },
            ],
          },
        },
      },
      queryDebounce: null,
    }
  },
  computed: {
    defaultTimeRange() {
      const date = new Date()
      const [morning, now] = [parseTime(date.getTime(), '{y}-{m}-{d} 00:00:00'), parseTime(date.getTime(), '{y}-{m}-{d} 23:59:59')]
      return [morning, now]
    },
  },
  mounted() {
    this.query.form.model.queryTime = this.defaultTimeRange
    this.initData()
    this.initOption()
    this.initDebounce()
    this.inputQuery()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const param = {
          type: this.query.form.model.type,
          srcIP: this.ipRange(this.query.form.model.srcIP),
          dstIP: this.ipRange(this.query.form.model.dstIP),
          queryTime: this.query.form.model.queryTime.toString(),
        }
        this.getLesData(param)
      }, 500)
    },
    // 初始化图
    initData() {
      this.option = {
        categories: [],
        links: [],
        nodes: [],
      }
    },
    ipRange(ipArr) {
      let ip = ''
      ipArr = ipArr.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipArr.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    initOption() {
      // 事件类型下拉数据
      queryEventTypeData().then((res) => {
        this.eventTypeOption = res
      })
    },
    // 获取图表数据
    getLesData(
      param = {
        queryTime: this.query.form.model.queryTime.toString(),
      }
    ) {
      this.query.loading = true
      queryLesData(param).then((res) => {
        if (res) {
          if (res.nodes) {
            res.nodes.forEach((item) => {
              item.label = {
                normal: {
                  show: true,
                },
              }
            })
          }
          this.option = res
          this.query.loading = false
        }
      })
    },
    // 导出
    checkOut() {
      const canvas = document.getElementsByTagName('canvas')
      // 生成图片
      const dataURL = canvas[0].toDataURL('image/png', 1.0)
      const a = document.createElement('a')
      a.href = dataURL
      a.download = this.$t('event.originalLes.originalLes')
      a.click()
    },
    // 查询方法
    inputQuery() {
      this.queryDebounce()
    },
    // 清空搜索条件
    clearQuery() {
      this.query.form.model = {
        type: '',
        srcIP: ['', ''],
        dstIP: ['', ''],
        queryTime: '',
      }
    },
    // 重置查询
    resetQuery() {
      this.clearQuery()
      this.initData()
      this.inputQuery()
    },
    onChartClick(params) {
      if (params.dataType === 'node') {
        this.$router.push({
          path: '/event/original',
          query: {
            srcIP: params.data.num > 0 ? '' : params.name,
            dstIP: params.data.num > 0 ? params.name : '',
          },
        })
        if (params.data.num > 0) {
          //目的
        } else {
          //源
        }
      }
    },
  },
}
</script>
<style scoped lang="scss">
.main {
  width: 100%;
  height: 100%;
}

::v-deep .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.3);
}

.router-wrap-table .table-header {
  padding: 0;
}

.router-wrap-table .table-header-extend {
  margin: 0;

  .el-row {
    padding: 0 0 10px 0;
  }
}
</style>
