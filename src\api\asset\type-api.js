import request from '@util/request'

// 资产类型增加
export function addData(obj) {
  return request({
    url: '/assettype/type',
    method: 'post',
    data: obj || {},
  })
}

// 资产类型删除
export function deleteData(ids) {
  return request({
    url: `/assettype/type/${ids}`,
    method: 'delete',
  })
}

// 资产类型修改
export function updateData(obj) {
  return request({
    url: `/assettype/type`,
    method: 'put',
    data: obj || {},
  })
}

// 资产类型查询
export function queryTableData(obj) {
  return request({
    url: '/assettype/types',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产一级分类下拉框
export function queryData() {
  return request({
    url: '/assettype/combo/categories',
    method: 'get',
  })
}
