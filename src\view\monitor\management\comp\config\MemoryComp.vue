<!--
 * @Description: 监控器配置 - 内存信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-02
 * @Editor:
 * @EditDate: 2021-08-02
-->
<template>
  <section>
    <el-form ref="memoryForm" :model="memoryModel" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="memoryUseRate" :label="$t('monitor.management.config.memory.memoryUseRate')">
            <el-input
              v-model="memoryModel.memoryUseRate"
              maxlength="3"
              oninput="value=value.replace(/[^0-9]/g,'')"
              @change="changeUseRate"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="memoryTimes" :label="$t('monitor.management.config.memory.memoryTimes')">
            <el-input v-model="memoryModel.memoryTimes" maxlength="1" oninput="value=value.replace(/[^0-9]/g,'')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
import { isEmpty } from '@util/common'
export default {
  props: {
    memoryModel: {
      required: true,
      type: Object,
    },
  },
  data() {
    const memoryUseRate = (rule, value, callback) => {
      if (value !== null && value !== '' && (value < 1 || value > 100)) {
        callback(new Error(this.$t('validate.monitor.useRate')))
      } else {
        callback()
      }
    }
    const memoryTimes = (rule, value, callback) => {
      if (value !== null && value !== '' && (value < 1 || value > 9)) {
        callback(new Error(this.$t('validate.monitor.times')))
      } else {
        callback()
      }
    }
    return {
      rules: {
        memoryUseRate: [
          {
            validator: memoryUseRate,
            trigger: 'blur',
          },
        ],
        memoryTimes: [
          {
            validator: memoryTimes,
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    changeUseRate(value) {
      if (isEmpty(value)) {
        this.memoryModel.memoryTimes = ''
      }
    },
    validateForm() {
      let validate = false
      this.$refs.memoryForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    resetForm() {
      this.$refs.memoryForm.resetFields()
    },
  },
}
</script>
