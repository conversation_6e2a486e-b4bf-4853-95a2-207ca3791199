<!--
 * @Description: 异常行为告警
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-02
 * @Editor:
 * @EditDate: 2021-11-02
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" :options="options" @on-change="changeQueryTable" @on-batch-handle="clickBatchHandle"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-select="clickSelectRows"
      @on-detail="clickDetail"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model" :options="options"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import DetailDialog from './TheDetailDialog'
import { prompt } from '@util/prompt'
import { handleStatus, anomalyType } from '@asset/js/code/option'
import { queryAbnormalAlarmTable, ignoreAbnormalAlarm } from '@api/alarm/abnormal-behavior-api'

export default {
  name: 'AbnormalBehaviorAlarm',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('alarm.abnormalBehavior.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          infoSystemIp: '',
          role: '',
          status: '',
          anomalyType: '',
          occurTime: [],
          updateTime: [],
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
      },
      options: {
        status: handleStatus,
        anomalyType: anomalyType,
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') {
        this.pagination.pageNum = 1
      }
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        this.query.form.occurTime = this.query.form.occurTime || ['', '']
        this.query.form.updateTime = this.query.form.updateTime || ['', '']
        params = Object.assign(params, {
          infoSystemIp: this.query.form.infoSystemIp,
          role: this.query.form.role,
          status: this.query.form.status,
          anomalyType: this.query.form.anomalyType,
          startTime: this.query.form.occurTime[0],
          endTime: this.query.form.occurTime[1],
          updateStartTime: this.query.form.updateTime[0],
          updateEndTime: this.query.form.updateTime[1],
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickSelectRows(select) {
      this.table.selected = select
    },
    clickBatchHandle() {
      if (this.table.selected.length > 0) {
        const ids = this.table.selected.map((item) => item.id).toString()
        this.$confirm(this.$t('tip.confirm.batchIgnore'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          this.ignoreAlarm(ids)
        })
      } else {
        prompt({
          i18nCode: 'tip.ignore.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickDetail(row) {
      this.dialog.detail.model = row
      this.dialog.detail.visible = true
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryAbnormalAlarmTable(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
    ignoreAlarm(ids) {
      ignoreAbnormalAlarm(ids).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.ignore.success',
              type: 'success',
            },
            () => {
              const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
              if (idArray.length === this.table.data.length) {
                this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
              }
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.ignore.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
