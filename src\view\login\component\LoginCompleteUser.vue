<!--
 * @Description: 登录 - 完善用户信息
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="userFormDialog"
    :visible="selfVisible"
    :title="$t('layout.setting.user.label')"
    width="60%"
    @on-close="close"
    @on-submit="submit"
  >
    <el-form ref="userForm" label-width="100px" :rules="userForm.model.rules" :model="userForm.model">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.password.new')" prop="newPassword">
            <el-input
              v-model="userForm.model.newPassword"
              show-password
              class="width-small"
              @paste.native.capture.prevent="disablePaste()"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.password.confirm')" prop="confirmPassword">
            <el-input
              v-model="userForm.model.confirmPassword"
              show-password
              class="width-small"
              @paste.native.capture.prevent="disablePaste()"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.username')" prop="userFullName">
            <el-input v-model="userForm.model.userFullName" maxlength="16" show-word-limit class="width-small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.nickname')" prop="userSortName">
            <el-input v-model="userForm.model.userSortName" maxlength="16" show-word-limit class="width-small"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.email')" prop="userMail">
            <el-input v-model="userForm.model.userMail" class="width-small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.mobilephone')" prop="userMobile">
            <el-input v-model="userForm.model.userMobile" class="width-small"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.telephone')" prop="userPhone">
            <el-input v-model="userForm.model.userPhone" class="width-small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.menu')" prop="defaultMenu">
            <el-cascader
              v-model="userForm.model.defaultMenu"
              class="width-small"
              expand-trigger="hover"
              :options="data.menu"
              :props="data.menuRule"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import { JSEncrypt } from 'jsencrypt'
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validatePassword, validateEmail, validateCellphone, validateTelephone } from '@util/validate'
import { queryMenuData, updateUserExtendData } from '@api/login/login-api'
import { querySystemConfigData } from '@api/management/system-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const getValidText = () => {
      const complexity = this.complexity
      const passwordConfig = {
        includeNumbers: complexity.includes('number'),
        includeLowercase: complexity.includes('lowercase'),
        includeUppercase: complexity.includes('uppercase'),
        includeSpecial: complexity.includes('special'),
        minLength: this.minLength,
      }
      let txt = '密码需要包含'
      if (passwordConfig.includeNumbers) {
        txt += ' 数字'
      }
      if (passwordConfig.includeLowercase) {
        txt += ' 小写字母'
      }
      if (passwordConfig.includeUppercase) {
        txt += ' 大写字母'
      }
      if (passwordConfig.includeSpecial) {
        txt += ' 特殊字符'
      }
      txt += ` 且长度不少于 ${passwordConfig.minLength} 位`
      return txt
    }
    const createPasswordRegex = () => {
      const complexity = this.complexity
      const passwordConfig = {
        includeNumbers: complexity.includes('number'),
        includeLowercase: complexity.includes('lowercase'),
        includeUppercase: complexity.includes('uppercase'),
        includeSpecial: complexity.includes('special'),
        minLength: this.minLength,
      }
      let regex = '^'
      if (passwordConfig.includeNumbers) {
        regex += '(?=.*\\d)'
      }
      if (passwordConfig.includeLowercase) {
        regex += '(?=.*[a-z])'
      }
      if (passwordConfig.includeUppercase) {
        regex += '(?=.*[A-Z])'
      }
      if (passwordConfig.includeSpecial) {
        regex += '(?=.*[!@#$%^&*(),.?":{}|<>])'
      }
      regex += `.{${passwordConfig.minLength},}$`
      return new RegExp(regex)
    }
    const complexityValidator = (password) => {
      const passwordRegex = createPasswordRegex()
      const isValid = passwordRegex.test(password)
      return isValid
    }
    const validatorNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback(this.$t('validate.password.new.empty'))
      } else if (!complexityValidator(value)) {
        callback(getValidText(this.complexity))
      } else {
        callback()
      }
    }
    const validatorConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.password.confirm.empty')))
      } else if (value !== this.userForm.model.newPassword) {
        callback(new Error(this.$t('validate.password.confirm.compare')))
      } else {
        callback()
      }
    }

    const validatorEmail = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    const validatorCellphone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validateCellphone(value)) {
        callback(new Error(this.$t('validate.comm.cellphone')))
      } else {
        callback()
      }
    }
    const validatorTelephone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validateTelephone(value)) {
        callback(new Error(this.$t('validate.comm.telephone')))
      } else {
        callback()
      }
    }

    return {
      complexity: [],
      selfVisible: this.visible,
      userForm: {
        model: {
          newPassword: '',
          confirmPassword: '',
          userFullName: '',
          userSortName: '',
          userMail: '',
          userMobile: '',
          userPhone: '',
          defaultMenu: [],
          rules: {
            newPassword: [{ required: true, validator: validatorNewPassword, trigger: 'blur' }],
            confirmPassword: [{ required: true, validator: validatorConfirmPassword, trigger: 'blur' }],
            userFullName: [
              {
                required: true,
                message: this.$t('validate.username.empty'),
                trigger: 'blur',
              },
            ],
            userSortName: [
              {
                required: true,
                message: this.$t('validate.username.empty'),
                trigger: 'blur',
              },
            ],
            userMail: [{ validator: validatorEmail, trigger: 'blur' }],
            userMobile: [{ validator: validatorCellphone, trigger: 'blur' }],
            userPhone: [{ validator: validatorTelephone, trigger: 'blur' }],
          },
        },
      },
      data: {
        menuRule: {
          value: 'menuId',
          label: 'menuName',
          children: 'children',
        },
        menu: [],
      },
    }
  },
  watch: {
    visible(vis) {
      this.selfVisible = vis
      if (vis) {
        this.getMenu()
        this.getComplexity()
      }
    },
    selfVisible(vis) {
      this.$emit('update:visible', vis)
    },
    'userForm.model.newPassword': function(val) {
      // 当新密码变更且确认密码已填写时，触发确认密码的验证
      if (this.userForm.model.confirmPassword) {
        this.$refs.userForm.validateField('confirmPassword')
      }
    }
  },
  methods: {
    close() {
      this.selfVisible = false
      this.$refs['userForm'].resetFields()
    },
    submit() {
      this.$refs['userForm'].validate((valid) => {
        if (valid) {
          const menuLength = this.userForm.model.defaultMenu.length
          const encryptor = new JSEncrypt()
          encryptor.setPublicKey(this.$store.getters.publicKey)
          const data = {
            userId: this.userId,
            userFullName: this.userForm.model.userFullName,
            userSortName: this.userForm.model.userSortName,
            userMail: this.userForm.model.userMail,
            userPhone: this.userForm.model.userPhone,
            userMobile: this.userForm.model.userMobile,
            defaultMenu: menuLength > 0 ? this.userForm.model.defaultMenu[menuLength - 1] : '',
            newPassword: encryptor.encrypt(this.userForm.model.newPassword),
            confirmPassword: encryptor.encrypt(this.userForm.model.confirmPassword),
          }
          this.updateUserExtend(data)
          this.close()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }

        this.$refs.userFormDialog.end()
      })
    },
    disablePaste() {
      return null
    },
    getComplexity() {
      querySystemConfigData().then((res) => {
        this.complexity = res.passwordComplexity?.split(',') || []
        this.minLength = res.minPasswordLength || 6
      })
    },
    getMenu() {
      queryMenuData().then((res) => {
        this.data.menu = res
      })
    },
    updateUserExtend(data) {
      updateUserExtendData(data).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.$emit('on-submit')
            }
          )
        } else if (res === 7) {
          prompt({
            i18nCode: 'tip.update.passwordComplexError',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
