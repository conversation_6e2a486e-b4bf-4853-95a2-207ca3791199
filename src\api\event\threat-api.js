import request from '@util/request'

// 查询列表事件总数
export function queryTableTotal(obj) {
  return request({
    url: '/event/threaten/events/total',
    method: 'get',
    params: obj || {},
  })
}
// 查询列表
export function queryTable(obj) {
  return request({
    url: '/event/threaten/events',
    method: 'get',
    params: obj || {},
  })
}
// 查询原始日志详情
export function queryOriginalLog(originalId, timestamp) {
  return request({
    url: `/event/threaten/original/event/${originalId}/${timestamp}`,
    method: 'get',
  })
}
// 查询原始日志总数
export function queryOriginalTotal(originalId, timestamp) {
  return request({
    url: `/event/threaten/original/event/total/${originalId}/${timestamp}`,
    method: 'get',
  })
}
// 查询服务信息
export function queryService() {
  return request({
    url: '/event/threaten/combo/forward-strategies',
    method: 'get',
  })
}
// 查询策略
export function queryStrategy() {
  return request({
    url: '/event/threaten/strategy',
    method: 'get',
  })
}
// 修改策略
export function updateStrategy(obj) {
  return request({
    url: '/event/threaten/strategy/config',
    method: 'put',
    data: obj || {},
  })
}
// 导出列表数据
export function downloadThreatEvent(obj) {
  return request(
    {
      url: '/event/threaten/download',
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}
