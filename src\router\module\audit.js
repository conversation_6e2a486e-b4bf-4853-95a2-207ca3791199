export default [
  {
    name: 'AuditEvent',
    path: '/audit/event',
    component: () => import('@view/audit/event/AuditEvent'),
  },
  {
    name: 'Audit<PERSON>erson',
    path: '/audit/person',
    component: () => import('@view/audit/person/AuditPerson'),
  },
  {
    name: 'AuditStrategy',
    path: '/audit/strategy',
    component: () => import('@view/audit/strategy/AuditStrategy'),
  },
  {
    name: 'AuditType',
    path: '/audit/type',
    component: () => import('@view/audit/type/AuditType'),
  },
  {
    name: 'BehaviorStrategy',
    path: '/audit/behavior-strategy',
    component: () => import('@view/audit/behavior-strategy/BehaviorStrategy'),
  },
]
