<template>
  <el-drawer :with-header="false" :title="title" :visible.sync="dialogVisible" :direction="direction" :size="size" v-bind="$attrs" v-on="$listeners">
    <el-collapse v-model="active" v-loading="loading">
      <el-collapse-item v-for="(detail, itemIndex) in tempData" :key="detail.value" :name="itemIndex">
        <el-row v-if="detail.children && detail.children.length > 0" class="detail-row">
          <el-col
            v-for="(item, index) in detail.children"
            :key="index"
            :span="detail.children.length === 1 || item.key === 'logMessage' || item.key === 'message' ? 30 : 5"
            :offset="1"
            class="detail-col"
          >
            <b class="detail-col-label">{{ item.label }}</b>
            <level-tag v-if="item.key === 'level' || item.key === 'levelName'" :level="item.value"></level-tag>
            <span v-else class="detail-col-value">{{ item.value }}</span>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </el-drawer>
</template>

<script>
import LevelTag from '@comp/LevelTag'
import { classify } from '@util/format'
import logColumn from './js/general-log-column'

export default {
  components: {
    LevelTag,
  },
  inheritAttrs: false,
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      type: String,
      default: '抽屉标题',
    },
    direction: {
      type: String,
      default: 'btt',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: '40%',
    },
    detailData: {
      type: [Object, Array],
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      active: 0,
    }
  },
  computed: {
    tempData() {
      let data = {}
      if (this.detailData.constructor === Object) {
        logColumn.forEach((item) => {
          Reflect.ownKeys(this.detailData).forEach((key) => {
            if (item.key === key) {
              item.value = this.detailData[key]
            }
          })
        })
        data = classify(logColumn, 'group').map((item) => ({
          label: item.group,
          children: item.children,
        }))
      }

      if (this.detailData.constructor === Array) {
        data = this.detailData
      }
      return data
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDrawer() {
      this.dialogVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-drawer {
  padding: 30px 50px;
  overflow: auto;
  outline: none;
  .el-collapse {
    &-item__header {
      display: none;
    }
    &-item {
      margin-top: 25px;
    }
  }
  .detail-row {
    .detail-col {
      display: flex;
      min-height: 50px;

      &-label {
        display: inline-block;
        width: 120px;
        max-width: 300px;
        color: #1873d7;
      }

      &-value {
        word-wrap: break-word;
        word-break: break-all;
        display: inline-block;
        width: calc(100% - 120px);
        white-space: pre-wrap;
      }
    }
  }
}
</style>
