<!--
 * @Description: 性能事件 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane :label="$t('event.perf.basicDetail')" name="first">
          <el-form :model="model" label-width="120px">
            <el-row>
              <el-col v-for="(item, index) in perfList" :key="index" :span="item.key === 'perfModule' || item.key === 'perfSolution' ? 24 : 12">
                <el-form-item :prop="item.key" :label="item.label">
                  <span v-if="item.key === 'currentStatus'">
                    {{ columnText(model[item.key], item.key) }}
                  </span>
                  <span v-else-if="item.key === 'perfLevel'">
                    <level-tag :level="model[item.key]"></level-tag>
                  </span>
                  <span v-else>
                    {{ model[item.key] }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="$t('event.perf.perfDetail')" name="second">
          <section>
            <el-divider content-position="left">
              <el-date-picker
                v-model="occurTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('repository.threatLibrary.table.lastStartTime')"
                :end-placeholder="$t('repository.threatLibrary.table.lastEndTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-divider>
          </section>
          <main class="table-body-main">
            <el-table
              v-loading="table.loading"
              :data="table.data"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="268"
            >
              <el-table-column
                v-for="(item, index) in tableColumns"
                :key="index"
                :prop="item"
                :label="$t(`event.perf.${item}`)"
                show-overflow-tooltip
              ></el-table-column>
            </el-table>
          </main>
          <footer class="table-footer">
            <el-pagination
              v-if="pagination.visible"
              small
              background
              align="right"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="pagination.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @size-change="tableSizeChange"
              @current-change="tablePageChange"
            ></el-pagination>
          </footer>
        </el-tab-pane>
      </el-tabs>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog/CustomDialog'
import LevelTag from '@comp/LevelTag'
import { queryPerfDetails } from '@api/event/performance-api'
export default {
  components: {
    CustomDialog,
    LevelTag,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'first',
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      occurTime: [],
      tableColumns: ['perfName', 'perfClassName', 'enterDate', 'recoveryDate'],
      perfList: [
        { key: 'perfName', label: this.$t('event.perf.perfName') },
        { key: 'currentStatus', label: this.$t('event.perf.currentStatus') },
        { key: 'edName', label: this.$t('event.perf.edName') },
        { key: 'domaName', label: this.$t('event.perf.domaName') },
        { key: 'perfClassName', label: this.$t('event.perf.perfClassName') },
        { key: 'perfLevel', label: this.$t('event.perf.perfLevel') },
        { key: 'enterDate', label: this.$t('event.perf.enterDate') },
        { key: 'perfModule', label: this.$t('event.perf.perfModule') },
        { key: 'perfSolution', label: this.$t('event.perf.perfSolution') },
      ],
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.activeName = 'first'
        this.queryTableData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    changeQueryCondition(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      this.occurTime = this.occurTime || ['', '']
      const params = {
        performanceNo: this.model.performanceNo,
        startTime: this.occurTime[0],
        endTime: this.occurTime[1],
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      this.queryTableData(params)
    },
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryCondition()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryCondition('turn-page')
    },
    queryTableData(
      params = {
        performanceNo: this.model.performanceNo,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryPerfDetails(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
  },
}
</script>

<style scoped lang="scss">
.el-divider--horizontal {
  height: 1px;
}
</style>
