export default {
  threat: {
    title: '威胁事件',
    domainName: '网站名称',
    eventIpv: '源IP',
    eventTime: '告警时间',
    receiveTime: '接收时间',
    eventType: '事件类型',
    eventLevel: '事件等级',
    eventDesc: '事件描述',
    startTime: '起始告警时间',
    endTime: '终止告警时间',
    strategy: {
      title: '威胁情报策略配置',
      strategySwitch: '策略是否开启',
      forwardService: '转发服务',
    },
    detailColumns: {
      type2Name: '原始事件名称',
      eventName: '事件类型',
      eventCategoryName: '事件类别',
      level: '事件等级',
      srcIp: '源IP',
      dstIp: '目的IP',
      dateTime: '时间',
      raw: '日志原文',
    },
    panel: {
      original: '原始日志查询',
      detail: '详情信息',
    },
  },
}
