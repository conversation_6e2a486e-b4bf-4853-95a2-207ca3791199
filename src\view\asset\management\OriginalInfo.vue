<!--
 * @Description: 资产发现 - 详情弹框 - 扩展信息
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <main class="table-body-main">
      <el-table
        v-loading="table.loading"
        :data="table.data"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="320"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          v-for="(item, index) in tableColoums"
          :key="index"
          :prop="item"
          :label="$t(`asset.management.originalLog.${item}`)"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <level-tag v-if="col.key === 'level' || col.key === 'levelName'" :level="scope.row[col.key]"></level-tag>
            <p v-else>
              {{ scope.row[col.key] }}
            </p>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
  </div>
</template>

<script>
import { queryAssetOriginalLog } from '@api/asset/management-api'

export default {
  props: {
    params: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      tableColoums: ['eventName', 'level', 'sourceIp', 'targetIp', 'time'],
    }
  },
  watch: {
    params: {
      handler() {
        this.changeQueryTable()
      },
      deep: true,
    },
  },
  mounted() {
    this.changeQueryTable()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      this.queryTableData()
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData() {
      const params = Object.assign({}, this.params, {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      })
      this.table.loading = true
      this.pagination.visible = false
      queryAssetOriginalLog(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
  },
}
</script>

<style scoped></style>
