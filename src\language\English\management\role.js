export default {
  role: {
    tipsError: 'The current authorized resource is a toolbar menu, please select at least one non-toolbar menu',
    infoItem: {
      roleName: 'Role Name',
      roleStatus: 'Role Status',
      roleDescription: 'Role Description',
      grantedUsers: 'Authorized',
      resources: 'Resources',
      grantResources: 'Authorized Resources',
      delete: 'Deleted Successfully',
      update: 'Updated Successfully',
      userName: 'User Name',
    },
    userManagement: {
      userAccount: 'User Account',
    },
    header: {
      dialogTitle: 'Role Management',
      placeholder: 'Please Select',
    },
    roleStatus: {
      show: 'Show',
      hidden: 'Hide',
    },
    placeholder: {
      keyword: 'Please enter keyword to search',
      roleName: 'Role Name',
      roleStatus: 'Role Status',
      roleDescription: 'Role Description',
    },
  },
}
