<!--
 * @Description: 系统管理 - 校时
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="tab-context-wrapper">
    <el-form ref="basisForm" :model="form.model" :rules="form.rule" label-width="180px">
      <el-row>
        <el-col :span="12" :offset="1">
          <el-form-item :label="$t('management.system.title.timing')">
            <el-radio-group v-model="form.model.timingMode">
              <el-radio :label="0">
                {{ $t('management.system.label.manualTiming') }}
              </el-radio>
              <el-radio :label="1">
                {{ $t('management.system.label.autoTiming') }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <template v-if="form.model.timingMode === 0">
        <el-row>
          <el-col :span="7" :offset="1">
            <el-form-item :label="$t('management.system.label.configCenterSeverTime')" prop="configCenterSeverTime">
              <el-date-picker
                v-model="form.model.configCenterSeverTime"
                type="datetime"
                :placeholder="$t('management.system.tip.manualTime')"
                value-format="yyyy-MM-dd HH:mm:ss"
                class="width-max"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
      <template v-if="form.model.timingMode === 1">
        <el-row>
          <el-col :span="7" :offset="1">
            <el-form-item :label="$t('management.system.label.ntpSeverConfig')" prop="ntpSeverConfig">
              <el-input v-model="form.model.ntpSeverConfig"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="7" :offset="1">
            <el-form-item :label="$t('management.system.label.autoValidate')">
              <el-switch v-model="form.model.autoValidate" :active-value="1" :inactive-value="0"></el-switch>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.model.autoValidate">
          <el-col :span="7" :offset="1">
            <el-form-item :label="$t('management.system.label.settingCycle')" prop="settingCycle">
              <el-select
                v-model="form.model.settingCycle"
                style="width: 45%;"
                :class="{ 'width-max': form.model.settingCycle === 'day' }"
                @change="form.model.settingCycleValue = form.model.settingCycle === 'day' ? '' : 1"
              >
                <el-option v-for="item in form.option.settingCycle" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
              <el-select v-if="form.model.settingCycle !== 'day'" v-model="form.model.settingCycleValue" style="width: 45%; margin-left: 10%;">
                <el-option v-for="item in settingCycleOption" :key="item.value" :value="item.value" :label="item.label"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="7">
            <el-form-item :label="$t('management.system.label.settingTime')" prop="settingTime">
              <el-time-picker
                v-model="form.model.settingTime"
                :placeholder="$t('management.system.tip.autoTime')"
                value-format="HH:mm:ss"
                class="width-max"
              ></el-time-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-if="form.model.timingMode === 1" v-has="'test'" :loading="form.model.loading" @click="clickTestReviseTimeConfig">
        {{ $t('button.test') }}
      </el-button>
      <el-button v-has="'upload'" @click="clickSaveReviseTimeConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button v-has="'query'" @click="clickResetReviseTimeConfig">
        {{ $t('button.reset.default') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { prompt } from '@util/prompt'

export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      time: {
        timer: null,
        value: '',
      },
      form: {
        model: {
          timingMode: 0,
          centerSeverTime: '',
          configCenterSeverTime: '',
          ntpSeverConfig: '',
          autoValidate: true,
          settingCycle: 'month',
          settingCycleValue: 1,
          settingTime: '00:00:00',
        },
        rule: {
          configCenterSeverTime: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          ntpSeverConfig: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          settingCycle: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          settingTime: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          systemCertificationKey: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          systemCertificationKeyId: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          encryption: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
        option: {
          settingCycle: [
            {
              label: this.$t('time.cycle.month'),
              value: 'month',
            },
            {
              label: this.$t('time.cycle.week'),
              value: 'week',
            },
            {
              label: this.$t('time.cycle.day'),
              value: 'day',
            },
          ],
          encryption: [],
        },
      },
    }
  },
  computed: {
    settingCycleOption() {
      let cycleValue = []
      if (this.form.model.settingCycle === 'month') {
        for (let i = 1; i < 32; i++) {
          cycleValue.push({
            label: i + this.$t('time.unit.day'),
            value: i,
          })
        }
      }
      if (this.form.model.settingCycle === 'week') {
        cycleValue = [
          {
            label: this.$t('time.week.mon'),
            value: 1,
          },
          {
            label: this.$t('time.week.tue'),
            value: 2,
          },
          {
            label: this.$t('time.week.wed'),
            value: 3,
          },
          {
            label: this.$t('time.week.thu'),
            value: 4,
          },
          {
            label: this.$t('time.week.fri'),
            value: 5,
          },
          {
            label: this.$t('time.week.sat'),
            value: 6,
          },
          {
            label: this.$t('time.week.sun'),
            value: 0,
          },
        ]
      }
      return cycleValue
    },
  },
  watch: {
    'form.model.centerSeverTime'() {
      this.autoTime()
    },
  },
  mounted() {
    this.init()
  },
  destroyed() {
    this.time.timer = null
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model = this.formData
      }
    },
    clickTestReviseTimeConfig() {
      this.$refs.basisForm.validate((valid) => {
        if (valid) {
          this.$emit('on-test', this.handleParams())
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickSaveReviseTimeConfig() {
      this.$refs.basisForm.validate((valid) => {
        if (valid) {
          this.$emit('on-save', this.handleParams())
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetReviseTimeConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-reset')
      })
    },
    handleParams() {
      let params = {
        timingMode: this.form.model.timingMode,
      }

      if (this.form.model.timingMode === 0) {
        params = Object.assign(params, {
          configCenterSeverTime: this.form.model.configCenterSeverTime,
        })
      }

      if (this.form.model.timingMode === 1) {
        params = Object.assign(params, {
          ntpSeverConfig: this.form.model.ntpSeverConfig,
          autoValidate: this.form.model.autoValidate,
          systemCertification: this.form.model.systemCertification,
        })

        if (this.form.model.autoValidate) {
          params = Object.assign(params, {
            settingCycle: this.form.model.settingCycle,
            settingCycleValue: this.form.model.settingCycleValue,
            settingTime: this.form.model.settingTime,
          })
        }
      }
      return params
    },
    autoTime() {
      if (this.form.model.centerSeverTime !== '') {
        this.time.value = this.form.model.centerSeverTime
        this.time.timer = setInterval(() => {
          const time = new Date(this.time.value)
          const second = time.getSeconds()
          const year = time.getFullYear()
          const month = time.getMonth() + 1
          const day = time.getDate()
          const hour = time.getHours()
          const minite = time.getMinutes()
          this.time.value = year + '-' + month + '-' + day + ' ' + hour + ':' + minite + ':' + second
        }, 1000)
      }
    },
  },
}
</script>
