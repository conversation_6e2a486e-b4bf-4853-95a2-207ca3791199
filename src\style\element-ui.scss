@import './mixin';

.el-dialog {
  border-radius: 5px;
  min-width: 400px;

  .el-dialog__body {
    max-height: 500px;
    overflow: auto;

    .el-form {
      &-item {
        &__label {
          font-size: 12px;
        }

        &__content {
          & > .el-input,
          & > .el-textarea,
          & > .el-select,
          & > .el-date-editor,
          & > .el-cascader,
          & > .el-input-number,
          & > .el-autocomplete {
            width: 90%;
          }
        }
      }
    }
  }
}

.el-form-item {
  margin-bottom: 20px;
}

.el-form-item__error {
  padding-top: 0;
}

.el-button--text {
  padding: 6px;
}

.el-pagination {
  .el-input--mini {
    .el-input__inner {
      height: 22px;
    }

    .el-input__icon {
      line-height: 22px;
    }
  }
}

.el-menu {
  [class^='soc-icon-'] {
    @include menuIcon;
  }

  [class^='el-icon-'] {
    @include menuIcon;
  }
}

.el-menu-item {
  [class^='el-icon-'] {
    @include menuIcon;
  }
}

.el-submenu {
  [class^='soc-icon-'] {
    @include menuIcon;
  }
}

.el-dropdown-menu {
  max-height: 200px;
  overflow: auto;

  &__item {
    white-space: nowrap;
  }
}

.el-transfer__buttons {
  .el-button {
    box-shadow: none;

    i ~ span {
      padding-left: 0;
    }
  }
}

.el-input-group__append,
.el-input-group__prepend {
  .el-button {
    box-shadow: none;

    i {
      padding-right: 0;
    }
  }
}

.el-table {
  tr {
    td {
      .cell {
        &.el-tooltip .col-white-space {
          white-space: pre;
        }

        .el-button {
          background-color: $CR;
          border: none;
          box-shadow: none;
        }

        .el-button.el-button--red {
          color: $danger-color;

          &:hover,
          &:focus {
            color: $danger-hover-color;
          }
        }

        .el-button.el-button--greed {
          color: $success-color;

          &:hover,
          &:focus {
            color: $success-hover-color;
          }
        }

        .el-button.el-button--yellow {
          color: $warning-color;

          &:hover,
          &:focus {
            color: $warning-hover-color;
          }
        }

        .el-button.el-button--blue {
          color: $primary-color;

          &:hover,
          &:focus {
            color: $primary-hover-color;
          }
        }
      }
    }
  }
}

.el-divider--horizontal {
  height: 2px;
}

.el-input {
  .el-input__suffix {
    cursor: pointer;
  }
}

.no-clearable {
  .el-picker-panel__footer {
    button:first-child {
      display: none;
    }
  }
}

.el-tooltip__popper {
  max-width: 80%;
}

.el-tab-pane {
  .table-footer {
    margin-top: 2px;
    min-height: 30px;
    overflow: auto;

    &.infinite-scroll {
      display: flex;
      margin-right: 30px;
      @include theme('color', table-body-title-color);

      .infinite-scroll-nomore {
        flex: 1;
        text-align: center;
      }

      .infinite-scroll-total {
        min-width: 50px;
      }
    }
  }
}
