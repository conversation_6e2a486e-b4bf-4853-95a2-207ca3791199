import { debounce } from '@util/effect'

export default {
  data() {
    return {
      $_sidebarMenuElm: null,
      $_resizeHandler: null,
    }
  },
  mounted() {
    this.initListener()
  },
  activated() {
    if (!this.$_resizeHandler) {
      // 避免重复初始化
      this.initListener()
    }
    // 当keep-alive图表activated状态,自动resize
    this.resize()
  },
  beforeD<PERSON>roy() {
    this.destroyListener()
  },
  deactivated() {
    this.destroyListener()
  },
  methods: {
    // 使用 $_ 来创建mixins，符合Vue标准
    $_sidebarMenuChangeResizeChart(e) {
      if (e.propertyName === 'width') {
        this.$_resizeHandler()
      }
    },
    initListener() {
      this.$_resizeHandler = debounce(() => {
        this.resize()
      }, 500)
      window.addEventListener('resize', this.$_resizeHandler)
      this.$_sidebarMenuElm = document.getElementsByClassName('menu-container')[0]
      this.$_sidebarMenuElm && this.$_sidebarMenuElm.addEventListener('transitionend', this.$_sidebarMenuChangeResizeChart)
    },
    destroyListener() {
      window.removeEventListener('resize', this.$_resizeHandler)
      this.$_resizeHandler = null
      this.$_sidebarMenuElm && this.$_sidebarMenuElm.removeEventListener('transitionend', this.$_sidebarMenuChangeResizeChart)
    },
    resize() {
      const { chart } = this
      chart && chart.resize()
    },
  },
}
