<template>
  <div :id="id" ref="radarChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-radar',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      require: false,
      type: String,
      default: '100%',
    },
    height: {
      require: false,
      type: String,
      default: '100%',
    },
    radarData: {
      type: Object,
      default() {
        return {
          axis: [],
          value: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    radarData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.radarData) {
      if (data && Object.keys(data).length > 0) {
        this.chart = echarts.init(this.$refs.radarChart)
        this.drawChart(data)
      }
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      let option = {}
      const [radar, series] = [this.chartRadarConfig(data), this.chartSeriesConfig(data)]
      option = Object.assign(option, radar, series)
      return option
    },
    chartRadarConfig(data) {
      return {
        radar: {
          name: {
            show: false,
            textStyle: {
              color: '#fff',
              backgroundColor: '#999',
              borderRadius: 3,
              padding: [3, 5],
            },
          },
          indicator: data.axis,
          splitArea: {
            areaStyle: {
              color: ['rgba(2, 13, 36, 0.2)', 'rgba(2, 13, 36, 0.4)', 'rgba(2, 13, 36, 0.6)', 'rgba(2, 13, 36, 0.8)', 'rgba(2, 13, 36, 0.9)'],
              shadowColor: 'rgba(60,190,238, 0.2)',
              shadowBlur: 10,
            },
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(60, 190, 238, 0.5)',
              type: 'dotted',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(176, 220, 249, 0.1)',
            },
          },
        },
      }
    },
    chartSeriesConfig(data) {
      return {
        series: [
          {
            type: 'radar',
            symbol: 'none',
            itemStyle: {
              normal: {
                color: 'rgba(123, 199, 212, 0.7)',
              },
            },
            data: [
              {
                value: data.value,
                name: '',
                areaStyle: {
                  opacity: 0.7,
                  color: '#7bc7d4',
                },
              },
            ],
          },
        ],
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
