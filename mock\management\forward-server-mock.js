const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    Id: '@ID',
    type: '@NAME',
    remark: '@ZIP',
    Ip: '@Email',
    port: '8080',
  },
})
const typeSelect = [
  { value: '1', label: 'Syslog', type: null },
  { value: '2', label: 'SNMP Trap', type: null },
  { value: '3', label: 'Kafka', type: null },
]
module.exports = [
  {
    url: '/strategy/forward/strategy',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/strategy/forward/strategy/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/forward/strategy',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/forward/strategy',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/strategy/forward/combo/forward-types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: typeSelect,
      }
    },
  },
  {
    url: '/strategy/forward/strategies',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          rows: [
            { name: '1', type: '1', reIP: '***********', remark: '' },
            { name: '2', type: '2', reIP: '***********', remark: '' },
            { name: '3', type: '3', reIP: '***********', remark: '' },
          ],
        },
      }
    },
  },
]
