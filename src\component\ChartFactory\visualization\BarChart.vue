<template>
  <div :id="id" ref="barChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-bar',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    barData: {
      type: Object,
      default() {
        return {
          axis: [],
          data: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    barData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.barData) {
      if (data && Object.keys(data).length > 0) {
        this.chart = echarts.init(this.$refs.barChart)
        this.drawChart(data)
      }
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      let option = {
        color: ['#7ac6d3'],
      }
      const [grid, axis, series] = [this.chartGridConfig(), this.chartAxisConfig(data), this.chartSeriesConfig(data)]
      option = Object.assign(option, grid, axis, series)
      return option
    },
    chartGridConfig() {
      return {
        grid: {
          top: '5%',
          bottom: '0%',
          containLabel: true,
          width: '100%',
          height: '100%',
        },
      }
    },
    chartAxisConfig(data) {
      return {
        xAxis: [
          {
            type: 'value',
            show: false,
          },
        ],
        yAxis: [
          {
            type: 'category',
            data: data.axis,
            axisLabel: {
              show: true,
              textStyle: {
                color: '#7ac6d3',
              },
            },
            show: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
              alignWithLabel: true,
            },
            splitLine: {
              show: false,
            },
          },
        ],
      }
    },
    chartSeriesConfig(data) {
      return {
        series: [
          {
            type: 'bar',
            barWidth: '20%',
            barGap: '10%',
            data: data.data,
          },
        ],
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
