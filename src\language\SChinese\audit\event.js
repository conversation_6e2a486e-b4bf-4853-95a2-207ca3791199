export default {
  event: {
    event: '审计事件',
    eventFrom: '事件溯源',
    log: '原始日志',
    id: '审计事件ID',
    eventName: '审计事件名称',
    level: '事件等级',
    auditTypeName: '审计类型',
    policyId: '审计策略ID',
    auditStrategyName: '审计策略',
    auditUserId: '审计人员ID',
    auditUser: '审计人员',
    auditIp: '审计对象IP',
    auditIpStart: '起始IP',
    auditIpEnd: '结束IP',
    total: '事件总数',
    createTime: '产生时间',
    createTimeStart: '起始产生时间',
    createTimeEnd: '结束产生时间',
    updateTime: '更新时间',
    secId: '安全事件ID',
    allCheck: '全选',
    none: '暂无数据',
    safe: {
      type2Name: '安全事件名称',
      safeEventName: '安全事件类型',
      eventTypeName: '安全事件类别',
      eventLevelName: '事件等级',
      srcIP: '事件源IP',
      srcPort: '事件源端口',
      dstIP: '事件目的IP',
      dstPort: '事件目的端口',
      dstAssetName: '目的资产名称',
      count: '聚合数量',
      aggrStartDate: '聚合开始时间',
      fromDeviceTypeName: '发生源设备类型',
    },
    link: {
      eventName: '关联事件名称',
      policyName: '策略名称',
      eventLevelName: '事件等级',
      createDate: '产生时间',
      updateDate: '更新时间',
      count: '次数',
    },
    threat: {
      eventType: '威胁事件类型',
      eventLevel: '事件等级',
      eventDesc: '事件描述',
      receiveTime: '接收时间',
      eventTime: '告警时间',
    },
    logList: {
      type2Name: '原始日志名称',
      eventName: '事件类型',
      eventCategoryName: '事件类别',
      level: '等级',
      srcIp: '源IP',
      dstIp: '目的IP',
      dateTime: '时间',
    },
    placeholder: {
      inputVal: '请输入关键字搜索',
      eventName: '审计事件名称',
      level: '事件等级',
      auditType: '审计类型',
      auditUserId: '审计人员',
      policyId: '审计策略',
    },
  },
}
