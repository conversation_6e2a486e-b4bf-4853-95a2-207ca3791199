<!--
 * @Description: 审计人员 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="25%">
      <template v-if="isGroup">
        <el-form-item :label="form.info.groupName.label" :prop="form.info.groupName.key">
          <el-input
            v-model.trim="form.model.groupName"
            :placeholder="$t('audit.person.placeholder.groupName')"
            class="width-mini"
            maxlength="16"
          ></el-input>
        </el-form-item>
        <el-form-item :label="form.info.remark.label" :prop="form.info.remark.key">
          <el-input
            v-model.trim="form.model.remark"
            :placeholder="$t('audit.person.placeholder.remark')"
            type="textarea"
            :rows="5"
            class="width-mini"
          ></el-input>
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item :label="form.info.groupId.label" :prop="form.info.groupId.key">
          <el-select v-model="form.model.groupId" :placeholder="$t('audit.person.placeholder.groupId')" clearable filterable class="width-mini">
            <el-option v-for="item in form.groupList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.info.accountName.label" :prop="form.info.accountName.key">
          <el-input
            v-model.trim="form.model.accountName"
            :placeholder="$t('audit.person.placeholder.accountName')"
            maxlength="16"
            class="width-mini"
          ></el-input>
        </el-form-item>
        <el-form-item :label="form.info.level.label" :prop="form.info.level.key">
          <el-select
            v-model="form.model.level"
            :placeholder="$t('audit.person.placeholder.level')"
            multiple
            collapse-tags
            clearable
            filterable
            class="width-mini"
          >
            <el-option v-for="item in form.eventLevel" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.info.typeId.label" :prop="form.info.typeId.key">
          <el-select
            v-model="form.model.typeId"
            :placeholder="$t('audit.person.placeholder.typeId')"
            multiple
            collapse-tags
            clearable
            filterable
            class="width-mini"
          >
            <el-option v-for="item in form.typeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.info.remark.label" :prop="form.info.remark.key">
          <el-input
            v-model.trim="form.model.remark"
            :placeholder="$t('audit.person.placeholder.remark')"
            type="textarea"
            :rows="5"
            class="width-mini"
          ></el-input>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { updateData } from '@api/audit/person-api'

export default {
  name: 'AuDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog是否只读
    isGroup: {
      type: Boolean,
      default: false,
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 关闭当前dialog
    clickCancelDialog() {
      this.form.model.update = false
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 修改审计人员
    update(obj) {
      const param = {
        ...obj,
        typeId: obj.typeId ? obj.typeId.toString() : '',
        level: obj.level ? obj.level.toString() : '',
      }
      updateData(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              // 给父级调用数据
              this.$emit('on-submit', 'pass')
              this.clickCancelDialog()
            }
          )
        } else if (res === 2) {
          prompt(
            {
              i18nCode: 'tip.update.repeatPerson',
              type: 'error',
            },
            () => {
              return false
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
          this.clickCancelDialog()
        }
      })
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            if (this.form.model.update) {
              const params = Object.assign({}, this.form.model)
              this.update(params)
            } else {
              const params = Object.assign({}, this.form.model)
              // 给父级调用数据
              this.$emit('on-submit', params)
              this.clickCancelDialog()
            }
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
