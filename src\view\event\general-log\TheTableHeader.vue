<!--
 * @Description: 通用日志 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-25
 * @Editor:
 * @EditDate: 2021-11-25
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('event.generalLog.placeholder.message')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button @click="clickLogForward">
          {{ $t('button.forward') }}
        </el-button>
        <el-button v-has="'download'" @click="clickDownloadButton">
          {{ $t('button.export.default') }}
        </el-button>
        <!-- <el-button style="height: 32px;" @click="clickCustomizeButton">
          {{ $t('button.th') }}
        </el-button> -->
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-cascader
                ref="cascader"
                v-model="filterCondition.form.deviceType"
                filterable
                clearable
                :options="options.deviceType"
                :placeholder="$t('event.generalLog.label.deviceName')"
                :props="{ expandTrigger: 'hover' }"
                @change="changeQueryCondition"
              ></el-cascader>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.severity"
                clearable
                :placeholder="$t('event.generalLog.placeholder.severity')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.severity" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <el-date-picker
                v-model="filterCondition.form.occurTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('time.option.startTime')"
                :end-placeholder="$t('time.option.endTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.facility"
                clearable
                :placeholder="$t('event.generalLog.placeholder.facility')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.facility" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <range-picker
                v-model="filterCondition.form.ipRange"
                type="ip"
                :start-placeholder="$t('event.generalLog.placeholder.fromStartIp')"
                :end-placeholder="$t('event.generalLog.placeholder.fromEndIp')"
                @change="changeQueryCondition"
              ></range-picker>
            </el-col>
            <el-col align="right" :offset="5" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import RangePicker from '@comp/RangePicker'
import { debounce } from '@util/effect'
import { querySeverityCombo, queryDeviceTypeCombo, queryFacilityCombo } from '@api/event/general-log-api'

export default {
  components: {
    RangePicker,
  },
  props: {
    condition: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      range: [],
      filterCondition: this.condition,
      debounce: null,
      options: {
        severity: [],
        deviceType: [],
        facility: [],
      },
    }
  },
  watch: {
    condition(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:condition', newCondition)
    },
  },
  mounted() {
    this.initDebounceQuery()
    this.initOptions()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 500)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        severity: '',
        deviceType: '',
        facility: '',
        occurTime: [],
        ipRange: ['', ''],
      }
      this.changeQueryCondition()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    clickDownloadButton() {
      this.$emit('on-download')
    },
    initOptions() {
      querySeverityCombo().then((res) => {
        this.options.severity = res
      })
      queryDeviceTypeCombo().then((res) => {
        this.options.deviceType = res
      })
      queryFacilityCombo().then((res) => {
        this.options.facility = res
      })
    },
    clickCustomizeButton() {
      this.$emit('on-custom')
    },
    clickLogForward() {
      this.$emit('on-forward')
    },
  },
}
</script>
