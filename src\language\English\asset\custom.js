export default {
  custom: {
    validate: {
      yes: 'Yes',
      no: 'No',
    },
    noDictionary: 'No Dictionary',
    custom: 'Custom Asset Properties',
    attributeId: 'Property ID',
    attributeName: 'Property Name',
    assetClass: 'Asset Type',
    newDic: 'Add Dictionary',
    assetClassName: 'Asset Primary Classification',
    assetTypeName: 'Asset Secondary Classification',
    remark: 'Description',
    controlType: 'Control Type',
    dictionary: 'Asset Dictionary',
    attributeLength: 'Text Length',
    checkType: 'Multiple Selection',
    reqType: 'Required',
    gridType: 'List Display',
    dicName: 'Dictionary Name',
    placeholder: {
      inputVal: 'Property Name/Description',
      attributeName: 'Property Name',
      assetClass: 'Asset Type',
      controlType: 'Control Type',
      attributeLength: 'Text Length',
      reqType: 'Required',
      checkType: 'Multiple Selection',
      remark: 'Description',
      dicName: 'Dictionary Name',
    },
    control: {
      text: 'Text Control',
      select: 'Dropdown Control',
      timer: 'Time Control',
      textarea: 'Textarea Control',
      radio: 'Radio Control',
      checkBox: 'Checkbox Control',
    },
  },
}
