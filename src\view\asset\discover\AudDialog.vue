<!--
 * @Description: 资产发现 - 转化弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" :label-width="'36%'">
      <template>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="form.info.assetName.label" :prop="form.info.assetName.key">
              <el-input v-model.trim="form.model.assetName" :placeholder="$t('asset.discover.placeholder.assetName')" maxlength="16"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.assetType.label" :prop="form.info.assetType.key">
              <el-cascader
                v-model="form.model.assetType"
                :placeholder="$t('asset.discover.placeholder.assetType')"
                :options="form.treeList"
                :props="{ expandTrigger: 'hover' }"
              ></el-cascader>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.netWorkId.label" :prop="form.info.netWorkId.key">
              <el-select v-model="form.model.netWorkId" filterable clearable :placeholder="$t('asset.discover.placeholder.netWorkId')">
                <el-option v-for="item in form.netList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="form.info.assetModel.label" :prop="form.info.assetModel.key">
              <el-input v-model.trim="form.model.assetModel" :placeholder="$t('asset.discover.placeholder.assetModel')" maxlength="16"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.manufactor.label" :prop="form.info.manufactor.key">
              <el-input v-model.trim="form.model.manufactor" :placeholder="$t('asset.discover.placeholder.manufactor')" maxlength="16"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.osType.label" :prop="form.info.osType.key">
              <el-input v-model.trim="form.model.osType" :placeholder="$t('asset.discover.placeholder.osType')" maxlength="16"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col v-show="form.addAllIds.length === 0" :span="8">
            <el-form-item :label="form.info.ipvAddress.label" :prop="form.info.ipvAddress.key">
              <el-input v-model.trim="form.model.ipvAddress" :placeholder="$t('asset.discover.placeholder.ipvAddress')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.memoryInfo.label" :prop="form.info.memoryInfo.key">
              <el-input v-model.trim="form.model.memoryInfo" :placeholder="$t('asset.discover.placeholder.memoryInfo')" maxlength="16"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.responsiblePerson.label" :prop="form.info.responsiblePerson.key">
              <el-input
                v-model.trim="form.model.responsiblePerson"
                :placeholder="$t('asset.discover.placeholder.responsiblePerson')"
                maxlength="16"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="form.info.contactPhone.label" :prop="form.info.contactPhone.key">
              <el-input v-model.trim="form.model.contactPhone" maxlength="16" :placeholder="$t('asset.discover.placeholder.contactPhone')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.email.label" :prop="form.info.email.key">
              <el-input v-model.trim="form.model.email" :placeholder="$t('asset.discover.placeholder.email')" maxlength="32"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.makerContactPhone.label" :prop="form.info.makerContactPhone.key">
              <el-input
                v-model.trim="form.model.makerContactPhone"
                maxlength="16"
                :placeholder="$t('asset.discover.placeholder.makerContactPhone')"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="form.info.assetCode.label" :prop="form.info.assetCode.key">
              <el-input v-model.trim="form.model.assetCode" :placeholder="$t('asset.discover.placeholder.assetCode')"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.domaName.label" :prop="form.info.domaName.key">
              <el-select v-model="form.model.domaId" :placeholder="$t('asset.discover.placeholder.domaName')" clearable filterable>
                <el-option v-for="item in options.domainOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="form.info.securityComponent.label" :prop="form.info.securityComponent.key">
              <el-input v-model.trim="form.model.securityComponent" :placeholder="$t('asset.management.placeholder.securityComponent')"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="form.info.assetDesc.label" :prop="form.info.assetDesc.key" label-width="12%">
              <el-input
                v-model.trim="form.model.assetDesc"
                :placeholder="$t('asset.discover.placeholder.assetDesc')"
                type="textarea"
                :rows="5"
                class="width-max"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { queryAssetDomain } from '@api/asset/management-api'

export default {
  name: 'AudDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '900',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      options: {
        domainOption: [],
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.initOptions()
  },
  methods: {
    initOptions() {
      queryAssetDomain().then((res) => {
        this.options.domainOption = res
      })
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const params = Object.assign({}, this.form.model)
            // 给父级调用数据
            this.$emit('on-submit', params, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    min-height: 300px;
  }
}
</style>
