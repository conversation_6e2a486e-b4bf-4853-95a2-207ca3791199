<!--
 * @Description: 日志备份
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model="data.fuzzyField"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('management.logBackup.label.name')])"
              prefix-icon="soc-icon-search"
              @change="clickQueryLogBackup"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="clickQueryLogBackup">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'update'" @click="clickBackupConfig">
            {{ $t('button.backups.config') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteLogBackup">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.logBackup.name') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          v-loading="loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="logBackupTableRowChange"
          @selection-change="logBackupTableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="backupName" :label="$t('management.logBackup.label.name')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="backupDate" :label="$t('management.logBackup.label.time')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column fixed="right" width="140">
            <template slot-scope="scope">
              <el-button v-has="'download'" class="el-button--blue" @click="clickDownloadLogBackup(scope.row)">
                {{ $t('button.download') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteLogBackup(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="logBackupTableSizeChange"
        @current-change="logBackupCurrentChange"
      ></el-pagination>
    </footer>
    <job-dialog
      :visible.sync="dialog.visible"
      :title="$t('management.logBackup.dialog.title.config')"
      :width="dialog.width"
      :form="dialog.form"
      @on-submit="clickSubmitBackupConfig"
    ></job-dialog>
  </div>
</template>

<script>
import JobDialog from './TheLogBackupDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import {
  queryBackupLogsData,
  queryBackupJobData,
  updateBackupJobData,
  deleteBackupLogData,
  downloadBackupLogData,
} from '@api/management/log-backup-api'

export default {
  name: 'ManagementLogBackup',
  components: {
    JobDialog,
  },
  data() {
    return {
      loading: false,
      data: {
        debounce: null,
        fuzzyField: '',
        table: [],
        selected: [],
      },
      dialog: {
        title: this.$t('management.logBackup.dialog.title.config'),
        visible: false,
        width: '35%',
        form: {
          model: {
            jobType: '',
            month: '',
            week: '',
            day: '',
            date: '',
          },
          rules: {
            jobType: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            week: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            month: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            day: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            date: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
          },
        },
      },
      pagination: {
        visible: true,
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
    }
  },
  mounted() {
    this.initDebounceQuery()
    this.getBackupLogs()
  },
  methods: {
    clickQueryLogBackup() {
      this.data.debounce()
    },
    async clickBackupConfig() {
      await this.getBackupJob()
      this.dialog.visible = true
    },
    clickDownloadLogBackup(row) {
      this.downloadBackupLog({
        backupFile: row.backupFile,
        backupName: row.backupName,
      })
    },
    clickBatchDeleteLogBackup() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.backupId).toString()
        this.deleteBackupLog(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickDeleteLogBackup(row) {
      this.deleteBackupLog(row.backupId)
    },
    clickSubmitBackupConfig(form) {
      this.updateBackupJob(form)
    },
    logBackupTableRowChange(row) {
      this.pagination.currentRow = row
    },
    logBackupTableSelectsChange(select) {
      this.data.selected = select
    },
    logBackupTableSizeChange(size) {
      this.pagination.pageSize = size
      this.getBackupLogs()
    },
    logBackupCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getBackupLogs()
    },
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        this.pagination.pageNum = 1
        this.getBackupLogs()
      }, 500)
    },
    getBackupLogs(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        fuzzyField: this.data.fuzzyField,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryBackupLogsData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.visible = true
        this.data.loading = false
      })
    },
    async getBackupJob() {
      await queryBackupJobData().then((res) => {
        this.dialog.form.model = res
      })
    },
    updateBackupJob(obj) {
      updateBackupJobData(obj).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: this.$t('tip.operate.success', [this.$t('button.backups.config')]),
              type: 'success',
            },
            () => {
              this.getBackupLogs()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.operate.error',
            type: 'error',
          })
        }
      })
    },
    deleteBackupLog(ids) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      })
        .then(() => {
          deleteBackupLogData(ids).then((res) => {
            if (res) {
              prompt(
                {
                  i18nCode: 'tip.delete.success',
                  type: 'success',
                },
                () => {
                  const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                  if (idArray.length === this.data.table.length) {
                    this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                  }
                  this.getBackupLogs()
                }
              )
            } else {
              prompt({
                i18nCode: 'tip.delete.error',
                type: 'error',
              })
            }
          })
        })
        .catch((e) => {
          console.error(e)
        })
    },
    downloadBackupLog(obj) {
      downloadBackupLogData(obj).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
