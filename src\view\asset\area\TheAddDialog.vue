<!--
 * @Description: 区域管理 - 添加弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-01
 * @Editor:
 * @EditDate: 2021-07-01
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.add', [titleName])"
    width="60%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formTemplate" :model="model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="domaName" :label="$t('asset.area.prop.domaName')">
            <el-input v-model.trim="model.domaName" maxlength="128"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="domaAbbreviation" :label="$t('asset.area.prop.domaAbbr')">
            <el-input v-model="model.domaAbbreviation" maxlength="16"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="officeTel" :label="$t('asset.area.prop.officeTel')">
            <el-input v-model="model.officeTel" maxlength="16"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="email" :label="$t('asset.area.prop.email')">
            <el-input v-model="model.email" maxlength="64"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="domaFunc" :label="$t('asset.area.prop.domaFunc')">
            <el-input v-model="model.domaFunc" maxlength="256"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="domaAddress" :label="$t('asset.area.prop.domaAddress')">
            <el-input v-model="model.domaAddress" maxlength="256"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="domaMemo" :label="$t('asset.area.prop.domaMemo')">
            <el-input v-model="model.domaMemo" type="textarea" :rows="4" maxlength="2048"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateExtensionTelephone, validateCellphone, validateEmail } from '@util/validate'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    const validatorTelephone = (rule, value, callback) => {
      if (value !== '' && !(validateExtensionTelephone(value) || validateCellphone(value))) {
        callback(new Error(this.$t('validate.telephone')))
      } else {
        callback()
      }
    }
    const validatorEmail = (rule, value, callback) => {
      if (value !== '' && !validateEmail(value)) {
        callback(new Error(this.$t('validate.email')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: this.visible,
      rules: {
        domaName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        officeTel: [
          {
            trigger: 'blur',
            validator: validatorTelephone,
          },
        ],
        email: [
          {
            trigger: 'blur',
            validator: validatorEmail,
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
  },
}
</script>
