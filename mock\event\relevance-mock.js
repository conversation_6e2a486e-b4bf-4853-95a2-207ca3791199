const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    eventName: '@NAME',
    policyName: '@CNAME',
    eventTypeName: '@CNAME',
    eventLevelName: '@CNAME',
    count: '1',
    updateDate: '@word(4)',
    createDate: '@DATETIME',
  },
})
const data = {
  eventName: '123',
  policyName: '334',
  eventTypeName: '256',
  eventLevelName: '789',
  count: '1',
  updateDate: '2020.2.2',
  createDate: '2020.2.2',
}
const col = [{ colIds: 'eventName,policyName,eventTypeName,eventLevelName,createDate,updateDate,count' }]
const categoryOption = [
  {
    value: '1',
    label: '网络设备',
    icon: null,
    description: null,
    children: [
      {
        value: '101',
        label: '二层交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '102',
        label: '核心交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '103',
        label: '三层交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '104',
        label: '集线器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '106',
        label: 'VPN',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '107',
        label: '路由器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '108',
        label: 'UTM',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '109',
        label: '流量控制',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '110',
        label: '身份认证网关',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '1045',
        label: '光纤交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '1046',
        label: '防病毒网关',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '2',
    label: '安全设备',
    icon: null,
    description: null,
    children: [
      {
        value: '105',
        label: '防火墙',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '201',
        label: '入侵检测系统IDS',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '202',
        label: '病毒监测系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '203',
        label: '网络审计系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '204',
        label: '网站安全检测系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '205',
        label: '邮件监控系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '506',
        label: '漏洞扫描',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '3',
    label: '服务器',
    icon: null,
    description: null,
    children: [
      {
        value: '300',
        label: '服务器-未知',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '301',
        label: '监控设备控制台',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '302',
        label: '监控设备管理中心',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '303',
        label: '应用服务器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '304',
        label: '数据库服务器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '305',
        label: '数据采集服务器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '306',
        label: '存储设备',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '307',
        label: 'KVM控制台',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '500',
        label: '工作站',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '4',
    label: '终端',
    icon: null,
    description: null,
    children: [
      {
        value: '401',
        label: '显示操作终端',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '402',
        label: '液晶屏幕',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '403',
        label: 'LED显示屏幕',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '5',
    label: '其他设备',
    icon: null,
    description: null,
    children: [
      {
        value: '501',
        label: '未知设备',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '502',
        label: '个人办公终端',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '503',
        label: '打印机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '504',
        label: '碎纸机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '505',
        label: '复印机',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
]
const downloadContext = `
PK<��P[Content_Types].xml�S�n�0����*6�PU�C���\\{�X�%����]8�R�
q�cfgfW�d�q�ZCB|��|�*�*h㻆},^�{Va�^K<4�6�N�XQ�ǆ�9�!P��$��҆�d�c�D�j);��ѝP�g��E�M'O�ʕ����H7L�h���R���G��^�'�{��zސʮB��3�˙��h.�h�W�жF�j娄CQՠ똈���}ιL�U:D�����%އ����,�B���[�	�� ;˱�	�{N��~��X�p�ykOL��kN�V��ܿBZ~����q�� �ar��{O�PKz��q;PK<��P_rels/.rels���j�0�_���8�\`�Q��2�m��4[ILb��ږ���.[K
�($}��v?�I�Q.���uӂ�h���x>=��@��p�H"�~�}�	�n����*"�H�׺؁�����8�Z�^'�#��7m{��O�3���G�u�ܓ�'��y|a�����D�	��l_EYȾ����vql3�ML�eh���*���\\3�Y0���oJ׏�	:��^��}PK��z��IPK<��PdocProps/app.xmlM��
�0D�~EȽ��ADҔ���A? ��6�lB�J?ߜ���0���ͯ�)�@��׍H6���V>��$;�SC
;̢(�ra�g�l�&�e��L!y�%��49��\`_���4G���F��J��Wg
�GS�b����
~�PK�|wؑ�PK<��PdocProps/core.xmlm�]K�0��J�}{�N�m�(Aq�d�]H�m�� �v�{�:+�wI��<���樆��I�Q��F�����~��I�גFcM�!���	�p�Ez�I�hτ�I�e^t���"�c�b��!^]��W�"���0p��I���HNJ)�}s�,�p@�:xȳ~؀N��d!��_�q�q5sq���n���^O_H��f�!(�(\`���F�����z�%MA��2������;/�+�5?	���5��������-�����PK?��K�PK<��Pxl/sharedStrings.xml=�A� ﾂ��.z0Ɣ�\`������,�����q2��o�ԇ���N�E��x5�z>�W���(R�K���^4{�����ŀ�5��y�V����y�m�XV�\\�.�j����
8�PKp��&x�PK<��P
xl/styles.xml�V�n�0��),߳�݆
J2�IE\\�Hܺ��X�Od�#�+p�{��
��vҤ]ӕi�7�}�}�9ǎ}_Ղ�[�
S2��FTf*gr�����)�J_��n8�))��$���zE&+� �LUT�L�� �z�JS�G<��F�"A��i,�b&�A�ZK���ҸP��\\�\`Hcs�n	��\\h�W1�Ӛ�	�:�$��5�l���#��M0O��G���J;c��o������߾x֞�EZ��G��8���G�\`2�wi\\k��3��?�T4�R�Ʊ�=�Ή^��ds:�(��_�ly��+L�.G=⩒>���}k�P:��Ӯ�x�[[sZX�k�,]kU�6SY�trF�J�<���(�v��s"&h?�tq탞Vͧ�]�"�{v�����5�ؿ�< O��4��PmŚ�R��ơ>�U9�
��}�0r�����t�L8��Z���^>J��V�=���}��c#RU|�.�\\�[��1͔��*�R
*-��I5�u;��E�e��f0M5F������v	#Sj&Ws5c-
����4��B\\e+�Gm�w]'9钼8�d��p��B������4&u4���a{.���D�y�^�dOPK���U_�PK<��Pxl/workbook.xml��AO�0�����w�tC����I�!1�{��Fk��	?��S�#'��=~����B�\\��A�D��I��aw�����F>c<�IC��XK�LO�*���E����L#��e?ȵR�ңp#��F�:g�%�OO!� L�R6�nL��4{ea1S��4t8$�6����~��h����Ԕ��s�e���4�M{�kg5��n@����j&,gry�~PK�����]PK<��Pxl/_rels/workbook.xml.rels��Mk�0@���}q���n/c����c+qh"K�迟���@;�$��{��~Γy�"#���i� �#
^�O7�\`D=E?1�b�n��8y�?$�YLE�8H���Z		g/
g����^�6�p��U���r΀%�좃����/�I�\`|�Rˤ��:f����~���mF�v�����:���ׯ�������p9HB�SyݵK~�����PK�;��3PK<��Pxl/worksheets/sheet1.xml��KN�0�����My	%AH�	Qk7�$��S��8��m@��-U,{���73vz0�-��5
���)l�L�����=~�o��u��@Fz�3� v�B��-��v\`褲NK����wdM��p�#�T��i�4�d������(�V��ok�SkoCpZf��C9�@��f���8�r�X	���xa�Pu�T�6�I�¶>~�V�xδ�ǹW%6O��3�V�,����9��$Jꑳ=sѾp|ᢅ-��,���f>��D�V����(�/��/����oO�@MQ� ʊ���J"k��(�E�K��"VML;YÙt�2�M-��|���Ye-�QzYˠ�
��3��/��v��p]��PKk#�%r�PK<��Pz��q;[Content_Types].xmlPK<��P��z��I|_rels/.relsPK<��P�|wؑ��docProps/app.xmlPK<��P?��K�gdocProps/core.xmlPK<��Pp��&x��xl/sharedStrings.xmlPK<��P���U_�
fxl/styles.xmlPK<��P�����]xl/workbook.xmlPK<��P�;��3	xl/_rels/workbook.xml.relsPK<��Pk#�%r�2
xl/worksheets/sheet1.xmlPK		?�
`

const fishBoardData = {
  hAxis: ['2022-05-09 09:32:00', '2022-05-09 10:13:00', '2022-05-09 11:13:00'],
  scatters: [
    {
      id: 'p5',
      name: '利用Web漏洞',
      time: '2022-05-09 09:32:00',
      pointNum: 6,
      sourceIp: '*********,********',
      count: 5,
      level: 0,
      levelName: '严重',
      items: [
        { srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' },
        { srcIp: '*********', dstIp: '********', count: 12, level: 0, levelName: '中' },
      ],
    },
    {
      id: 'p6',
      name: '利用缓冲区溢出漏洞',
      time: '2022-05-09 09:32:00',
      pointNum: 6,
      sourceIp: '*********,********',
      count: 234,
      level: 1,
      levelName: '很高',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p7',
      name: '跨站脚本攻击',
      time: '2022-05-09 09:32:00',
      pointNum: 6,
      sourceIp: '*********,********',
      count: 123,
      level: 2,
      levelName: '中',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p12',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 09:32:00',
      pointNum: 6,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p13',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 09:32:00',
      pointNum: 6,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p14',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 09:32:00',
      pointNum: 6,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p8',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 10:13:00',
      pointNum: 2,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p15',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 10:13:00',
      pointNum: 2,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p9',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 11:13:00',
      pointNum: 2,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
    {
      id: 'p10',
      name: 'SQL注入攻击告警',
      time: '2022-05-09 11:13:00',
      pointNum: 2,
      sourceIp: '*********,********',
      count: 66,
      level: 4,
      levelName: '一般',
      items: [{ srcIp: '*********', dstIp: '********', count: 5, level: 0, levelName: '严重' }],
    },
  ],
  links: [
    { source: '2022-05-09 09:32:00', target: 'p5' },
    { source: '2022-05-09 09:32:00', target: 'p6' },
    { source: '2022-05-09 09:32:00', target: 'p7' },
    { source: '2022-05-09 09:32:00', target: 'p12' },
    { source: '2022-05-09 09:32:00', target: 'p13' },
    { source: '2022-05-09 09:32:00', target: 'p14' },
    { source: '2022-05-09 10:13:00', target: 'p8' },
    { source: '2022-05-09 10:13:00', target: 'p15' },
    { source: '2022-05-09 11:13:00', target: 'p9' },
    { source: '2022-05-09 11:13:00', target: 'p10' },
  ],
}
module.exports = [
  {
    url: '/associatedevent/events',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/associatedevent/associatedevent-custom-columns',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: col,
      }
    },
  },
  {
    url: '/associatedevent/associatedevent-custom-column',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/associatedevent/associatedevent-custom-column',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/associatedevent/event-types',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: categoryOption,
      }
    },
  },
  {
    url: '/associatedevent/export-associatedevent',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: downloadContext,
      }
    },
  },
  {
    url: '/associatedevent/original-events',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: data,
      }
    },
  },
  {
    url: '/event/associated/fishBoard',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: fishBoardData,
      }
    },
  },
]
