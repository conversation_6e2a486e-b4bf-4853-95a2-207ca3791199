<!--
 * @Description: 安全事件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--页面搜索框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('event.security.placeholder.type2Name')])"
              clearable
              @change="inputQuery('e')"
              @keyup.enter.native="inputQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--查询按钮-->
        <section class="table-header-button">
          <el-button v-has="'download'" @click="clickDownloadButton">
            {{ $t('button.export.default') }}
          </el-button>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-cascader
                  ref="cascader"
                  v-model="query.seniorQuery.deviceType"
                  filterable
                  clearable
                  :options="options.fromDeviceOption"
                  :placeholder="$t('event.security.table.deviceTypeName')"
                  :props="{ expandTrigger: 'hover', checkStrictly: true }"
                  @change="submitSeniorQuery()"
                ></el-cascader>
              </el-col>
              <el-col :span="4">
                <el-input
                  v-model.trim="query.seniorQuery.type2Name"
                  clearable
                  :placeholder="$t('event.security.table.type2Name')"
                  @change="submitSeniorQuery"
                ></el-input>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="query.seniorQuery.alarmType"
                  clearable
                  filterable
                  :placeholder="$t('event.security.table.alarmTypeName')"
                  @change="submitSeniorQuery()"
                >
                  <el-option v-for="item in options.eventNames" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-select v-model="query.seniorQuery.level" clearable :placeholder="$t('event.security.table.level')" @change="submitSeniorQuery()">
                  <el-option v-for="item in options.levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <range-picker
                  v-model="query.seniorQuery.ipRange"
                  type="ip"
                  :start-placeholder="$t('event.security.placeholder.startIp')"
                  :end-placeholder="$t('event.security.placeholder.endIp')"
                  @change="submitSeniorQuery()"
                ></range-picker>
              </el-col>
              <el-col :span="6">
                <range-picker
                  v-model="query.seniorQuery.srcRange"
                  type="ip"
                  :start-placeholder="$t('event.security.placeholder.srcStartIp')"
                  :end-placeholder="$t('event.security.placeholder.srcEndIp')"
                  @change="submitSeniorQuery()"
                ></range-picker>
              </el-col>
              <el-col :span="6">
                <range-picker
                  v-model="query.seniorQuery.dstRange"
                  type="ip"
                  :start-placeholder="$t('event.security.placeholder.dstStartIp')"
                  :end-placeholder="$t('event.security.placeholder.dstEndIp')"
                  @change="submitSeniorQuery()"
                ></range-picker>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-date-picker
                  v-model="query.seniorQuery.aggrDateRange"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :start-placeholder="$t('time.option.startDate')"
                  :end-placeholder="$t('time.option.endDate')"
                  @change="submitSeniorQuery()"
                ></el-date-picker>
              </el-col>
              <el-col :span="4" :offset="12" align="right">
                <el-button v-has="'query'" @click="submitSeniorQuery()">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetSeniorQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体区-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('event.security.header') }}
        </h2>
        <el-button @click="clickCustomizeButton">
          {{ $t('button.th') }}
        </el-button>
      </header>
      <main class="table-body-main">
        <el-table
          v-if="show.tableShow"
          v-loading="data.loading"
          v-el-table-scroll="scrollEventSecurityTable"
          :data="data.table"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="selectsChange"
        >
          <el-table-column type="index"></el-table-column>
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            v-for="(item, index) in options.columnOption"
            :key="index"
            :prop="item"
            :min-width="tableColumnWidth(item)"
            :label="$t(`event.security.table.${item}`)"
            show-overflow-tooltip
            sortable
          >
            <template slot-scope="scope">
              <level-tag v-if="item === 'level'" :level="scope.row.level"></level-tag>
              <p v-else>
                {{ scope.row[item] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column width="120" fixed="right">
            <template slot-scope="scope">
              <el-button class="el-button--blue" @click="clickDetailButton(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <!--页面分页组件-->
    <footer class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
        <i v-show="data.totalLoading" class="el-icon-loading"></i>
      </section>
      <section v-show="!data.totalLoading" class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span>{{ data.total }}</span>
      </section>
    </footer>
    <!--自定义弹出框-->
    <col-dialog
      :visible.sync="dialog.columnDialog.visible"
      :title="dialog.columnDialog.title"
      :form="dialog.columnDialog.form"
      @on-submit="submitCustomize"
    ></col-dialog>
    <!--详情框弹出框-->
    <detail-dialog
      :visible.sync="dialog.detailDialog.visible"
      :title="dialog.detailDialog.title"
      :form="dialog.detailDialog.form"
      :loading="dialog.detailDialog.loading"
      :detail-columns-option="dialog.detailDialog.detailColumnsOption"
      :actions="false"
      :width="'70%'"
      @drawerShow="clickDetailDrawer"
    ></detail-dialog>
    <!--抽屉-->
    <drawer :visible.sync="drawer.visible" :loading="drawer.loading" :detail-data="drawer.data"></drawer>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import colDialog from './EventSecurityCustomizeDialog'
import DetailDialog from './EventSecurityDetailDialog'
import drawer from './EventSecurityDetailDrawer'
import { debounce } from '@/util/effect'
import levelTag from '@comp/LevelTag'
import { prompt } from '@util/prompt'
import RangePicker from '@comp/RangePicker'
import {
  querySecurityEventTableData,
  queryEventCategoryData,
  queryFromDeviceData,
  queryColumnsData,
  queryEventNames,
  querySecurityEventDetail,
  queryTotal,
  updateColumnsData,
  downloadSecurityEventTableData,
} from '@api/event/security-api'
import { isEmpty } from '@util/common'

export default {
  name: 'EventSecurity',
  directives: {
    elTableScroll,
  },
  components: {
    colDialog,
    DetailDialog,
    levelTag,
    drawer,
    RangePicker,
  },
  data() {
    return {
      data: {
        loading: false,
        table: [],
        selected: [],
        total: 0,
        nomore: false,
        scroll: true,
        totalLoading: false,
        debounce: {
          query: null,
          downloadDebounce: null,
          resetQueryDebounce: null,
        },
      }, // 列表数据
      show: {
        seniorQueryShow: false,
        tableShow: true,
      }, // 控制显示隐藏
      options: {
        eventNames: [], // 安全事件名
        eventTypeOption: [], // 事件分类
        fromDeviceOption: [], // 日志源设备
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        // 自定义列全集
        columnOption: [
          'type2Name',
          'alarmTypeName',
          'alarmCategoryName',
          'level',
          'count',
          'aggrStartDate',
          'aggrEndDate',
          'deviceTypeName',
          'fromIpv',
        ],
      }, // 下拉框options
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      }, // 分页信息
      dialog: {
        detailDialog: {
          visible: false,
          title: this.$t('event.security.dialog.detailTitle'),
          loading: false,
          form: {
            model: {
              eventId: '',
              type2Name: '',
              alarmTypeName: '',
              alarmCategoryName: '',
              deviceTypeName: '',
              aggrStartDate: '',
              aggrEndDate: '',
              level: '',
              count: '',
              fromIpv: '',
            },
            info: {
              type2Name: {
                key: 'type2Name',
                label: this.$t('event.security.table.type2Name'),
              },
              alarmTypeName: {
                key: 'alarmTypeName',
                label: this.$t('event.security.table.alarmTypeName'),
              },
              alarmCategoryName: {
                key: 'alarmCategoryName',
                label: this.$t('event.security.table.alarmCategoryName'),
              },
              level: {
                key: 'level',
                label: this.$t('event.security.table.level'),
              },
              count: {
                key: 'count',
                label: this.$t('event.security.table.count'),
              },
              fromIpv: {
                key: 'fromIpv',
                label: this.$t('event.security.table.fromIpv'),
              },
              aggrStartDate: {
                key: 'aggrStartDate',
                label: this.$t('event.security.table.aggrStartDate'),
              },
              aggrEndDate: {
                key: 'aggrEndDate',
                label: this.$t('event.security.table.aggrEndDate'),
              },
              deviceTypeName: {
                key: 'deviceTypeName',
                label: this.$t('event.security.table.deviceTypeName'),
              },
              raw: {
                key: 'raw',
                label: this.$t('event.security.table.raw'),
              },
            },
          },
          detailColumnsOption: [
            { key: 'type2Name', label: this.$t('event.security.table.type2Name') },
            { key: 'alarmTypeName', label: this.$t('event.security.table.alarmTypeName') },
            { key: 'alarmCategoryName', label: this.$t('event.security.table.alarmCategoryName') },
            { key: 'level', label: this.$t('event.security.table.level') },
            { key: 'deviceTypeName', label: this.$t('event.security.table.deviceTypeName') },
            { key: 'aggrStartDate', label: this.$t('event.security.table.aggrStartDate') },
            { key: 'aggrEndDate', label: this.$t('event.security.table.aggrEndDate') },
            { key: 'count', label: this.$t('event.security.table.count') },
            { key: 'fromIpv', label: this.$t('event.security.table.fromIpv') },
            { key: 'srcIpv', label: this.$t('event.security.table.srcIpv') },
            { key: 'srcPort', label: this.$t('event.security.table.srcPort') },
            { key: 'dstIpv', label: this.$t('event.security.table.dstIpv') },
            { key: 'dstPort', label: this.$t('event.security.table.dstPort') },
            { key: 'alarmDesc', label: this.$t('event.security.table.alarmDesc') },
          ],
        },
        columnDialog: {
          visible: false,
          title: this.$t('event.security.dialog.colTitle'),
          form: {
            model: {
              checkList: [],
              checkAll: false,
              isIndeterminate: false,
            },
            info: {
              checkList: {
                key: 'checkList',
                label: this.$t('alarm.table.dialog.option'),
              },
            },
          },
        },
      }, // 弹出框内容
      query: {
        rawLogParams: {},
        fuzzyField: '',
        seniorQuery: {
          type2Name: '',
          alarmType: '',
          deviceType: '',
          level: '',
          alarmCategory: '',
          aggrStartDate: [],
          ipRange: ['', ''],
          srcIpv: ['', ''],
          dstIpv: ['', ''],
        },
        tempParams: {},
      }, // 查询相关内容
      drawer: {
        visible: false,
        data: [],
        loading: false,
      },
    }
  },
  computed: {
    disableScroll() {
      return this.data.scroll
    },
    tableColumnWidth() {
      return (column) => {
        let width = 'none'
        if (column === 'aggrStartDate' || column === 'aggrEndDate') {
          width = 140
        }
        if (column === 'srcIpv' || column === 'dstIpv' || column === 'fromIpv') {
          width = 110
        }
        return width
      }
    },
  },
  watch: {
    $route: {
      handler(route) {
        const params = {
          pageSize: 20,
          fuzzyField: route.query.fuzzyField,
        }
        this.query.fuzzyField = route.query.fuzzyField
        this.data.table = []
        this.data.nomore = false
        this.queryEventSecurityTable(params)
        this.queryTotalData(params)
      },
      immediate: true,
    },
  },
  mounted() {
    this.initLoadData()
  },
  methods: {
    // 页面初始化方法
    initLoadData() {
      this.initOption()
      this.initDebounce()
      this.queryEventSecurityColumnsData()
      this.queryTotalData()
      // if (Object.keys(this.$route.query).length === 0 || this.$route.query.fuzzyField === "") {
      //     this.queryEventSecurityTable();
      // }
    },
    // 初始化防抖函数
    initDebounce() {
      this.initChangeDebounce()
      this.initStaticDebounce()
    },
    // 初始化变化的防抖方法
    initChangeDebounce() {
      this.data.debounce.query = debounce(() => {
        this.data.nomore = false
        this.data.table = []
        let params = {}
        this.query.seniorQuery.aggrDateRange = this.query.seniorQuery.aggrDateRange || ['', '']
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.seniorQuery, {
            ipRange: '',
            srcRange: '',
            dstRange: '',
            startIp: this.ipRange(this.query.seniorQuery.ipRange),
            srcIpv: this.ipRange(this.query.seniorQuery.srcRange),
            dstIpv: this.ipRange(this.query.seniorQuery.dstRange),
            deviceType: this.query.seniorQuery.deviceType.toString(),
            aggrStartDate: this.query.seniorQuery.aggrDateRange[0] ? this.query.seniorQuery.aggrDateRange[0] : null,
            aggrEndDate: this.query.seniorQuery.aggrDateRange[1] ? this.query.seniorQuery.aggrDateRange[1] : null,
            pageSize: this.pagination.pageSize,
          })
          delete params['aggrDateRange']
          this.query.tempParams = params
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            fuzzyField: this.query.fuzzyField,
          }
        }
        this.queryEventSecurityTable(params)
        this.queryTotalData(params)
      }, 500)
    },
    // 初始化固定的防抖方法
    initStaticDebounce() {
      // 下载的防抖
      this.data.debounce.downloadDebounce = debounce(() => {
        this.data.loading = true
        let params = {}
        if (this.show.seniorQueryShow) {
          this.query.seniorQuery.aggrDateRange = this.query.seniorQuery.aggrDateRange || ['', '']
          params = Object.assign({}, this.query.seniorQuery, {
            eventId: this.data.selected.length !== 0 ? this.data.selected.map((item) => item.eventId).toString() : null,
            deviceType: this.query.seniorQuery.deviceType.toString(),
            ipRange: '',
            srcRange: '',
            dstRange: '',
            startIp: this.ipRange(this.query.seniorQuery.ipRange),
            srcIpv: this.ipRange(this.query.seniorQuery.srcRange),
            dstIpv: this.ipRange(this.query.seniorQuery.dstRange),
            aggrStartDate: this.query.seniorQuery.aggrDateRange[0] ? this.query.seniorQuery.aggrDateRange[0] : null,
            aggrEndDate: this.query.seniorQuery.aggrDateRange[1] ? this.query.seniorQuery.aggrDateRange[1] : null,
            pageSize: 20,
          })
          delete params['aggrDateRange']
        } else {
          params = {
            pageSize: 20,
            eventId: this.data.selected.length !== 0 ? this.data.selected.map((item) => item.eventId).toString() : null,
            fuzzyField: this.query.fuzzyField,
          }
        }
        this.downloadApi(params)
      }, 500)
      // 重置的防抖
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.data.nomore = false
        this.query.tempParams = {}
        this.data.table = []
        this.data.scroll = true
        this.query.seniorQuery = {
          type2Name: '',
          alarmType: '',
          deviceType: '',
          level: '',
          alarmCategory: '',
          aggrDateRange: [],
          ipRange: ['', ''],
          srcRange: ['', ''],
          dstRange: ['', ''],
        }
        setTimeout(() => {
          this.queryEventSecurityTable()
        }, 150)
        this.queryTotalData()
      }, 500)
    },
    // 初始化下拉框方法
    initOption() {
      // 查询事件类型option
      queryEventCategoryData().then((res) => {
        this.options.eventTypeOption = res
      })
      // 查询发生源设备option
      queryFromDeviceData().then((res) => {
        this.options.fromDeviceOption = res
      })
      // 查询事件名称option
      queryEventNames().then((res) => {
        this.options.eventNames = res
      })
    },
    // 模糊查询方法
    inputQuery() {
      this.data.debounce.query()
    },
    ipRange(ipArr) {
      let ip = ''
      ipArr = ipArr.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipArr.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    // 下载api
    downloadApi(params = {}) {
      this.data.loading = true
      downloadSecurityEventTableData(params).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    // 查询安全事件
    queryEventSecurityTable(
      params = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.data.scroll = true
      this.data.loading = true
      querySecurityEventTableData(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.push(...res)
          this.data.scroll = true
          if (this.data.table.length > this.pagination.pageSize) {
            this.data.nomore = true
          }
        } else {
          this.data.table.push(...res)
          this.data.scroll = false
        }
        this.data.loading = false
      })
    },
    // 查询安全事件总数
    queryTotalData(
      params = {
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.data.totalLoading = true
      queryTotal(params).then((res) => {
        this.data.total = res
        this.data.totalLoading = false
      })
    },
    // 查询安全事件自定义列
    queryEventSecurityColumnsData() {
      this.show.tableShow = false
      queryColumnsData().then((res) => {
        if (res.length !== 0) {
          this.options.columnOption = res
        }
        setTimeout(() => {
          this.show.tableShow = true
        }, 100)
      })
    },
    // 点击下载按钮
    clickDownloadButton() {
      this.data.debounce.downloadDebounce()
    },
    // 点击向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetSeniorQuery()
      this.initChangeDebounce()
    },
    // 点击高级查询按钮
    clickQueryButton() {
      this.query.fuzzyField = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initChangeDebounce()
      this.resetSeniorQuery()
    },
    // 点击自定义列
    clickCustomizeButton() {
      this.dialog.columnDialog.visible = true
      queryColumnsData().then((res) => {
        if (res.length === 14) {
          this.dialog.columnDialog.form.model.checkList = res
          this.dialog.columnDialog.form.model.checkAll = true
          this.dialog.columnDialog.form.model.isIndeterminate = false
        } else if (res.length === 0) {
          this.dialog.columnDialog.form.model.checkList = [
            'type2Name',
            'alarmTypeName',
            'alarmCategoryName',
            'level',
            'count',
            'aggrStartDate',
            'aggrEndDate',
            'deviceTypeName',
            'fromIpv',
            'srcIpv',
            'srcPort',
            'dstIpv',
            'dstPort',
            'protocol',
          ]
          this.dialog.columnDialog.form.model.checkAll = true
          this.dialog.columnDialog.form.model.isIndeterminate = false
        } else {
          this.dialog.columnDialog.form.model.checkList = res
          this.dialog.columnDialog.form.model.checkAll = false
          this.dialog.columnDialog.form.model.isIndeterminate = true
        }
      })
    },
    // 点击页面详情按钮
    clickDetailButton(row) {
      this.dialog.detailDialog.loading = true
      querySecurityEventDetail(row.eventId, row.aggrStartDate).then((res) => {
        this.dialog.detailDialog.form.model = res
        this.dialog.detailDialog.loading = false
      })
      this.dialog.detailDialog.visible = true
    },
    // 点击显示抽屉
    clickDetailDrawer(row) {
      this.drawer.visible = true
      this.drawer.data = row
    },
    // 提交高级查询
    submitSeniorQuery() {
      this.seniorQueryMethod()
    },
    // 提交自定义列
    submitCustomize(formModel) {
      updateColumnsData(formModel.checkList).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.queryEventSecurityColumnsData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 高级查询方法
    seniorQueryMethod() {
      this.data.debounce.query()
    },
    // 列表多选框选择
    selectsChange(select) {
      this.data.selected = select
    },
    // 滚动加载安全事件
    scrollEventSecurityTable() {
      const lastRow = this.data.table[this.data.table.length - 1]
      let params = {}
      if (lastRow) {
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.tempParams, {
            pageSize: this.pagination.pageSize,
            eventId: lastRow.eventId,
            timestamp: lastRow.aggrStartDate,
          })
        } else {
          params = {
            eventId: lastRow.eventId,
            timestamp: lastRow.aggrStartDate,
            pageSize: this.pagination.pageSize,
            fuzzyField: this.query.fuzzyField,
          }
        }
      }
      this.queryEventSecurityTable(params)
    },
    // 重置高级查询条件
    resetSeniorQuery() {
      this.data.debounce.resetQueryDebounce()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
/*::v-deep.el-cascader-panel .el-radio__input {visibility: hidden;}.el-cascader-panel .el-cascader-node__postfix {top: 10px;}*/
</style>
