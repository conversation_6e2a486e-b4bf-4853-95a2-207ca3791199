const path = require('path')
/* node.js body 解析中间件，提供四种解析器
 * 处理程序之前，在中间件中对传入的请求体进行解析
 * 1. JSON body parser
 * 2. Raw body parser
 * 3. Text body parser
 * 4. URL-encoded form body parser
 */
const bodyParser = require('body-parser')
// 引入mock
const Mock = require('mockjs')
// node终端样式库
const chalk = require('chalk')
// 监听mock模块的变化
const chokidar = require('chokidar')
// 获取mock目录
const mockDir = path.join(process.cwd(), 'mock')

/**
 * @func mock数据模拟端进行api注册
 * @param {function} app - 函数包含参数req,res,next
 * @return {object} - 对象包含mock数据所有api的个数和mock执行的api
 * <AUTHOR> @date 2020/7/22
 */
function registerMockApi(app) {
  let mockLastIndex
  const { mocks } = require('./index.js')
  const mocksForServer = mocks.map((api) => {
    return mockServerResponse(api.url, api.type, api.response)
  })
  for (const mock of mocksForServer) {
    app[mock.type](mock.url, mock.response)
    mockLastIndex = app._router.stack.length
  }
  const mockApiLength = Object.keys(mocksForServer).length
  return {
    mockApiLength: mockApiLength,
    mockStartIndex: mockLastIndex - mockApiLength,
  }
}

/**
 * @func 注销所有api
 * <AUTHOR> @date 2020/7/22
 */
function cancellationMockApi() {
  Object.keys(require.cache).forEach((i) => {
    if (i.includes(mockDir)) {
      delete require.cache[require.resolve(i)]
    }
  })
}

const mockServerResponse = (url, type, response) => {
  return {
    url: new RegExp(`${url}`),
    type: type || 'get',
    response(req, res) {
      console.log(chalk.greenBright('request invoke:' + req.path))
      res.json(Mock.mock(response instanceof Function ? response(req, res) : response))
    },
  }
}

module.exports = (app) => {
  app.use(bodyParser.json())
  app.use(bodyParser.urlencoded({ extended: true }))
  const mockApi = registerMockApi(app)
  let mockApiLength = mockApi.mockApiLength
  let mockStartIndex = mockApi.mockStartIndex

  chokidar
    .watch(mockDir, {
      ignored: /server/,
      ignoreInitial: true,
    })
    .on('all', (event, path) => {
      if (event === 'change' || event === 'add') {
        try {
          app._router.stack.splice(mockStartIndex, mockApiLength)
          cancellationMockApi()
          const mockApi = registerMockApi(app)
          mockApiLength = mockApi.mockApiLength
          mockStartIndex = mockApi.mockStartIndex
          console.log(chalk.yellowBright(`\n > Mock Server hot reload success! changed  ${path}`))
        } catch (error) {
          console.log(chalk.redBright(error))
        }
      }
    })
}
