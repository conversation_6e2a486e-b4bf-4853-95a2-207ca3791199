export default {
  management: {
    title: '监控器',
    props: {
      monitorName: '监控器名称',
      status: '状态',
      monitorType: '监控器类型',
      monitorTypeName: '监控器类型',
      pollDate: '轮询时间',
      edName: '被监控资产名称',
      agentId: '代理服务器',
      agentIp: '代理服务器',
      edIp: '被监控资产IP',
      agentStatus: '代理状态',
      monitorEnabled: '监控器状态',
      createUser: '创建人',
      createDate: '创建时间',
      pollUnit: '轮询时间',
      stateChange: '启用停用',
      health: '健康度',
      healthTip: {
        unableConnect: '服务无法连通',
        noData: '无数据',
        normal: '正常',
      },
    },
    status: {
      on: '启用',
      off: '停用',
      normal: '正常',
      abnormal: '异常',
      online: '在线',
      offline: '下线',
    },
    placeholder: {
      startTime: '开始时间',
      endTime: '结束时间',
    },
    config: {
      asset: {
        title: '资产信息',
        edName: '资产名称',
        edIp: 'IP地址',
        assetType: '资产类型',
        domaAbbr: '区域简称',
        createDate: '创建时间',
      },
      cpu: {
        cpuUseRate: 'CPU使用率阈值(%)',
        cpuTimes: '连续达到阈值次数',
      },
      memory: {
        memoryUseRate: '内存使用率阈值(%)',
        memoryTimes: '连续达到阈值次数：',
      },
      disk: {
        diskUseRate: '磁盘使用率阈值(%)',
      },
      snmp: {
        template: 'SNMP模板',
        version: '版本',
        port: '端口',
        readCommunity: '读COMMUNITY',
        writeCommunity: '写COMMUNITY',
        authWay: '认证方式',
        authPwd: '认证口令',
        encryptionWay: '加密方式',
        encryptionPwd: '加密口令',
        context: 'Context ID',
        contextName: 'Context Name',
        snmpUserName: '用户名',
        securityLevel: '安全级别',
      },
    },
    view: {
      basic: {
        title: '基本信息',
        monitorName: '监控器名称',
        monitorTypeName: '监控器类型',
        edName: '被监控资产名称',
        edIp: 'IP',
        currentMemory: '当前内存',
        currentCpu: '当前CPU',
        currentDisk: '当前磁盘',
      },
      cpu: {
        cpuId: 'CPU',
        usage: '使用率（%）',
      },
      disk: {
        diskName: '磁盘卷',
        allDisk: '磁盘总量（G）',
        restDisk: '剩余磁盘空间（G）',
        usage: '使用率（%）',
      },
      perf: {
        title: '性能事件',
        perfName: '事件名称',
        currentStatus: '当前状态',
        perfStatus: '状态',
        perfClassName: '事件类型',
        perfLevel: '事件等级',
        edName: '资产名称',
        domaName: '隶属区域',
        enterDate: '发生时间',
        recoveryDate: '恢复时间',
        perfModule: '事件描述',
        perfSolution: '解决方案',
        basicDetail: '基本详情',
        perfDetail: '历史性能',
      },
      fault: {
        title: '故障事件',
        faultName: '事件名称',
        currentStatus: '当前状态',
        faultStatus: '状态',
        faultClassName: '事件类型',
        faultLevel: '事件等级',
        edName: '资产名称',
        domaName: '隶属区域',
        enterDate: '发生时间',
        recoveryDate: '恢复时间',
        faultModule: '事件描述',
        faultSolution: '解决方案',
        basicDetail: '基本详情',
        faultDetail: '历史故障',
      },
    },
  },
}
