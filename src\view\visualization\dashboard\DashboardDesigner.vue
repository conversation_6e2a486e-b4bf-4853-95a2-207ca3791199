<!--
 * @Description: 仪表盘 - 编辑
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <section class="dashboard-designer-wrapper">
    <el-row v-for="(row, rowIndex) in tempData" :key="rowIndex" :gutter="10" class="chunk-row">
      <el-col v-for="(col, colIndex) in row['list']" :key="colIndex" :span="col.span" :style="{ height: row.height + 'px' }" class="chunk-wrapper">
        <section
          v-if="
            row['list'] &&
              row['list'][colIndex]['data'] &&
              (row['list'][colIndex]['data']['length'] > 0 || Object.keys(row['list'][colIndex]['data'])['length'] > 0)
          "
          class="chunk-non-empty"
        >
          <header class="chunk-header">
            <h2 class="chunk-header-title">
              {{ col.data.title }}
            </h2>
            <section class="chunk-header-button">
              <i class="el-icon-edit" @click="clickUpdateChart(rowIndex, colIndex, col)"></i>
              <i class="el-icon-delete" @click="clickClearChart(rowIndex, colIndex, col)"></i>
            </section>
          </header>
          <section ref="chart" class="chunk-chart">
            <template v-if="col.data.type === 'line'">
              <line-chart ref="lineChartDom" :line-data="col.data.option" mouse-event></line-chart>
            </template>
            <template v-if="col.data.type === 'pie'">
              <pie-chart ref="pieChartDom" :pie-data="col.data.option" mouse-event></pie-chart>
            </template>
            <template v-if="col.data.type === 'bar'">
              <bar-chart ref="barChartDom" :bar-data="col.data.option" mouse-event></bar-chart>
            </template>
            <template v-if="col.data.type === 'bar-stack'">
              <bar-stack-chart ref="barStackChartDom" :bar-data="col.data.option" mouse-event></bar-stack-chart>
            </template>
          </section>
        </section>
        <section v-else class="chunk-empty">
          <section class="chunk-empty-icon">
            <i class="el-icon-plus" @click="clickAddChart(rowIndex, colIndex)"></i>
          </section>
          <p class="chunk-empty-tip">
            {{ $t('visualization.dashboard.tip.empty.data') }}
          </p>
        </section>
      </el-col>
      <aside @click="clickDeleteRow(rowIndex)">
        {{ $t('button.delete') }}
      </aside>
    </el-row>

    <footer class="add-layout-target" @click="clickAddLayout">
      {{ $t('visualization.dashboard.tip.add.layout') }}
    </footer>

    <layout-dialog :visible.sync="visible.layout" :title="title.layout" :width="'60%'" @on-submit="clickCreateDashboard"></layout-dialog>

    <template v-if="tempData.length > 0">
      <chart-dialog
        :visible.sync="visible.chart"
        :type-data.sync="data.type"
        :chunk-data.sync="tempData[current.row]['list'][current.col]['data']"
        :title="title.chart"
        :width="'60%'"
        @on-submit="clickSubmitAddChartComponent"
      ></chart-dialog>
    </template>
  </section>
</template>

<script>
import LineChart from '@comp/ChartFactory/dashboard/LineChart'
import PieChart from '@comp/ChartFactory/dashboard/PieChart'
import BarChart from '@comp/ChartFactory/dashboard/BarChart'
import BarStackChart from '@comp/ChartFactory/dashboard/BarStackChart'
import LayoutDialog from './DashboardLayoutDialog'
import ChartDialog from './DashboardDataCompDialog'
import { prompt } from '@util/prompt'
import { queryChartTypeData } from '@api/visualization/dashboard-api'

export default {
  components: {
    LineChart,
    PieChart,
    BarChart,
    BarStackChart,
    LayoutDialog,
    ChartDialog,
  },
  props: {
    dashboardData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      data: {
        type: [],
      },
      visible: {
        layout: false,
        chart: false,
      },
      title: {
        layout: this.$t('visualization.dashboard.tip.add.layout'),
        chart: this.$t('visualization.dashboard.tip.add.chart'),
      },
      tempData: this.dashboardData,
      current: {
        row: 0,
        col: 0,
        chartId: '',
      },
    }
  },
  watch: {
    dashboardData: {
      handler(data) {
        this.tempData = data
        this.handleChartType()
      },
      deep: true,
    },
    tempData: {
      handler(data) {
        this.$emit('update:dashboardData', data)
      },
      deep: true,
    },
  },
  mounted() {
    this.loaderData()
  },
  methods: {
    async loaderData() {
      await this.getChartType()
      this.handleChartType()
    },
    clickUpdateChart(rowIndex, colIndex, col) {
      if (col && col.data.id) {
        this.current.chartId = col.data.id
      }
      this.handleCurrentData(rowIndex, colIndex)
    },
    clickClearChart(rowIndex, colIndex, col) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.current.row = rowIndex
        this.current.col = colIndex
        this.handleTypeData([col.data.id])
        this.tempData[rowIndex]['list'][colIndex]['data'] = null
        this.$forceUpdate()
      })
    },
    clickAddChart(rowIndex, colIndex) {
      this.current.chartId = ''
      this.handleCurrentData(rowIndex, colIndex)
    },
    clickDeleteRow(index) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        if (this.tempData.length > 1) {
          this.$nextTick(() => {
            this.current.row = this.current.col = 0
            this.tempData[index]['list'].forEach((item) => {
              if (item.data && item.data.id && Object.keys(item.data).length > 0) {
                this.handleTypeData([item.data.id])
              }
            })
            this.tempData.splice(index, 1)
          })
        } else {
          prompt({
            i18nCode: 'visualization.dashboard.tip.layout',
            type: 'warning',
          })
        }
      })
    },
    clickAddLayout() {
      this.visible.layout = true
    },
    clickCreateDashboard(param) {
      this.tempData.push(...param.dashboardData)
    },
    clickSubmitAddChartComponent(param) {
      if (param.id !== this.current.chartId) {
        this.handleTypeData([this.current.chartId])
      }
      this.tempData[this.current.row]['list'][this.current.col]['data'] = param
    },
    handleCurrentData(rowIndex, colIndex) {
      this.current.row = rowIndex
      this.current.col = colIndex
      this.visible.chart = true
    },
    handleChartType() {
      const notVisible = []
      this.dashboardData.forEach(async (item) => {
        if (item.list && item.list.length > 0) {
          await item.list.forEach((list) => {
            if (list.data) {
              notVisible.push(list.data.id)
            }
          })
        }
        this.resize()
      })
      this.handleTypeData(notVisible, false)
    },
    handleTypeData(ids, visible = true) {
      const chartVisible = (id) => {
        this.data.type.forEach((row) => {
          row.chart.forEach((col) => {
            if (col.id === id) {
              col.visible = visible
            }
          })
        })
      }

      ids.forEach((id) => {
        chartVisible(id)
      })
    },
    resize() {
      this.$nextTick(() => {
        Object.getOwnPropertyNames(this.$refs).forEach((domKey) => {
          if (domKey.indexOf('ChartDom') > -1) {
            this.$refs[domKey].forEach((chart) => {
              chart.resize()
            })
          }
        })
      })
    },
    async getChartType() {
      await queryChartTypeData().then((res) => {
        this.data.type = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard-designer-wrapper {
  .el-row {
    margin-bottom: 10px;
  }

  .chunk-row {
    position: relative;
    margin: 10px auto;

    & > aside {
      position: absolute;
      bottom: 0;
      width: calc(100% - 10px);
      margin: 0 5px;
      padding: 10px 0;
      opacity: 0;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
      text-align: center;
      font: normal bold 18px/20px monospace;
      letter-spacing: 10px;
      cursor: pointer;

      &:hover {
        opacity: 1;
        @include theme('background-color', border-color);
      }
    }

    .chunk-wrapper {
      .chunk-non-empty,
      .chunk-empty {
        height: 100%;
        border: 1px solid;
        border-radius: 10px;
        text-align: center;
        @include theme('border-color', border-color);
      }

      .chunk-non-empty {
        .chunk-header {
          display: flex;
          height: 30px;
          padding: 5px 10px;
          justify-content: space-between;

          &-title {
            width: calc(100% - 80px);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            text-align: left;
          }

          &-button {
            min-width: 80px;

            i {
              font-size: 20px;
              margin: 0 10px;
              cursor: pointer;

              &:hover {
                color: #1873d7;
              }
            }
          }
        }

        .chunk-chart {
          height: calc(100% - 40px);
          padding: 5px 10px;
        }
      }

      .chunk-empty {
        height: 100%;
        text-align: center;

        &-icon {
          position: relative;
          height: 70%;
          font-size: 50px;
          line-height: 100%;

          i {
            position: absolute;
            left: 50%;
            top: 50%;
            padding: 20px;
            border: 1px dashed #ccc;
            border-radius: 5px;
            cursor: pointer;
            transform: translate(-50%, -50%);

            &:hover {
              color: #1873d7;
              border: 1px dashed #1873d7;
            }
          }
        }

        &-tip {
        }
      }
    }
  }

  .add-layout-target {
    height: 80px;
    line-height: 80px;
    font: normal bold 20px/80px monospace;
    letter-spacing: 10px;
    text-align: center;
    border-radius: 10px;
    cursor: pointer;
    @include theme('background-color', border-color);

    &:hover {
      @include theme('color', button-font-hover-color);
      @include theme('background-color', header-bg-color);
    }
  }
}
</style>
