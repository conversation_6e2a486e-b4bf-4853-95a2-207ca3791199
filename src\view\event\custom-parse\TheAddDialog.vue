<!--
 * @Description: 自定义解析 - 添加弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-12-31
 * @Editor:
 * @EditDate: 2022-12-31
-->
<template>
  <el-dialog
    ref="dialogDom"
    v-el-dialog-drag
    :visible.sync="visible"
    :title="$t('dialog.title.rule', [titleName])"
    :close-on-click-modal="false"
    width="70%"
  >
    <el-form ref="formTemplate" :model="model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="devType" :label="$t('event.customParse.label.devType')">
            <el-select v-model="model.devType" clearable filterable @change="changeDevType">
              <el-option v-for="item in options.devType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="status" :label="$t('event.customParse.label.status')">
            <el-switch v-model="model.status" active-value="1" inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item prop="message" :label="$t('event.customParse.label.message')">
          <el-input
            id="logMessage"
            v-model="model.message"
            type="textarea"
            :rows="4"
            maxlength="4000"
            :readonly="readonlyLog"
            @paste.native="pasteLogMessage()"
            @mouseup.native="selectKeyword"
          ></el-input>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item prop="points" :label="$t('event.customParse.label.patternInfo')">
          <section class="parse-item">
            <div v-for="(item, index) in model.points" :key="index" class="parse-item-row">
              <el-form-item :key="item.id" class="parse-item-col" :label="$t('event.customParse.label.propDetail')" label-width="100px">
                <span :title="item.content">{{ item.content }}</span>
              </el-form-item>
              <el-form-item :prop="item.key" class="parse-item-col" :label="$t('event.customParse.label.multiGroup')" label-width="60px">
                <el-select v-model="item.key" filterable clearable @change="changeKey(item)">
                  <el-option
                    v-for="item in options.multiGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item style="width: 25%;" label-width="0">
                <el-select v-if="item.key === 'code'" v-model="item.eventType" filterable clearable :disabled="item.eventTypeDisabled">
                  <el-option
                    v-for="item in options.eventType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.eventTypeDisabled"
                  ></el-option>
                </el-select>
                <el-select v-if="item.key === 'level'" v-model="item.level" filterable clearable>
                  <el-option v-for="item in options.level" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-button class="del-button" @click="clickDelete(item)">
                {{ $t('button.delete') }}
              </el-button>
            </div>
          </section>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item prop="pattern" :label="$t('event.customParse.label.pattern')">
          <el-input v-model="model.pattern" type="textarea" :rows="3" readonly maxlength="1024"></el-input>
        </el-form-item>
      </el-row>
    </el-form>
    <footer slot="footer">
      <el-button @click="clickCancel">
        {{ $t('button.cancel') }}
      </el-button>
      <el-button @click="clickParse">
        {{ $t('button.generateParse') }}
      </el-button>
      <el-button @click="clickSubmit">
        {{ $t('button.save') }}
      </el-button>
    </footer>
  </el-dialog>
</template>

<script>
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import { validateIp, validatePort } from '@util/validate'
import elDialogDrag from '@/directive/el-dialog-drag'
import { generateParseExpress, queryKeywordEventType } from '@api/event/custom-parse-api'
export default {
  directives: {
    elDialogDrag,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      readonlyLog: false,
      rules: {
        devType: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        message: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        points: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.readonlyLog = false
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.dialogVisible = false
    },
    clickDelete(row) {
      this.model.points.map((item, index) => {
        if (item.id === row.id) {
          this.model.points.splice(index, 1)
        }
      })
      this.handleMultiGroup()
    },
    clickParse() {
      // 校验多元组必选属性特征值
      if (!this.validMultiGroupProps()) {
        return false
      }
      if (!this.validMultiGroupRequired()) {
        return false
      }
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.generateParseExpress()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickSubmit() {
      if (!this.validMultiGroupProps()) {
        return false
      }
      if (!this.validMultiGroupRequired()) {
        return false
      }
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    pasteLogMessage() {
      setTimeout(() => {
        this.readonlyLog = true
      }, 5000)
    },
    selectKeyword(e) {
      const element = document.getElementById('logMessage')
      const start = element.selectionStart
      const end = element.selectionEnd
      const selValue = window.getSelection().toString()
      const itemObj = {
        id: Math.random(),
        content: selValue,
        key: '',
        start: start,
        end: end,
        eventType: '',
        level: '',
      }
      if (this.validKeywordLegal(itemObj)) {
        this.model.points.push(itemObj)
      }
    },
    // 校验划词是否合法
    validKeywordLegal(obj) {
      const keyword = obj.content
      if (isEmpty(keyword)) {
        return false
      }
      // 划词中首尾不能为空白字符
      if (/\s/i.test(keyword.charAt(0)) || /\s/i.test(keyword.charAt(keyword.length - 1))) {
        this.$message({
          message: this.$t('event.customParse.tip.validKeyword'),
          type: 'warning',
          duration: 1000,
        })
        return false
      }
      // 划词不允许重复
      for (let i = 0; i < this.model.points.length; i++) {
        if (
          (obj.start >= this.model.points[i].start && obj.start <= this.model.points[i].end - 1) ||
          (obj.end - 1 >= this.model.points[i].start && obj.end - 1 <= this.model.points[i].end) ||
          (obj.start <= this.model.points[i].start && obj.end >= this.model.points[i].end)
        ) {
          this.$message({
            message: this.$t('event.customParse.tip.repeatKeyword'),
            type: 'warning',
            duration: 1000,
          })
          return false
        }
      }
      // 相邻划词不允许选中
      for (let i = 0; i < this.model.points.length; i++) {
        if (obj.start === this.model.points[i].end || obj.end === this.model.points[i].start) {
          this.$message({
            message: this.$t('event.customParse.tip.neighborNoSelected'),
            type: 'warning',
            duration: 1000,
          })
          return false
        }
      }
      return true
    },
    changeDevType() {
      this.model.points = []
      this.handleMultiGroup()
    },
    changeKey(item) {
      item.eventType = ''
      item.level = ''
      item.eventTypeDisabled = false
      if (item.key === 'code') {
        if (!isEmpty(this.model.devType)) {
          this.queryKeywordEventType(item)
        } else {
          this.$message({
            message: this.$t('event.customParse.tip.selDevType'),
            type: 'info',
          })
        }
      } else if ((item.key === 'ip1' || item.key === 'ip2') && !this.validatorIp(item.content)) {
        item.key = ''
        return false
      } else if ((item.key === 'port1' || item.key === 'port2') && !this.validatorPort(item.content)) {
        item.key = ''
        return false
      }
      this.handleMultiGroup()
    },
    validatorIp(ip) {
      if (ip !== '' && !validateIp(ip)) {
        prompt({
          i18nCode: 'validate.ip.incorrect',
          type: 'info',
        })
        return false
      } else {
        return true
      }
    },
    validatorPort(port) {
      if (port !== '' && !validatePort(port)) {
        prompt({
          i18nCode: 'validate.port.incorrect',
          type: 'info',
        })
        return false
      } else {
        return true
      }
    },
    // 多元组不允许重复选
    handleMultiGroup() {
      this.options.multiGroup.map((group) => {
        group.disabled = false
        this.model.points.map((item) => {
          if (group.value === item.key) {
            group.disabled = true
          }
        })
      })
    },
    validMultiGroupRequired() {
      let pointFlag = true
      this.model.points.map((point) => {
        if (point.key === '') {
          pointFlag = false
        }
        if ((point.key === 'code' && point.eventType === '') || (point.key === 'level' && point.level === '')) {
          pointFlag = false
        }
      })
      if (pointFlag === false) {
        prompt({
          i18nCode: 'validate.form.warning',
          type: 'warning',
        })
      }
      return pointFlag
    },
    validMultiGroupProps() {
      let flag = false
      this.model.points.map((item) => {
        if (item.key === 'code') {
          flag = true
        }
      })
      if (flag === false) {
        prompt({
          i18nCode: 'event.customParse.tip.eventTypeRequired',
          type: 'info',
        })
      }
      return flag
    },
    generateParseExpress() {
      generateParseExpress(this.model).then((res) => {
        this.model.pattern = res
      })
    },
    queryKeywordEventType(item) {
      const params = {
        content: item.content,
        devType: this.model.devType,
      }
      queryKeywordEventType(params).then((res) => {
        if (!isEmpty(res)) {
          item.eventType = res
          item.eventTypeDisabled = true
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.parse-item {
  width: 90%;
  min-height: 80px;
  max-height: 140px;
  border: 1px solid;
  @include theme('border-color', el-input-border);
  overflow-y: auto;
  &-row {
    display: flex;
    justify-content: space-between;
    width: 90%;
    height: 40px;
    min-height: 40px;
    margin: 5px auto;
    border-bottom: 1px solid;
    @include theme('border-bottom-color', el-input-border);
    .parse-item-col {
      display: inline-block;
      width: 45%;
      span {
        display: inline-block;
        width: 140px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        @include theme('color', font-color);
      }
      .el-select {
        width: 70% !important;
      }
      ::v-deep.el-form-item__label {
        @include theme('color', font-color);
      }
    }
    .del-button {
      border: none;
      box-shadow: none;
      color: #fff;
    }
    .el-button:focus,
    .del-button:hover {
      color: #fff;
      font-weight: bold;
      @include theme('background-color', error-text-color);
    }
  }
}
</style>
