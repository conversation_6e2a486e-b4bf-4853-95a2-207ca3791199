import i18n from '@/language'
const compProperty = {
  basic: [
    { key: 'monitorName', label: i18n.t('monitor.management.props.monitorName') },
    { key: 'monitorEnabled', label: i18n.t('monitor.management.props.status') },
    { key: 'monitorTypeName', label: i18n.t('monitor.management.props.monitorType') },
    { key: 'pollDate', label: i18n.t('monitor.management.props.pollDate') },
    { key: 'edName', label: i18n.t('monitor.management.props.edName') },
    { key: 'agentIp', label: i18n.t('monitor.management.props.agentId') },
  ],
  cpu: [
    { key: 'cpuUseRate', label: i18n.t('monitor.management.config.cpu.cpuUseRate') },
    { key: 'cpuTimes', label: i18n.t('monitor.management.config.cpu.cpuTimes') },
  ],
  memory: [
    { key: 'memoryUseRate', label: i18n.t('monitor.management.config.memory.memoryUseRate') },
    { key: 'memoryTimes', label: i18n.t('monitor.management.config.memory.memoryTimes') },
  ],
  disk: [{ key: 'diskUseRate', label: i18n.t('monitor.management.config.disk.diskUseRate') }],
  snmp: [
    { key: 'snmpVersionName', label: i18n.t('monitor.management.config.snmp.version') },
    { key: 'snmpPort', label: i18n.t('monitor.management.config.snmp.port') },
    { key: 'readCommunity', label: i18n.t('monitor.management.config.snmp.readCommunity') },
    { key: 'writeCommunity', label: i18n.t('monitor.management.config.snmp.writeCommunity') },
  ],
  snmpV3: [
    { key: 'snmpVersionName', label: i18n.t('monitor.management.config.snmp.version') },
    { key: 'snmpPort', label: i18n.t('monitor.management.config.snmp.port') },
    { key: 'authWayName', label: i18n.t('monitor.management.config.snmp.authWay') },
    { key: 'authPwd', label: i18n.t('monitor.management.config.snmp.authPwd') },
    { key: 'encryptionWayName', label: i18n.t('monitor.management.config.snmp.encryptionWay') },
    { key: 'encryptionPwd', label: i18n.t('monitor.management.config.snmp.encryptionPwd') },
    { key: 'context', label: i18n.t('monitor.management.config.snmp.context') },
    { key: 'contextName', label: i18n.t('monitor.management.config.snmp.contextName') },
    { key: 'snmpUserName', label: i18n.t('monitor.management.config.snmp.snmpUserName') },
    { key: 'secLevName', label: i18n.t('monitor.management.config.snmp.securityLevel') },
  ],
}
export default compProperty
