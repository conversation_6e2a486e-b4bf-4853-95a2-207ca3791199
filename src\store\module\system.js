const state = {
  pageSize: 20,
  theme: 'light',
}

const mutations = {
  SET_PAGE_SIZE(state, pageSize) {
    state.pageSize = pageSize
  },
  SET_THEME(state, theme) {
    state.theme = theme
  },
}

const actions = {
  updatePageSize({ commit }, pageSize) {
    commit('SET_PAGE_SIZE', pageSize)
  },
  switchTheme({ commit }, theme) {
    commit('SET_THEME', theme)
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
