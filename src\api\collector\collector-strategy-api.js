import request from '@util/request'

//查询采集器策略
export function queryStrategyTableData(obj) {
  return request({
    url: '/collector/strategy/filter/strategies',
    method: 'get',
    params: obj || {},
  })
}

//删除采集器策略
export function deleteStrategyData(id) {
  return request({
    url: `/collector/strategy/filter/strategy/${id}`,
    method: 'delete',
  })
}

//添加采集器策略
export function addStrategyData(obj) {
  return request({
    url: '/collector/strategy/filter/strategy',
    method: 'post',
    data: obj || {},
  })
}

//修改采集器策略
export function updateStrategyData(obj) {
  return request({
    url: '/collector/strategy/filter/strategy',
    method: 'put',
    data: obj || {},
  })
}

// 查询采集器事件类型
export function queryComboEventType() {
  return request({
    url: '/collector/strategy/filter/combo/event-types',
    method: 'get',
  })
}
