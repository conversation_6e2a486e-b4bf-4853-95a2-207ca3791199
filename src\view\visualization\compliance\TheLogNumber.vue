<!--
 * @Description: 日志接收总数
 * @Version: 3.5
 * @Author:
 * @Date: 2023/03/07
 * @Editor:
 * @EditDate: 2023/03/07
-->
<template>
  <div class="widget">
    <header class="widget-header">
      {{ this.$t('visualization.compliance.title.logTotalNumber') }}
    </header>
    <main class="widget-body">
      <div class="icon-box">
        <i class="el-icon-s-data"></i>
      </div>
      <div class="detail-box">
        <p class="detail-box-word">
          <span class="high-bit">{{ highBit }}</span>
          {{ otherBit }}&nbsp;{{ $t('visualization.compliance.label.strip') }}
        </p>
        <div class="detail-box-chart">
          <img src="./img/log-trend.png" height="100%" width="100%" />
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { queryLogTotalNumber } from '@api/visualization/compliance-api'
export default {
  data() {
    return {
      highBit: '',
      otherBit: '',
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    numberFormat(value) {
      const k = 10000,
        sizes = ['', '万', '亿', '万亿']
      if (value < k) {
        this.highBit = ''
        this.otherBit = value
      } else {
        const i = Math.floor(Math.log(value) / Math.log(k))
        this.highBit = parseInt(value / Math.pow(k, i))
        this.otherBit = String(value).substring(String(this.highBit).length, String(value).length)
        this.otherBit = this.toThousands(this.otherBit)
        this.highBit = this.highBit + sizes[i]
      }
    },
    toThousands(num = 0) {
      return num.toString().replace(/\d+/, function(n) {
        return n.replace(/(\d)(?=(?:\d{3})+$)/g, '$1, ')
      })
    },
    loadData() {
      this.getLogTotalNumber()
    },
    getLogTotalNumber() {
      queryLogTotalNumber().then((res) => {
        if (res) {
          this.numberFormat(res.zongshu)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.widget-body {
  .icon-box {
    background-color: #00bbd9;
  }
  .detail-box-word {
    span {
      color: #00bbd9;
    }
  }
}
</style>
