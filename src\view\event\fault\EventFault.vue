<!--
 * @Description: 故障事件
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" @on-change="changeQueryTable"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-select="clickSelectRows"
      @on-detail="clickDetail"
      @on-jump="clickJumpColumn"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model" :options="options"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from '@view/event/fault/TheTableFooter'
import DetailDialog from '@view/event/fault/TheDetailDialog'
import { runStatus } from '@asset/js/code/option'
import { queryFaultEvents } from '@api/event/fauft-api'

export default {
  name: 'EventFault',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('event.fault.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          faultName: '',
          faultClassName: '',
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
        handle: {
          visible: false,
          model: {},
        },
      },
      options: {
        currentStatus: runStatus,
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          faultName: this.query.form.faultName,
          faultClass: this.query.form.faultClass,
          faultLevel: this.query.form.faultLevel,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickSelectRows(select) {
      this.table.selected = select
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    clickHandle(row) {
      this.dialog.handle.visible = true
      this.dialog.handle.model = Object.assign({}, row)
    },
    clickJumpColumn(params) {
      this.$router.push({
        path: params.path,
        query: {
          drillKey: params.columnKey,
        },
      })
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryFaultEvents(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
  },
}
</script>
