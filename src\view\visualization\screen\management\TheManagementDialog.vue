<!--
 * @Description: 大屏管理 - 弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemp" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemp" :model="formData" :rules="rule" label-width="25%">
      <el-form-item :label="$t('visualization.management.table.name')" prop="screenName">
        <el-input v-model="formData.screenName" class="width-small"></el-input>
      </el-form-item>
      <el-form-item :label="$t('visualization.management.table.url')" prop="screenUrl">
        <el-input v-model="formData.screenUrl" class="width-small"></el-input>
      </el-form-item>
      <el-form-item :label="$t('visualization.management.table.description')">
        <el-input v-model="formData.screenDescription" type="textarea" class="width-small"></el-input>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '35%',
    },
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      rule: {
        screenName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        screenUrl: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemp.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemp.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.formData)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemp.end()
    },
  },
}
</script>
