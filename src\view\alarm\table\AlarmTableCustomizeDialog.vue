<!--
 * @Description: 告警列表 - 自定义列弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules">
      <el-form-item>
        <el-checkbox v-model="form.model.checkAll" :indeterminate="form.model.isIndeterminate" @change="handleCheckAllChange">
          {{ $t('button.checkedAll') }}
        </el-checkbox>
        <div style="margin: 15px 0;"></div>
        <el-checkbox-group v-model="form.model.checkList" @change="handleCheckedChange">
          <el-row>
            <el-col v-for="(item, index) in options.checkboxOption" :key="index" :span="6">
              <el-checkbox :label="item.key">
                {{ item.value }}
              </el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      options: {
        checkboxOption: [
          {
            key: 'name',
            value: this.$t('alarm.table.label.name'),
          },
          {
            key: 'level',
            value: this.$t('alarm.table.label.level'),
          },
          {
            key: 'auditTypeName',
            value: this.$t('alarm.table.label.auditTypeName'),
          },
          {
            key: 'alarmStrategyName',
            value: this.$t('alarm.table.label.alarmStrategyName'),
          },
          {
            key: 'total',
            value: this.$t('alarm.table.label.total'),
          },
          {
            key: 'createTime',
            value: this.$t('alarm.table.label.createTime'),
          },
          {
            key: 'updateTime',
            value: this.$t('alarm.table.label.updateTime'),
          },
          {
            key: 'state',
            value: this.$t('alarm.table.label.state'),
          },
        ],
        columnOption: ['name', 'level', 'auditTypeName', 'alarmStrategyName', 'total', 'createTime', 'updateTime', 'state'],
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      if (this.form.model.checkList && this.form.model.checkList.length > 0) {
        this.$refs.formTemplate.validate((valid) => {
          if (valid) {
            // 给父级调用数据
            this.$emit('on-submit', this.form.model)
            this.clickCancelDialog()
          } else {
            prompt(
              {
                i18nCode: 'validate.form.warning',
                type: 'warning',
              },
              () => {
                return false
              }
            )
          }
        })
      } else {
        prompt(
          {
            i18nCode: 'validate.form.lessOne',
            type: 'warning',
          },
          () => {
            return false
          }
        )
      }
      this.$refs.dialogTemplate.end()
    },
    handleCheckAllChange(val) {
      this.form.model.checkList = val ? this.options.columnOption : []
      this.form.model.isIndeterminate = false
    },
    handleCheckedChange(val) {
      this.form.model.checkAll = this.options.columnOption.length === val.length
      this.form.model.isIndeterminate = val.length > 0 && val.length < this.options.columnOption.length
    },
  },
}
</script>
