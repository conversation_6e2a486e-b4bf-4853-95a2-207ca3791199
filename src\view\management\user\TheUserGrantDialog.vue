<!--
 * @Description: 用户管理 -授权弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmit">
    <section class="table-header">
      <el-input
        v-model="data.filterRole"
        maxlength="32"
        :placeholder="$t('tip.component.searchKeywords')"
        :inline="true"
        clearable
        @change="clickQueryRole"
      >
        <i slot="suffix" class="el-input__icon soc-icon-search" @click="clickQueryRole"></i>
      </el-input>
    </section>
    <section class="table-body">
      <el-table
        ref="roleTable"
        v-loading="data.loading"
        :data="data.table"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="300"
        @selection-change="roleTableSelectsChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column prop="roleName" :label="$t('management.role.infoItem.roleName')" width="150"></el-table-column>
        <el-table-column prop="roleDescription" :label="$t('management.role.infoItem.roleDescription')"></el-table-column>
      </el-table>
    </section>
  </custom-dialog>
</template>
<script>
import CustomDialog from '@comp/CustomDialog'
import { queryRoleData } from '@api/management/user-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    roleIds: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      data: {
        filterRole: '',
        table: [],
        allData: [],
        loading: false,
        selected: [],
      },
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
    roleIds(ids) {
      this.selectedRoleTable([...ids])
    },
  },
  mounted() {
    this.getRoleTableData()
  },
  methods: {
    getRoleTableData() {
      this.data.loading = true
      queryRoleData().then((res) => {
        this.data.table = res
        this.data.allData = res
        this.data.loading = false
      })
    },
    clickQueryRole() {
      const [filter, roles] = [this.data.filterRole.toLowerCase(), [...this.data.allData]]
      if (this.data.filterRole && this.data.filterRole.trim() !== '') {
        this.data.table = roles.filter((item) => {
          return Object.keys(item).some((key) => {
            if (key === 'roleName' || key === 'roleDescription')
              return (
                String(item[key])
                  .toLowerCase()
                  .indexOf(filter) > -1
              )
          })
        })
      } else {
        this.getRoleTableData()
      }
    },
    clickCancelDialog() {
      this.getRoleTableData()
      this.data.filterRole = ''
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$confirm(this.$t('tip.confirm.grant'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        const roleIds = []
        this.data.selected.forEach((item) => {
          roleIds.push(item.roleId)
        })
        this.$emit('on-submit', roleIds)
        this.clickCancelDialog()
      })
      this.$refs.dialogTemplate.end()
    },
    roleTableSelectsChange(select) {
      this.data.selected = select
    },
    selectedRoleTable(ids) {
      this.data.table.forEach((row) => {
        if (ids.indexOf(row.roleId) >= 0) {
          this.$refs.roleTable.toggleRowSelection(row)
        }
      })
    },
  },
}
</script>
