export default {
  original: {
    name: 'Original Log',
    total: 'Total',
    label: {
      expression: 'Expression Name',
      type2Name: 'Original Log Name',
      logName: 'Event Type',
      level: 'Level',
      srcDevice: 'Log Source Device',
      dstDevice: 'Source IP',
      srcIP: 'Source IP',
      dstIP: 'Destination IP',
      customName: 'Custom Name',
    },
    placeholder: {
      srcStartIP: 'Source Start IP',
      srcEndIP: 'Source End IP',
      dstStartIP: 'Destination Start IP',
      dstEndIP: 'Destination End IP',
      fromStartIP: 'Source Start IP',
      fromEndIP: 'Source End IP',
      logFormat: 'Log Format',
      forwardServer: 'Forward Server',
      username: 'User',
      targetObject: 'Target Object',
      logRecieveTime: 'Receive Time',
      logTime: 'Log Time',
      srcStartMac: "Source Start MAC Address",
      srcEndMac: "Source End MAC Address",
      dstStartMac: "Destination Start MAC Address",
      dstEndMac: "Destination End MAC Address",
    },
    group: {
      basic: 'Basic Information',
      source: 'Source',
      destination: 'Destination',
      from: 'Source',
      geo: 'Geographic Information',
      other: 'Other',
      log: 'Log Original Text',
    },
    basic: {
      type2Name: 'Original Log Name',
      eventName: 'Event Type',
      eventCategoryName: 'Event Category',
      level: 'Event Level',
      deviceCategoryName: 'Device Category',
      deviceTypeName: 'Device Type',
      time: 'Receive Time',
      logTime: 'Log Time',
      code: 'Feature Value',
      username: 'User',
      targetObject: 'Target Object',
      eventDesc: 'Event Description',
      action: 'Action',
      resultName: 'Event Result',
    },
    source: {
      sourceIp: 'Source IP',
      sourceAddress: 'Source Address',
      sourcePort: 'Source Port',
      sourceAsset: 'Source Asset',
      sourceMac: 'Source MAC Address',
    },
    destination: {
      targetIp: 'Destination IP',
      targetAddress: 'Destination Address',
      targetPort: 'Destination Port',
      targetAsset: 'Destination Asset',
      targetMac: 'Destination MAC Address',
    },
    from: {
      fromIp: 'Source IP',
    },
    geo: {
      sourceCountryName: 'Source Country',
      sourceCountryLongitude: 'Source Country Longitude',
      sourceCountryLatitude: 'Source Country Latitude',
      sourceAreaName: 'Source Area',
      sourceAreaLongitude: 'Source Area Longitude',
      sourceAreaLatitude: 'Source Area Latitude',
      targetCountryName: 'Destination Country',
      targetCountryLongitude: 'Destination Country Longitude',
      targetCountryLatitude: 'Destination Country Latitude',
      targetAreaName: 'Destination Area',
      targetAreaLongitude: 'Destination Area Longitude',
      targetAreaLatitude: 'Destination Area Latitude',
    },
    other: {
      protocol: 'Protocol',
      receiveProtocol: 'Receive Protocol',
      year: 'Year',
      month: 'Month',
      day: 'Day',
      hour: 'Hour',
      minute: 'Minute',
      second: 'Second',
      otherIp: 'Other IP',
      otherPort: 'Other Port',
    },
    log: {
      raw: 'Log Original Text',
    },
    forward: {
      status: 'Forward Status',
      logFormat: 'Log Format',
      forwardServer: 'Forward Server',
    },
    fuzzyQuery: 'Original Log Name/Log Original Text',
    parseRateDesc: 'Average success time for parsing rules per thousand logs: {0}ms',
  },
}
