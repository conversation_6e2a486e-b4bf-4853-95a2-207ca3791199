<!--
 * @Description: 菜单管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!search.high" v-has="'query'" class="table-header-search-input">
            <el-input
              v-model="search.fuzzyField"
              prefix-icon="soc-icon-search"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('management.menu.table.name')])"
              @change="changeQueryMenuTable"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <section class="table-header-search-button">
              <el-button v-if="!search.high" v-has="'query'" @click="changeQueryMenuTable">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="clickHighQueryMenu">
                {{ $t('button.search.exact') }}
                <i :class="search.high ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
              </el-button>
            </section>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddMenu">
            {{ $t('button.add') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <section v-show="search.high" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model="search.query.form.model.menuName"
                  :placeholder="$t('management.menu.table.name')"
                  clearable
                  @change="changeQueryMenuTable"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model="search.query.form.model.menuLocation"
                  :placeholder="$t('management.menu.table.location')"
                  clearable
                  @change="changeQueryMenuTable"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="search.query.form.model.menuStatus"
                  :placeholder="$t('management.menu.table.status')"
                  clearable
                  @change="changeQueryMenuTable"
                >
                  <el-option v-for="item in option.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model="search.query.form.model.menuDescription"
                  :placeholder="$t('management.menu.table.description')"
                  clearable
                  @change="changeQueryMenuTable"
                ></el-input>
              </el-col>
              <el-col align="right" :span="4">
                <el-button v-has="'query'" @click="changeQueryMenuTable">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="clickResetQueryMenuForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button @click="clickShrinkHighQuery">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </section>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.menu.name') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="menuTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          row-key="menuId"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          @row-dblclick="dblclickMenuDisplayDetail"
          @current-change="menuTableRowChange"
        >
          <el-table-column prop="menuName" :label="$t('management.menu.table.name')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="menuStatusText" :label="$t('management.menu.table.status')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="menuLocation" :label="$t('management.menu.table.location')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="menuDescription" :label="$t('management.menu.table.description')" show-overflow-tooltip></el-table-column>
          <el-table-column width="280" fixed="right">
            <template slot-scope="scope">
              <el-button v-has="'sort'" class="el-button--blue" @click="clickMoveMenu(scope.row, 'up')">
                {{ $t('button.move.up') }}
              </el-button>
              <el-button v-has="'sort'" class="el-button--blue" @click="clickMoveMenu(scope.row, 'down')">
                {{ $t('button.move.down') }}
              </el-button>
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateMenu(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteMenu(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <menu-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :width="'35%'"
      :menu-parent="data.parent"
      :menu-form="dialog.form"
      :resource-option="data.resource"
      @on-submit="clickSubmitAddMenu"
    ></menu-dialog>

    <menu-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :width="'35%'"
      :menu-parent="data.parent"
      :menu-form="dialog.form"
      :resource-option="data.resource"
      @on-submit="clickSubmitUpdateMenu"
    ></menu-dialog>

    <menu-dialog
      :visible.sync="dialog.visible.detail"
      :title="dialog.title.detail"
      :width="'35%'"
      :menu-parent="data.parent"
      :menu-form="dialog.form"
      :resource-option="data.resource"
      readonly
    ></menu-dialog>
  </div>
</template>
<script>
import MenuDialog from './TheMenuDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import {
  addMenuData,
  deleteMenuData,
  updateMenuData,
  queryMenuTableData,
  queryMenuDetailData,
  sortMenuData,
  queryParentMenuData,
  queryResourceOptionData,
} from '@api/management/menu-api'

export default {
  name: 'ManagementMenu',
  components: {
    MenuDialog,
  },
  data() {
    return {
      search: {
        high: false,
        fuzzyField: '',
        query: {
          form: {
            model: {
              menuName: '',
              menuStatus: '',
              menuLocation: '',
              menuDescription: '',
            },
          },
        },
      },
      option: {
        status: [
          {
            value: '0',
            label: this.$t('management.user.option.normal'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.manualLock'),
          },
          {
            value: '2',
            label: this.$t('management.user.option.systemLock'),
          },
        ],
      },
      data: {
        debounce: null,
        loading: false,
        table: [],
        parent: [],
        resource: [],
        detail: {},
      },
      dialog: {
        visible: {
          add: false,
          update: false,
          detail: false,
        },
        title: {
          add: this.$t('dialog.title.add', [this.$t('management.menu.name')]),
          update: this.$t('dialog.title.update', [this.$t('management.menu.name')]),
          detail: this.$t('dialog.title.detail', [this.$t('management.menu.name')]),
        },
        form: {
          model: {
            parentId: '',
            menuName: '',
            menuLocation: '',
            menuStatus: '',
            menuIcon: '',
            menuDescription: '',
            resourceId: '',
          },
          rules: {
            parentId: [
              {
                required: true,
                trigger: 'change',
                validator: (rule, value, callback) => {
                  if (value === '') {
                    callback(new Error(this.$t('validate.empty')))
                  } else {
                    callback()
                  }
                },
              },
            ],
            menuName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            menuStatus: [
              {
                required: true,
                trigger: 'change',
                validator: (rule, value, callback) => {
                  if (value === '') {
                    callback(new Error(this.$t('validate.empty')))
                  } else {
                    callback()
                  }
                },
              },
            ],
          },
        },
      },
      currentRow: {},
    }
  },
  mounted() {
    this.preloadData()
  },
  methods: {
    preloadData() {
      this.initDebounceQuery()
      this.getMenuTable()
      this.getParentMenu({ level: 1, leaf: true })
      this.getResourceOption()
    },
    clickHighQueryMenu() {
      this.search.high = !this.search.high
      this.changeQueryMenuTable()
    },
    clickAddMenu() {
      this.clearAddDialogForm()
      this.dialog.visible.add = true
    },
    clearAddDialogForm() {
      this.dialog.form.model = {
        parentId: '',
        menuName: '',
        menuLocation: '',
        menuStatus: '',
        menuIcon: '',
        menuDescription: '',
        resourceId: '',
      }
    },
    clickSubmitAddMenu(form) {
      this.addMenu(form)
    },
    clickMoveMenu(row, direction) {
      if (direction === 'up') {
        if (row.menuOrder > 1) {
          this.sortMenu({
            menuId: row.menuId,
            parentId: row.parentId,
            menuOrder: row.menuOrder,
            orderType: 0,
          })
        }
      }
      if (direction === 'down') {
        let allowMove = true

        if (row.level === 1) {
          if (row.menuOrder < this.data.table.length) {
            allowMove = true
          }
        } else {
          allowMove = this.searchTree(this.data.table, row)
        }

        if (allowMove) {
          this.sortMenu({
            menuId: row.menuId,
            parentId: row.parentId,
            menuOrder: row.menuOrder,
            orderType: 1,
          })
        }
      }
    },
    async clickUpdateMenu(row) {
      await this.getMenuDetail(row.menuId)
      this.getParentMenu({ level: row.level || 1, leaf: row.leaf || true })
      this.dialog.form.model = this.data.detail
      this.dialog.visible.update = true
    },
    clickSubmitUpdateMenu(form) {
      this.updateMenu(form)
    },
    async clickDeleteMenu(row) {
      await this.deleteMenu(row.menuId)
    },
    async dblclickMenuDisplayDetail(row) {
      await this.getMenuDetail(row.menuId)
      this.dialog.form.model = this.data.detail
      this.dialog.visible.detail = true
    },
    menuTableRowChange(row) {
      this.currentRow = row
    },
    searchTree(data, param) {
      for (const item in data) {
        if (data.hasOwnProperty(item) && param.parentId === data[item].menuId) {
          if (param.menuOrder < data[item].children.length) {
            return true
          }
        }
        if (data.hasOwnProperty(item) && data[item].children) {
          this.searchTree(data[item].children, param)
        }
      }
      return false
    },
    clickResetQueryMenuForm() {
      this.clearHighQueryForm()
      this.changeQueryMenuTable()
    },
    clickShrinkHighQuery() {
      this.clearHighQueryForm()
      this.search.high = false
      this.changeQueryMenuTable()
    },
    changeQueryMenuTable() {
      this.data.debounce()
    },
    clearHighQueryForm() {
      this.search.fuzzyField = ''
      this.search.query.form.model = {
        menuName: '',
        menuStatus: '',
        menuLocation: '',
        menuDescription: '',
      }
    },
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        if (this.search.high) {
          this.getMenuTable({
            menuName: this.search.query.form.model.menuName,
            menuLocation: this.search.query.form.model.menuLocation,
            menuStatus: this.search.query.form.model.menuStatus,
            menuDescription: this.search.query.form.model.menuDescription,
          })
        } else {
          this.getMenuTable()
        }
      }, 500)
    },
    getMenuTable(
      param = {
        fuzzyField: this.search.fuzzyField,
      }
    ) {
      this.data.loading = true
      queryMenuTableData(param).then((res) => {
        if (res) {
          this.data.table = res
        }
        this.data.loading = false
      })
    },
    getParentMenu(obj) {
      queryParentMenuData(obj).then((res) => {
        this.data.parent = res
      })
    },
    getResourceOption() {
      queryResourceOptionData().then((res) => {
        this.data.resource = res
      })
    },
    addMenu(form) {
      addMenuData(form).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.getMenuTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    deleteMenu(menuId) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteMenuData(menuId).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.getMenuTable()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    updateMenu(form) {
      updateMenuData(form).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getMenuTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    async getMenuDetail(menuId) {
      await queryMenuDetailData(menuId).then((res) => {
        this.data.detail = res
      })
    },
    sortMenu(menuId) {
      sortMenuData(menuId).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.sort.success',
              type: 'success',
            },
            () => {
              this.getMenuTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.sort.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
