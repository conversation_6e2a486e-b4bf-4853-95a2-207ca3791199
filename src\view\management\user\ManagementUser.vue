<!--
 * @Description: 用户管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!search.high" v-has="'query'" class="table-header-search-input">
            <el-input
              v-model="search.fuzzyField"
              prefix-icon="soc-icon-search"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('management.user.label.account')])"
              @change="changeQueryUserTable"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!search.high" v-has="'query'" @click="changeQueryUserTable">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickHighQueryUser">
              {{ $t('button.search.exact') }}
              <i :class="search.high ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'grant'" @click="clickBatchGrantTable">
            {{ $t('button.batch.grant') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAddUser">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteUser">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <section v-show="search.high" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model="search.query.form.model.userAccount"
                  :placeholder="$t('management.user.label.account')"
                  clearable
                  @change="changeQueryUserTable"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="search.query.form.model.userStatus"
                  :placeholder="$t('management.user.label.userState')"
                  clearable
                  @change="changeQueryUserTable"
                >
                  <el-option v-for="item in option.user" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <!--此版本没有用到此项，等以后可修改
                            <el-col :span="5">
                                 <el-select
                                     v-model="search.query.form.model.accountStatus"
                                     :placeholder="$t('management.user.label.accountState')"
                                     clearable
                                     @change="changeQueryUserTable">
                                     <el-option
                                         v-for="item in option.account"
                                         :key="item.value"
                                         :label="item.label"
                                         :value="item.value">
                                     </el-option>
                                 </el-select>
                             </el-col>
                             -->
              <el-col :span="5">
                <el-select
                  v-model="search.query.form.model.passwordStatus"
                  :placeholder="$t('management.user.label.passwordState')"
                  clearable
                  @change="changeQueryUserTable"
                >
                  <el-option v-for="item in option.password" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model="search.query.form.model.userDescription"
                  :placeholder="$t('management.user.label.description')"
                  clearable
                  @change="changeQueryUserTable"
                ></el-input>
              </el-col>
              <el-col align="right" :span="4">
                <el-button v-has="'query'" @click="changeQueryUserTable">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="clickResetQueryUserForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button @click="clickShrinkHighQuery">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </section>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.user.name') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="userTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @row-dblclick="dblclickUserDisplayDetail"
          @current-change="userTableRowChange"
          @selection-change="userTableSelectsChange"
        >
          <el-table-column type="selection" :selectable="judgeRowSelected"></el-table-column>
          <el-table-column prop="userAccount" :label="$t('management.user.label.account')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="userStatusText" :label="$t('management.user.label.userState')" show-overflow-tooltip></el-table-column>
          <!-- 此版本没有用到此项，等以后可修改
                    <el-table-column
                        prop="accountStatusText"
                        :label="$t('management.user.label.accountState')"
                        sortable
                        show-overflow-tooltip>
                    </el-table-column>
                    -->
          <el-table-column prop="passwordStatusText" :label="$t('management.user.label.passwordState')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="userDescription" :label="$t('management.user.label.description')" show-overflow-tooltip></el-table-column>
          <el-table-column width="400" fixed="right">
            <!-- 内置用户：systemDefault="1",非内置用户：systemDefault="0"-->
            <template v-if="scope.row.systemDefault !== '1'" slot-scope="scope">
              <el-button v-has="'grant'" class="el-button--blue" @click="clickGrantUser(scope.row)">
                {{ $t('button.grant') }}
              </el-button>
              <el-button class="el-button--blue" @click="clickResetPassword(scope.row)">
                {{ $t('button.reset.password') }}
              </el-button>
              <el-button class="el-button--blue" @click="clickLockUser(scope.row)">
                {{ scope.row.userStatus === '0' ? $t('button.lock') : $t('button.unlock') }}
              </el-button>
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateUser(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteUser(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="userTableSizeChange"
        @current-change="userTableCurrentChange"
      ></el-pagination>
    </footer>
    <au-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form.add"
      :width="'35%'"
      @on-submit="clickSubmitAddUser"
    ></au-dialog>
    <au-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :width="'35%'"
      :form="dialog.form.update"
      disabled
      @on-submit="clickSubmitUpdateUser"
    ></au-dialog>
    <qd-dialog :visible.sync="dialog.visible.detail" :title="dialog.title.detail" :width="'35%'" readonly :form="dialog.form.detail"></qd-dialog>
    <grant-dialog
      :visible.sync="dialog.visible.grant"
      :title="dialog.title.grant"
      :width="'35%'"
      :role-ids="data.roleIds"
      @on-submit="clickSubmitGrant"
    ></grant-dialog>
  </div>
</template>

<script>
import AuDialog from './TheUserAddUpdateDialog'
import GrantDialog from './TheUserGrantDialog'
import QdDialog from './TheUserQueryDetailDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import {
  addUserData,
  deleteUserData,
  updateUserData,
  queryUserTableData,
  queryUserDetailData,
  updateUserLockStateData,
  updateUserGrantRoleData,
  resetPasswordData,
  queryUserGrantRoleData,
} from '@api/management/user-api'

export default {
  name: 'ManagementUser',
  components: {
    AuDialog,
    GrantDialog,
    QdDialog,
  },
  data() {
    return {
      search: {
        high: false,
        fuzzyField: '',
        query: {
          form: {
            model: {
              userAccount: '',
              userStatus: '',
              accountStatus: '',
              passwordStatus: '',
              userDescription: '',
            },
          },
        },
      },
      data: {
        loading: false,
        debounce: null,
        table: [],
        selected: [],
        userIds: [],
        roleIds: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
        visible: true,
      },
      option: {
        user: [
          {
            value: '0',
            label: this.$t('management.user.option.normal'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.manualLock'),
          },
          {
            value: '2',
            label: this.$t('management.user.option.systemLock'),
          },
        ],
        account: [
          {
            value: '0',
            label: this.$t('management.user.option.enable'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.disable'),
          },
        ],
        password: [
          {
            value: '0',
            label: this.$t('management.user.option.normal'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.reset'),
          },
          {
            value: '2',
            label: this.$t('management.user.option.init'),
          },
        ],
      },
      dialog: {
        visible: {
          add: false,
          update: false,
          grant: false,
          detail: false,
        },
        form: {
          add: {},
          update: {},
          detail: {
            model: {
              userAccount: '',
              userStatus: '',
              accountStatus: '',
              passwordStatus: '',
              userDescription: '',
            },
            info: {
              userAccount: {
                key: 'account',
                label: this.$t('management.user.label.account'),
                value: '',
              },
              userStatus: {
                key: 'userStatus',
                label: this.$t('management.user.label.userState'),
                value: '',
              },
              accountStatus: {
                key: 'accountStatus',
                label: this.$t('management.user.label.accountState'),
                value: '',
              },
              passwordStatus: {
                key: 'passwordStatus',
                label: this.$t('management.user.label.passwordState'),
                value: '',
              },
              userDescription: {
                key: 'userDescription',
                label: this.$t('management.user.label.description'),
                value: '',
              },
            },
          },
        },
        title: {
          add: this.$t('dialog.title.add', [this.$t('management.user.name')]),
          update: this.$t('dialog.title.update', [this.$t('management.user.name')]),
          detail: this.$t('dialog.title.detail', [this.$t('management.user.name')]),
          grant: this.$t('management.user.name') + this.$t('button.grant'),
        },
      },
    }
  },
  mounted() {
    this.initDebounceQuery()
    this.getUserTableData()
  },
  methods: {
    clickAddUser() {
      this.dialog.form.add = {
        userAccount: '',
        startTime: '',
        endTime: '',
        activeCyclic: ['', ''],
        userDescription: '',
      }
      this.dialog.visible.add = true
    },
    clickBatchDeleteUser() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.userId).toString()
        this.deleteUser(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickBatchGrantTable() {
      if (this.data.selected.length > 0) {
        this.data.userIds = []
        this.data.selected.forEach((item) => {
          this.data.userIds.push(item.userId)
        })
        this.getUserGrantRole(this.data.userIds)
        this.dialog.visible.grant = true
      } else {
        prompt({
          i18nCode: 'tip.operate.prompt',
          i18nParam: [this.$t('button.grant')],
          type: 'warning',
        })
      }
    },
    clickGrantUser(row) {
      this.data.userIds = [row.userId]
      this.getUserGrantRole(row.userId)
      this.dialog.visible.grant = true
    },
    clickResetPassword(row) {
      this.resetPassword({ userId: row.userId })
    },
    clickLockUser(row) {
      this.updateUserLockState({
        userId: row.userId,
        userStatus: row.userStatus === '0' ? '1' : '0',
      })
    },
    clickUpdateUser(row) {
      this.dialog.form.update = {
        userId: row.userId,
        userAccount: row.userAccount,
        userPassword: row.userPassword,
        userType: row.userType,
        userStatus: row.userStatus,
        userStatusText: row.userStatusText,
        accountStatus: row.accountStatus,
        accountStatusText: row.accountStatusText,
        passwordStatus: row.passwordStatus,
        passwordStatusText: row.passwordStatusText,
        passwordUpdateDate: row.passwordUpdateDate,
        userDescription: row.userDescription,
        startValidDate: row.startValidDate,
        endValidDate: row.endValidDate,
        activeCyclic: [row.activeCyclicStartTime, row.activeCyclicEndTime],
      }
      this.dialog.visible.update = true
    },
    clickDeleteUser(row) {
      this.deleteUser(row.userId)
    },
    clickSubmitAddUser(obj) {
      obj.activeCyclic = obj.activeCyclic || ['', '']
      this.addUser({
        userAccount: obj.account,
        userDescription: obj.description,
        startValidDate: obj.startTime,
        endValidDate: obj.endTime,
        activeCyclicStartTime: obj.activeCyclic[0],
        activeCyclicEndTime: obj.activeCyclic[1],
      })
    },
    clickSubmitUpdateUser(obj) {
      obj.activeCyclic = obj.activeCyclic || ['', '']
      this.updateUser({
        userId: this.dialog.form.update.userId,
        userStatus: this.dialog.form.update.userStatus,
        accountStatus: this.dialog.form.update.accountStatus,
        userDescription: obj.description,
        startValidDate: obj.startTime,
        endValidDate: obj.endTime,
        activeCyclicStartTime: obj.activeCyclic[0],
        activeCyclicEndTime: obj.activeCyclic[1],
      })
    },
    clickSubmitGrant(roleIds) {
      this.updateUserGrantRole({
        users: [...this.data.userIds],
        roles: roleIds,
      })
    },
    clickHighQueryUser() {
      this.search.high = !this.search.high
      this.clickResetQueryUserForm()
    },
    clickResetQueryUserForm() {
      this.clearHighQueryForm()
      this.changeQueryUserTable()
    },
    clickShrinkHighQuery() {
      this.clearHighQueryForm()
      this.search.high = false
      this.changeQueryUserTable()
    },
    dblclickUserDisplayDetail(row) {
      this.getUserDetail(row.userId)
      this.dialog.visible.detail = true
    },
    changeQueryUserTable() {
      this.data.debounce()
    },
    clearHighQueryForm() {
      this.search.fuzzyField = ''
      this.search.query.form.model = {
        userAccount: '',
        userStatus: '',
        accountStatus: '',
        passwordStatus: '',
        userDescription: '',
      }
    },
    userTableRowChange(row) {
      this.pagination.currentRow = row
    },
    userTableSelectsChange(select) {
      this.data.selected = select
    },
    userTableSizeChange(size) {
      this.pagination.pageSize = size
      this.getUserTableData()
    },
    judgeRowSelected(row) {
      return row.systemDefault !== '1'
    },
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        if (this.search.high) {
          this.getUserTableData({
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            userAccount: this.search.query.form.model.userAccount,
            userStatus: this.search.query.form.model.userStatus,
            accountStatus: this.search.query.form.model.accountStatus,
            pdStatus: this.search.query.form.model.passwordStatus,
            userDescription: this.search.query.form.model.userDescription,
          })
        } else {
          this.getUserTableData()
        }
      }, 500)
    },
    userTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getUserTableData()
    },
    getUserTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        fuzzyField: this.search.fuzzyField,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryUserTableData(params).then((res) => {
        if (res) {
          this.data.table = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.data.loading = false
        this.pagination.visible = true
        res.rows.map((row) => {
          if (row.userId === this.$store.getters.userID) {
            this.$refs.userTable.setCurrentRow(row)
          }
        })
      })
    },
    addUser(form) {
      addUserData(form).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.getUserTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    deleteUser(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteUserData(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                if (ids.indexOf(this.$store.getters.userID) > -1) {
                  this.$store.dispatch('user/logout')
                } else {
                  const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                  if (idArray.length === this.data.table.length) {
                    this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                  }
                }
                this.getUserTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    updateUser(form) {
      updateUserData(form).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.changeQueryUserTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    getUserDetail(userId) {
      queryUserDetailData(userId).then((res) => {
        this.dialog.form.detail = res
      })
    },
    updateUserLockState(obj) {
      updateUserLockStateData(obj).then((res) => {
        if (res) {
          if (obj.userStatus === '1') {
            prompt({
              i18nCode: 'tip.operate.success',
              i18nParam: [this.$t('button.lock')],
              type: 'success',
            })
          }
          if (obj.userStatus === '0') {
            prompt({
              i18nCode: 'tip.operate.success',
              i18nParam: [this.$t('button.unlock')],
              type: 'success',
            })
          }
          this.changeQueryUserTable()
        } else {
          if (obj.userStatus === '1') {
            prompt({
              i18nCode: 'tip.operate.error',
              i18nParam: [this.$t('button.lock')],
              type: 'error',
            })
          }
          if (obj.userStatus === '0') {
            prompt({
              i18nCode: 'tip.operate.error',
              i18nParam: [this.$t('button.unlock')],
              type: 'error',
            })
          }
        }
      })
    },
    resetPassword(userId) {
      resetPasswordData(userId).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.operate.success',
              i18nParam: [this.$t('button.reset.default')],
              type: 'success',
            },
            () => {
              this.changeQueryUserTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.operate.error',
            i18nParam: [this.$t('button.reset.default')],
            type: 'error',
          })
        }
      })
    },
    updateUserGrantRole(obj) {
      updateUserGrantRoleData(obj).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.operate.success',
            i18nParam: [this.$t('button.grant')],
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.operate.error',
            i18nParam: [this.$t('button.grant')],
            type: 'error',
          })
        }
      })
    },
    getUserGrantRole(userId) {
      queryUserGrantRoleData(userId).then((res) => {
        this.data.roleIds = res.split(',')
      })
    },
  },
}
</script>
