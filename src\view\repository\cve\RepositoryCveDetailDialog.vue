<!--
 * @Description: CVE漏洞库 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog">
    <section>
      <el-form :model="form.model" label-width="120px">
        <el-row>
          <el-col v-for="(item, index) in option.columnOption" :key="index" :span="24">
            <el-form-item :prop="item.key" :label="item.label">
              {{ form.model[item.key] }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    form: {
      required: true,
      type: Object,
    },
    width: {
      type: String,
      default: '1000',
    },
    actions: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      option: {
        columnOption: [
          { key: 'name', label: this.$t('repository.cve.table.name') },
          { key: 'status', label: this.$t('repository.cve.table.status') },
          { key: 'phase', label: this.$t('repository.cve.table.phase') },
          { key: 'description', label: this.$t('repository.cve.table.description') },
          { key: 'references', label: this.$t('repository.cve.table.references') },
          { key: 'votes', label: this.$t('repository.cve.table.votes') },
          { key: 'comments', label: this.$t('repository.cve.table.comments') },
        ],
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
  },
}
</script>
