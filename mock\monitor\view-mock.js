const Mock = require('mockjs')
const { TableMock } = require('../util')

const basicInfo = {
  monitorName: '@NAME',
  'monitorTypeName|1': ['防火墙', 'MySQL', '联想网御入侵检测系统'],
  edName: '@IP',
  edIp: '@IP',
}

const cpuInfo = () => {
  return Mock.mock({
    axis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    data: [
      { name: 'CPU1', 'value|7': ['@NATURAL(400, 1200)'] },
      { name: 'CPU2', 'value|7': ['@NATURAL(400, 1200)'] },
    ],
  })
}

const memoryInfo = () => {
  return Mock.mock({
    axis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    'data|7': ['@NATURAL(400, 1200)'],
  })
}

const diskInfo = () => {
  return Mock.mock({
    axis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    data: [
      { name: '磁盘1', 'value|7': ['@NATURAL(400, 1200)'] },
      { name: '磁盘2', 'value|7': ['@NATURAL(400, 1200)'] },
    ],
  })
}

const currentCpu = [
  { cpuId: '4', usage: 1 },
  { cpuId: '5', usage: 8 },
]

const currentMemory = [
  {
    allMemory: '7982.39',
    restMemory: '5135.44',
    usage: 35,
  },
]

const currentDisk = [
  { allDisk: '291.00', diskName: '/', restDisk: '218.00', usage: 0 },
  { allDisk: '3.00', diskName: '/dev/shm', restDisk: '3.00', usage: 0 },
  { allDisk: '4.00', diskName: '/mnt/cdrom', restDisk: '0.00', usage: 0 },
]

const performanceTable = new TableMock({
  total: 10,
  template: {
    perfId: '@ID',
    perfName: '@CNAME',
    'currentStatus|1': ['0', '1'],
    'perfStatus|1': ['0', '1'],
    'perfClassName|1': ['应用服务性能', '数据库性能', '网络设备性能', '主机性能'],
    edName: '@IP',
    domaName: '@CNAME',
    value: '@Number',
    enterDate: '@DATETIME',
    updateDate: '@DATETIME',
    perfModule: '@TITLE',
    perfSolution: '@DESC',
  },
})

const faultTable = new TableMock({
  total: 10,
  template: {
    faultId: '@ID',
    faultName: '@CNAME',
    'currentStatus|1': ['0', '1'],
    'faultStatus|1': ['0', '1', '2', '3'],
    'faultClassName|1': ['应用服务性能', '数据库性能', '网络设备性能', '主机性能'],
    edName: '@IP',
    domaName: '@CNAME',
    'faultLevel|1': ['1', '2', '3'],
    enterDate: '@DATETIME',
    updateDate: '@DATETIME',
    faultModule: '@TITLE',
    faultSolution: '@DESC',
  },
})

module.exports = [
  {
    url: '/monitormanagement/basic',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: basicInfo,
      }
    },
  },
  {
    url: '/monitormanagement/getCpuStatic',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: cpuInfo,
      }
    },
  },
  {
    url: '/monitormanagement/getMemoryStatic',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: memoryInfo,
      }
    },
  },
  {
    url: '/monitormanagement/getDiskStatic',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: diskInfo,
      }
    },
  },
  {
    url: '/monitormanagement/perf/currentCpu',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: currentCpu,
      }
    },
  },
  {
    url: '/monitormanagement/perf/currentMemory',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: currentMemory,
      }
    },
  },
  {
    url: '/monitormanagement/perf/currentDisk',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: currentDisk,
      }
    },
  },
  {
    url: '/monitormanagement/queryFaultEvents',
    type: 'get',
    response: (option) => {
      faultTable.query(option)
      return {
        code: 200,
        data: faultTable.getMockData(),
      }
    },
  },
  {
    url: '/monitormanagement/queryPerformanceEvents',
    type: 'get',
    response: (option) => {
      performanceTable.query(option)
      return {
        code: 200,
        data: performanceTable.getMockData(),
      }
    },
  },
]
