const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    id: '@ID',
    alarmName: '@NAME',
    alarmRole: '@CNAME',
    enterDate: '@DATETIME',
    'isIgnore|1': [0, 1],
    alarmDesc: '@TITLE',
  },
})

module.exports = [
  {
    url: '/systemalarm/findAllAlar',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/systemalarm/ignoreSystemAlarm',
    type: 'put',
    response: (option) => {
      tableData.updateColumn(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
]
