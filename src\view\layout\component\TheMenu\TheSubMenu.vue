<!--
 * @Description: 登录首页 - 左侧菜单 - 递归菜单
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <fragment v-if="Object.keys(menuData).length > 0">
    <template v-for="menu in menuData" :index="menu">
      <el-menu-item v-if="menu.children === null || menu.children.length === 0" :key="menu.menuId" :index="menu.menuId" @click="clickMenu(menu)">
        <i v-if="menu.menuIcon" :class="menu.menuIcon"></i>
        <span slot="title">{{ menu.menuName }}</span>
      </el-menu-item>

      <el-submenu v-else :key="menu.menuId" :index="menu.menuId">
        <template slot="title">
          <i v-if="menu.menuIcon" :class="menu.menuIcon"></i>
          <span slot="title">{{ menu.menuName }}</span>
        </template>
        <sub-menu :menu-data="menu.children" @on-menu="clickMenu"></sub-menu>
      </el-submenu>
    </template>
  </fragment>
</template>

<script>
export default {
  name: 'SubMenu',
  props: {
    menuData: {
      required: true,
      type: Array,
    },
  },
  methods: {
    clickMenu(menu) {
      this.$emit('on-menu', menu)
    },
  },
}
</script>

<style lang="scss" scoped>
html[data-theme='light'] {
  i.soc-icon-point {
    display: none;
  }
}
</style>
