const { TableMock, createMockTable } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    patternId: '@ID',
    pattern: '@TITLE',
    patternValue: '@NAME',
    patternName: '@NAME',
    patternKey: 'ip1',
    'status|1': ['0', '1'],
    devTypeId: '11',
    devTypeName: '@NAME',
    message:
      '<14>Oct 21 18:47:16 IDS evLog_daemon: <event evttype="ATTACK" evtid="" level="0" sensorid="" ethid=""> <evtname>OTHER Jenkins-Xstream常规类路径反序列化漏洞(CVE-2016-0792)</evtname> <sip>**************</sip> <dip>*************</dip> <sport>10</sport> <dport>10</dport> <repeat>1</repeat> <start_time>2020-10-21 18:47:13</start_time> <end_time>2020-10-21 18:47:13</end_time> <policy>02421,100000110111000000000000,00000110</policy> </event>\r\n',
    points: "[{id: 123, key: '11', value: '22', start: 0, end: 6}]",
  },
})

// 设备类型
const devType = createMockTable(
  {
    label: '@name',
    value: '@id',
  },
  10
)

// 多元组
const multiGroup = [
  { value: 'code', label: '特征值' },
  { value: 'level', label: '事件等级' },
  { value: 'category', label: '事件类别' },
]

// 事件类型
const eventType = createMockTable(
  {
    label: '@name',
    value: '@id',
  },
  10
)

module.exports = [
  {
    url: '/customPattern/queryList',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/customPattern/detail/[A-Za-z0-9]',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: tableData.detail(option, 'userId'),
      }
    },
  },
  {
    url: '/customPattern/combo/devTypes',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: devType,
      }
    },
  },
  {
    url: '/customPattern/combo/keys',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: multiGroup,
      }
    },
  },
  {
    url: '/customPattern/code-alarm/alarm-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: eventType,
      }
    },
  },
  {
    url: '/customPattern/addPattern',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/customPattern/status',
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: `/customPattern/delPattern/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'patternId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 生成解析表达式
  {
    url: '/customPattern/generate',
    type: 'post',
    response: (option) => {
      return {
        code: 200,
        data: 'rrrrrrrrrrrr',
      }
    },
  },
  // 查询所选关键字对应事件类型
  {
    url: '/customPattern/queryKeywordEventType',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: '22',
      }
    },
  },
]
