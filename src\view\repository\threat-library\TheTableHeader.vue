<!--
 * @Description: 威胁情报库 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <section class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model="filterCondition.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('repository.threatLibrary.fuzzyQuery')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model="filterCondition.threatItem"
                clearable
                :placeholder="$t('repository.threatLibrary.table.threatItem')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.threatType"
                clearable
                :placeholder="$t('repository.threatLibrary.table.threatType')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in typeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <el-date-picker
                v-model="filterCondition.lastTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('repository.threatLibrary.table.lastStartTime')"
                :end-placeholder="$t('repository.threatLibrary.table.lastEndTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select
                v-model="filterCondition.threatLevel"
                clearable
                :placeholder="$t('repository.threatLibrary.table.threatLevel')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col align="right" :offset="15" :span="4">
              <el-button @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </section>
</template>

<script>
import { debounce } from '@util/effect'

export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      debounce: null,
      typeOption: [
        {
          label: 'ip',
          value: 'ip',
        },
        {
          label: 'domain',
          value: 'domain',
        },
        {
          label: 'url',
          value: 'url',
        },
        {
          label: 'mail',
          value: 'mail',
        },
        {
          label: 'hash',
          value: 'hash',
        },
      ],
      levelOption: [
        {
          label: '严重',
          value: '0',
        },
        {
          label: '高级',
          value: '1',
        },
        {
          label: '中级',
          value: '2',
        },
        {
          label: '低级',
          value: '3',
        },
        {
          label: '一般',
          value: '4',
        },
      ],
    }
  },
  watch: {
    condition: function(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition: function(newCondition) {
      this.$emit('update: condition', newCondition)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.fuzzyField = ''
      this.filterCondition.threatItem = ''
      this.filterCondition.threatType = ''
      this.filterCondition.threatLevel = ''
      this.filterCondition.lastTime = []
      this.changeQueryCondition()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
  },
}
</script>

<style scoped></style>
