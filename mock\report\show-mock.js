const { createMockTable } = require('../util')

const assetType = createMockTable(
  {
    'value|1': ['10', '11', '12', '13', '14'],
    'label|1': ['IDS/IPS', '操作系统', '网络设备', '应用系统', '流量监控'],
    'children|1-10': [
      {
        'type|1': ['10', '11', '12', '13', '14'],
        label: '@CWORD(2,6)',
        value: '@ID',
        children: null,
      },
    ],
  },
  10
)

const reportLocation = '/ureport/preview?_u=mysql:assetinfo.echarts.ureport.xml&startDate=2022-02-07 00:00:00&endDate=2022-02-07 23:59:59'

module.exports = [
  {
    url: '/reportshow/combo/types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: assetType,
      }
    },
  },
  {
    url: '/reportshow/queryReport',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: reportLocation,
      }
    },
  },
]
