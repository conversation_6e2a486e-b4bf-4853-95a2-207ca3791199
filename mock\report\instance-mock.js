const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    taskId: '@ID',
    taskName: '@WORD',
    'taskType|1': ['每天', '每周', '每月', '每年'],
    'taskAmount|0-100': 100,
    'taskInstance|1-10': [
      {
        instanceId: '@GUID',
        instanceTime: '@DATE',
        'instanceUrl|1': [
          'https://element.eleme.cn/#/zh-CN/component/form',
          'https://cn.vuejs.org/v2/guide/',
          'https://echarts.apache.org/zh/index.html',
        ],
      },
    ],
  },
})

module.exports = [
  {
    url: '/reportmanagement/instance',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/reportmanagement/instance/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
]
