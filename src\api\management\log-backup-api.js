import request from '@util/request'

export function queryBackupLogsData(obj) {
  return request({
    url: '/auditbackup/backups',
    method: 'get',
    params: obj || {},
  })
}

// 查询备份任务
export function queryBackupJobData() {
  return request({
    url: '/auditbackup/job',
    method: 'get',
  })
}

// 修改备份任务
export function updateBackupJobData(obj) {
  return request({
    url: '/auditbackup/job',
    method: 'put',
    data: obj || {},
  })
}

// 删除备份日志
export function deleteBackupLogData(id) {
  return request({
    url: `/auditbackup/backup/${id}`,
    method: 'delete',
  })
}

// 下载备份日志
export function downloadBackupLogData(obj) {
  return request(
    {
      url: '/auditbackup/backup/download',
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}
