const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    alarmName: '@NAME',
    alarmTimeout: '@CNAME',
    countThreshold: '@CNAME',
    eventFrequency: '@CNAME',
    matchCriteriaList: ['2', '4', '5', '14'],
  },
})
const system = [{ label: 'syslog', value: '1' }]
const rules = [
  {
    pageSize: null,
    pageNum: null,
    value: '2',
    label: '事件等级',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '4',
    label: '事件类型',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '5',
    label: '设备种类',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '6',
    label: '设备类型',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '7',
    label: '日志编号',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '8',
    label: '协议',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '9',
    label: '发生源IP',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '11',
    label: '代理ID',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '14',
    label: '源IP',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '15',
    label: '源端口',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '19',
    label: '目的IP',
  },
  {
    pageSize: null,
    pageNum: null,
    value: '20',
    label: '目的端口',
  },
]
module.exports = [
  {
    url: '/strategy/aggregated/strategies',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/strategy/aggregated/combo/forward',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: system,
      }
    },
  },
  {
    url: '/strategy/aggregated/combo/rules',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: rules,
      }
    },
  },
  {
    url: '/strategy/aggregated/strategy',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
]
