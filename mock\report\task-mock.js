const { TableMock, createMockTable } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    taskId: '@ID',
    taskName: '@CNAME',
    taskInstanceName: '@CTITLE',
    'taskType|1': ['day', 'week', 'month', 'year'],
    taskTypeText() {
      switch (this.taskType) {
        case 'day':
          return '每天'
        case 'week':
          return '每周'
        case 'month':
          return '每月'
        case 'year':
          return '每年'
        default:
          break
      }
    },
    taskStartDate: '@DATE',
    taskStartTime: '@TIME',
    'taskStartValue|1-31': 1,
    taskTimeText() {
      return this.taskStartValue + '日'
    },
    'taskSendType|1': ['pdf', 'word', 'excel'],
    updateDate: '@DATE',
    'taskStatus|1': true,
    taskDescription: '@TITLE',
    taskRecipient: '@EMAIL',
  },
})

const instanceOption = createMockTable(
  {
    label: '@CNAME',
    value: '@ID',
  },
  15
)

module.exports = [
  {
    url: '/reporttask/catalogue/instances',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: instanceOption,
      }
    },
  },
  {
    url: '/reporttask/catalogue',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/reporttask/catalogue/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'taskId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/reporttask/catalogue',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'taskId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/reporttask/catalogue/status',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'taskId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/reporttask/catalogue/[A-Za-z0-9]',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: tableData.detail(option, 'taskId'),
      }
    },
  },
  {
    url: '/reporttask/catalogue',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
]
