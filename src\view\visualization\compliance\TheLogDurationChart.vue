<!--
 * @Description: 日志存储时长图表
 * @Version: 3.5
 * @Author:
 * @Date: 2023/03/05
 * @Editor:
 * @EditDate: 2023/03/05
-->
<template>
  <el-container class="widget" :style="{ height: height + 'px' }">
    <el-header class="widget-header" height="30px">
      <el-col :span="20" class="widget-header-title">
        {{ convert === true ? $t('visualization.compliance.title.devStorageDurationTop') : $t('visualization.compliance.title.devStorageDuration') }}
      </el-col>
      <el-col align="right">
        <el-link type="primary" :underline="false" @click="clickConvert">
          ･･･
        </el-link>
      </el-col>
    </el-header>
    <el-main v-if="convert === true" class="widget-main">
      <bar-chart ref="barChartDom" :option="option"></bar-chart>
    </el-main>
    <section v-else style="height: 100%;">
      <el-table
        v-loading="table.loading"
        :data="table.data"
        height="98%"
        :row-style="{ height: '0' }"
        :cell-style="{ padding: '5px 0' }"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
      >
        <el-table-column
          align="left"
          prop="label"
          :label="$t(`visualization.compliance.label.logSourceName`)"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column align="right" prop="sec" :label="$t(`visualization.compliance.label.storageDuration`)" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.sec }}
          </template>
        </el-table-column>
      </el-table>
    </section>
  </el-container>
</template>

<script>
import BarChart from '@comp/ChartFactory/forecast/BarChart'
import { queryLogDurationChartData, queryLogDurationTable } from '@api/visualization/compliance-api'

export default {
  components: {
    BarChart,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      chartData: [],
      convert: true,
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      },
    }
  },
  computed: {
    option() {
      return {
        type: 'bar',
        axis: 'y',
        data: this.chartData,
        axisDefine: {
          xAxis: {
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        },
      }
    },
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getLogDurationChart()
      this.getLogDurationTable()
      // this.$refs.barChartDom.resize();
    },
    getLogDurationChart() {
      queryLogDurationChartData().then((res) => {
        this.chartData = res
      })
    },
    getLogDurationTable(
      param = {
        pageSize: this.pagination.pageSize,
      }
    ) {
      this.table.loading = true
      queryLogDurationTable(param).then((res) => {
        this.table.data = res
        this.table.loading = false
      })
    },
    clickConvert() {
      this.convert = !this.convert
    },
  },
}
</script>
<style lang="scss" scoped>
.widget {
  &-header {
    .el-link:hover {
      color: #06699c;
    }
    &-title {
      padding-left: 2px !important;
    }
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10px;
  }
  ::v-deep .el-table__header-wrapper {
    width: 99%;
  }
}
</style>
