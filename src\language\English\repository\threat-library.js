export default {
  threatLibrary: {
    header: 'Threat Intelligence Library',
    type: {
      ip: 'Threat Intelligence IP',
      domain: 'Threat Intelligence Domain',
      url: 'Threat Intelligence URL',
      email: 'Threat Intelligence Email',
      hash: 'Threat Intelligence Hash',
    },
    table: {
      threatItem: 'Threat Item',
      threatIp: 'Threat IP',
      threatType: 'Threat Type',
      frequency: 'Occurrence Count',
      createTime: 'Creation Time',
      firstTime: 'First Time',
      firstStartTime: 'First Start Time',
      firstEndTime: 'First End Time',
      lastTime: 'Last Time',
      lastStartTime: 'Last Start Time',
      lastEndTime: 'Last End Time',
      record: 'Record',
      action: 'Action Tag',
      objectType: 'Object Type',
      historyUrl: 'Historical URL',
      malware: 'Malware Tag',
      country: 'Country',
      province: 'Province',
      city: 'City',
      organization: 'Organization',
      operator: 'Operator',
      code: 'Code',
      longitude: 'Longitude',
      latitude: 'Latitude',
      sha1: 'SHA1 Value',
      sha265: 'SHA256 Value',
      hash: 'MD5 HASH Value',
      hashType: 'Hash Type',
      source: 'Source',
      threatLevel: 'Threat Level',
      activeTime: 'Activation Time',
      confidence: 'Confidence',
      subscriptionChannel: 'Subscription Channel',
      attackProtocol: 'Attack Protocol',
    },
    tabPanel: {
      info: 'Information',
      confidence: 'Confidence',
    },
    fuzzyQuery: 'Threat Item/Threat Type',
  },
}
