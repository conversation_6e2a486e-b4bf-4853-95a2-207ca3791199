<!--
 * @Description: 日志审计 - 转发弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023-05-29
 * @Editor:
 * @EditDate: 2023-05-29
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.forward', [$t('management.logAudit.name')])"
    width="40%"
    height="500px"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <section class="table-header-button" style="text-align: right;">
      <el-button @click="addRow">
        {{ $t('button.insert') }}
      </el-button>
    </section>
    <el-divider></el-divider>
    <el-form ref="forwardDom" :model="form" label-width="100px">
      <section v-if="form.model.length > 0">
        <div v-for="(item, index) in form.model" :key="index" class="item-row">
          <el-col :span="15">
            <el-form-item :prop="'model.' + index + '.ip'" :rules="rules.ip" :label="$t('management.logAudit.label.forwardAddress') + `${index + 1}`">
              <el-input v-model="item.ip" :placeholder="$t('management.logAudit.placeholder.ip')" maxlength="256"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item :prop="'model.' + index + '.port'" :rules="rules.port" label-width="0">
              <el-input v-model="item.port" :placeholder="$t('management.logAudit.placeholder.port')" maxlength="10"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-button class="del-button" @click="deleteRow(item)">
              {{ $t('button.delete') }}
            </el-button>
          </el-col>
        </div>
      </section>
      <section v-else class="no-data">
        {{ $t('management.logAudit.label.noData') }}
      </section>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateIp, validatePort } from '@util/validate'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    form: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value !== '') {
        if (!validateIp(value)) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('validate.empty')))
      }
    }
    const validatorPort = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validatePort(value)) {
        callback(new Error(this.$t('validate.port.incorrect')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: this.visible,
      rules: {
        ip: [
          {
            required: true,
            trigger: 'blur',
            validator: validatorIp,
          },
        ],
        port: [
          {
            required: true,
            trigger: 'blur',
            validator: validatorPort,
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      console.log('visible')
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    addRow() {
      const itemObj = {
        id: Math.random(),
        ip: '',
        port: '',
      }
      this.form.model.push(itemObj)
    },
    deleteRow(row) {
      this.form.model.map((item, index) => {
        if (item.id === row.id) {
          this.form.model.splice(index, 1)
        }
      })
    },
    clickSubmit() {
      if (!this.validateRepeatRecord()) {
        prompt({
          i18nCode: 'tip.update.ipAddress',
          type: 'warning',
        })
        return false
      }
      this.$refs.forwardDom.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.form.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
    validateRepeatRecord(model) {
      const newArr = []
      for (const t of this.form.model) {
        if (newArr.find((c) => c.ip === t.ip && c.port === t.port)) {
          continue
        }
        newArr.push(t)
      }
      if (this.form.model.length === newArr.length) {
        return true
      } else {
        return false
      }
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__body {
  padding-top: 8px;
  padding-bottom: 8px;
  .el-divider--horizontal {
    margin: 8px 0;
    height: 1px;
  }
  .item-row {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 40px;
    min-height: 40px;
    margin: 8px auto;
    .del-button {
      border: none;
      box-shadow: none;
      @include theme('color', error-text-color);
    }
    .del-button:hover {
      @include theme('color', error-text-color);
      font-weight: bold;
      @include theme('background-color', tag-bg-color);
    }
  }
  .no-data {
    padding: 5px;
    text-align: center;
  }
}
</style>
