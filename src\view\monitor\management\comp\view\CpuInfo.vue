<!--
 * @Description: 监控器展示 - CPU信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-11
 * @Editor:
 * @EditDate: 2021-08-11
-->
<template>
  <line-chart ref="lineChartDom" height="300px" proto :line-data="cpuOption"></line-chart>
</template>

<script>
import LineChart from '@comp/ChartFactory/common/LineChart.vue'
import { queryMonitorCpu } from '@api/monitor/view-api'

export default {
  components: {
    LineChart,
  },
  props: {
    params: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      cpuOption: {},
    }
  },
  computed: {
    condition() {
      return {
        edId: this.params.edId,
        monitorId: this.params.monitorId,
        startTime: this.params.startTime,
        endTime: this.params.endTime,
      }
    },
  },
  watch: {
    params: {
      handler() {
        this.getCpuInfo()
      },
      deep: true,
    },
  },
  mounted() {
    this.getCpuInfo()
  },
  methods: {
    getCpuInfo(params = this.condition) {
      queryMonitorCpu(params).then((res) => {
        this.cpuOption = this.getLineOption(res)
      })
    },
    getLineOption(data) {
      const legend = this.chartLegendConfig(data)
      const series = this.chartSeriesConfig(data)
      return {
        backgroundColor: 'transparent',
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          subtext: 'CPU使用率',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          type: 'scroll',
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: legend,
        },
        xAxis: {
          type: 'category',
          data: data.axis,
        },
        yAxis: {
          type: 'value',
        },
        series: series,
      }
    },
    chartSeriesConfig(data) {
      const series = []
      const seriesItem = (name, data) => {
        return {
          name: 'CPU' + name,
          type: 'line',
          data: data,
        }
      }
      data.data.forEach((item, index) => {
        series.push(seriesItem(item.name, item.value))
      })
      return series
    },
    chartLegendConfig(data) {
      const legend = []
      data.data.forEach((item, index) => {
        legend.push('CPU' + item.name)
      })
      return legend
    },
  },
}
</script>
