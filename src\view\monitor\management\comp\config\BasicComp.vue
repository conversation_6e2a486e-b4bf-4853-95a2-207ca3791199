<!--
 * @Description: 监控器配置 - 基本信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-29
 * @Editor:
 * @EditDate: 2021-07-29
-->
<template>
  <el-form ref="basicForm" :model="model" :rules="rules" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="monitorName" :label="$t('monitor.management.props.monitorName')">
          <el-input v-model.trim="model.monitorName" maxlength="128"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="status" :label="$t('monitor.management.props.status')">
          <el-switch v-model="model.monitorEnabled" active-value="1" inactive-value="0"></el-switch>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item prop="monitorType" :label="$t('monitor.management.props.monitorType')">
          <el-cascader
            ref="cascader"
            v-model="model.monitorTypeValue"
            filterable
            :options="options.monitorTypeOption"
            :props="{ expandTrigger: 'hover' }"
            @change="changeMonitorType"
          ></el-cascader>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="pollDate" :label="$t('monitor.management.props.pollDate')">
          <el-input v-model="model.pollDate" oninput="value=value.replace(/[^0-9]/g,'')" maxlength="2"></el-input>
          &nbsp;分
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item prop="assetId" :label="$t('monitor.management.props.edName')">
          <el-cascader
            v-model="model.assetId"
            :options="options.assetOption"
            :placeholder="$t('monitor.management.props.edName')"
            collapse-tags
            :show-all-levels="false"
            :props="{ multiple: mulAssetFlag }"
          ></el-cascader>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="agentId" :label="$t('monitor.management.props.agentId')">
          <el-select v-model="model.agentId" clearable :placeholder="$t('monitor.management.props.agentId')">
            <el-option
              v-for="item in options.agentOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.type === '0'"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { isEmpty } from '@util/common'
import { queryMonitorType, queryAsset, queryAgentOptionData } from '@api/monitor/management-api'

export default {
  props: {
    model: {
      required: true,
      type: Object,
    },
    func: {
      type: String,
      default: 'add',
    },
  },
  data() {
    const validatorPollDate = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (value < 3 || value > 60) {
        callback(new Error(this.$t('validate.monitor.pollDate')))
      } else {
        callback()
      }
    }
    const validatorAsset = (rule, value, callback) => {
      if (isEmpty(value) || value.length === 0) {
        callback(new Error(this.$t('validate.empty')))
      } else {
        callback()
      }
    }
    return {
      mulAssetFlag: true,
      options: {
        monitorTypeOption: [],
        agentOption: [],
        assetOption: [],
      },
      rules: {
        monitorName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        monitorType: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'change',
          },
        ],
        pollDate: [
          {
            required: true,
            validator: validatorPollDate,
            trigger: 'blur',
          },
        ],
        assetId: [
          {
            required: true,
            validator: validatorAsset,
            trigger: 'change',
          },
        ],
        agentId: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'change',
          },
        ],
      },
    }
  },
  mounted() {
    this.initOptions()
    if (this.func === 'upd') {
      this.mulAssetFlag = false
    }
  },
  methods: {
    validateForm() {
      let validate = false
      this.$refs.basicForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    resetForm() {
      this.$refs.basicForm.resetFields()
    },
    changeMonitorType(value) {
      if (!isEmpty(value)) {
        this.queryMonitorAsset(value[1])
        this.$emit('on-change-type', value[1])
      }
      this.model.assetId = ''
    },
    initOptions() {
      queryMonitorType().then((res) => {
        this.options.monitorTypeOption = res
      })
      queryAgentOptionData().then((res) => {
        this.options.agentOption = res
      })
      this.queryMonitorAsset(this.model.monitorType)
    },
    queryMonitorAsset(monitorType) {
      if (this.func === 'add') {
        this.queryNoMonitoredAsset(monitorType)
      } else {
        this.queryNoMonitoredAsset(monitorType, this.model.monitorId)
      }
    },
    queryNoMonitoredAsset(monitorType, monitorId) {
      queryAsset(monitorType, monitorId).then((res) => {
        this.options.assetOption = res
      })
    },
  },
}
</script>
