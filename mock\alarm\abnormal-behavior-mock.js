const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    id: '@ID',
    infoSystemName: '@CNAME',
    infoSystemIp: '@IP',
    'action|1': ['验证/认证失败', '主机探测'],
    'anomalyType|1': ['illegalAction', 'illegalIntruder'],
    role: '@IP',
    total: '@Number',
    'status|1': [0, 1],
    occurTime: '@DATETIME',
    updateTime: '@DATETIME',
    raw:
      '<14>Oct 21 18:47:17 IDS evLog_daemon: <event evttype="ATTACK" evtid="" level="2" sensorid="" ethid=""> <evtname>OTHER_4 Microsoft MSDE/SQL Server 2000 sa账号口令猜测(CVE-2000-1209)</evtname></event>',
    desc: '验证失败(************)信息系统在2021-12-28 22:23:04发生验证/认证失败异常行为',
  },
})

module.exports = [
  {
    url: '/infosystemanomaly/queryResults',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/infosystemanomaly/ignoreResults',
    type: 'put',
    response: (option) => {
      tableData.updateColumn(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
]
