<template>
  <div class="widget">
    <header class="widget-header">
      {{ $t('visualization.compliance.title.logStorageDuration') }}
    </header>
    <main class="widget-body">
      <div class="icon-box">
        <i class="el-icon-date"></i>
      </div>
      <div class="detail-box">
        <p class="detail-box-word">
          <span>{{ highBit }}</span>
          {{ lowBit }}
          <br />
          <b>{{ $t('visualization.compliance.label.storageDurationTotal', [storageDuration]) }}</b>
        </p>
      </div>
    </main>
  </div>
</template>

<script>
import { queryLogStorageDuration } from '@api/visualization/compliance-api'

export default {
  data() {
    return {
      storageDuration: '',
      highBit: '',
      lowBit: '',
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getLogStorageDuration()
    },
    conversion(day) {
      if (day < 30) {
        this.highBit = day + '天'
      } else if (day < 365) {
        const month = Math.floor(day / 30)
        const days = Math.round((day / 30 - month) * 30)
        if (days === 0) {
          this.highBit = month + '个月'
        } else {
          this.highBit = month + '个月'
          this.lowBit = days + '天'
        }
      } else {
        const year = Math.floor(day / 365)
        const month = Math.floor(((day / 365 - year) * 365) / 30)
        const days = Math.round((((day / 365 - year) * 365) / 30 - month) * 30)
        if (month === 0 && days === 0) {
          this.highBit = year + '年'
        } else if (days === 0) {
          this.highBit = year + '年'
          this.lowBit = '零' + month + '个月'
        } else {
          this.highBit = year + '年'
          this.lowBit = '零' + month + '个月' + days + '天'
        }
      }
    },
    getLogStorageDuration() {
      queryLogStorageDuration().then((res) => {
        if (res) {
          this.storageDuration = res.sec
          this.conversion(res.sec)
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.widget-body {
  .icon-box {
    background-color: #ffb136;
  }
  .detail-box-word {
    color: #ffb136;
    b {
      color: #000;
      @include theme('color', font-color);
    }
    /*color: #2ecc71;*/
  }
}
</style>
