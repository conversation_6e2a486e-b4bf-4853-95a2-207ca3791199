import request from '@util/request'

// API上传操作
export function uploadDemoTableData(obj) {
  return request(
    {
      url: `/demo/tabletree/upload`,
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

// API下载操作
export function downloadDemoTableData(ids) {
  return request(
    {
      url: `/demo/tabletree/download/${ids}`,
      method: 'get',
    },
    'download'
  )
}

// API增加操作
export function addDemoData(obj) {
  return request({
    url: '/demo/tabletree',
    method: 'post',
    data: obj || {},
  })
}

// API删除操作
export function deleteDemoData(ids) {
  return request({
    url: `/demo/tabletree/${ids}`,
    method: 'delete',
  })
}

// API更新操作
export function updateDemoData(obj) {
  return request({
    url: `/demo/tabletree`,
    method: 'put',
    data: obj || {},
  })
}

// API查询列表操作
export function queryDemoTableData(obj) {
  return request({
    url: '/demo/tabletree',
    method: 'get',
    params: obj || {},
  })
}
