<!--
 * @Description: 威胁情报库
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <section class="router-wrap-table">
    <table-header :condition.sync="query" @on-change="changeCondition"></table-header>
    <table-body
      :title-name="title"
      :loading="table.loading"
      :scroll="effect.scroll"
      :data="table.data"
      @on-detail="detailTable"
      @on-scroll="scrollTable"
    ></table-body>
    <table-footer :loading="effect.totalLoading" :total="table.total" :nomore="effect.nomore"></table-footer>
    <table-detail :visible.sync="dialog.visible" :title-name="title" :detail="table.detail"></table-detail>
  </section>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import TableDetail from './TheDetailDialog'
import { queryThreatInfoTableData, queryThreatInfoTotalData, queryThreatInfoDetailData } from '@api/repository/threat-library-api'
export default {
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    TableDetail,
  },
  data() {
    return {
      title: this.$t('repository.threatLibrary.header'),
      query: {
        senior: false,
        fuzzyField: '',
        threatItem: '',
        threatType: '',
        threatLevel: '',
        lastTime: [],
      },
      effect: {
        nomore: false,
        tableShow: true,
        scroll: true,
        totalLoading: false,
      },
      table: {
        loading: false,
        data: [],
        total: 0,
        detail: {},
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
      },
      dialog: {
        visible: false,
      },
    }
  },
  mounted() {
    this.getThreatInfoTable()
  },
  methods: {
    changeCondition() {
      this.effect.nomore = false
      this.table.data = []
      if (this.query.senior) {
        const seniorParam = this.handleSeniorQueryParams()
        this.getThreatInfoTable(seniorParam)
      } else {
        this.getThreatInfoTable()
      }
    },
    handleSeniorQueryParams(scrollParam) {
      let param = {
        pageSize: this.pagination.pageSize,
        threatItem: this.query.threatItem,
        category: this.query.threatType,
        threatLevel: this.query.threatLevel,
        lastTime: this.query.lastTime ? this.query.lastTime.toString() : '',
      }
      if (scrollParam) {
        param = Object.assign(param, scrollParam)
      }
      return param
    },
    scrollTable() {
      const param = {
        threatId: this.table.data.length > 0 ? this.table.data[this.table.data.length - 1]['threatId'] : '',
        timestamp: this.table.data.length > 0 ? this.table.data[this.table.data.length - 1]['timestamp'] : '',
      }
      if (this.query.senior) {
        const seniorParam = this.handleSeniorQueryParams(param)
        this.getThreatInfoTable(seniorParam, false)
      } else {
        this.getThreatInfoTable(
          Object.assign(param, {
            pageSize: this.pagination.pageSize,
            fuzzyField: this.query.fuzzyField,
          }),
          false
        )
      }
    },
    detailTable(row) {
      this.getThreatInfoDetail({
        threatId: row.threatId,
        timestamp: row.timestamp,
      })
      this.dialog.visible = true
    },
    getThreatInfoTable(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      },
      total = true
    ) {
      this.effect.scroll = true
      this.effect.loading = true
      if (total) {
        this.effect.totalLoading = true
      }
      queryThreatInfoTableData(param).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.table.data.push(...res)
          this.effect.scroll = true
          if (this.table.data.length > this.pagination.pageSize) {
            this.effect.nomore = true
          }
        } else {
          this.table.data.push(...res)
          this.effect.scroll = false
          this.table.nomore = false
        }
        this.effect.loading = false
        if (total) {
          this.getThreatInfoTotal(param)
        }
      })
    },
    getThreatInfoTotal(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.effect.totalLoading = true
      queryThreatInfoTotalData(param).then((res) => {
        this.table.total = res
        this.effect.totalLoading = false
      })
    },
    getThreatInfoDetail(obj) {
      queryThreatInfoDetailData(obj).then((res) => {
        this.table.detail = res
      })
    },
  },
}
</script>
