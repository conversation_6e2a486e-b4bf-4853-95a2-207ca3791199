<template>
  <div ref="originalLogTableDom" class="widget router-wrap-table">
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="query.show" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model.trim="query.form.type2Name"
                clearable
                :placeholder="$t('event.original.label.type2Name')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="query.form.name"
                clearable
                filterable
                :placeholder="$t('event.original.label.logName')"
                @change="changeQueryCondition()"
              >
                <el-option v-for="item in options.eventName" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-select v-model="query.form.level" clearable :placeholder="$t('event.original.label.level')" @change="changeQueryCondition">
                <el-option v-for="item in options.level" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-cascader
                v-model="query.form.logSource"
                filterable
                clearable
                :options="options.logSource"
                :placeholder="$t('event.original.label.srcDevice')"
                :props="{ expandTrigger: 'hover' }"
                @change="changeQueryCondition"
              ></el-cascader>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <range-picker
                v-model="query.form.fromIp"
                type="ip"
                :start-placeholder="$t('event.original.placeholder.fromStartIP')"
                :end-placeholder="$t('event.original.placeholder.fromEndIP')"
                @change="changeQueryCondition"
              ></range-picker>
            </el-col>
            <el-col :span="5">
              <el-date-picker
                v-model="query.form.date"
                type="date"
                :editable="false"
                :clearable="false"
                value-format="yyyy-MM-dd"
                :placeholder="$t('time.option.date')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
            <el-col :span="5">
              <el-time-picker
                v-model="query.form.timeRange"
                is-range
                :clearable="false"
                value-format="HH:mm:ss"
                format="HH:mm:ss"
                range-separator="~"
                :start-placeholder="$t('time.option.startTime')"
                :end-placeholder="$t('time.option.endTime')"
                @change="changeQueryCondition"
              ></el-time-picker>
            </el-col>
            <el-col align="right" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="clickResetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
    <section>
      <main class="table-body-main">
        <el-table
          v-loading="table.loading"
          v-el-table-scroll="scrollOriginalLogTable"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          :data="table.data"
          highlight-current-row
          tooltip-effect="light"
          border
          fit
          height="255"
        >
          <el-table-column width="50" type="index"></el-table-column>
          <template v-for="(item, key) in columns">
            <el-table-column :key="key" :prop="item" :label="$t(`asset.management.log.${item}`)" show-overflow-tooltip sortable>
              <template slot-scope="scope">
                <level-tag v-if="item === 'level' || item === 'levelName'" :level="scope.row[item]"></level-tag>
                <p v-else>
                  {{ scope.row[item] }}
                </p>
              </template>
            </el-table-column>
          </template>
        </el-table>
      </main>
    </section>
    <section class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="table.nomore">{{ $t('validate.data.nomore') }}</span>
      </section>
      <section class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span v-loading="table.totalLoading">{{ table.total }}</span>
      </section>
    </section>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import RangePicker from '@comp/RangePicker'
import LevelTag from '@comp/LevelTag'
import { debounce } from '@util/effect'
import { formatTime } from '@util/format'
import { isEmpty } from '@util/common'
import { queryOriginalLogTableData, queryOriginalLogTotalData, queryEventTypes, queryLogSources } from '@api/asset/management-api'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    LevelTag,
    RangePicker,
  },
  props: {
    height: {
      type: [Number, String],
      default: '300px',
    },
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      table: {
        loading: false,
        scroll: true,
        nomore: false,
        data: [],
        total: 0,
        totalLoading: false,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        currentRow: {},
      },
      columns: ['type2Name', 'eventName', 'level', 'deviceCategoryName', 'deviceTypeName', 'time', 'fromIp'],
      query: {
        show: true,
        fuzzyField: '',
        form: {
          type2Name: '',
          name: '',
          level: '',
          date: '',
          timeRange: ['00:00:00', '23:59:59'],
          logSource: '',
          fromIp: ['', ''],
        },
      },
      debounce: null,
      options: {
        level: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        eventName: [],
        logSource: [],
      },
    }
  },
  computed: {
    disableScroll() {
      return this.table.scroll
    },
    timeRange() {
      let range = []
      if (this.query.form.date !== '' && this.query.form.date !== null) {
        range = [`${this.query.form.date} ${this.query.form.timeRange[0]}`, `${this.query.form.date} ${this.query.form.timeRange[1]}`]
      }

      return range
    },
  },
  methods: {
    loadData() {
      this.initOption()
      this.initDebounceQuery()
      this.query.form = {
        type2Name: '',
        name: '',
        level: '',
        date: '',
        timeRange: ['00:00:00', '23:59:59'],
        logSource: '',
        fromIp: ['', ''],
      }
      this.query.form.logSource = [this.data.categoryID, this.data.typeId]
      this.query.form.fromIp = [this.data.ip]
      this.query.form.date = formatTime(new Date().getTime(), '{y}-{m}-{d}')
      this.changeQueryCondition()
    },
    initDebounceQuery() {
      this.debounce = debounce(() => {
        const params = this.handleQueryParams()
        this.getOriginalLogTable(params)
      }, 500)
    },
    changeQueryCondition() {
      this.table.data = []
      this.debounce()
    },
    scrollOriginalLogTable() {
      const params = this.handleQueryParams(true)
      this.getOriginalLogTable(params, false)
    },
    handleQueryParams(scroll = false) {
      this.table.nomore = false
      let params = {
        pageSize: this.pagination.pageSize,
      }
      if (scroll) {
        const lastRow = this.table.data[this.table.data.length - 1] || null
        if (lastRow) {
          params = Object.assign(params, {
            id: lastRow.id,
            timestamp: lastRow.timestamp,
          })
        }
      }
      if (this.query.show) {
        params = Object.assign(params, {
          type2Name: this.query.form.type2Name,
          eventName: this.query.form.name,
          level: this.query.form.level,
          timeRange: this.timeRange.toString(),
          fromIp: this.ipRange(this.query.form.fromIp),
          deviceType: this.query.form.logSource.toString(),
        })
      }
      return params
    },
    ipRange(ipArr) {
      let ip = ''
      ipArr = ipArr.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipArr.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    clickResetQuery() {
      this.table.data = []
      this.pagination.pageNum = 1
      this.query.form = {
        type2Name: '',
        name: '',
        level: '',
        date: '',
        timeRange: ['00:00:00', '23:59:59'],
        logSource: '',
        fromIp: ['', ''],
      }
      this.query.form.date = formatTime(new Date().getTime(), '{y}-{m}-{d}')
      this.changeQueryCondition()
    },
    getOriginalLogTable(
      param = {
        pageSize: this.pagination.pageSize,
      },
      total = true
    ) {
      this.table.scroll = true
      this.table.loading = true
      queryOriginalLogTableData(param).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.table.data.push(...res)
          this.table.scroll = true
          if (this.table.data.length > this.pagination.pageSize) {
            this.table.nomore = true
          }
        } else {
          this.table.data.push(...res)
          this.table.scroll = false
        }
        this.table.loading = false
        if (total) {
          this.getOriginalLogTotal(param)
        }
      })
    },
    getOriginalLogTotal(
      param = {
        pageSize: this.pagination.pageSize,
      }
    ) {
      this.table.totalLoading = true
      queryOriginalLogTotalData(param).then((res) => {
        this.table.totalLoading = false
        this.table.total = res
      })
    },
    initOption() {
      queryEventTypes().then((res) => {
        this.options.eventName = res
      })
      queryLogSources().then((res) => {
        this.options.logSource = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.widget {
  .table-header-extend {
    margin: 0;
    .el-row {
      padding: 5px 0;
      background-color: transparent;
    }
  }
}
.router-wrap-table .table-body-main {
  width: 100%;
}
</style>
