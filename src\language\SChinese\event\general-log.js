export default {
  generalLog: {
    title: '通用日志',
    total: '总计',
    label: {
      deviceName: '设备类型',
      insertTime: '接收时间',
      facilityName: '模块',
      severity: '级别',
      severityName: '级别',
      fromIp: '发生源IP',
      appName: '应用名称',
      procId: 'PROCID',
      msgId: 'MSGID',
      logTimestamp: '日志时间 ',
      hostName: '主机名称',
      structuredData: '结构数据',
      message: '日志原文',
      logMessage: '日志内容',
    },
    placeholder: {
      message: '日志原文',
      severity: '级别',
      deviceType: '日志源类型',
      facility: '日志模块',
      fromIp: '发生源IP',
      fromStartIp: '发生源起始IP',
      fromEndIp: '发生源终止IP',
      receiveDate: '接收日期',
    },
    group: {
      basic: '通用日志详情',
    },
    basic: {
      deviceName: '设备类型',
      insertTime: '接收时间',
      facilityName: '模块',
      severityName: '级别',
      fromIp: '发生源IP',
      appName: '应用名称',
      procId: 'PROCID',
      msgId: 'MSGID',
      logTimestamp: '日志时间 ',
      hostName: '主机名称',
      structuredData: '结构数据',
      message: '日志原文',
      logMessage: '日志内容',
    },
    parseRateDesc: '每千条日志解析规则平均失败耗时{0}ms',
  },
}
