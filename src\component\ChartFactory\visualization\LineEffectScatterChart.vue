<template>
  <div :id="id" ref="lineChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    lineData: {
      type: Object,
      default() {
        return {
          axis: [],
          lineData: [],
          scatterData: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    lineData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart(this.chart)
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.lineData) {
      if (data && Object.keys(data).length > 0) {
        this.chart = echarts.init(this.$refs.lineChart)
        this.drawChart(data)
      }
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      let option = {}
      const [grid, axis, series] = [this.chartGridConfig(), this.chartAxisConfig(data), this.chartSeriesConfig(data)]
      option = Object.assign(option, grid, axis, series)
      return option
    },
    chartGridConfig() {
      return {
        grid: {
          top: '50px',
          left: '5px',
          right: '5px',
          bottom: '50px',
          backgroundColor: '#fff',
          width: '90%',
          height: '50%',
        },
      }
    },
    chartAxisConfig(data) {
      return {
        xAxis: {
          show: false,
          type: 'category',
          data: data.axis ? data.axis : [],
          splitLine: {
            show: false,
          },
        },
        yAxis: {
          show: false,
          type: 'value',
          splitLine: {
            show: false,
          },
        },
      }
    },
    chartSeriesConfig(data) {
      const seriesOption = {
        series: [],
      }
      const [line, effectScatter] = [this.chartSeriesLineConfig(data), this.chartSeriesEffectScatterConfig(data)]

      seriesOption.series.push(line, effectScatter)
      return seriesOption
    },
    chartSeriesLineConfig(data) {
      return {
        data: data.lineData,
        type: 'line',
        itemStyle: {
          color: 'rgba(112,180,191,0.8)',
        },
        lineStyle: {
          width: 2,
        },
      }
    },
    chartSeriesEffectScatterConfig(data) {
      return {
        type: 'effectScatter',
        effectType: 'ripple',
        coordinateSystem: 'cartesian2d',
        symbolSize: (val) => {
          return val[2]
        },
        rippleEffect: {
          color: 'rgba(112,180,191,0.8)',
          brushType: 'stroke',
          period: 10,
        },
        itemStyle: {
          color: 'transparent',
        },
        data: data.scatterData,
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
