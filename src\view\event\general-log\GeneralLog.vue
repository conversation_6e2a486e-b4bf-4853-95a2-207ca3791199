<!--
 * @Description: 通用日志
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-25
 * @Editor:
 * @EditDate: 2021-11-25
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" @on-change="changeQueryTable" @on-download="clickDownload" @on-forward="clickForward"></table-header>
    <table-body
      :title-name="title"
      :table-show="tableShow"
      :table-loading="table.loading"
      :table-scroll="table.scroll"
      :table-data="table.data"
      :columns="columnOption"
      @on-scroll="scrollTable"
      @on-detail="doDetail"
      @on-custom="doCustomize"
    ></table-body>
    <table-footer :total-loading="table.totalLoading" :total="table.total" :nomore="table.nomore"></table-footer>
    <custom-dialog
      :visible.sync="dialog.column.visible"
      :title-name="title"
      :form="dialog.column.form"
      :columns="dialog.column.columns"
      @on-submit="clickSubmitCustomize"
    ></custom-dialog>
    <detail-drawer :visible.sync="dialog.detail.visible" :detail-data="dialog.detail.model"></detail-drawer>
    <log-forward-dialog
      :visible.sync="dialog.forward.visible"
      :title-name="title"
      :forward-server-option="option.forwardServer"
      :collector-server-option="option.collectorServer"
      :device-type-option="option.deviceType"
      @on-submit="updateLogForward"
    ></log-forward-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import CustomDialog from './TheCustomizeDialog'
import DetailDrawer from './TheDetailDrawer'
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import LogForwardDialog from './TheLogForwardDialog'
import {
  queryGeneralLogTable,
  queryGeneralLogTotal,
  downloadGeneralLogEvent,
  queryColumnsData,
  updateColumnsData,
  updateLogForward,
  queryForwardCollectorOptions,
  queryForwardServerOptions,
  queryDeviceTypeCombo,
} from '@api/event/general-log-api'
export default {
  name: 'GeneralLOg',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    CustomDialog,
    DetailDrawer,
    LogForwardDialog,
  },
  data() {
    return {
      title: this.$t('event.generalLog.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          severity: '',
          deviceType: '',
          facility: '',
          occurTime: [],
          ipRange: ['', ''],
        },
      },
      table: {
        loading: false,
        scroll: true,
        data: [],
        selected: [],
        nomore: false,
        totalLoading: false,
        total: 0,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      },
      tableShow: true,
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
        column: {
          visible: false,
          form: {
            model: {
              checkList: [],
              checkAll: false,
              isIndeterminate: false,
            },
            info: {
              checkList: {
                key: 'checkList',
                label: this.$t('alarm.table.dialog.option'),
              },
            },
          },
          columns: [
            { key: 'deviceName', label: this.$t('event.generalLog.label.deviceName') },
            { key: 'insertTime', label: this.$t('event.generalLog.label.insertTime') },
            { key: 'facilityName', label: this.$t('event.generalLog.label.facilityName') },
            { key: 'severityName', label: this.$t('event.generalLog.label.severityName') },
            { key: 'fromIp', label: this.$t('event.generalLog.label.fromIp') },
            { key: 'appName', label: this.$t('event.generalLog.label.appName') },
            { key: 'procId', label: this.$t('event.generalLog.label.procId') },
            { key: 'msgId', label: this.$t('event.generalLog.label.msgId') },
            { key: 'logTimestamp', label: this.$t('event.generalLog.label.logTimestamp') },
            { key: 'hostName', label: this.$t('event.generalLog.label.hostName') },
            { key: 'structuredData', label: this.$t('event.generalLog.label.structuredData') },
            { key: 'message', label: this.$t('event.generalLog.label.message') },
            { key: 'logMessage', label: this.$t('event.generalLog.label.logMessage') },
          ],
        },
        forward: {
          visible: false,
          model: {},
        },
      },
      option: {
        forwardServer: [],
        collectorServer: [],
        deviceType: [],
      },
      columnOption: ['deviceName', 'insertTime', 'facilityName', 'severityName', 'fromIp', 'message'],
    }
  },
  mounted() {
    this.getTableData()
    this.queryColumns()
    this.getOptions()
  },
  methods: {
    getOptions() {
      queryForwardCollectorOptions().then((res) => {
        this.option.collectorServer = res.map((item) => {
          return { label: item.ip, value: item.devId }
        })
      })
      queryForwardServerOptions().then((res) => {
        this.option.forwardServer = res
      })
      queryDeviceTypeCombo().then((res) => {
        this.option.deviceType = res
      })
    },
    changeQueryTable() {
      this.table.data = []
      const param = this.handleQueryParams()
      this.getTableData(param)
    },
    scrollTable() {
      const params = this.handleQueryParams(true)
      this.getTableData(params, false)
    },
    handleQueryParams(scroll = false) {
      let params = {
        pageSize: this.pagination.pageSize,
      }
      if (scroll) {
        const lastRow = this.table.data[this.table.data.length - 1] || null
        if (lastRow) {
          params = Object.assign(params, {
            id: lastRow.id,
            timestamp: lastRow.timestamp,
            scrollToken: lastRow.scrollToken,
          })
        }
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          timeRange: this.timeRange(),
          severity: this.query.form.severity,
          deviceType: this.query.form.deviceType.toString(),
          facility: this.query.form.facility,
          fromIp: this.ipRange(),
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    timeRange() {
      this.query.form.occurTime = this.query.form.occurTime || ['', '']
      const timeRange = this.query.form.occurTime.filter((item) => !isEmpty(item))
      return timeRange.toString()
    },
    // ipRange() {
    //     let ip = [];
    //     const ipRange = this.query.form.ipRange.filter(item => !isEmpty(item));
    //     if (ipRange.length === 2) {
    //         ip = ipRange.join("-");
    //     } else {
    //         ip = ipRange.toString();
    //     }
    //     return ip;
    // },
    ipRange() {
      let ip = ''
      const ipRange = this.query.form.ipRange.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipRange.length > 0) {
        ip = this.query.form.ipRange.join('-')
      }
      return ip
    },
    doDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    clickDetailDrawer(row) {
      this.drawer.visible = true
      this.drawer.data = row
    },
    getTableData(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.form.fuzzyField,
      },
      total = true
    ) {
      this.table.scroll = true
      this.table.loading = true
      queryGeneralLogTable(param).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.table.data.push(...res)
          this.table.scroll = true
          if (this.table.data.length > this.pagination.pageSize) {
            this.table.nomore = true
          }
        } else {
          this.table.data.push(...res)
          this.table.scroll = false
        }
        this.table.loading = false
        if (total) {
          this.getTableTotal(param)
        }
      })
    },
    getTableTotal(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.form.fuzzyField,
      }
    ) {
      this.table.totalLoading = true
      queryGeneralLogTotal(param).then((res) => {
        this.table.total = res
        this.table.totalLoading = false
      })
    },
    doCustomize() {
      this.dialog.column.visible = true
      queryColumnsData().then((res) => {
        if (res.length === 13) {
          this.dialog.column.form.model.checkList = res
          this.dialog.column.form.model.checkAll = true
          this.dialog.column.form.model.isIndeterminate = false
        } else if (res.length === 0) {
          this.dialog.column.form.model.checkList = [
            'deviceName',
            'insertTime',
            'facilityName',
            'severityName',
            'fromIp',
            'appName',
            'procId',
            'msgId',
            'logTimestamp',
            'hostName',
            'structuredData',
            'message',
            'logMessage',
          ]
          this.dialog.column.form.model.checkAll = true
          this.dialog.column.form.model.isIndeterminate = false
        } else {
          this.dialog.column.form.model.checkList = res
          this.dialog.column.form.model.checkAll = false
          this.dialog.column.form.model.isIndeterminate = true
        }
      })
    },
    clickSubmitCustomize(formModel) {
      updateColumnsData(formModel.checkList).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.queryColumns()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 查询自定义列
    queryColumns() {
      this.tableShow = false
      queryColumnsData().then((res) => {
        if (res.length !== 0) {
          this.columnOption = res
        }
        setTimeout(() => {
          this.tableShow = true
        }, 100)
      })
    },
    clickDownload() {
      const ids = this.table.selected.length !== 0 ? this.table.selected.map((item) => item.id).toString() : null
      let params = this.handleQueryParams()
      params = Object.assign(params, {
        downloadId: ids,
      })
      this.downloadEvent(params)
    },
    downloadEvent(params = {}) {
      this.table.loading = true
      downloadGeneralLogEvent(params).then((res) => {
        if (res) {
          this.table.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    clickForward() {
      this.dialog.forward.visible = true
    },
    updateLogForward(formModel) {
      const params = Object.assign({}, formModel)
      updateLogForward(params).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.update.success',
            type: 'success',
          })
          this.dialog.forward.visible = false
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
