const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    strategyName: '@NAME',
    description: '@CNAME',
    createTime: '@DATETIME',
    'state|0-1': '1',
  },
})

const auditTypeOption = [
  {
    label: '网络攻击',
    value: '网络攻击',
  },
]

const sourceDeviceOption = [
  {
    label: 'window系统',
    value: 'window系统',
  },
]

module.exports = [
  {
    url: '/strategy/alarm/strategies',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: `/strategy/alarm/strategy/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/alarm/strategy',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/alarm/strategy',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/strategy/alarm/combo/audit-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: auditTypeOption,
      }
    },
  },
  {
    url: '/strategy/alarm/combo/source-device-type',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: sourceDeviceOption,
      }
    },
  },
]
