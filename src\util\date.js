// 根据系统时间判断上午、下午、晚上好
export function getGreetings(flag = true) {
  const now = new Date()
  const hours = now.getHours()
  let r = ''
  if (hours >= 0 && hours <= 8) {
    r = '早上'
  } else if (hours > 8 && hours <= 12) {
    r = '上午'
  } else if (hours > 12 && hours < 18) {
    r = '下午'
  } else {
    r = '晚上'
  }
  if (flag === true) {
    r = r + '好'
  }
  return r
}

// 定时器工具类
export class Timer {
  constructor(callback, interval) {
    this.callback = callback // 定时器到期后要执行的回调函数
    this.interval = interval // 定时器的间隔时间（毫秒）
    this.timerId = null // 存储定时器的ID
    this.startTime = null // 定时器开始的时间
    this.remainingTime = interval // 剩余时间，用于暂停后恢复
  }

  // 开始定时器
  start() {
    if (this.timerId) return // 如果定时器已经启动，不重复启动

    this.startTime = Date.now() // 记录开始时间

    this.timerId = setTimeout(() => {
      this.callback() // 执行回调函数
      this.clear() // 清除定时器
    }, this.remainingTime) // 使用剩余时间设置定时器
  }

  // 停止定时器
  stop() {
    if (!this.timerId) return // 如果定时器没有启动，不执行停止操作

    clearTimeout(this.timerId) // 清除定时器
    this.timerId = null // 重置定时器ID
    this.remainingTime -= Date.now() - this.startTime // 计算剩余时间
  }

  // 重置定时器
  reset() {
    this.clear() // 清除定时器
    this.remainingTime = this.interval // 重置剩余时间为初始间隔时间
  }

  // 清除定时器
  clear() {
    if (this.timerId) {
      clearTimeout(this.timerId) // 清除定时器
      this.timerId = null // 重置定时器ID
    }
  }
}

// 轮询定时器
export class IntervalTimer {
  constructor(callback, interval) {
    this.callback = callback // 定时器到期后要执行的回调函数
    this.interval = interval // 定时器的间隔时间（毫秒）
    this.timerId = null // 存储定时器的ID
    this.remainingTime = interval // 剩余时间，用于暂停后恢复
    this.isPaused = false // 是否暂停状态
  }

  // 运行定时器
  runTimer() {
    this.timerId = setInterval(() => {
      if (!this.isPaused) {
        this.callback() // 执行回调函数
      }
    }, this.interval)
  }

  // 暂停定时器
  pause() {
    if (!this.timerId || this.isPaused) return // 如果定时器没有启动或已经暂停，不执行暂停操作

    this.isPaused = true // 设置为暂停状态
    clearInterval(this.timerId) // 清除定时器
    this.timerId = null // 重置定时器ID
  }

  // 恢复定时器
  resume() {
    if (!this.isPaused) return // 如果没有暂停，不执行恢复操作

    this.isPaused = false // 重置暂停状态
    this.runTimer() // 重新启动定时器
  }

  // 开始定时器
  start() {
    if (this.timerId) return // 如果定时器已经启动，不重复启动
    this.isPaused = false // 重置暂停状态
    this.remainingTime = this.interval // 重置剩余时间为初始间隔时间
    this.runTimer() // 启动定时器
  }

  // 停止定时器
  stop() {
    if (!this.timerId) return // 如果定时器没有启动，不执行停止操作
    clearInterval(this.timerId) // 清除定时器
    this.timerId = null // 重置定时器ID
    this.isPaused = false // 重置暂停状态
  }

  // 重置定时器
  reset() {
    this.stop() // 清除定时器
    this.remainingTime = this.interval // 重置剩余时间为初始间隔时间
    this.start() // 重新启动定时器
  }
}

// 用来判断当前时间是否在这两个时间之间
export function isCurrentTimeBetween(starttime, endtime) {
  // 获取当前时间
  const currentTime = new Date()
  // 定义一个函数将参数转换为Date对象
  function toDate(time) {
    if (time instanceof Date) {
      return time
    } else if (typeof time === 'string' || typeof time === 'number') {
      return new Date(time)
    } else {
      throw new Error('Invalid time format')
    }
  }
  // 将starttime和endtime转换为Date对象
  const startTime = toDate(starttime)
  const endTime = toDate(endtime)

  // 判断当前时间是否在starttime和endtime之间
  return currentTime >= startTime && currentTime <= endTime
}
