import request from '@util/request'

// 查询预测信息列表
export function queryForecastInfoTable(obj) {
  return request({
    url: '/dataanalysis/queryDataAnalysisLists',
    method: 'get',
    params: obj || {},
  })
}

// 查询分类信息项
export function queryInfoItemCombo(type) {
  return request({
    url: `/dataanalysis/findLableByType/${type}`,
    method: 'get',
  })
}

// 查询预测信息
export function queryAnalysisData(obj) {
  return request({
    url: '/dataanalysis/queryDataAnalysisByTypeAndLable',
    method: 'get',
    params: obj || {},
  })
}

// 查询策略配置
export function queryStrategyConfig() {
  return request({
    url: '/dataanalysis/dataAnalysisStatus',
    method: 'get',
  })
}

// 修改策略配置
export function updateStrategyConfig(obj) {
  return request({
    url: '/dataanalysis/updateDataAnalysisStatus',
    method: 'put',
    params: obj || {},
  })
}

// 查询外边系统下拉
export function queryExternalSystem() {
  return request({
    url: '/dataanalysis/combo/forward-relay-way',
    method: 'get',
  })
}
