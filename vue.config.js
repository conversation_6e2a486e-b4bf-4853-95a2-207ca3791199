// 参考文档： https://cli.vuejs.org/zh/guide/
const path = require('path')
const { execSync } = require('child_process')
function getCurrentBranch() {
  try {
    // 执行Git命令获取当前分支
    const branchName = execSync('git rev-parse --abbrev-ref HEAD', {
      encoding: 'utf8',
    }).trim()
    return branchName
  } catch (error) {
    console.error('Error getting git branch:', error)
    return ''
  }
}
const resolve = (dir) => path.join(__dirname, dir)
const resolveConfig = {
  name: 'SMP',
  resolve: {
    extensions: ['.js', '.vue', '.json', 'scss', 'css'],
    alias: {
      '@': resolve('src'),
      '@api': resolve('src/api'),
      '@asset': resolve('src/asset'),
      '@comp': resolve('src/component'),
      '@style': resolve('src/style'),
      '@theme': resolve('src/theme'),
      '@util': resolve('src/util'),
      '@view': resolve('src/view'),
      '@vendor': resolve('src/vendor'),
    },
  },
}
const { NODE_ENV, VUE_APP_IS_MOCK, VUE_APP_BASE_API, VUE_APP_PROXY_TARGET } = process.env
const proxy = {}
const reWirteKey = `^${VUE_APP_BASE_API}`
const reWirteObj = {}
reWirteObj[reWirteKey] = ''
proxy[VUE_APP_BASE_API] = {
  target: VUE_APP_PROXY_TARGET,
  pathRewrite: reWirteObj,
  secure: false,
  changeOrigin: true,
}
const devServer = { port: 7001 }
if (VUE_APP_IS_MOCK === 'true') devServer.before = require('./mock/server.js')
else devServer.proxy = proxy
module.exports = {
  publicPath: '/',
  outputDir: 'dist',
  assetsDir: 'static',
  lintOnSave: NODE_ENV === 'development',
  productionSourceMap: NODE_ENV !== 'production',
  devServer: devServer,
  css: {
    sourceMap: NODE_ENV === 'development',
    loaderOptions: {
      sass: {
        // 引入scss全局变量
        prependData: [`@import "~@style/variable.scss";`, `@import "~@theme/index.scss";`].join('\n'),
      },
    },
  },
  configureWebpack: {
    module: {
      // 解决Critical dependency: require function is used in a way in which dependencies cannot be statically extracted的问题
      unknownContextCritical: false,
      // 解决Critical dependency: the request of a dependency is an expression 问题
      exprContextCritical: false,
    },
    name: 'SMP',
    resolve: resolveConfig.resolve,
    devtool: 'source-map',
  },
  chainWebpack(config) {
    // 开启预加载，提高第一次加载的速度
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // 先忽略runtime.js
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial',
      },
    ])

    // 随着页面的增加，会产生很多无意义的请求
    config.plugins.delete('prefetch')
    // 页面标题
    config.plugin('html').tap((args) => {
      args[0].title = resolveConfig.name
      return args
    })
    // 分包处理
    config.when(NODE_ENV !== 'development', (config) => {
      config
        .plugin('ScriptExtHtmlWebpackPlugin')
        .after('html')
        .use('script-ext-html-webpack-plugin', [{ inline: /runtime\..*\.js$/ }])
        .end()
      config.optimization.splitChunks({
        chunks: 'all',
        cacheGroups: {
          libs: {
            name: 'chunk-libs',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'initial', // 只打包最初依赖的第三方
          },
          elementUI: {
            name: 'chunk-elementUI', // 将element-ui单独拆分成一个包
            priority: 20, // 权重需要大于libs和app，否则会被打包到libs或app中
            test: /[\\/]node_modules[\\/]_?element-ui(.*)/, // 兼容cnpm
          },
          echarts: {
            name: 'chunk-echarts', // 将echarts单独拆分成一个包
            priority: 20,
            test: /[\\/]node_modules[\\/]_?echarts(.*)/,
          },
          commons: {
            name: 'chunk-commons',
            test: resolve('src/component'),
            minChunks: 3,
            priority: 5,
            reuseExistingChunk: true,
          },
        },
      })
      config.optimization.runtimeChunk('single')
    })
    // 打包压缩处理
    // config.when(process.env.ENV === 'production', (config) => {
    //   // 按顺序执行为数组
    //   let branch = getCurrentBranch() || ''
    //   if (branch) {
    //     branch = '.' + branch.substr(branch.indexOf('-') + 1, branch.length)
    //   }
    //   const onEndOption = [
    //     {
    //       delete: ['./dist/html.tar.gz'],
    //     },
    //     {
    //       archive: [
    //         {
    //           source: './dist',
    //           destination: `./dist/html.tar${branch}.gz`,
    //           format: 'tar',
    //           options: {
    //             gzip: true,
    //             gzipOptions: {
    //               level: 1,
    //             },
    //           },
    //         },
    //       ],
    //     },
    //   ]
    //   // preview下不删除文件
    //   if (process.title.indexOf('preview') === -1) {
    //     onEndOption.push({
    //       delete: ['./dist/static/', './dist/index.html', './dist/favicon.ico', './dist/config.js'],
    //     })
    //   }
    //   config.plugin('filemanager-webpack-plugin').use('filemanager-webpack-plugin', [{ onEnd: onEndOption }])
    // })
  },
}
