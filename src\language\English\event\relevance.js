export default {
  relevance: {
    table: {
      eventName: 'Event Name',
      policyName: 'Strategy Name',
      eventLevelName: 'Event Level',
      createDate: 'Creation Time',
      eventTypeName: 'Event Name',
      eventCategoryName: 'Event Type',
      updateDate: 'Update Time',
      count: 'Count',
      handel: 'Operation',
      typeName: 'Event Type',
      level: 'Event Level',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      dateTime: 'Time',
      raw: 'Log Original Text',
      eventDesc: 'Event Description',
    },
    time: {
      createDateStart: 'Creation Time Start',
      createDateEnd: 'Creation Time End',
      updateDateStart: 'Update Time Start',
      updateDateEnd: 'Update Time End',
    },
    dialog: {
      detailTitle: 'Correlation Event Details',
      colTitle: 'Correlation Event Custom Columns',
    },
    header: 'Correlation Event',
    detailOriginalColumn: {
      type2Name: 'Original Event Name',
      eventName: 'Event Type',
      eventCategoryName: 'Event Category',
      level: 'Event Level',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      dateTime: 'Time',
      raw: 'Log Original Text',
    },
    chart: {
      attackCount: 'Attack Count',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      level: 'Level',
    },
  },
}
