<!--
 * @Description: 采集器管理 - 日志导入弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-19
 * @Editor:
 * @EditDate: 2021-08-19
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.logImport', [title])"
    width="60%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane :label="$t('collector.management.dialog.columns.importConfig')" name="first" style="height: 272px;">
        <el-form ref="logForm" :model="form.model" :rules="rules" label-width="120px">
          <el-row>
            <el-col :span="12">
              <el-form-item prop="IP" :label="$t('collector.management.dialog.columns.ip')">
                <el-input v-model.trim="form.model.IP"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="codeWay" :label="$t('collector.management.dialog.columns.codeWay')">
                <el-select v-model="form.model.codeWay" filterable clearable :placeholder="$t('collector.management.placeholder.codeWay')">
                  <el-option v-for="item in options.codeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item prop="propertyKind" :label="$t('collector.management.dialog.columns.propertyKind')">
                <el-cascader
                  ref="cascader"
                  v-model="form.model.propertyKind"
                  filterable
                  clearable
                  :options="deviceTypeOption"
                  :placeholder="$t('collector.management.placeholder.propertyKind')"
                  :props="{ expandTrigger: 'hover' }"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item prop="strategy" :label="$t('collector.management.dialog.columns.strategy')">
                <el-select v-model="form.model.strategy" filterable clearable :placeholder="$t('collector.management.placeholder.strategy')">
                  <el-option v-for="item in filterOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="12">
              <el-form-item prop="files" :label="$t('collector.management.dialog.columns.logFile')">
                <el-upload
                  ref="upload"
                  v-has="'upload'"
                  class="header-button-upload"
                  action="#"
                  :headers="{
                    'Content-Type': 'multipart/form-data',
                  }"
                  auto-upload
                  :show-file-list="true"
                  :limit="1"
                  accept=".txt"
                  :file-list="form.model.files"
                  :on-exceed="handleExceed"
                  :on-change="onUploadFileChange"
                  :http-request="submitUploadFile"
                  :on-remove="handleRemove"
                  :before-upload="beforeUploadValidate"
                  @click="clickUploadTable"
                >
                  <el-input
                    style="cursor:pointer"
                    :placeholder="$t('collector.management.placeholder.logFile')"
                    suffix-icon="el-icon-folder-opened"
                  ></el-input>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('collector.management.dialog.columns.historyImport')" name="second">
        <main class="table-body-main">
          <el-table
            v-loading="table.loading"
            :data="table.data"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
            height="240"
          >
            <el-table-column
              v-for="(item, index) in tableColoums"
              :key="index"
              :prop="item"
              :label="$t(`collector.management.dialog.columns.${item}`)"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <p v-if="item === 'propertyKind'">{{ scope.row.categoryName }} - {{ scope.row.typeName }}</p>
                <p v-else>
                  {{ scope.row[item] }}
                </p>
              </template>
            </el-table-column>
          </el-table>
        </main>
        <footer class="table-footer">
          <el-pagination
            v-if="pagination.visible"
            small
            background
            align="right"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            @size-change="tableSizeChange"
            @current-change="tableCurrentChange"
          ></el-pagination>
        </footer>
      </el-tab-pane>
    </el-tabs>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { queryImportLogRecords } from '@api/collector/collector-management-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    form: {
      required: true,
      type: Object,
    },
    filterOption: {
      required: true,
      type: Array,
    },
    deviceTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'first',
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      uploadFile: {},
      options: {
        codeOption: [
          {
            label: '自动',
            value: 'auto',
          },
          {
            label: 'UTF-8',
            value: 'utf8',
          },
          {
            label: 'GBK',
            value: 'gbk',
          },
        ],
      },
      tableColoums: ['ipAddress', 'propertyKind', 'fileName', 'createTime'],
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.initData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    initData() {
      this.activeName = 'first'
      this.uploadFile = {}
    },
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    handleExceed(files, fileList) {
      const text = this.$t('collector.management.dialog.exceed', [files.length, files.length, fileList.length])
      this.$message.warning(text)
    },
    onUploadFileChange(file) {
      this.form.model.files.push(file)
    },
    submitUploadFile(param) {
      if (param.file && this.form.model.files.length > 0) {
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.uploadFile = formData
      }
    },
    handleRemove(file, fileList) {
      this.form.model.files.splice(0, 1)
    },
    beforeUploadValidate(file) {
      if (this.form.model.files.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.txt') + 1)
        const isRight = suffix === 'txt'
        if (!isRight) {
          prompt({
            i18nCode: 'tip.upload.typeError',
            type: 'warning',
          })
          return isRight
        }
      }
    },
    clickUploadTable() {
      this.form.model.files = []
      this.$refs.upload.submit()
    },
    handleClick(tab, event) {
      if (this.activeName === 'second') {
        this.queryTableData()
      }
    },
    clickSubmit() {
      this.$refs.logForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.handleImportItems()
            this.$emit('on-submit', this.uploadFile)
            this.$refs.logForm.resetFields()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    handleImportItems() {
      const node = this.$refs.cascader.getCheckedNodes(true)
      this.buildTreeData(this.form.model, node)
      this.form.model.propertyKind = ''
      Object.keys(this.form.model).map((key) => {
        this.uploadFile.append(key, this.form.model[key])
      })
    },
    buildTreeData(formModel, leaf) {
      const [option] = leaf
      const [categoryName, typeName] = option.pathLabels
      const [categoryID, typeId] = option.path
      Object.assign(formModel, {
        categoryName,
        categoryID,
        typeName,
        typeId,
      })
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.queryTableData()
    },
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.queryTableData()
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryImportLogRecords(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-upload {
  width: 90%;
  &-list {
    width: 90%;
  }
}
</style>
