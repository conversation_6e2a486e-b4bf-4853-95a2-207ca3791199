<!--
 * @Description: 异常页面 - 资源异常 - 401
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="soc-http401">
    <el-row class="soc-http401-container">
      <el-col :span="12" class="soc-http401-img">
        <img src="@/asset/image/exception/401/401.gif" width="313" height="428" />
      </el-col>
      <el-col :span="12" class="soc-http401-context">
        <section v-if="department" class="soc-http401-context-department">
          {{ $t('exception.page401.department') }}
        </section>
        <section v-if="info" class="soc-http401-context-info">
          <p>
            <span>{{ $t('exception.page401.copyright.label') }}</span>
            :
            <span>{{ $t('exception.page401.copyright.name') }}</span>
          </p>
          <p>
            <span>{{ $t('exception.page401.author.label') }}</span>
            :
            <a class="link-type">{{ $t('exception.page401.author.name') }}</a>
          </p>
        </section>
        <section class="soc-http401-context-headline">
          {{ $t('exception.page401.instructions') }}
        </section>
        <section class="soc-http401-context-info">
          {{ $t('exception.page401.check') }}
        </section>
        <el-button v-if="goRecently" plain class="soc-http401-context-backhome" @click="goRecently()">
          {{ $t('exception.page401.home') }}
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Page401',
  data() {
    return {
      department: false,
      info: false,
      backhome: true,
    }
  },
  methods: {
    goRecently() {
      this.$router.replace({
        path: this.$store.getters.homePath || this.$store.getters.homePath !== '' ? this.$store.getters.homePath : '/layout',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.soc-http401 {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: $WT;

  &-container {
    position: relative;
    width: 1200px;
    padding: 0 50px;
    overflow: hidden;

    .soc-http401-context {
      position: relative;
      float: left;
      width: 300px;
      padding: 30px 0;
      overflow: hidden;

      &-department {
        margin-bottom: 20px;
        line-height: 40px;
        font-size: 32px;
        font-weight: bold;
        opacity: 0;
        animation: slideUp 0.5s forwards;
      }

      &-headline {
        margin-bottom: 10px;
        line-height: 24px;
        font-size: 20px;
        font-weight: bold;
        opacity: 0;
        animation: slideUp 0.5s 0.1s forwards;
      }

      &-info {
        margin-bottom: 30px;
        line-height: 21px;
        font-size: 13px;
        opacity: 0;
        animation: slideUp 0.5s 0.2s forwards;
      }

      &-backhome {
        border-radius: 100px;
        font-size: 14px;
        opacity: 0;
        animation: slideUp 0.5s 0.3s forwards;
        cursor: pointer;
        @include theme('background-color', primary-color);
      }

      @keyframes slideUp {
        0% {
          transform: translateY(60px);
          opacity: 0;
        }
        100% {
          transform: translateY(0);
          opacity: 1;
        }
      }
    }
  }
}
</style>
