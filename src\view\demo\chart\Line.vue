<template>
  <div class="chart-wrapper">
    <el-row :gutter="20">
      <el-col :span="5">
        <el-select v-model="type" class="width-max">
          <el-option value="line" label="线图"></el-option>
          <el-option value="line-stack" label="堆叠线图"></el-option>
          <el-option value="line-step" label="阶梯线图"></el-option>
          <el-option value="line-stack-step" label="堆叠阶梯线图"></el-option>
        </el-select>
      </el-col>
      <el-col :span="5">
        <template v-if="isNone">
          <el-select v-model="axis" class="width-max">
            <el-option value="x" label="横向"></el-option>
            <el-option value="y" label="纵向"></el-option>
          </el-select>
        </template>
      </el-col>
      <el-col :span="4" :offset="10" align="right">
        <el-button @click="clickLookData">
          数据结构
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <line-chart :option="option"></line-chart>
    </el-row>
    <el-dialog title="线图数据结构" :visible.sync="visible">
      <ace-editor v-model="jsonString" auto-complete lang="json" width="100%" height="400" @init="initEditorConfig()"></ace-editor>
    </el-dialog>
  </div>
</template>

<script>
import AceEditor from 'vue2-ace-editor'
import LineChart from '@comp/ChartFactory/forecast/LineChart'

export default {
  name: 'ChartBar',
  components: {
    AceEditor,
    LineChart,
  },
  data() {
    return {
      visible: false,
      type: 'line',
      axis: 'x',
      data: [
        [
          ' ',
          'aa',
          'bb',
          'cc',
          'dd',
          'ee',
          'ff',
          'gg',
          'hh',
          'ii',
          'jj',
          'kk',
          'll',
          'mm',
          'nn',
          'oo',
          'pp',
          'qq',
          'rr',
          'ss',
          'tt',
          'uu',
          'vv',
          'ww',
          'xx',
          'yy',
          'zz',
        ],
        ['线1', 12, 23, 9, 34, 54, 45, 98, 56, 87, 67, 78, 76, 89, 90, 36, 49, 59, 29, 54, 92, 87, 66, 47, 28, 64, 94],
        ['线2', 51, 56, 23, 95, 91, 45, 36, 15, 61, 45, 95, 46, 95, 92, 53, 35, 53, 19, 17, 78, 57, 13, 35, 77, 99, 63],
      ],
    }
  },
  computed: {
    isNone() {
      return this.type !== 'bar-polar' && this.type !== 'bar-radial'
    },
    option() {
      return {
        type: this.type,
        axis: this.axis,
        data: this.data,
      }
    },
    jsonString() {
      return JSON.stringify(this.data, null, '\t')
    },
  },
  methods: {
    initEditorConfig(mode = 'json', theme = 'tomorrow_night_blue') {
      require(`brace/mode/${mode}`)
      require(`brace/theme/${theme}`)
    },
    clickLookData() {
      this.visible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}

div.el-row:first-child {
  height: 32px;
}

div.el-row:nth-child(2) {
  height: calc(100% - 32px);
}
</style>
