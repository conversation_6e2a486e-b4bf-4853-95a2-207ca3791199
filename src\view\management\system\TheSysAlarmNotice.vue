<!--
 * @Description: 系统管理 - 系统告警通知
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <section class="tab-context-wrapper">
    <section class="form-container">
      <h2>{{ $t('management.system.systemAlarm.title') }}</h2>
      <el-form ref="sysAlarmForm" :model="formData" :rules="form.rule" label-width="120px">
        <section class="notice-wrapper">
          <section class="notice-wrapper-item">
            <section class="notice-wrapper-item-checkbox">
              <el-checkbox v-model="formData.isMail" true-label="1" false-label="0" :label="$t('management.system.systemAlarm.isMail')"></el-checkbox>
            </section>
            <section v-if="formData.isMail === '1'" class="notice-wrapper-item-content">
              <el-col :span="7">
                <el-form-item prop="mailTo" :label="$t('management.system.systemAlarm.mailTo')">
                  <el-input v-model="formData.mailTo" maxlength="64"></el-input>
                </el-form-item>
              </el-col>
            </section>
          </section>
          <section class="notice-wrapper-item">
            <section class="notice-wrapper-item-checkbox">
              <el-checkbox v-model="formData.isSnmp" true-label="1" false-label="0" label="snmp trap"></el-checkbox>
            </section>
            <section v-if="formData.isSnmp === '1'" class="notice-wrapper-item-content">
              <el-col :span="7">
                <el-form-item prop="snmpForwardServer" :label="$t('management.system.systemAlarm.snmpForward')">
                  <el-select v-model="formData.snmpForwardServer" clearable class="width-max">
                    <el-option v-for="item in options.snmpForward" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </section>
          </section>
          <section class="notice-wrapper-item notice-wrapper-last">
            <section class="notice-wrapper-item-checkbox">
              <el-checkbox
                v-model="formData.isSound"
                true-label="1"
                false-label="0"
                :label="$t('management.system.systemAlarm.isSound')"
              ></el-checkbox>
            </section>
          </section>
          <!--<section class="notice-wrapper-item notice-wrapper-last">
                        <section class="notice-wrapper-item-checkbox">
                            <el-checkbox
                                v-model="formData.isSms"
                                true-label="1"
                                false-label="0"
                                :label="$t('management.system.systemAlarm.isSms')">
                            </el-checkbox>
                        </section>
                        <section v-if="formData.isSms === '1'" class="notice-wrapper-item-content">
                            <el-row>
                                <el-col :span="7">
                                    <el-form-item prop="mobileUrl" :label="$t('management.system.systemAlarm.mobileUrl')">
                                        <el-input
                                            v-model="formData.mobileUrl"
                                            maxlength="100">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item prop="mobileEcName" :label="$t('management.system.systemAlarm.mobileEcName')">
                                        <el-input
                                            v-model="formData.mobileEcName"
                                            maxlength="100">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item prop="mobileApId" :label="$t('management.system.systemAlarm.mobileApId')">
                                        <el-input
                                            v-model="formData.mobileApId"
                                            maxlength="100">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="7">
                                    <el-form-item prop="mobileSecretKey" :label="$t('management.system.systemAlarm.mobileSecretKey')">
                                        <el-input
                                            v-model="formData.mobileSecretKey"
                                            maxlength="100">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item prop="mobileMobiles" :label="$t('management.system.systemAlarm.mobileMobiles')">
                                        <el-input
                                            v-model="formData.mobileMobiles"
                                            maxlength="32">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="7">
                                    <el-form-item prop="mobileSign" :label="$t('management.system.systemAlarm.mobileSign')">
                                        <el-input
                                            v-model="formData.mobileSign"
                                            maxlength="64">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                            <el-row>
                                <el-col :span="7">
                                    <el-form-item prop="mobileAddSerial" :label="$t('management.system.systemAlarm.mobileAddSerial')">
                                        <el-input
                                            v-model="formData.mobileAddSerial"
                                            maxlength="32">
                                        </el-input>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </section>
                    </section>-->
        </section>
        <section>
          <el-col :span="5" :offset="19">
            <el-form-item>
              <el-button v-has="'update'" @click="clickSaveSysAlarmNotice">
                {{ $t('button.save') }}
              </el-button>
            </el-form-item>
          </el-col>
        </section>
      </el-form>
    </section>
  </section>
</template>

<script>
import { validateEmail, validateMultiCellphone } from '@util/validate'
import { prompt } from '@util/prompt'
import { querySnmpForward } from '@api/management/system-api'
export default {
  name: 'TheSystemAlarmNotice',
  props: {
    formData: {
      required: true,
      type: Object,
    },
  },
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    const validatorCellphone = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateMultiCellphone(value)) {
        callback(new Error(this.$t('validate.comm.cellphone')))
      } else {
        callback()
      }
    }
    return {
      activeName: '1',
      form: {
        rule: {
          mailTo: [
            {
              required: true,
              validator: validatorEmail,
              trigger: 'blur',
            },
          ],
          snmpForwardServer: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          mobileUrl: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          mobileEcName: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          mobileApId: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          mobileSecretKey: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          mobileMobiles: [
            {
              required: true,
              validator: validatorCellphone,
              trigger: 'blur',
            },
          ],
          mobileSign: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          mobileAddSerial: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
      options: {
        snmpForward: [],
      },
    }
  },
  mounted() {
    this.initOptions()
  },
  methods: {
    initOptions() {
      querySnmpForward().then((res) => {
        this.options.snmpForward = res
      })
    },
    clickSaveSysAlarmNotice() {
      this.$refs.sysAlarmForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', this.formData)
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.notice-wrapper {
  position: relative;
  margin: 0 auto;
  width: 90%;
  &-item {
    border-top: 1px solid #ebeef5;
    &-checkbox {
      display: flex;
      align-items: center;
      height: 48px;
      line-height: 48px;
    }
    &-content {
      display: flex;
      flex-direction: column;
      margin-top: -12px;
      margin-bottom: 7px;
      width: 100%;
    }
  }
  &-last {
    border-bottom: 1px solid #ebeef5;
  }
}
</style>
