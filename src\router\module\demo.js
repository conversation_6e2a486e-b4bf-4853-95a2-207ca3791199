export default [
  {
    name: 'TableNormal',
    path: '/demo/table/normal',
    component: () => import('@view/demo/table/TableNormal'),
  },
  {
    name: 'TableDetail',
    path: '/demo/table/detail',
    component: () => import('@view/demo/table/TableDetail'),
  },
  {
    name: 'TableTree',
    path: '/demo/table/tree',
    component: () => import('@view/demo/table/TableTree'),
  },
  {
    name: 'ChartBar',
    path: '/demo/chart/bar',
    component: () => import('@view/demo/chart/Bar'),
  },
  {
    name: 'ChartLine',
    path: '/demo/chart/line',
    component: () => import('@view/demo/chart/Line'),
  },
  {
    name: 'Chart<PERSON><PERSON>',
    path: '/demo/chart/pie',
    component: () => import('@view/demo/chart/Pie'),
  },
  {
    name: 'Fishbone',
    path: '/demo/chart/fishbone',
    component: () => import('@view/demo/chart/Fishbone'),
  },
]
