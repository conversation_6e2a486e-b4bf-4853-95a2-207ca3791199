import request from '@util/request'

export function addReportTaskData(obj) {
  return request({
    url: '/reporttask/catalogue',
    method: 'post',
    data: obj || {},
  })
}

export function deleteReportTaskData(taskId) {
  return request({
    url: `/reporttask/catalogue/${taskId}`,
    method: 'delete',
  })
}

export function updateReportTaskData(obj) {
  return request({
    url: '/reporttask/catalogue',
    method: 'put',
    data: obj || {},
  })
}

export function updateReportTaskStatusData(taskIds, enable) {
  return request({
    url: `/reporttask/catalogue/status`,
    method: 'put',
    data: {
      taskEnableStatus: enable,
      taskIds,
    },
  })
}

export function queryReportTaskTableData(obj) {
  return request({
    url: '/reporttask/catalogue',
    method: 'get',
    params: obj || {},
  })
}

export function queryReportTaskDetailData(taskId) {
  return request({
    url: `/reporttask/catalogue/${taskId}`,
    method: 'get',
  })
}

export function queryTaskInstanceData() {
  return request({
    url: '/reporttask/catalogue/instances',
    method: 'get',
  })
}
