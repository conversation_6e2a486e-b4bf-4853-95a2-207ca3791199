import request from '@util/request'

// 查询资产列表
export function queryTableData(obj) {
  return request({
    url: '/assetmanagement/assets',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产类型下拉框
export function queryType() {
  return request({
    url: '/assetmanagement/combo/types',
    method: 'get',
  })
}

// 查询网段下拉框
export function queryNet() {
  return request({
    url: '/assetmanagement/combo/networks',
    method: 'get',
  })
}

// 查询资产自定义列
export function queryCustom(obj) {
  return request({
    url: '/assetmanagement/columns',
    method: 'get',
    params: obj ? { type: '1', ...obj } : { type: '1' },
  })
}

// 修改资产自定义列
export function addCustom(obj) {
  return request({
    url: '/assetmanagement/columns',
    method: 'put',
    data: obj || {},
  })
}

// 添加资产
export function addAsset(obj) {
  return request({
    url: '/assetmanagement/asset',
    method: 'post',
    data: obj || {},
  })
}

// 批量添加资产
export function addAssets(obj) {
  return request({
    url: '/assetmanagement/assets',
    method: 'post',
    data: obj || {},
  })
}

// 修改资产
export function updateAsset(obj) {
  return request({
    url: `/assetmanagement/asset`,
    method: 'put',
    data: obj || {},
  })
}

// 批量修改资产
export function updateAssets(obj) {
  return request({
    url: `/assetmanagement/assets`,
    method: 'put',
    data: obj || {},
  })
}

// (批量)删除资产
export function deleteAssets(ids) {
  return request({
    url: `/assetmanagement/asset/${ids}`,
    method: 'delete',
  })
}

// 导出资产信息
export function exportAsset(obj) {
  return request(
    {
      url: `/assetmanagement/download`,
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

// 下载资产导入模板
export function downloadAsset(obj) {
  return request(
    {
      url: `/assetmanagement/download/template`,
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

// 导入资产
export function uploadAsset(obj) {
  return request(
    {
      url: `/assetmanagement/upload`,
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

// 根据资产类型查询自定义属性
export function getCustomAttr(assetClass, assetType) {
  return request({
    url: `/assetmanagement/properties/${assetClass}/${assetType}`,
    method: 'get',
  })
}

// 查询资产隶属区域
export function queryAssetDomain() {
  return request({
    url: '/assetmanagement/combo/domains',
    method: 'get',
  })
}

// 查询监控器菜单是否存在
export function queryMonitorPanel() {
  return request({
    url: '/assetmanagement/monitorMenu/exist',
    method: 'get',
  })
}

// 查询监控Tab是否显示
export function queryMonitorTab(obj) {
  return request({
    url: '/assetmanagement/assetTab',
    method: 'get',
    params: obj || {},
  })
}

// 查询监控信息
export function queryMonitorTabData(obj) {
  return request({
    url: '/assetmanagement/queryMonitorTabData',
    method: 'get',
    params: obj || {},
  })
}

// 查询原始日志列表
export function queryAssetOriginalLog(obj) {
  return request({
    url: '/assetmanagement/events',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产日志源信息
export function queryAssetLogSources(ip) {
  return request({
    url: `/assetmanagement/sources/tab/${ip}`,
    method: 'get',
  })
}

// 查询资产日志源-日志时间信息
export function queryLogTimeInfo(obj) {
  return request({
    url: '/assetmanagement/rizhiyuanxinxi',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产日志源-日志接收总数
export function queryLogTotalNumber(obj) {
  return request({
    url: '/assetmanagement/rizhijieshouzongshu',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产日志源-日志存储时长
export function queryLogStorageDuration(obj) {
  return request({
    url: '/assetmanagement/rizhicunchushichang',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产日志源-日志采集趋势
export function queryLogCollectTrendChart(obj) {
  return request({
    url: '/assetmanagement/rizhicaijiqushi',
    method: 'get',
    params: obj || {},
  })
}

// 查询原始日志列表
export function queryOriginalLogTableData(obj) {
  return request({
    url: '/assetmanagement/events',
    method: 'get',
    params: obj || {},
  })
}

// 查询原始日志总数
export function queryOriginalLogTotalData(obj) {
  return request({
    url: '/assetmanagement/total',
    method: 'get',
    params: obj || {},
  })
}

export function queryEventTypes() {
  return request({
    url: '/assetmanagement/combo/event-types',
    method: 'get',
  })
}

export function queryLogSources() {
  return request({
    url: '/assetmanagement/combo/asset-types',
    method: 'get',
  })
}

export function queryGeneralLogTable(obj) {
  return request({
    url: '/assetmanagement/unknowlog/events',
    method: 'get',
    params: obj || {},
  })
}

export function queryGeneralLogTotal(obj) {
  return request({
    url: '/assetmanagement/unknowlog/total',
    method: 'get',
    params: obj || {},
  })
}

// 查询级别下拉数据
export function querySeverityCombo() {
  return request({
    url: '/assetmanagement/combo/severity-categories',
    method: 'get',
  })
}

// 查询发生源设备类型下拉数据
export function queryDeviceTypeCombo() {
  return request({
    url: '/assetmanagement/combo/asset-types',
    method: 'get',
  })
}

// 查询日志模块下拉数据
export function queryFacilityCombo() {
  return request({
    url: '/assetmanagement/combo/facility-categories',
    method: 'get',
  })
}

// 查询资产存活启停配置信息
export function queryAliveConfig() {
  return request({
    url: '/assetmanagement/alive/config',
    method: 'get',
  })
}
// 更新资产存活启停配置
export function updateAliveConfig(obj) {
  return request({
    url: `/assetmanagement/alive/updateConfig`,
    method: 'put',
    data: obj || {},
  })
}
