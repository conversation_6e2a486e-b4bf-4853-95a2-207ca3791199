<!--
 * @Description: 系统管理 - 系统快照
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-01-13
 * @Editor:
 * @EditDate: 2022-01-13
-->
<template>
  <div class="tab-context-wrapper">
    <section class="form-container">
      <el-form ref="backupForm" :model="form.model" :rules="form.rule" label-width="120px">
        <el-row>
          <h2 class="header-title">
            {{ $t('management.system.title.snapBackupConfig') }}
          </h2>
        </el-row>
        <el-row>
          <el-col :span="8">
            <el-form-item :label="$t('management.system.label.backupCycle')">
              <el-radio-group v-model="form.model.cycle" @change="form.model.timeValue = ''">
                <el-radio label="day">
                  {{ $t('time.cycle.day') }}
                </el-radio>
                <el-radio label="week">
                  {{ $t('time.cycle.week') }}
                </el-radio>
                <el-radio label="month">
                  {{ $t('time.cycle.month') }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8" class="backup-setting">
            <el-form-item :label="$t('management.system.label.backupTime')"></el-form-item>
            <el-form-item v-if="form.model.cycle !== 'day'" prop="timeValue">
              <el-select v-model="form.model.timeValue" clearable class="width-small">
                <template v-if="form.model.cycle === 'week'">
                  <el-option :value="1" :label="$t('time.week.mon')"></el-option>
                  <el-option :value="2" :label="$t('time.week.tue')"></el-option>
                  <el-option :value="3" :label="$t('time.week.wed')"></el-option>
                  <el-option :value="4" :label="$t('time.week.thu')"></el-option>
                  <el-option :value="5" :label="$t('time.week.fri')"></el-option>
                  <el-option :value="6" :label="$t('time.week.sat')"></el-option>
                  <el-option :value="0" :label="$t('time.week.sun')"></el-option>
                </template>
                <template v-else>
                  <el-option v-for="index in 31" :key="index" :value="index" :label="index + $t('time.unit.day')"></el-option>
                </template>
              </el-select>
            </el-form-item>
            <el-form-item prop="time">
              <el-time-picker v-model="form.model.time" format="HH:mm:ss" value-format="HH:mm:ss" clearable class="width-small"></el-time-picker>
            </el-form-item>
          </el-col>
          <el-col :span="8" align="right" style="padding-right: 5px;">
            <el-form-item>
              <el-button v-has="'update'" @click="clickSaveBackupConfig">
                {{ $t('button.save') }}
              </el-button>
              <el-button v-has="'reset'" @click="clickResetBackupConfig">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button v-has="'add'" @click="clickNowExecute">
                {{ $t('button.nowExecute') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <section class="table-container router-wrap-table">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.system.title.snapshotBackupTable') }}
        </h2>
        <el-upload
          ref="uploadSnapshot"
          action="#"
          :headers="upload.header"
          auto-upload
          :show-file-list="false"
          accept=".zip"
          :file-list="upload.files"
          :before-upload="beforeUploadValidate"
          :on-change="onUploadFileChange"
          :http-request="submitUploadFile"
          class="header-button-upload"
        >
          <el-button v-has="'upload'" style="margin-right: 8px;">导入</el-button>
        </el-upload>
      </header>
      <section class="table-body-main">
        <el-table :data="tableData.slice((pagination.pageNum - 1) * pagination.pageSize, pagination.pageNum * pagination.pageSize)" height="100%">
          <el-table-column width="50"></el-table-column>
          <el-table-column prop="backupTime" :label="$t('management.system.label.time')"></el-table-column>
          <el-table-column prop="backupDescription" :label="$t('management.system.label.description')"></el-table-column>
          <el-table-column prop="backupResult" :label="$t('management.system.label.result')">
            <template slot-scope="scope">
              {{ columnText(scope.row.backupResult, 'resultStatus') }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="220">
            <template slot-scope="scope">
              <el-button v-has="'recover'" class="el-button--blue" @click="clickRecovery(scope.row)">
                {{ $t('button.recovery') }}
              </el-button>
              <el-button v-has="'download'" class="el-button--blue" @click="clickDownload(scope.row)">
                {{ $t('button.download') }}
              </el-button>
              <el-button class="el-button--red" @click="clickDelete(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </section>
      <section class="table-footer">
        <el-pagination
          small
          background
          align="right"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          @size-change="backupConfigTableSizeChange"
          @current-change="backupConfigTableCurrentChange"
        ></el-pagination>
      </section>
    </section>
    <!-- 修改loading组件，增加淡入淡出效果 -->
    <transition name="fade">
      <div class="loading-mask" v-if="loading">
        <div class="el-loading-spinner">
          <svg class="circular" viewBox="25 25 50 50">
            <circle class="path" cx="50" cy="50" r="20" fill="none"></circle>
          </svg>
          <p class="el-loading-text">{{ '正在恢复系统配置，请稍候...' }}</p>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { validateIp } from '@util/validate'
import { prompt } from '@util/prompt'
import { resultStatus } from '@asset/js/code/option'
export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
    tableData: {
      type: Array,
      default() {
        return []
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateIp(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }

    return {
      form: {
        model: {
          cycle: 'week',
          timeValue: 1,
          time: '',
          ip: '',
          account: '',
          password: '',
          path: '',
        },
        rule: {
          timeValue: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          time: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          ip: [
            {
              required: true,
              validator: validatorIp,
              trigger: 'blur',
            },
          ],
          account: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          password: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          path: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
      options: {
        resultStatus: resultStatus,
      },
      upload: {
        header: {
          'Content-Type': 'multipart/form-data',
        },
        files: [],
      },
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model = this.formData
      }
    },
    clickSaveBackupConfig() {
      this.$refs.backupForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', this.handleParam())
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetBackupConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-reset')
      })
    },
    clickNowExecute() {
      this.$emit('on-execute')
    },
    clickDownload(row) {
      this.$emit('on-download', row)
    },
    clickDelete(row) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-delete', row)
      })
    },
    clickRecovery(row) {
      this.$emit('on-recovery', row)
    },
    handleParam() {
      return {
        backupCycle: this.form.model.cycle,
        backupTimeValue: this.form.model.timeValue,
        backupTime: this.form.model.time,
      }
    },
    backupConfigTableSizeChange(size) {
      this.pagination.pageSize = size
    },
    backupConfigTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
    },
    beforeUploadValidate(file) {
      if (this.upload.files.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isLic = suffix === 'zip'
        if (!isLic) {
          prompt({
            i18nCode: 'validate.upload.zip',
            type: 'warning',
          })
        }
        return isLic
      }
    },
    onUploadFileChange(file) {
      this.upload.files.push(file)
    },
    submitUploadFile(param) {
      if (param.file && this.upload.files.length > 0) {
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.$emit('on-upload', formData)
      }
    },
    clickUploadLicense() {
      this.upload.files = []
      this.$refs.uploadSnapshot.submit()
    },
  },
}
</script>

<style lang="scss" scoped>
.tab-context-wrapper {
  display: flex;
  flex-direction: column;
  height: 100% !important;
  .el-form {
    padding-top: 0 !important;
    .header-title {
      height: 30px;
      line-height: 30px;
      padding-left: 30px;
      color: #1873d7;
      font-weight: 600;
    }
    .backup-setting {
      display: flex;
      justify-content: flex-start;
      ::v-deep .el-form-item {
        .el-form-item__content {
          margin-left: 0 !important;
        }
      }
    }
  }
}
</style>
<style scoped="scoped" lang="scss">
.el-upload .el-button {
  margin-left: 10px;
}

/* 添加自定义loading样式 */
.loading-mask {
  position: absolute;
  z-index: 2000;
  background-color: rgba(0, 0, 0, 0.4);
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 添加淡入淡出过渡效果 */
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.el-loading-spinner {
  display: inline-block;
  vertical-align: middle;
}

.el-loading-spinner .circular {
  width: 42px;
  height: 42px;
  animation: loading-rotate 2s linear infinite;
}

.el-loading-spinner .path {
  animation: loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: #409eff;
  stroke-linecap: round;
}

.el-loading-text {
  color: #fff;
  margin: 3px 0;
  font-size: 14px;
}

@keyframes loading-rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}
</style>
