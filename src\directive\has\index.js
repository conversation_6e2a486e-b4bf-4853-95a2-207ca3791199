import Vue from 'vue'
import store from '@/store'

Vue.directive('has', {
  inserted(el, binding) {
    const btnPermission = (value) => {
      const list = store.getters.actions ? store.getters.actions : []
      for (const index in list) {
        if (list[index] === value) {
          return true
        }
      }
      return false
    }

    if (!btnPermission(binding['value'])) {
      el.parentNode.removeChild(el)
    }
  },
})
