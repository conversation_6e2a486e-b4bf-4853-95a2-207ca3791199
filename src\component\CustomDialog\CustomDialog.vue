<template>
  <el-dialog
    ref="elDialog"
    v-el-dialog-drag
    :v-loading="dialogLoading"
    append-to-body
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :width="dialogWidth"
    :data-id="id"
    class="custom-dialog-container"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <main v-if="show" v-loading="loading" class="custom-dialog-body">
      <section v-if="readonly" class="dialog-mask"></section>
      <slot></slot>
    </main>
    <footer slot="footer" class="custom-dialog-footer">
      <slot v-if="action" name="action">
        <el-button :loading="loading" @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
        <el-button :loading="loading" @click="submit">
          {{ $t('button.determine') }}
        </el-button>
      </slot>
    </footer>
  </el-dialog>
</template>

<script>
import elDialogDrag from '@/directive/el-dialog-drag'

export default {
  directives: { elDialogDrag },
  inheritAttrs: false,
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: 'dialog title',
    },
    width: {
      type: String,
      default: '600',
    },
    embed: {
      type: Boolean,
      default: true,
    },
    action: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogLoading: this.loading,
      dialogVisible: this.visible,
      id: 'dialog_' + new Date().getTime(),
      showBody: false,
      window: {
        width: '',
        height: '',
      },
    }
  },
  computed: {
    show() {
      return this.embed ? this.showBody : true
    },
    dialogWidth() {
      if (this.width.indexOf('px') > -1 || this.width.indexOf('%') > -1) {
        return this.width
      } else {
        return `${this.width}px`
      }
    },
  },
  watch: {
    dialogVisible(nVal) {
      if (!nVal) {
        this.dialogLoading = false
        this.$emit('on-close')
        setTimeout(() => {
          this.showBody = false
        }, 300)
      } else {
        this.showBody = true
      }
    },
    visible(nVal) {
      this.dialogVisible = nVal
    },
  },
  mounted() {
    this.windowResize()
  },
  methods: {
    windowResize() {
      this.window.width = document.body.clientWidth
      this.window.height = document.body.clientHeight

      window.addEventListener('resize', () => {
        this.window.width = document.body.clientWidth
        this.window.height = document.body.clientHeight
      })
    },
    cancel() {
      this.dialogVisible = false
    },
    submit() {
      this.dialogLoading = true
      this.$emit('on-submit')
    },
    end() {
      this.dialogLoading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: absolute;
  top: 55px;
  left: 0;
  z-index: 2000;
  width: calc(100% - 10px);
  height: calc(100% - 118px);
}
</style>
