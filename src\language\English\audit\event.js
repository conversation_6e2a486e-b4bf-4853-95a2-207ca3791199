export default {
  event: {
    event: 'Audit Event',
    eventFrom: 'Event Tracing',
    log: 'Original Log',
    id: 'Audit Event ID',
    eventName: 'Audit Event Name',
    level: 'Event Level',
    auditTypeName: 'Audit Type',
    policyId: 'Audit Policy ID',
    auditStrategyName: 'Audit Strategy',
    auditUserId: 'Auditor ID',
    auditUser: 'Auditor',
    auditIp: 'Audit Target IP',
    auditIpStart: 'Start IP',
    auditIpEnd: 'End IP',
    total: 'Total Events',
    createTime: 'Creation Time',
    createTimeStart: 'Start Creation Time',
    createTimeEnd: 'End Creation Time',
    updateTime: 'Update Time',
    secId: 'Security Event ID',
    allCheck: 'Select All',
    none: 'No Data',
    safe: {
      type2Name: 'Security Event Name',
      safeEventName: 'Security Event Type',
      eventTypeName: 'Security Event Category',
      eventLevelName: 'Event Level',
      srcIP: 'Source IP',
      srcPort: 'Source Port',
      dstIP: 'Destination IP',
      dstPort: 'Destination Port',
      dstAssetName: 'Destination Asset Name',
      count: 'Aggregation Count',
      aggrStartDate: 'Aggregation Start Time',
      fromDeviceTypeName: 'Source Device Type',
    },
    link: {
      eventName: 'Related Event Name',
      policyName: 'Policy Name',
      eventLevelName: 'Event Level',
      createDate: 'Creation Time',
      updateDate: 'Update Time',
      count: 'Count',
    },
    threat: {
      eventType: 'Threat Event Type',
      eventLevel: 'Event Level',
      eventDesc: 'Event Description',
      receiveTime: 'Receive Time',
      eventTime: 'Alert Time',
    },
    logList: {
      type2Name: 'Original Log Name',
      eventName: 'Event Type',
      eventCategoryName: 'Event Category',
      level: 'Level',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      dateTime: 'Time',
    },
    placeholder: {
      inputVal: 'Please enter keyword to search',
      eventName: 'Audit Event Name',
      level: 'Event Level',
      auditType: 'Audit Type',
      auditUserId: 'Auditor',
      policyId: 'Audit Policy',
    },
  },
}
