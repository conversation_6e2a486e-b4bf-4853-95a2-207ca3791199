<!--
 * @Description: 登录首页 - 主体
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="app-wrapper">
    <!--顶部导航-->
    <top-nav
      v-if="data.username !== ''"
      :username="data.username"
      :system-name="data.systemName"
      :alarm-amount="data.alarms"
      :system-alarm-amount="data.systemAlarms"
      @on-search="asyncMenuPath"
      @shrink-top-nav="shrinkTopNavMenuHeight"
      @stretch-top-nav="stretchTopNavMenuHeight"
    ></top-nav>
    <template>
      <audio ref="sysAlarmRef" :muted="audioMuted" src="@/asset/audio/red-warning.mp3"></audio>
    </template>
    <main v-if="data.menu.length > 0" class="main-container">
      <!--侧面菜单-->
      <side-menu
        class="menu-container"
        :style="{ width: width.menu }"
        :height="height"
        :menu-data="data.menu"
        :default-menu="data.activeMenuID"
        :width="width.menu"
        @get-action="setRouteAuthAction"
        @scale-menu="menuWidthChangeRelayout"
      ></side-menu>
      <!-- 渲染出口 -->
      <section
        ref="renderRouter"
        class="router-container"
        :style="{
          width: renderRouterWidth,
          'margin-left': width.menu + 10 + 'px',
        }"
      >
        <section class="router-wrapper">
          <transition name="slide-fade" mode="out-in" appear>
            <router-view v-if="isUpdateView" class="app-main-body"></router-view>
          </transition>
        </section>
      </section>
    </main>
  </div>
</template>

<script>
import { staticRouters } from '@/router'
import TopNav from './TheHeader/TheHeader.vue'
import SideMenu from './TheMenu/TheMenu.vue'
import { getTreePath, getFirstNode } from '@util/find'
import {
  queryMenuData,
  querySystemData,
  queryLicenseRemainDayData,
  queryAlarmAmountData,
  querySystemAlarmAmountData,
  querySystemNoticeConfig,
} from '@api/layout/layout-api'

export default {
  name: 'LayoutWrapper',
  components: {
    TopNav,
    SideMenu,
  },
  provide() {
    return {
      updateView: this.updateView,
      alarm: this.getAlarmAmount,
      sysAlarm: this.getSystemAlarmAmount,
    }
  },
  data() {
    return {
      height: '',
      width: {
        window: window.innerWidth,
        menu: 220,
      },
      data: {
        license: -1,
        alarms: 0,
        systemAlarms: 0,
        isSound: 0,
        menu: [],
        defaultMenuID: '',
        activeMenuID: '',
        defaultMenuPath: '',
        username: '',
        systemName: this.$store.getters.systemName,
        routeQuery: {},
      },
      audioMuted: false,
      isUpdateView: true,
    }
  },
  computed: {
    renderRouterWidth() {
      // 20为主体页面的margin
      const renderWidth = this.width.window - this.width.menu - 20
      return `${renderWidth}px`
    },
  },
  watch: {
    $route: {
      handler(newRouter) {
        const IDArray = getTreePath(newRouter.path, 'menuLocation', this.data.menu, true, 'menuId')
        this.loadBreadcrumb(newRouter.path)
        if (IDArray) {
          this.data.activeMenuID = IDArray[IDArray.length - 1]
        }
      },
      deep: true,
      immediate: true,
    },
    'data.license'(remain) {
      if (remain !== -10000 && remain <= 7) {
        // remain=-10000为未启用时间限制
        this.$notify({
          title: this.$t('tip.sweet'),
          type: 'warning',
          duration: 0,
          message: this.$t('layout.license', [remain]),
          position: 'bottom-right',
        })
      }
    },
    'data.systemAlarms'(count) {
      if (count > 0 && this.data.isSound === '1') {
        const notify = this.$notify({
          title: this.$t('tip.notice'),
          duration: 10000,
          dangerouslyUseHTMLString: true,
          message: "<span style='cursor: pointer'>" + this.$t('layout.systemAlarm', [count]) + '</span>',
          position: 'bottom-right',
        })
        notify.$el.querySelector('span').onclick = () => {
          this.$router.push({ path: '/alarm/system' })
        }
      }
    },
  },
  created() {
    this.initWebsocket()
  },
  mounted() {
    document.querySelector('title').innerText = ''
    this.preloadData()
    this.resize()
  },
  beforeDestroy() {
    this.$store.dispatch('websocket/close')
  },
  methods: {
    async preloadData() {
      await this.getSystem()
      await this.getMenu()
      await this.getAlarmAmount()
      await this.getSystemNoticeConfig()
      await this.getSystemAlarmAmount()
      this.getLicenseRemainDay()
    },
    asyncMenuPath(url) {
      const treePath = getTreePath(url, 'menuLocation', this.data.menu, true, 'menuId')
      this.data.activeMenuID = treePath[treePath.length - 1]
      this.loadBreadcrumb(url)
    },
    shrinkTopNavMenuHeight() {
      this.height = '100%'
    },
    stretchTopNavMenuHeight() {
      this.height = 'calc(100% - 70px)'
    },
    menuWidthChangeRelayout(width) {
      this.width.menu = width
    },
    loadBreadcrumb(path) {
      const actions = getTreePath(path, 'menuLocation', this.data.menu, true, 'actions')
      if (actions) {
        this.$store.dispatch('user/authAction', actions[actions.length - 1])
      }
    },
    setMenuDefaultValue(data) {
      const defaultMenu = () => {
        const firstNodesArray = getFirstNode(data),
          firstNode = firstNodesArray[firstNodesArray.length - 1]
        this.data.defaultMenuID = firstNode['menuId']
        this.data.defaultMenuPath = firstNode['menuLocation']
        this.$store.dispatch('user/authAction', firstNode['actions'])
      }
      if (this.data.defaultMenuID === '' || this.data.defaultMenuID === null) {
        defaultMenu()
      } else {
        const currentMenu = getTreePath(this.data.defaultMenuID, 'menuId', data)
        if (currentMenu) {
          this.data.defaultMenuPath = currentMenu[currentMenu.length - 1]['menuLocation']
          this.$store.dispatch('user/authAction', currentMenu[currentMenu.length - 1]['actions'])
        } else {
          defaultMenu()
        }
      }
    },
    setStoreHomePath(data) {
      this.data.routeQuery = this.$route.query
      if (this.$store.getters.mode === 'online') {
        const treePath = getTreePath(this.$route.path, 'menuLocation', data)
        if (this.$route.path === '' || !treePath) {
          this.$router?.replace({
            path: this.data.defaultMenuPath,
          })
          this.loadBreadcrumb(this.data.defaultMenuPath)
          this.data.activeMenuID = this.data.defaultMenuID
        } else {
          this.loadBreadcrumb(this.$route.path)
          this.data.activeMenuID = treePath[treePath.length - 1]['menuId']
          this.$router?.replace({
            path: treePath[treePath.length - 1]['menuLocation'],
            query: this.data.routeQuery,
          })
        }
      }
    },
    setRouteAuthAction(menu) {
      if (JSON.stringify(staticRouters).indexOf(menu.menuLocation) > -1) {
        this.$store.dispatch('user/updatePath', menu.menuLocation)
        this.$store.dispatch('user/authAction', menu.actions)
      }
    },
    updateView() {
      this.isUpdateView = false
      this.$nextTick(() => {
        this.isUpdateView = true
      })
    },
    resize() {
      window.addEventListener('resize', () => {
        this.width.window = window.innerWidth
      })
    },
    initWebsocket() {
      const serveBaseURL = process.env.NODE_ENV === 'production' ? window.GLOBAL_CONFIG.PRO_SERVE_API : window.GLOBAL_CONFIG.DEV_SERVE_API
      let url = ''
      if (serveBaseURL === '') {
        const wsProtocol = document.location.protocol === 'http' ? 'ws' : 'wss'
        url = `${wsProtocol}://${document.location.host}/websocket`
      } else {
        url = serveBaseURL?.replace('https', 'wss')?.replace('http', 'ws') + '/websocket'
      }
      this.$store.dispatch('websocket/init', {
        url,
        token: this.$store.getters.token,
      })
    },
    async getMenu() {
      await queryMenuData().then((res) => {
        this.data.menu = res
        // 如果没有设置默认页面就走第一个页面的
        this.setMenuDefaultValue(res)
        // 进入首页跳转
        this.setStoreHomePath(res)
      })
    },
    async getSystem() {
      await querySystemData().then((res) => {
        this.data.username = res.account
        this.data.defaultMenuID = res.defaultMenu
        this.data.systemName = res.systemName
        document.querySelector('title').innerText = res.systemName
        this.$store.dispatch('user/saveUserID', res.userId)

        // 设置系统语言
        if (res.localeLanguage) {
          const locale = res.localeLanguage
          if (this.$i18n.locale !== locale) {
            this.$i18n.locale = locale
          }
          sessionStorage.setItem('localeLanguage', locale)
        }
      })
    },
    getLicenseRemainDay() {
      queryLicenseRemainDayData().then((res) => {
        this.data.license = res
      })
    },
    getAlarmAmount() {
      queryAlarmAmountData().then((res) => {
        this.data.alarms = res
      })
    },
    async getSystemNoticeConfig() {
      await querySystemNoticeConfig().then((res) => {
        this.data.isSound = res.isSound
      })
    },
    async getSystemAlarmAmount() {
      if (this.data.isSound === '1') {
        await querySystemAlarmAmountData().then((res) => {
          this.data.systemAlarms = res
          if (this.data.systemAlarms > 0) {
            this.playaudio()
          }
        })
      }
    },
    playaudio() {
      this.audioMuted = true
      this.$refs.sysAlarmRef.play()
    },
  },
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;

  .main-container {
    flex: 1;
    overflow: auto;

    .menu-container {
      position: fixed;
      height: calc(100% - 60px);
      overflow-y: auto;
    }

    .router-container {
      height: calc(100% - 20px);
      margin: 10px;
      transition: all 0.5s;

      .router-wrapper {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .app-main-body {
        position: relative;
        height: 100%;
      }
    }
  }

  .router-container {
    @include theme('background-color', main-body-bg-color);
  }
}

.slide-fade-enter-active {
  transition: all 0.5s ease;
}

.slide-fade-leave-active {
  transition: all 0.5s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter,
.slide-fade-leave-to {
  transform: translateX(10px);
  opacity: 0;
}
</style>
