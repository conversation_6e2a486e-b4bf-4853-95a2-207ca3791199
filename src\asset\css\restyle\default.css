head {
  display: none;
}

body {
  margin: 8px;
  line-height: 1.12;
}

button,
textarea,
input,
object,
select {
  display: inline-block;
}

ol,
ul,
dir,
menu,
dd {
  margin-left: 40px;
}

i,
cite,
em,
var,
address {
  font-style: italic;
}

a:hover {
  color: blue;
}

/*块级元素*/
html,
body,
div,
ol,
p,
ul,
h1,
h2,
h3,
h4,
h5,
h6,
address,
blockquote,
form,
dd,
dl,
dt,
fieldset,
frame,
frameset,
noframes,
center,
dir,
hr,
menu,
pre {
  display: block;
}

/*列表元素*/
li {
  display: list-item;
}

ol {
  list-style-type: decimal;
}

ol ul,
ul ol,
ul ul,
ol ol {
  margin-top: 0;
  margin-bottom: 0;
}

/*标题*/
h1,
h2,
h3,
h4,
h5,
h6 {
  margin-inline-start: 0px;
  margin-inline-end: 0px;
}

h1 {
  font-size: 2em;
  margin: 0.67em 0;
}

h2 {
  margin-block-start: 0.83em;
  margin-block-end: 0.83em;
  font-size: 1.5em;
  margin: 0.75em 0;
}

h3 {
  font-size: 1.17em;
  margin: 0.83em 0;
}

h4,
p,
blockquote,
ul,
fieldset,
form,
ol,
dl,
dir,
menu {
  margin: 1.12em 0;
  margin-block-start: 1.33em;
  margin-block-end: 1.33em;
}

h5 {
  font-size: 0.83em;
  margin: 1.5em 0;
}

h6 {
  font-size: 0.75em;
  margin: 1.67em 0;
}

h1,
h2,
h3,
h4,
h5,
h6,
b,
strong {
  font-weight: bolder;
}

/*伪类*/
br:before {
  content: '\A';
}

:before,
:after {
  white-space: pre-line;
}

:link,
:visited {
  text-decoration: underline;
}

:focus {
  outline: thin dotted invert;
}

/*表格*/
table {
  display: table;
}

tr {
  display: table-row;
}

thead {
  display: table-header-group;
}

tbody {
  display: table-row-group;
}

tfoot {
  display: table-footer-group;
}

col {
  display: table-column;
}

colgroup {
  display: table-column-group;
}

td,
th {
  display: table-cell;
}

caption {
  display: table-caption;
}

th {
  font-weight: bolder;
  text-align: center;
}

caption {
  text-align: center;
}

table {
  border-spacing: 2px;
}

thead,
tbody,
tfoot {
  vertical-align: middle;
}

td,
th {
  vertical-align: inherit;
}

/*其它元素*/
blockquote {
  margin-left: 40px;
  margin-right: 40px;
}

pre,
tt,
code,
kbd,
samp {
  font-family: monospace;
}

pre {
  white-space: pre;
}

big {
  font-size: 1.17em;
}

small,
sub,
sup {
  font-size: 0.83em;
}

sub {
  vertical-align: sub;
}

sup {
  vertical-align: super;
}

s,
strike,
del {
  text-decoration: line-through;
}

hr {
  border: 1px inset;
}

u,
ins {
  text-decoration: underline;
}

center {
  text-align: center;
}

abbr,
acronym {
  font-variant: small-caps;
  letter-spacing: 0.1em;
}

/*定义BDO元素当其属性为DIR="ltr/rtl"时的默认文本读写显示顺序*/
bdo[DIR='ltr'] {
  direction: ltr;
  unicode-bidi: bidi-override;
}

bdo[DIR='rtl'] {
  direction: rtl;
  unicode-bidi: bidi-override;
}

/*定义任何元素当其属性为DIR="rtl/rtl"时的默认文本读写显示顺序*/
*[DIR='ltr'] {
  direction: ltr;
  unicode-bidi: embed;
}

*[DIR='rtl'] {
  direction: rtl;
  unicode-bidi: embed;
}

/*定义标题和列表默认的打印样式*/
@media print {
  h1 {
    page-break-before: always;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    page-break-after: avoid;
  }

  ul,
  ol,
  dl {
    page-break-before: avoid;
  }
}
