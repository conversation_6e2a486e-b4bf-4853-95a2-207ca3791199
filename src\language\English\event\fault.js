export default {
  fault: {
    title: 'Fault Event',
    faultId: 'Event ID',
    faultName: 'Event Name',
    currentStatus: 'Current Status',
    faultStatus: 'Status',
    faultClass: 'Event Type',
    faultClassName: 'Event Type',
    faultLevel: 'Event Level',
    edName: 'Asset Name',
    monitorName: 'Monitor Name',
    domaName: 'Domain Area',
    enterDate: 'Occurrence Time',
    updateDate: 'Update Time',
    recoveryDate: 'Recovery Time',
    faultModule: 'Event Description',
    faultSolution: 'Solution',
    basicDetail: 'Basic Details',
    faultDetail: 'Historical Faults',
    handleSuggest: 'Handling Advice',
    handleState: 'Handling Status',
    state: {
      normal: 'Normal',
      abnormal: 'Abnormal',
      handled: 'Handled',
      unhandle: 'Unhandled',
    },
  },
}
