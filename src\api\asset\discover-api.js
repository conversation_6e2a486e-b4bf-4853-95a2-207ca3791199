import request from '@util/request'

// 查询资产发现列表
export function queryTableData(obj) {
  return request({
    url: '/assetdiscover/assets',
    method: 'get',
    params: obj || {},
  })
}

// 查询资产类型下拉框
export function queryType() {
  return request({
    url: '/assetdiscover/combo/types',
    method: 'get',
  })
}

// 查询网段下拉框
export function queryNet() {
  return request({
    url: '/assetdiscover/combo/networks',
    method: 'get',
  })
}

// 转化资产
export function addAsset(obj) {
  return request({
    url: '/assetdiscover/asset',
    method: 'put',
    data: obj || {},
  })
}

// 批量转化资产
export function addAssets(obj) {
  return request({
    url: '/assetdiscover/assets',
    method: 'put',
    data: obj || {},
  })
}

// (批量)删除资产
export function deleteAssets(ids) {
  return request({
    url: `/assetdiscover/asset/${ids}`,
    method: 'delete',
  })
}

// 查询资产发现任务列表
export function queryData(obj) {
  return request({
    url: '/assetdiscover/tasks',
    method: 'get',
    params: obj || {},
  })
}

// 添加发现任务
export function addTasks(obj) {
  return request({
    url: '/assetdiscover/task',
    method: 'post',
    data: obj || {},
  })
}

// 立即发现任务
export function nowTasks(id) {
  return request({
    url: `/assetdiscover/task/startup/${id}`,
    method: 'get',
  })
}

// (批量)删除任务
export function deleteTasks(ids) {
  return request({
    url: `/assetdiscover/task/${ids}`,
    method: 'delete',
  })
}

// 查询资产隶属区域
export function queryAssetDomain() {
  return request({
    url: '/assetdiscover/combo/domains',
    method: 'get',
  })
}
