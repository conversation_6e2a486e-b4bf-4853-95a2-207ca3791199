<!--
 * @Description: 监控器 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-1
 * @Editor:
 * @EditDate: 2021-08-1
-->
<template>
  <section class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model.trim="filterCondition.form.fuzzyField"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('monitor.management.props.monitorName')])"
            prefix-icon="soc-icon-search"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'add'" @click="clickAdd">
          {{ $t('button.add') }}
        </el-button>
        <el-dropdown placement="bottom" trigger="click" @command="handleCommand">
          <el-button type="primary">
            {{ $t('button.batchText') }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-has="'update'" command="stop">
              {{ $t('button.batch.stop') }}
            </el-dropdown-item>
            <el-dropdown-item v-has="'delete'" command="delete" style="color: #e32a0c;">
              {{ $t('button.batch.delete') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model.trim="filterCondition.form.monitorName"
                clearable
                :placeholder="$t('monitor.management.props.monitorName')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.agentId"
                clearable
                :placeholder="$t('monitor.management.props.agentId')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.agent" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-cascader
                v-model="filterCondition.form.monitorType"
                :placeholder="$t('monitor.management.props.monitorType')"
                :options="monitorTypeOption"
                :props="{ expandTrigger: 'hover' }"
                collapse-tags
                clearable
                @change="changeQueryCondition"
              ></el-cascader>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.monitorEnabled"
                clearable
                :placeholder="$t('monitor.management.props.monitorEnabled')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="4" align="right">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button icon="el-icon-arrow-up" @click="clickUpButton"></el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </section>
</template>

<script>
import { debounce } from '@util/effect'
import { queryAgentOptionData } from '@api/monitor/management-api'

export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
    monitorTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      debounce: null,
      options: {
        agent: [],
        status: [
          { value: '1', label: this.$t('monitor.management.status.on') },
          { value: '0', label: this.$t('monitor.management.status.off') },
        ],
      },
    }
  },
  watch: {
    condition(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:condition', newCondition)
    },
  },
  mounted() {
    this.initLoadData()
  },
  methods: {
    initLoadData() {
      this.getAgentOption()
      this.initDebounceQuery()
    },
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        monitorName: '',
        agentId: '',
        monitorType: '',
        monitorEnabled: '',
      }
      this.changeQueryCondition()
    },
    clickAdd() {
      this.$emit('on-add')
    },
    handleCommand(command) {
      this.$emit('on-command', command)
    },
    getAgentOption() {
      queryAgentOptionData().then((res) => {
        this.options.agent = res
      })
    },
  },
}
</script>
