<!--
 * @Description: 资源管理
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <section class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('management.resource.infoItem.resourceName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQuery('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="handleAdd">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="handleBatchDel">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.resourceToken"
                  clearable
                  :placeholder="$t('management.resource.placeholder.resourceToken')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.resourceName"
                  clearable
                  :placeholder="$t('management.resource.placeholder.resourceName')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.resourceStatus"
                  :placeholder="$t('management.resource.placeholder.resourceStatus')"
                  clearable
                  @change="inputQuery('e')"
                >
                  <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.authority"
                  :placeholder="$t('management.resource.placeholder.authority')"
                  clearable
                  @change="inputQuery('e')"
                >
                  <el-option v-for="item in authorityList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10">
                <el-input
                  v-model.trim="queryInput.resourceDescription"
                  clearable
                  :placeholder="$t('management.resource.placeholder.resourceDescription')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="4" align="right" :offset="10">
                <el-button v-has="'query'" @click="inputQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button icon="el-icon-arrow-up" @click="seniorQuery"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </section>

    <main class="table-body">
      <el-table
        ref="resourceTable"
        v-loading="loading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        highlight-current-row
        fit
        size="mini"
        height="100%"
        @selection-change="selectsChange"
        @current-change="handleRowChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          prop="resourceToken"
          :label="$t('management.resource.infoItem.resourceToken')"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="resourceName"
          :label="$t('management.resource.infoItem.resourceName')"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          prop="resourceStatusText"
          :label="$t('management.resource.infoItem.resourceStatus')"
          sortable
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="authorityText" :label="$t('management.resource.infoItem.authority')" sortable show-overflow-tooltip></el-table-column>
        <el-table-column
          prop="resourceDescription"
          :label="$t('management.resource.infoItem.resourceDescription')"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column fixed="right" width="200">
          <template slot-scope="scope">
            <el-button v-has="'update'" class="el-button--blue" @click="handleUpdate(scope.row)">
              {{ $t('button.update') }}
            </el-button>
            <el-button v-has="'delete'" class="el-button--red" @click="handleDelete(scope.row)">
              {{ $t('button.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        small
        background
        align="right"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </footer>
    <section class="dialog">
      <!-- 添加、修改对话框 -->
      <au-dialog :visible.sync="dialog.visible" :title="dialog.title" :form="resourceForm" :width="'60%'" @on-submit="handleAddSubmit"></au-dialog>
    </section>
  </div>
</template>

<script>
import AuDialog from './TheResourceAuDialog'
import { delResource, getResourcesList, getResourceDetail, getActionToken, addResource, updResource } from '@api/management/resource-api'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'

export default {
  name: 'ManagementResource',
  components: {
    AuDialog,
  },
  data() {
    return {
      loading: false,
      isShow: false,
      queryInput: {
        fuzzyField: '',
        resourceToken: '',
        resourceName: '',
        resourceStatus: '',
        authority: '',
        resourceDescription: '',
      },
      statusList: [
        {
          value: '0',
          label: this.$t('management.resource.codeList.resourceStatus.show'),
        },
        {
          value: '1',
          label: this.$t('management.resource.codeList.resourceStatus.hide'),
        },
      ],
      authorityList: [
        {
          value: '0',
          label: this.$t('management.resource.authority.system'),
        },
        {
          value: '1',
          label: this.$t('management.resource.authority.running'),
        },
        {
          value: '2',
          label: this.$t('management.resource.authority.audit'),
        },
      ],
      tableData: [],
      multipleSelection: [],
      pagination: {
        visible: true, // 防止页码错误
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
      },
      authority: [],
      resourceForm: {
        actionTokens: [],
        rules: {
          resourceToken: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          resourceName: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          resourceStatus: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          authorities: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
        model: {
          pageSize: '',
          pageNum: '',
          resourceToken: '',
          resourceName: '',
          resourceStatus: '',
          authorities: [],
          authority: '',
          resourceDescription: '',
          actions: [],
          status: '',
        },
        info: {
          resourceId: {
            key: 'resourceId',
            label: this.$t('management.resource.infoItem.resourceId'),
            value: '',
          },
          resourceToken: {
            key: 'resourceToken',
            label: this.$t('management.resource.infoItem.resourceToken'),
            value: '',
          },
          resourceName: {
            key: 'resourceName',
            label: this.$t('management.resource.infoItem.resourceName'),
            value: '',
          },
          resourceStatusText: {
            key: 'resourceStatusText',
            label: this.$t('management.resource.infoItem.resourceStatusText'),
            value: '',
          },
          resourceDescription: {
            key: 'resourceDescription',
            label: this.$t('management.resource.infoItem.resourceDescription'),
            value: '',
          },
          authorityText: {
            key: 'authorityText',
            label: this.$t('management.resource.infoItem.authorityText'),
            value: '',
          },
          actions: {
            key: 'actions',
            label: this.$t('management.resource.infoItem.actions'),
            value: '',
          },
          authority: [
            {
              value: 0,
              label: this.$t('management.resource.authority.system'),
            },
            {
              value: 1,
              label: this.$t('management.resource.authority.running'),
            },
            {
              value: 2,
              label: this.$t('management.resource.authority.audit'),
            },
          ],
        },
      },
      filter: {
        resourceToken: '',
        resourceName: '',
        resourceStatus: '',
        authority: '',
        resourceDescription: '',
      },
      queryData: {
        visible: false,
        filter: [],
        title: this.$t('dialog.title.query', [this.$t('management.resource.header.dialogTitle')]),
        form: {
          model: {
            resourceToken: '',
            resourceName: '',
            resourceStatus: '',
            authority: '',
            resourceDescription: '',
          },
          info: {
            resourceStatus: {
              key: 'resourceStatus',
              label: this.$t('management.resource.infoItem.resourceStatus'),
              value: '',
            },
            authority: {
              key: 'authority',
              label: this.$t('management.resource.infoItem.authority'),
              value: '',
            },
            resourceName: {
              key: 'resourceName',
              label: this.$t('management.resource.infoItem.resourceName'),
              value: '',
            },
            resourceToken: {
              key: 'resourceToken',
              label: this.$t('management.resource.infoItem.resourceToken'),
              value: '',
            },
            resourceDescription: {
              key: 'resourceDescription',
              label: this.$t('management.resource.infoItem.resourceDescription'),
              value: '',
            },
          },
          combo: {
            authority: [
              {
                value: 0,
                label: this.$t('management.resource.authority.system'),
              },
              {
                value: 1,
                label: this.$t('management.resource.authority.running'),
              },
              {
                value: 2,
                label: this.$t('management.resource.authority.audit'),
              },
            ],
          },
        },
      },
      detailData: {
        model: {
          pageSize: '',
          pageNum: '',
          resourceId: '',
          resourceToken: '',
          resourceName: '',
          resourceStatusText: '',
          authorityText: '',
          resourceDescription: '',
          actions: [],
        },
        info: {
          resourceId: {
            key: 'resourceId',
            label: this.$t('management.resource.infoItem.resourceId'),
            value: '',
          },
          resourceToken: {
            key: 'resourceToken',
            label: this.$t('management.resource.infoItem.resourceToken'),
            value: '',
          },
          resourceName: {
            key: 'resourceName',
            label: this.$t('management.resource.infoItem.resourceName'),
            value: '',
          },
          resourceStatusText: {
            key: 'resourceStatusText',
            label: this.$t('management.resource.infoItem.resourceStatusText'),
            value: '',
          },
          resourceDescription: {
            key: 'resourceDescription',
            label: this.$t('management.resource.infoItem.resourceDescription'),
            value: '',
          },
          authorityText: {
            key: 'authorityText',
            label: this.$t('management.resource.infoItem.authorityText'),
            value: '',
          },
          actions: {
            key: 'actions',
            label: this.$t('management.resource.infoItem.actions'),
            value: '',
          },
        },
      },
      dialog: {
        visible: false,
        status: '',
        title: '',
        inputReadonly: false,
      },
      actionForm: {
        rules: {
          actionName: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          actionToken: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          actionStatus: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
        data: {
          actionId: '',
          actionName: '',
          actionToken: '',
          actionStatus: '',
          actionStatusText: '',
          actionDescription: '',
        },
      },
      actionTokens: [],
      actionDialog: {
        visible: false,
        status: '',
        title: '',
        inputReadonly: false,
      },
      queryDebounce: null,
    }
  },
  mounted() {
    this.getListData()
    // 隶属权限下拉数据
    this.authority = [
      {
        value: 0,
        label: this.$t('management.resource.authority.system'),
      },
      {
        value: 1,
        label: this.$t('management.resource.authority.running'),
      },
      {
        value: 2,
        label: this.$t('management.resource.authority.audit'),
      },
    ]
    // 功能标识下拉数据
    this.getActionTokens()
    this.initDebounce()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const params = {
          ...this.queryInput,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
        }
        this.getListData(params)
      }, 500)
    },
    // 加载列表数据
    getListData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.loading = true
      this.pagination.visible = false
      getResourcesList(params).then((res) => {
        this.loading = false
        this.tableData = res.rows
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.total = res.total
        this.pagination.visible = true
      })
    },
    // 多选弹入数组操作
    selectsChange(select) {
      this.multipleSelection = select
    },
    // 选中当前行
    handleRowChange(val) {
      this.pagination.currentRow = val
    },
    handleSizeChange(size) {
      this.pagination.pageSize = size
      this.inputQuery('e')
    },
    handleCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQuery()
    },
    // 点击添加操作并弹出对话框操作
    handleAdd() {
      this.dialog = {
        visible: true,
        status: 'add',
        title: this.$t('dialog.title.add', [this.$t('management.resource.header.dialogTitle')]),
        inputReadonly: false,
      }
      this.resourceForm.model = {
        resourceToken: '',
        resourceName: '',
        resourceStatus: '',
        authorities: [],
        authority: '',
        resourceDescription: '',
        actions: [],
        status: 'add',
      }
    },
    // 点击删除操作
    handleDelete(row) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      })
        .then(() => {
          const para = { id: row.resourceId }
          delResource(para).then((res) => {
            this.$message({
              message: this.$t('tip.delete.success'),
              type: 'success',
              center: true,
            })
            this.inputQuery()
          })
        })
        .catch((e) => {
          console.log(e)
        })
    },
    // 点击批量删除操作
    handleBatchDel() {
      if (this.multipleSelection.length > 0) {
        this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        })
          .then(() => {
            const resourceId = this.multipleSelection.map((item) => item.resourceId).toString()
            const param = { id: resourceId }
            delResource(param).then((res) => {
              this.$message({
                message: this.$t('tip.operate.success', [this.$t('button.batch.delete')]),
                type: 'success',
                center: true,
              })
              this.pagination.pageNum = this.pagination.pageNum > 1 ? this.pagination.pageNum - 1 : 1
              this.inputQuery()
            })
          })
          .catch((e) => {
            console.log(e)
          })
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击修改并弹出对话框操作
    handleUpdate(row) {
      this.dialog = {
        visible: true,
        status: 'update',
        title: this.$t('dialog.title.update', [this.$t('management.resource.header.dialogTitle')]),
        inputReadonly: false,
      }
      this.resourceForm.model.status = 'update'
      this.handleRowChange(row)
      this.getUpdDetail(row.resourceId)
    },
    // 加载资源详情
    getUpdDetail(resourceId) {
      const param = { id: resourceId }
      getResourceDetail(param).then((res) => {
        this.resourceForm.model = Object.assign({}, res)
      })
    },
    // 清空搜索表单
    clearDialogFormModel() {
      this.queryData.form.model = {
        resourceToken: '',
        resourceName: '',
        resourceStatus: '',
        authority: '',
        resourceDescription: '',
      }
    },
    // 获取功能标识
    getActionTokens() {
      getActionToken().then((res) => {
        this.actionTokens = res
        this.resourceForm.actionTokens = res
      })
    },
    // 提交表单
    handleAddSubmit() {
      if (this.dialog.status === 'add') {
        const param = Object.assign(
          {},
          {
            authority: this.resourceForm.model.authorities.join(','),
            resourceDescription: this.resourceForm.model.resourceDescription,
            resourceName: this.resourceForm.model.resourceName,
            resourceStatus: this.resourceForm.model.resourceStatus,
            resourceToken: this.resourceForm.model.resourceToken,
            actions: this.resourceForm.model.actions,
          }
        )
        // const param = Object.assign({}, this.resourceForm.model);
        // param.authority = param.authorities.join(",");
        addResource(param).then((res) => {
          if (res === 1) {
            this.getListData()
            this.dialog.visible = false
            this.$message({
              message: this.$t('tip.add.success'),
              type: 'success',
              center: true,
            })
          } else if (res === 2) {
            this.$message({
              message: this.$t('management.resource.header.tipInfo.resourceTokenRepeat'),
              type: 'info',
            })
          } else if (res === 3) {
            this.$message({
              message: this.$t('management.resource.header.tipInfo.actionTokenRepeat'),
              type: 'info',
            })
          }
        })
      } else {
        const param = Object.assign({}, this.resourceForm.model)
        param.authority = param.authorities.join(',')
        updResource(param).then((res) => {
          this.inputQuery()
          this.$message({
            message: this.$t('tip.update.success'),
            type: 'success',
            center: true,
          })
        })
        this.dialog.visible = false
      }
    },
    // 输入框搜索方法
    inputQuery(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    // 清空搜索条件
    clearQuery() {
      this.queryInput = {
        fuzzyField: '',
        resourceToken: '',
        resourceName: '',
        resourceStatus: '',
        authority: '',
        resourceDescription: '',
      }
      this.pagination.pageNum = 1
    },
    // 重置并查询列表
    resetQuery() {
      this.clearQuery()
      this.queryDebounce()
    },
  },
}
</script>
