<!--
 * @Description: 报表任务 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemp" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <template v-if="Object.keys(formData).length === 0">
      <el-form ref="formTemp" :model="form.model" :rules="form.rule" label-width="25%">
        <el-form-item v-if="recipient === ''" :label="$t('report.task.label.name')" prop="taskName">
          <el-input v-model.trim="form.model.taskName" maxlength="256" class="width-small"></el-input>
        </el-form-item>
        <el-form-item v-if="recipient === ''" :label="$t('report.task.label.instance')" prop="taskInstance">
          <el-select v-model="form.model.taskInstance" clearable filterable class="width-small">
            <el-option v-for="item in option.taskInstance" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="recipient === ''" :label="$t('report.task.label.type')" prop="taskType">
          <el-select v-model="form.model.taskType" class="width-small" clearable @change="form.model.taskTimeValue = ''">
            <el-option v-for="item in option.taskType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="recipient === '' && form.model.taskType !== ''" :label="$t('time.option.startDate')" prop="taskStartDate">
          <el-date-picker
            v-model="form.model.taskStartDate"
            clearable
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            :placeholder="$t('time.option.startDate')"
            :picker-options="disablePickerOption.taskStartDate"
            class="width-small"
            @change="changeTaskStartDate"
          ></el-date-picker>
        </el-form-item>
        <el-form-item v-if="recipient === '' && form.model.taskType !== ''" :label="$t('time.option.startTime')" prop="taskStartTime">
          <el-time-picker
            v-model="form.model.taskStartTime"
            type="time"
            clearable
            value-format="HH:mm:ss"
            format="HH:mm:ss"
            :placeholder="$t('time.option.startTime')"
            class="width-small"
          ></el-time-picker>
        </el-form-item>
        <el-form-item v-if="recipient === '' && form.model.taskType === 'month'" :label="$t('time.cycle.month')" prop="taskTimeValue">
          <el-select v-model="form.model.taskTimeValue" clearable class="width-small">
            <el-option v-for="item in 31" :key="item" :label="item + $t('time.unit.day')" :value="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="recipient === '' && form.model.taskType === 'week'" :label="$t('time.cycle.week')" prop="taskTimeValue">
          <el-select v-model="form.model.taskTimeValue" clearable class="width-small">
            <el-option v-for="item in option.week" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="recipient === ''" :label="$t('report.task.label.sendType')" prop="taskSendType">
          <el-select v-model="form.model.taskSendType" clearable class="width-small">
            <el-option v-for="item in option.sendType" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('report.task.label.recipient')" prop="taskRecipient">
          <el-input v-model="form.model.taskRecipient" class="width-small" maxlength="64"></el-input>
        </el-form-item>
        <el-form-item v-if="recipient === ''" :label="$t('report.task.label.description')" prop="taskDescription">
          <el-input v-model="form.model.taskDescription" type="textarea" class="width-small" maxlength="1024"></el-input>
        </el-form-item>
      </el-form>
    </template>
    <template v-else>
      <el-form ref="formTemp" label-width="25%">
        <el-form-item :label="$t('report.task.label.name')">
          {{ formData.taskName }}
        </el-form-item>
        <el-form-item :label="$t('report.task.label.instance')">
          {{ formData.taskInstance }}
        </el-form-item>
        <el-form-item :label="$t('report.task.label.status')">
          {{ formData.taskStatus }}
        </el-form-item>
        <el-form-item :label="$t('time.option.startTime')">
          {{ formData.taskStartDate + ' ' + formData.taskStartTime }}
        </el-form-item>
        <el-form-item :label="$t('report.task.label.type')">
          {{ formData.taskType }}
        </el-form-item>
        <el-form-item :label="$t('time.option.executeTime')">
          {{ formData.taskStartTime }}
        </el-form-item>
        <el-form-item :label="$t('report.task.label.sendType')">
          {{ formData.taskSendType }}
        </el-form-item>
        <el-form-item :label="$t('report.task.label.recipient')">
          {{ formData.taskRecipient }}
        </el-form-item>
        <el-form-item :label="$t('report.task.label.description')">
          {{ formData.taskDescription }}
        </el-form-item>
      </el-form>
      <template slot="action">
        <fragment></fragment>
      </template>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateEmail } from '@util/validate'
import { queryTaskInstanceData } from '@api/report/task-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '35%',
    },
    recipient: {
      type: String,
      default: '',
    },
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: this.visible,
      disablePickerOption: {
        taskStartDate: {
          disabledDate(date) {
            return date.getTime() <= Date.now() - 24 * 60 * 60 * 1000
          },
        },
      },
      option: {
        taskInstance: [],
        taskType: [
          {
            value: 'day',
            label: this.$t('time.cycle.day'),
          },
          {
            value: 'week',
            label: this.$t('time.cycle.week'),
          },
          {
            value: 'month',
            label: this.$t('time.cycle.month'),
          },
        ],
        week: [
          {
            label: this.$t('time.week.mon'),
            value: 1,
          },
          {
            label: this.$t('time.week.tue'),
            value: 2,
          },
          {
            label: this.$t('time.week.wed'),
            value: 3,
          },
          {
            label: this.$t('time.week.thu'),
            value: 4,
          },
          {
            label: this.$t('time.week.fri'),
            value: 5,
          },
          {
            label: this.$t('time.week.sat'),
            value: 6,
          },
          {
            label: this.$t('time.week.sun'),
            value: 0,
          },
        ],
        sendType: [
          {
            label: 'PDF',
            value: 'pdf',
          },
          {
            label: 'Word',
            value: 'word',
          },
          {
            label: 'Excel',
            value: 'excel',
          },
        ],
      },
      form: {
        model: {
          taskName: '',
          taskInstance: '',
          taskType: '',
          taskStartDate: '',
          taskStartTime: '',
          taskTimeValue: '',
          updateDate: '',
          taskSendType: '',
          taskRecipient: '',
          taskDescription: '',
        },
        rule: {
          taskName: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          taskInstance: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          taskStartDate: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          taskStartTime: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          taskTimeValue: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          taskType: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          taskSendType: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          taskRecipient: [
            {
              required: true,
              validator: validatorEmail,
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
    recipient(recipient) {
      this.form.model.taskRecipient = recipient
    },
  },
  mounted() {
    this.getTaskInstance()
  },
  methods: {
    clickCancelDialog() {
      this.$refs.formTemp.resetFields()
      this.$refs.dialogTemp.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemp.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', Object.assign({}, this.form.model))
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemp.end()
    },
    changeTaskStartDate(date) {
      this.form.model.taskStartDate = date
    },
    getTaskInstance() {
      queryTaskInstanceData().then((res) => {
        this.option.taskInstance = res
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog {
  ::v-deep &__body {
    overflow: hidden;
  }
}
</style>
