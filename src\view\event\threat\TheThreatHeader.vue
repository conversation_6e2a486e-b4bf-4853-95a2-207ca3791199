<!--
 * @Description: 威胁事件 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.high" v-has="'query'" class="table-header-search-input">
          <el-input
            v-model="filterCondition.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('event.threat.eventDesc')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.high" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.high ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'update'" @click="clickStrategyConfig">
          {{ $t('button.strategyConfig') }}
        </el-button>
        <el-button v-has="'download'" v-debounce="clickDownload">
          {{ $t('button.export.default') }}
        </el-button>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <section v-show="filterCondition.high" class="table-header-query">
          <el-row :gutter="24">
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.eventLevel"
                clearable
                :placeholder="$t('event.threat.eventLevel')"
                @change="changeQueryCondition"
              >
                <el-option v-for="(i, k) in levelList" :key="k" :label="i.label" :value="i.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <el-date-picker
                v-model="filterCondition.form.eventTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('event.threat.startTime')"
                :end-placeholder="$t('event.threat.endTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
            <el-col :span="4" :offset="5" align="right">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button @click="clickShrinkAdvancedQuery">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </section>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
import { eventLevel } from '@asset/js/code/option'

export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      debounce: null,
      levelList: eventLevel,
    }
  },
  watch: {
    condition(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:condition', newCondition)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.high = !this.filterCondition.high
      this.resetQuery()
    },
    clickShrinkAdvancedQuery() {
      this.filterCondition.high = false
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.fuzzyField = ''
      this.filterCondition.form = {
        eventLevel: '',
        eventTime: '',
      }
      this.changeQueryCondition()
    },
    clickStrategyConfig() {
      this.$emit('on-strategy-config')
    },
    clickDownload() {
      this.$emit('on-download')
    },
  },
}
</script>
