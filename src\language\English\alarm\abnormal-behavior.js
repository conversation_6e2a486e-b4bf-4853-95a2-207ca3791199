export default {
  abnormalBehavior: {
    title: 'Abnormal Behavior Alarm',
    label: {
      infoSystemName: 'Information System Name',
      infoSystemIp: 'Information System IP',
      action: 'Behavior Name',
      role: 'Intruder',
      occurTime: 'Occurrence Time',
      updateTime: 'Update Time',
      anomalyType: 'Anomaly Type',
      status: 'Status',
      total: 'Count',
      desc: 'Description',
      raw: 'Log Original Text',
      occurStartTime: 'Occurrence Start Time',
      occurEndTime: 'Occurrence End Time',
      updateStartTime: 'Update Start Time',
      updateEndTime: 'Update End Time',
    },
  },
}
