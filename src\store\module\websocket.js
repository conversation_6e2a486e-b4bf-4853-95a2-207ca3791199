const state = {
  websocket: null,
  status: false,
  assetDiscover: [],
  managementNetwork: {},
}

const mutations = {
  WEBSOCKET_INIT(state, option) {
    state.status = false
    state.websocket = new WebSocket(option.url, [option.token])
    state.websocket.onopen = () => {
      console.warn('WebSocket is open')
      state.status = true
    }
    // 1-订阅 2-退订 3-客户端主动向服务端获取数据 4-服务端主动向客户端发数据
    state.websocket.onmessage = (e) => {
      const data = JSON.parse(e.data)
      if (data.topic === 'heart') {
        state.websocket.send(
          JSON.stringify({
            topic: 'heart',
            action: 3,
            message: 1,
          })
        )
      }

      if (data.topic === 'asset_discover') {
        state.assetDiscover = data
      }

      if (data.topic === 'check_network_card_push') {
        state.managementNetwork = data
      }
    }

    state.websocket.onerror = () => {
      console.error('WebSocket is error')
      state.status = false
    }
    state.websocket.onclose = () => {
      console.warn('WebSocket is close')
      state.status = false
    }
  },
  WEBSOCKET_SEND(state, data) {
    state.websocket.send(JSON.stringify(data))
  },
  WEBSOCKET_ClOSE(state) {
    state.status = false
    state.websocket.close()
  },
}

const actions = {
  init({ commit }, option) {
    commit('WEBSOCKET_INIT', option)
  },
  send({ commit }, data) {
    commit('WEBSOCKET_SEND', data)
  },
  close({ commit }) {
    commit('WEBSOCKET_CLOSE')
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
