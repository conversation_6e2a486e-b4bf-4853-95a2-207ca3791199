<!--
 * @Description: 聚合策略 - 修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="20">
          <el-form-item prop="alarmName" :label="form.info.alarmName.label">
            <!--{{ form.model.alarmName }}-->
            <el-input v-model="form.model.alarmName" maxlength="16"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item prop="matchCriteriaList" :label="form.info.matchCriteriaList.label">
            <el-select v-model="form.model.matchCriteriaList" multiple collapse-tags filterable clearable>
              <el-option v-for="item in rulesOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item prop="alarmTimeout" :label="form.info.alarmTimeout.label">
            <el-input-number v-model.trim="form.model.alarmTimeout" controls-position="right" :min="60" :max="99999"></el-input-number>
            <span style="margin-left: 5px">{{ '秒' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item prop="countThreshold" :label="form.info.countThreshold.label">
            <el-input-number v-model.trim="form.model.countThreshold" controls-position="right" :min="0" :max="2147483647"></el-input-number>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item prop="forwardType" :label="form.info.forwardTypeList.label">
            <el-select v-model="form.model.forwardTypeList" filterable multiple collapse-tags clearable>
              <el-option v-for="item in forwardOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from './EventPolymerizationStrategyCustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    actions: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    rulesOption: {
      required: true,
      type: Array,
    },
    forwardOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      time: 0,
      errorShow: false,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
      this.errorShow = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.form.model)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
<style lang="scss" scoped>
.row {
  div:nth-child(1) {
    z-index: 9999;
  }

  div:nth-child(2) {
    position: relative;
    transform: translate(0%, 25%);
  }

  div:nth-child(3) {
    position: relative;
    transform: translate(-50%, 0%);
  }

  div:nth-child(4) {
    position: relative;
    transform: translate(-180%, 25%);
  }
}
</style>
