<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item>
            <el-radio-group v-model="form.model.ipType">
              <el-radio label="0">
                {{ 'Ipv4' }}
              </el-radio>
              <el-radio label="1">
                {{ 'Ipv6' }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col v-if="form.model.ipType === '0'" :span="24">
          <el-row class="row">
            <el-col :span="11">
              <el-form-item prop="startIpv4" :label="form.info.startIpv4.label">
                <el-input v-model.trim="form.model.startIpv4" class="width-small"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="2">
              -
            </el-col>
            <el-col :span="11">
              <el-form-item prop="endIpv4">
                <el-input v-model.trim="form.model.endIpv4" class="width-small"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
        <el-col v-if="form.model.ipType === '1'" :span="24">
          <el-form-item prop="ipv6" :label="form.info.ipv6.label">
            <el-input v-model.trim="form.model.ipv6" class="width-small"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          const inputVal = this.handleData()
          this.$emit('on-submit', this.form.model, inputVal)
          this.clickCancelDialog()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    handleData() {
      if (this.form.model.ipType === '0') {
        return `${this.form.model.startIpv4}-${this.form.model.endIpv4}`
      } else {
        return this.form.model.ipv6
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.row {
  div:nth-child(2) {
    position: relative;
    transform: translate(0%, 30%);
  }

  div:nth-child(3) {
    position: relative;
    transform: translate(-50%, 0%);
  }
}
</style>
