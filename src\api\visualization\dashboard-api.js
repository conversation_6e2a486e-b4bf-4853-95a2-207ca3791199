import request from '@util/request'

export function queryDashboardNameData() {
  return request({
    url: '/visualization/panel/dashboards',
    method: 'get',
  })
}

export function addDashboardData(obj) {
  return request({
    url: '/visualization/panel/dashboard',
    method: 'post',
    data: obj || {},
  })
}

export function deleteDashboardData(dashboardId) {
  return request({
    url: `/visualization/panel/dashboard/${dashboardId}`,
    method: 'delete',
  })
}

export function updateDashboardData(obj) {
  return request({
    url: '/visualization/panel/dashboard',
    method: 'put',
    data: obj || {},
  })
}

export function queryDashboardData(dashboardId) {
  return request({
    url: `/visualization/panel/dashboard/${dashboardId}`,
    method: 'get',
  })
}

export function queryChartTypeData() {
  return request({
    url: '/visualization/panel/dashboard/type',
    method: 'get',
  })
}

export function queryChartData(chartId) {
  return request({
    url: `/visualization/panel/dashboard/type/${chartId}`,
    method: 'get',
  })
}

export function queryChartOptionData(chartId) {
  return request({
    url: `/visualization/panel/dashboard/chart/${chartId}`,
    method: 'get',
  })
}
