.sunny {
  position: absolute;
  left: 90px;
  top: 20px;
  width: 20px;
  height: 140px;
  margin-left: -15px;
  background: linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
  animation: sunny 15s linear infinite;

  &:before {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 20px;
    height: 140px;
    background: linear-gradient(top, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.8) 50%, rgba(255, 255, 255, 0) 100%);
    content: '';
    opacity: 1;
    transform: rotate(90deg);
  }

  &:after {
    position: absolute;
    left: -30px;
    top: 30px;
    width: 80px;
    height: 80px;
    background: #ffee44;
    border-radius: 50%;
    box-shadow: rgba(255, 255, 0, 0.2) 0 0 0 15px;
    content: '';
  }
}

@keyframes sunny {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.cloudy {
  position: absolute;
  left: 255px;
  top: 70px;
  width: 50px;
  height: 50px;
  margin-left: -60px;
  background: #fff;
  border-radius: 50%;
  box-shadow: #fff 65px -15px 0 -5px, #fff 25px -25px, #fff 30px 10px, #fff 60px 15px 0 -10px, #fff 85px 5px 0 -5px;
  animation: cloudy 5s ease-in-out infinite;

  &:after {
    position: absolute;
    left: 5px;
    bottom: -60px;
    width: 120px;
    height: 15px;
    background: #000;
    border-radius: 50%;
    content: '';
    opacity: 0.2;
    transform: scale(0.7);
    animation: cloudy_shadow 5s ease-in-out infinite;
  }
}

@keyframes cloudy {
  50% {
    transform: translateY(-20px);
  }
}

@keyframes cloudy_shadow {
  50% {
    transform: translateY(20px) scale(1);
    opacity: 0.05;
  }
}

.rainy {
  position: absolute;
  left: 427px;
  top: 70px;
  display: block;
  height: 50px;
  width: 50px;
  margin-left: -60px;
  background: #ccc;
  border-radius: 50%;
  box-shadow: #ccc 65px -15px 0 -5px, #ccc 25px -25px, #ccc 30px 10px, #ccc 60px 15px 0 -10px, #ccc 85px 5px 0 -5px;
  animation: rainy 5s ease-in-out infinite 1s;

  &:before {
    display: block;
    width: 3px;
    height: 6px;
    background: #ccc;
    border-radius: 50%;
    opacity: 0.3;
    transform: scale(0.9);
    content: '';
    animation: rainy_rain 0.7s infinite linear;
  }

  &:after {
    position: absolute;
    left: 5px;
    bottom: -60px;
    width: 120px;
    height: 15px;
    background: #000;
    border-radius: 50%;
    content: '';
    opacity: 0.2;
    transform: scale(0.7);
    animation: rainy_shadow 5s ease-in-out infinite 1s;
  }
}

@keyframes rainy {
  50% {
    transform: translateY(-20px);
  }
}

@keyframes rainy_shadow {
  50% {
    transform: translateY(20px) scale(1);
    opacity: 0.05;
  }
}

@keyframes rainy_rain {
  0% {
    box-shadow: rgba(0, 0, 0, 0) 30px 30px, rgba(0, 0, 0, 0) 40px 40px, #000 50px 75px, #000 55px 50px, #000 70px 100px, #000 80px 95px,
      #000 110px 45px, #000 90px 35px;
  }
  25% {
    box-shadow: #000 30px 45px, #000 40px 60px, #000 50px 90px, #000 55px 65px, rgba(0, 0, 0, 0) 70px 120px, rgba(0, 0, 0, 0) 80px 120px,
      #000 110px 70px, #000 90px 60px;
  }
  26% {
    box-shadow: #000 30px 45px, #000 40px 60px, #000 50px 90px, #000 55px 65px, rgba(0, 0, 0, 0) 70px 40px, rgba(0, 0, 0, 0) 80px 20px,
      #000 110px 70px, #000 90px 60px;
  }
  50% {
    box-shadow: #000 30px 70px, #000 40px 80px, rgba(0, 0, 0, 0) 50px 100px, #000 55px 80px, #000 70px 60px, #000 80px 45px, #000 110px 95px,
      #000 90px 85px;
  }
  51% {
    box-shadow: #000 30px 70px, #000 40px 80px, rgba(0, 0, 0, 0) 50px 45px, #000 55px 80px, #000 70px 60px, #000 80px 45px, #000 110px 95px,
      #000 90px 85px;
  }
  75% {
    box-shadow: #000 30px 95px, #000 40px 100px, #000 50px 60px, rgba(0, 0, 0, 0) 55px 95px, #000 70px 80px, #000 80px 70px,
      rgba(0, 0, 0, 0) 110px 120px, rgba(0, 0, 0, 0) 90px 110px;
  }
  76% {
    box-shadow: #000 30px 95px, #000 40px 100px, #000 50px 60px, rgba(0, 0, 0, 0) 55px 35px, #000 70px 80px, #000 80px 70px,
      rgba(0, 0, 0, 0) 110px 25px, rgba(0, 0, 0, 0) 90px 15px;
  }
  100% {
    box-shadow: rgba(0, 0, 0, 0) 30px 120px, rgba(0, 0, 0, 0) 40px 120px, #000 50px 75px, #000 55px 50px, #000 70px 100px, #000 80px 95px,
      #000 110px 45px, #000 90px 35px;
  }
}

.rainbow {
  position: absolute;
  left: 610px;
  top: 71px;
  width: 70px;
  height: 70px;
  margin-left: -40px;
  border-radius: 170px 0 0 0;
  box-shadow: #fb323c -2px -2px 0 1px, #f99716 -4px -4px 0 3px, #fee124 -6px -6px 0 5px, #afdf2e -8px -8px 0 7px, #6ad7f8 -10px -10px 0 9px,
    #60b1f5 -12px -12px 0 11px, #a3459b -14px -14px 0 13px;
  transform: rotate(40deg);
  animation: rainbow 5s ease-in-out infinite;

  &:after {
    position: absolute;
    bottom: -23px;
    left: 17px;
    width: 120px;
    height: 15px;
    background: #000;
    border-radius: 50%;
    opacity: 0.2;
    content: '';
    transform: rotate(-40deg);
    transform-origin: 50% 50%;
    animation: rainbow_shadow 5s ease-in-out infinite;
  }
}

@keyframes rainbow {
  50% {
    transform: rotate(50deg);
  }
}

@keyframes rainbow_shadow {
  50% {
    transform: rotate(-50deg) translate(10px) scale(0.7);
    opacity: 0.05;
  }
}

.starry {
  position: absolute;
  left: 777px;
  top: 150px;
  width: 4px;
  height: 4px;
  margin-left: -10px;
  background: #fff;
  border-radius: 50%;
  opacity: 1;
  box-shadow: #fff 26px 7px 0 -1px, rgba(255, 255, 255, 0.1) -36px -19px 0 -1px, rgba(255, 255, 255, 0.1) -51px -34px 0 -1px, #fff -52px -62px 0 -1px,
    #fff 14px -37px, rgba(255, 255, 255, 0.1) 41px -19px, #fff 34px -50px, rgba(255, 255, 255, 0.1) 14px -71px 0 -1px, #fff 64px -21px 0 -1px,
    rgba(255, 255, 255, 0.1) 32px -85px 0 -1px, #fff 64px -90px, rgba(255, 255, 255, 0.1) 60px -67px 0 -1px, #fff 34px -127px,
    rgba(255, 255, 255, 0.1) -26px -103px 0 -1px;
  animation: starry_star 5s ease-in-out infinite;

  &:after {
    position: absolute;
    top: -106px;
    width: 100px;
    height: 100px;
    border-radius: 50%;
    box-shadow: #fff -25px 0;
    content: '';
    transform: rotate(-5deg);
    transform-origin: 0 50%;
    animation: starry 5s ease-in-out infinite;
  }
}

@keyframes starry {
  50% {
    transform: rotate(10deg);
  }
}

@keyframes starry_star {
  50% {
    box-shadow: rgba(255, 255, 255, 0.1) 26px 7px 0 -1px, #fff -36px -19px 0 -1px, #fff -51px -34px 0 -1px,
      rgba(255, 255, 255, 0.1) -52px -62px 0 -1px, rgba(255, 255, 255, 0.1) 14px -37px, #fff 41px -19px, rgba(255, 255, 255, 0.1) 34px -50px,
      #fff 14px -71px 0 -1px, rgba(255, 255, 255, 0.1) 64px -21px 0 -1px, #fff 32px -85px 0 -1px, rgba(255, 255, 255, 0.1) 64px -90px,
      #fff 60px -67px 0 -1px, rgba(255, 255, 255, 0.1) 34px -127px, #fff -26px -103px 0 -1px;
  }
}

.stormy {
  position: absolute;
  left: 947px;
  top: 70px;
  width: 50px;
  height: 50px;
  margin-left: -60px;
  background: #222;
  border-radius: 50%;
  box-shadow: #222 65px -15px 0 -5px, #222222 25px -25px, #222222 30px 10px, #222222 60px 15px 0 -10px, #222222 85px 5px 0 -5px;
  animation: stormy 5s ease-in-out infinite;

  &:before {
    position: absolute;
    left: 57px;
    top: 70px;
    display: block;
    width: 0;
    height: 0;
    border-left: 0 solid transparent;
    border-right: 7px solid transparent;
    border-top: 43px solid yellow;
    box-shadow: yellow -7px -32px;
    content: '';
    transform: rotate(14deg);
    transform-origin: 50% -60px;
    animation: stormy_thunder 2s steps(1, end) infinite;
  }

  &:after {
    position: absolute;
    left: 5px;
    bottom: -60px;
    width: 120px;
    height: 15px;
    background: #000;
    border-radius: 50%;
    opacity: 0.2;
    content: '';
    transform: scale(0.7);
    animation: stormy_shadow 5s ease-in-out infinite;
  }
}

@keyframes stormy {
  50% {
    transform: translateY(-20px);
  }
}

@keyframes stormy_shadow {
  50% {
    transform: translateY(20px) scale(1);
    opacity: 0.05;
  }
}

@keyframes stormy_thunder {
  0% {
    transform: rotate(20deg);
    opacity: 1;
  }
  5% {
    transform: rotate(-34deg);
    opacity: 1;
  }
  10% {
    transform: rotate(0deg);
    opacity: 1;
  }
  15% {
    transform: rotate(-34deg);
    opacity: 0;
  }
}

.snowy {
  position: absolute;
  left: 1112px;
  top: 70px;
  display: block;
  width: 50px;
  height: 50px;
  margin-left: -60px;
  background: #fff;
  border-radius: 50%;
  box-shadow: #fff 65px -15px 0 -5px, #fff 25px -25px, #fff 30px 10px, #fff 60px 15px 0 -10px, #fff 85px 5px 0 -5px;
  animation: snowy 5s ease-in-out infinite 1s;

  &:before {
    display: block;
    width: 7px;
    height: 7px;
    border-radius: 50%;
    opacity: 0.8;
    content: '';
    transform: scale(0.9);
    animation: snowy_snow 2s infinite linear;
  }

  &:after {
    position: absolute;
    left: 8px;
    bottom: -60px;
    width: 120px;
    height: 15px;
    opacity: 0.2;
    background: #000000;
    border-radius: 50%;
    content: '';
    transform: scale(0.7);
    animation: snowy_shadow 5s ease-in-out infinite 1s;
  }
}

@keyframes snowy {
  50% {
    transform: translateY(-20px);
  }
}

@keyframes snowy_shadow {
  50% {
    transform: translateY(20px) scale(1);
    opacity: 0.05;
  }
}

@keyframes snowy_snow {
  0% {
    box-shadow: rgba(238, 238, 238, 0) 30px 30px, rgba(238, 238, 238, 0) 40px 40px, #eee 50px 75px, #eee 55px 50px, #eee 70px 100px, #eee 80px 95px,
      #eee 110px 45px, #eee 90px 35px;
  }
  25% {
    box-shadow: #eee 30px 45px, #eee 40px 60px, #eee 50px 90px, #eee 55px 65px, rgba(238, 238, 238, 0) 70px 120px, rgba(238, 238, 238, 0) 80px 120px,
      #eee 110px 70px, #eee 90px 60px;
  }
  26% {
    box-shadow: #eee 30px 45px, #eee 40px 60px, #eee 50px 90px, #eee 55px 65px, rgba(238, 238, 238, 0) 70px 40px, rgba(238, 238, 238, 0) 80px 20px,
      #eee 110px 70px, #eee 90px 60px;
  }
  50% {
    box-shadow: #eee 30px 70px, #eee 40px 80px, rgba(238, 238, 238, 0) 50px 100px, #eee 55px 80px, #eee 70px 60px, #eee 80px 45px, #eee 110px 95px,
      #eee 90px 85px;
  }
  51% {
    box-shadow: #eee 30px 70px, #eee 40px 80px, rgba(238, 238, 238, 0) 50px 45px, #eee 55px 80px, #eee 70px 60px, #eee 80px 45px, #eee 110px 95px,
      #eee 90px 85px;
  }
  75% {
    box-shadow: #eee 30px 95px, #eee 40px 100px, #eee 50px 60px, rgba(238, 238, 238, 0) 55px 95px, #eee 70px 80px, #eee 80px 70px,
      rgba(238, 238, 238, 0) 110px 120px, rgba(238, 238, 238, 0) 90px 110px;
  }
  76% {
    box-shadow: #eee 30px 95px, #eee 40px 100px, #eee 50px 60px, rgba(238, 238, 238, 0) 55px 35px, #eee 70px 80px, #eee 80px 70px,
      rgba(238, 238, 238, 0) 110px 25px, rgba(238, 238, 238, 0) 90px 15px;
  }
  100% {
    box-shadow: rgba(238, 238, 238, 0) 30px 120px, rgba(238, 238, 238, 0) 40px 120px, #eee 50px 75px, #eee 55px 50px, #eee 70px 100px, #eee 80px 95px,
      #eee 110px 45px, #eee 90px 35px;
  }
}

#fabrizio {
  border-top: 100px solid black;
  border-right: 63px solid transparent;
  width: 0;
  transform: scale(0.33);
  transform-origin: 0 0;
  margin: 20px;
  opacity: 0.6;

  &:before {
    position: relative;
    top: -64px;
    left: 30px;
    display: block;
    width: 20px;
    height: 30px;
    background: black;
    content: '';
    transform: skewX(-32deg);
  }

  &:after {
    position: relative;
    top: -130px;
    left: 50px;
    display: block;
    width: 40px;
    height: 36px;
    background: black;
    content: '';
    transform: skewX(-32deg);
  }
}

.container {
  position: absolute;
  left: 50%;
  top: 30%;
  width: 1200px;
  height: 210px;
  margin: -65px -600px;
  background: linear-gradient(
    left,
    #00bbff,
    #00bbff 14.3%,
    #2eb5e5 14.3%,
    #2eb5e5 28.6%,
    #e6e6e6 28.6%,
    #e6e6e6 42.9%,
    #f3d166 42.9%,
    #f3d166 57.2%,
    #222233 57.2%,
    #222233 71.5%,
    #444444 71.5%,
    #444444 85.8%,
    #85db8c 85.8%
  );
  transform: scale(0.9);
}
