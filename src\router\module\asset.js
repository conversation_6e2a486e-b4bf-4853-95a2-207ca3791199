export default [
  {
    name: 'AreaManagement',
    path: '/asset/area',
    component: () => import('@view/asset/area/AreaManagement'),
  },
  {
    name: 'AssetCustom',
    path: '/asset/custom',
    component: () => import('@view/asset/custom/AssetCustom'),
  },
  {
    name: 'AssetDiscover',
    path: '/asset/discover',
    component: () => import('@view/asset/discover/AssetDiscover'),
  },
  {
    name: 'AssetManagement',
    path: '/asset/management',
    component: () => import('@view/asset/management/AssetManagement'),
  },
  {
    name: 'AssetNetworkManagement',
    path: '/asset/network-management',
    component: () => import('@view/asset/network-management/AssetNetworkManagement'),
  },
  {
    name: 'AssetType',
    path: '/asset/type',
    component: () => import('@view/asset/type/AssetType'),
  },
]
