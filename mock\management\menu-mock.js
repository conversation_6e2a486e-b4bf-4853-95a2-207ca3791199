const { createMockTable } = require('../util')
const menuList = createMockTable(
  {
    menuId: '@ID',
    parentId: '@ID',
    menuName: '@CTITLE',
    menuLocation: '@URL',
    'menuStatus|1': ['0', '1'],
    'menuStatusText|1': ['显示', '隐藏'],
    'menuIcon|1': ['el-icon-star-off', 'el-icon-star-on', 'soc-icon-point'],
    'menuOrder|1': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
    menuDescription: '@CPARAGRAPH',
    resourceId: '@ID',
    resourceName: '@CTITLE',
    resourceToken: '@GUID',
    'children|1-5': [
      {
        menuId: '@ID',
        parentId: '@ID',
        menuName: '@CTITLE',
        menuLocation: '@URL',
        'menuStatus|1': ['0', '1'],
        'menuStatusText|1': ['显示', '隐藏'],
        'menuIcon|1': ['el-icon-star-off', 'el-icon-star-on', 'soc-icon-point'],
        'menuOrder|1': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
        menuDescription: '@CPARAGRAPH',
        resourceId: '@ID',
        resourceToken: '@GUID',
        resourceName: '@CTITLE',
        'level|0-3': 3,
        'leaf|1': true,
      },
    ],
  },
  10
)

const parentMenu = createMockTable(
  {
    menuId: '@ID',
    parentId: '@ID',
    menuName: '@CTITLE',
    'menuIcon|1': ['el-icon-star-off', 'el-icon-star-on', 'soc-icon-point'],
    'children|1-5': [
      {
        menuId: '@ID',
        parentId: '@ID',
        menuName: '@CTITLE',
        'menuIcon|1': ['el-icon-star-off', 'el-icon-star-on', 'soc-icon-point'],
      },
    ],
  },
  10
)

const resource = createMockTable(
  {
    value: '@GUID',
    label: '@CTITLE',
  },
  20
)

module.exports = [
  {
    url: '/menumanagement/menus',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: menuList,
      }
    },
  },
  {
    url: '/menumanagement/menu',
    type: 'post',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/menumanagement/menu',
    type: 'delete',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/menumanagement/menu',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/menumanagement/menu/[A-Za-z0-9]',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          menuId: '@ID',
          parentId: parentMenu[0]['parentMenu'],
          menuName: '@CTITLE',
          menuLocation: '@URL',
          'menuStatus|1': ['0', '1'],
          'menuStatusText|1': ['显示', '隐藏'],
          'menuIcon|1': ['el-icon-star-off', 'el-icon-star-on', 'soc-icon-point'],
          'menuOrder|1': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
          menuDescription: '@CPARAGRAPH',
          resourceId: '@ID',
          resourceToken: '@GUID',
          resourceName: '@CTITLE',
        },
      }
    },
  },
  {
    url: '/menumanagement/menu/sort',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/menumanagement/parent-menus/[A-Za-z0-9]/[A-Za-z0-9]',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: parentMenu,
      }
    },
  },
  {
    url: '/menumanagement/resource-combo',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: resource,
      }
    },
  },
]
