export default {
  strategy: {
    strategy: '审计策略',
    orderNumber: '顺序',
    safeEvent: '安全事件',
    linkEvent: '关联事件',
    threatEvent: '威胁事件',
    day: '每日',
    week: '星期',
    dater: '日期',
    policyName: '策略名称',
    outputEventRemark: '策略描述',
    systemOwn: {
      title: '系统内置',
      own: '内置',
      write: '自定义',
    },
    state: '使用状态',
    states: {
      on: '启用',
      off: '停用',
    },
    date: '时间段',
    eventType: '事件',
    dateType: '审计有效时间段',
    startTime: '开始时间',
    startDate: '开始日期',
    endDate: '终止日期',
    endTime: '终止时间',
    weekStart: '星期开始',
    weekEnd: '星期结束',
    outputEventName: '审计事件名称',
    outputEventLevel: '审计事件级别',
    flag: '审计事件类型',
    outputEventType: '审计类型',
    auditUser: '审计人员',
    forwardSystemId: '转发外系统',
    forwardAudit: '转发内容',
    forwardAuditEvent: '审计事件',
    placeholder: {
      placeholder: '请选择',
      inputVal: '请输入关键字搜索',
      policyName: '策略名称',
      state: '使用状态',
      eventTypeList: '事件',
      outputEventName: '审计事件名称',
      outputEventLevel: '事件级别',
      outputEventType: '审计类型',
      auditUser: '审计人员',
      outputEventRemark: '策略描述',
      forwardSystemId: '转发外系统',
      time: '星期起始日',
    },
    weekList: {
      mon: '星期一',
      tue: '星期二',
      wed: '星期三',
      thu: '星期四',
      fri: '星期五',
      sat: '星期六',
      sun: '星期日',
    },
  },
}
