const { TableMock, createMockTable } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    id: '@ID',
    devTypeName: '@NAME',
    code: '@NAME',
    eventTypeName: '@NAME',
  },
})

// 设备类型
const devType = createMockTable(
  {
    label: '@name',
    value: '@id',
  },
  10
)

// 事件类型
const eventType = createMockTable(
  {
    label: '@name',
    value: '@id',
  },
  10
)

module.exports = [
  {
    url: '/customPattern/code/code-alarm',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/customPattern/code/add',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/customPattern/code/update',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/customPattern/code/del/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/customPattern/code/combo/devTypes',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: devType,
      }
    },
  },
  {
    url: '/customPattern/combo/code-alarm/alarm-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: eventType,
      }
    },
  },
]
