import Vue from 'vue'
import VueRouter from 'vue-router'
import { getFolderContext } from '@util/find'
import visualization from './visualization'

Vue.use(VueRouter)

const modules = getFolderContext(require.context('./module', true, /\.js$/))

const childrenRouters = []
Object.values(modules).forEach((item) => {
  if (childrenRouters.indexOf(item) === -1) {
    childrenRouters.push(...item)
  }
})

export const staticRouters = [
  {
    path: '/',
    redirect: '/layout',
  },
  {
    name: 'mailTo',
    path: '/login',
    component: () => import('@view/login/Login'),
  },
  {
    name: 'Page404',
    path: '/404',
    component: () => import('@view/exception/404/404'),
  },
  {
    name: 'Page401',
    path: '/401',
    component: () => import('@view/exception/401/401'),
  },
  {
    name: 'Layout',
    path: '/layout',
    component: () => import('@view/layout/Layout'),
    children: [...childrenRouters],
  },
  ...visualization,
  {
    path: '*',
    redirect: '/404',
    hidden: true,
  },
]

export const dynamicRouters = []

const createRouter = () =>
  new VueRouter({
    mode: 'history',
    base: process.env.BASE_URL,
    scrollBehavior: () => ({ y: 0 }),
    routes: staticRouters,
  })

const router = createRouter()

export function resetRouter() {
  router.matcher = createRouter().matcher
}

export default router
