<template>
  <div class="tab-context-wrapper">
    <el-form ref="systemForm" :model="form.model" :rules="form.rule" label-width="180px">
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.flowGather')" prop="status">
          <el-switch v-model="form.model.status"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.flowGather') }}
          </section>
        </el-form-item>
      </el-col>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-has="'upload'" @click="clickSaveSystemConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button v-has="'query'" @click="clickResetSystemConfig">
        {{ $t('button.reset.default') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { getFlowGatherData, saveFlowGatherData } from '@api/management/system-api'
export default {
  data() {
    return {
      networkCardOptions: [],
      form: {
        model: {
          status: false,
        },
        rule: {},
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      getFlowGatherData().then((res) => {
        this.form.model.status = res.status === '0' ? false : true
      })
    },
    clickSaveSystemConfig() {
      const payload = {
        status: this.form.model.status ? '1' : '0',
      }
      saveFlowGatherData(payload).then((r) => {
        if (r === 1) this.$message.success('保存成功')
        if (r === 2) this.$message.error('开启无效，license未开启流量采集器')
      })
    },
    clickResetSystemConfig() {
      this.init()
    },
  },
}
</script>
<style lang="scss"></style>
