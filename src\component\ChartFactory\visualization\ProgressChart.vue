<template>
  <div :id="id" ref="lineChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    seriesData: {
      type: Object,
      default() {
        return { name: '', value: 0, color: '#0097ff' }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    seriesData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart(this.chart)
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data) {
      const pd = data || this.seriesData
      this.chart = echarts.init(this.$refs.lineChart)
      this.drawChart(pd)
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.clear()
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      const colorLeft = '#00FCF7'
      const colorRight = '#5098ed'
      const colorLeftAlpha = ['#00FCF788', '#00FCF70c']
      const colorRightAlpha = ['#5098ed88', '#5098ed08']
      data.percent = (data.value / 100).toFixed(2)
      const option = {
        series: [
          // 外侧灰色轴线
          {
            type: 'gauge',
            radius: '98%', // 位置
            center: ['50%', '70%'],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: true,
              lineStyle: {
                // 轴线样式
                width: 2, // 宽度
                color: [[1, 'rgba(229,229,229,0.3)']], // 颜色
              },
            },
            axisTick: {
              // 刻度
              show: false,
            },
            splitLine: {
              // 分割线
              show: false,
            },
            axisLabel: {
              // 刻度标签
              show: false,
            },
            pointer: {
              // 仪表盘指针
              show: false,
            },
            detail: {
              // 仪表盘详情
              show: false,
            },
          },
          // 内侧刻度
          {
            type: 'gauge',
            radius: '85%', // 位置
            center: ['50%', '70%'],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: true,
              lineStyle: {
                // 轴线样式
                width: 18, // 宽度
                color: [
                  [
                    data.percent,
                    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: colorLeftAlpha[0],
                      },
                      {
                        offset: 1,
                        color: colorLeftAlpha[1],
                      },
                    ]),
                  ],
                  [
                    1,
                    new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                      {
                        offset: 0,
                        color: colorRightAlpha[0],
                      },
                      {
                        offset: 1,
                        color: colorRightAlpha[1],
                      },
                    ]),
                  ],
                ], // 颜色
              },
            },
            axisTick: {
              // 刻度
              show: true,
              splitNumber: 1,
              length: 4,
            },
            splitLine: {
              // 分割线
              show: false,
            },
            axisLabel: {
              // 刻度标签
              show: false,
            },
            pointer: {
              // 仪表盘指针
              show: false,
            },
            detail: {
              // 仪表盘详情
              show: false,
            },
          },
          // 中间白色半圆
          {
            type: 'gauge',
            radius: '40%', // 位置
            center: ['50%', '70%'],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: true,
              lineStyle: {
                // 轴线样式
                width: 24, // 宽度
                color: [
                  [
                    1,
                    new echarts.graphic.RadialGradient(0.5, 1, 1, [
                      {
                        offset: 1,
                        color: 'rgba(229, 229, 229,0.15)',
                      },
                      {
                        offset: 0.72,
                        color: 'rgba(229, 229, 229,0.05)',
                      },
                      {
                        offset: 0.7,
                        color: 'rgba(229, 229, 229,0.4)',
                      },
                      {
                        offset: 0.401,
                        color: 'rgba(229, 229, 229,0.05)',
                      },
                      {
                        offset: 0.4,
                        color: 'rgba(229, 229, 229,0.8)',
                      },
                      {
                        offset: 0,
                        color: 'rgba(229, 229, 229,0.8)',
                      },
                    ]),
                  ],
                ], // 颜色
              },
            },
            axisTick: {
              // 刻度
              show: false,
            },
            splitLine: {
              // 分割线
              show: false,
            },
            axisLabel: {
              // 刻度标签
              show: false,
            },
            pointer: {
              // 仪表盘指针
              show: false,
            },
            detail: {
              // 仪表盘详情
              show: false,
            },
          },
          // 内侧轴线
          {
            type: 'gauge',
            radius: '90%', // 位置
            center: ['50%', '70%'],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: true,
              lineStyle: {
                // 轴线样式
                width: 10, // 宽度
                color: [
                  [data.percent, colorLeft],
                  [1, colorRight],
                ], // 颜色
              },
            },
            pointer: {
              // 仪表盘指针
              show: false,
            },
            axisTick: {
              // 刻度
              show: false,
            },
            splitLine: {
              // 分割线
              show: false,
            },
            axisLabel: {
              // 刻度标签
              show: false,
            },
            detail: {
              // 仪表盘详情
              show: false,
            },
          },
          // 指针
          {
            type: 'gauge',
            radius: '80%', // 位置
            center: ['50%', '70%'],
            min: 0,
            max: 100,
            startAngle: 180,
            endAngle: 0,
            axisLine: {
              show: false,
            },
            data: [
              {
                value: data.value,
                name: data.name,
              },
            ],
            pointer: {
              // 仪表盘指针
              show: true,
            },
            itemStyle: {
              color: data.color,
              borderColor: data.color,
              borderWidth: '2',
              borderType: 'solid',
            },
            axisTick: {
              // 刻度
              show: false,
            },
            splitLine: {
              // 分割线
              show: false,
            },
            axisLabel: {
              // 刻度标签
              show: false,
            },
            detail: {
              // 仪表盘详情
              show: true,
              formatter: function(value) {
                return value + '%'
              },
              offsetCenter: ['0%', '40%'],
              fontSize: '28px',
              color: '#fff',
            },
            title: {
              show: true,
              offsetCenter: ['-120%', '-130%'],
              color: '#ddd',
              fontWeight: 'bold',
            },
          },
        ],
      }
      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
