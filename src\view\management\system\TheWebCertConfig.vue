<template>
  <div class="tab-context-wrapper" v-loading="loading" element-loading-text="上传中...">
    <el-form label-width="160px">
      <el-col :span="12" :offset="1">
        <el-form-item :label="$t('management.system.tab.webcert')">
          <el-upload
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :file-list="fileList"
            accept=".p12"
            :on-change="uploadFileChange"
            style="width:100%"
          >
            <div class="upload-input-container">
              <el-input v-model="fileName" disabled style="width: 100%;"></el-input>
              <el-button v-has="'upload'" slot="trigger" class="upload-button">
                {{ $t('button.choseFile') }}
              </el-button>
            </div>
            <section class="form-validate-tip">
              {{ $t('management.system.tip.webcertP12') }}
            </section>
          </el-upload>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="1">
        <el-form-item :label="$t('management.system.label.password')">
          <el-input type="password" v-model="password" />
          <section class="form-validate-tip">
            {{ $t('management.system.tip.webcertPassword') }}
          </section>
        </el-form-item>
      </el-col>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-has="'upload'" @click="submitUploadFile">
        {{ $t('button.upload') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { prompt } from '@util/prompt'
import { uploadWebCert } from '@api/management/system-api'

export default {
  data() {
    return {
      fileList: [],
      password: '',
      loading: false,
    }
  },
  computed: {
    fileName() {
      return this.fileList.length > 0 ? this.fileList[0].name : ''
    },
  },
  methods: {
    beforeUploadValidate(file) {
      if (this.fileList.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isP12 = suffix === 'p12'
        if (!isP12) {
          prompt({
            i18nCode: 'validate.upload.p12webcert',
            type: 'warning',
          })
        }
        return isP12
      }
    },
    uploadFileChange(file) {
      this.fileList = [file]
    },
    submitUploadFile() {
      if (this.password === '') {
        prompt({
          i18nCode: 'validate.password.empty',
          type: 'warning',
        })
        return
      }
      if (this.fileList.length <= 0) {
        prompt({
          i18nCode: 'validate.upload.empty',
          type: 'warning',
        })
        return
      }
      if (this.fileList.length > 0) {
        const file = this.fileList[0]
        const formData = new FormData()
        formData.append('file', file.raw)
        formData.append('pass', this.password)
        this.loading = true
        uploadWebCert(formData).then((res) => {
          this.loading = false
          if (res.code === 'success') {
            this.$message.error('证书上传成功，系统正在重启，请耐心等待')
          } else {
            this.$message.error('证书或密码错误')
          }
        })
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.tab-context-wrapper {
  height: 100%;
  ::v-deep .el-upload {
    width: 100%;
  }
}
.upload-input-container {
  position: relative;
  width: 100%;
  .upload-button {
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
  }
}
.form-validate-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
  text-align: left;
}
.tab-footer-button {
  margin-right: 48px;
  text-align: right;
}
</style>
