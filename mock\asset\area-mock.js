const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 20,
  template: {
    domaId: '@ID',
    domaName: '@CNAME',
    domaAbbreviation: '@CNAME',
    officeTel: /^([0-9]{3,4}-)?[0-9]{7,8}$/,
    email: '@Email',
    domaFunc: '@TITLE(3, 5)',
    domaAddress: '@REGION',
    domaMemo: '@CPARAGRAPH(1, 3)',
    deviceNum: '@INTEGER(60, 100)',
  },
})

module.exports = [
  // 查询区域列表
  {
    url: '/domainmanagement/domains',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  // 修改区域
  {
    url: '/domainmanagement/updateDomain',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'domaId')
      return {
        code: 200,
        data: 'success',
      }
    },
  },
  // 添加区域
  {
    url: '/domainmanagement/addDomain',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 'success',
      }
    },
  },
  // (批量)删除区域
  {
    url: `/domainmanagement/deleteDomains/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'domaId')
      return {
        code: 200,
        data: 'success',
      }
    },
  },
]
