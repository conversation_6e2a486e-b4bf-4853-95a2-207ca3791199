import request from '@util/request'

export function queryLogSourceNumber() {
  return request({
    url: '/hegui/management/rizhiyuanzichanshu',
    method: 'get',
  })
}

export function queryLogTotalNumber() {
  return request({
    url: '/hegui/management/rizhijieshouzongshu',
    method: 'get',
  })
}

export function queryLogStorageSpace() {
  return request({
    url: '/hegui/management/queryRizhicunchuzhanyongkongjian',
    method: 'get',
  })
}

export function queryLogStorageDuration() {
  return request({
    url: '/hegui/management/queryRizhicunchushichangTotal',
    method: 'get',
  })
}

export function queryLogNumberChartData() {
  return request({
    url: '/hegui/management/queryRizhishebeishuliangTop10',
    method: 'get',
  })
}

export function queryLogDurationChartData() {
  return request({
    url: '/hegui/management/queryRizhishebeishichangTop10',
    method: 'get',
  })
}

export function queryLogSourceTypeChartData() {
  return request({
    url: '/hegui/management/rizhiyuanzichanleixing',
    method: 'get',
  })
}

export function queryLogNumberTrendChart(obj) {
  return request({
    url: '/hegui/management/rizhijieshouqushi',
    method: 'get',
    params: obj || {},
  })
}

export function querySystemHealthyChart() {
  return request({
    url: '/hegui/management/queryXitongzhuangkuang',
    method: 'get',
  })
}

// 查询日志数量列表
export function queryLogNumberTable(obj) {
  return request({
    url: '/hegui/management/queryRizhishebeishuliangliebiao',
    method: 'get',
    params: obj || {},
  })
}

// 查询日志存储时长列表
export function queryLogDurationTable(obj) {
  return request({
    url: '/hegui/management/queryRizhishebeishichangliebiao',
    method: 'get',
    params: obj || {},
  })
}

export function queryLogReceivingStatus() {
  return request({
    url: '/hegui/management/queryRizhijieshouzhuangtai',
    method: 'get',
  })
}

export function queryDeviceCombo() {
  return request({
    url: '/hegui/management/device/combo',
    method: 'get',
  })
}

export function updateLogSourceStatus(obj) {
  return request({
    url: '/hegui/management/updateRizhiyuanshichang',
    method: 'put',
    data: obj || {},
  })
}

export function queryAuditAlarmTrend(obj) {
  return request({
    url: '/hegui/management/auditAlarmTread',
    method: 'get',
    params: obj || {},
  })
}

export function querySecurityEventCount(obj) {
  return request({
    url: '/hegui/management/securityEvent/count',
    method: 'get',
    params: obj || {},
  })
}

export function querySecurityEventTable(obj) {
  return request({
    url: '/hegui/management/querySecurityeventliebiao',
    method: 'get',
    params: obj || {},
  })
}
