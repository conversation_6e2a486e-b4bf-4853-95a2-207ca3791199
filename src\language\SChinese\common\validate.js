export default {
  validate: {
    upload: {
      empty: '请上传文件',
      license: '只能上传lic或license文件',
      p12webcert: '只能上传p12文件',
      excel: '只能上传xlsx或xls文件',
      word: '只能上传doc或docx文件',
      ppt: '只能上传ppt或pptx文件',
      pdf: '只能上传pdf文件',
      sql: '只能上传sql文件',
      zip: '只能上传zip文件',
    },
    data: {
      empty: '暂无数据',
      nomore: '没有更多了...',
    },
    empty: '此项不能为空',
    length: {
      space: '输入的内容不能含有空格',
      maxTen: '输入内容的长度不能大于10个字符',
    },
    comm: {
      url: '您输入的URL不合法',
      email: '您输入的邮箱不合法',
      cellphone: '您输入的移动电话号码不合法',
      telephone: '您输入的固定电话号码不合法',
      port: '您输入的端口号不合法',
      pwd: '只允许输入字母、数字和符号',
    },
    mac: {
      incorrect: '该MAC地址不合法',
      checkRange: '检测到输入项存在非法MAC地址',
    },
    ip: {
      compare: '终止IP应大于起始IP',
      incorrect: 'IP地址不合法',
      segment: 'IP段不同',
      range: '端口格式不正确，只能输入1~65535之间的整数',
      domain: '输入项IP或域名不合法',
      empty: 'IP不能为空',
      error: '请输入同一类型的合法IP!',
      checkRange: '检测到输入项存在非法IP，或者IP范围类型不统一',
      rangeExist: '起始IP、终止IP必须同时存在',
    },
    port: {
      incorrect: '端口格式不合法',
    },
    address: {
      incorrect: '地址格式不合法',
    },
    form: {
      empty: '{0}不能为空',
      error: '表单校验失败',
      warning: '表单校验未通过',
      lessOne: '请至少选择一项',
    },
    date: {
      compare: '终止时间应该大于起始时间',
    },
    username: {
      empty: '用户名不能为空',
      rule: '只允许输入字母、数字和“_”“-”“.”符号，且“_”“-”“.”只能位于中间位置',
    },
    password: {
      empty: '输入密码不能为空',
      rule: '密码至少分别包括一个大写字母小写字母数字和特殊字符',
      size: '密码位数不得少于8位，不多于20位',
      sizemin8: '密码位数不得少于8位',
      old: {
        empty: '请输入旧密码',
      },
      new: {
        empty: '请输入新密码',
        compare: '新密码不能与旧密码相同',
      },
      confirm: {
        empty: '请确认新密码',
        compare: '两次输入的密码不一致',
      },
    },
    captcha: {
      empty: '校验码不能为空',
      rule: '只允许输入字母和数字',
    },
    choose: '请先选择后再提交',
    onlyNumber: '只能输入数字',
    none: '此项不能为空或包含空格',
    number: {
      compare: '终止数字应该大于起始数字',
    },
    nameInput: {
      rule: '只允许输入汉字、字母、数字、“_”“-”“.”符号，且“_”“-”和“.”只能位于中间位置。',
    },
    telephone: '输入的电话格式不合法',
    email: '输入的邮箱不合法',
    faxNo: '输入的传真号不合法',
    monitor: {
      pollDate: '请输入3-60之间的数值',
      useRate: '请输入1-100之间的数值',
      times: '请输入1-9之间的数值',
      port: '端口格式不正确，只能输入1~65535之间的整数。',
    },
    chooseItem: {
      empty: '选择的信息项不能为空',
    },
    filterCondition: '至少选择一个采集器过滤条件',
  },
}
