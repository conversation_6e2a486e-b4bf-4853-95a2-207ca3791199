<template>
  <section :id="id" ref="chart" :class="className" :style="{ height: height, width: width }"></section>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import common from '../mixin/common'
import { getColor } from '@util/effect'
import { deepMerge } from '@util/format'

export default {
  mixins: [common],
  props: {
    className: {
      type: String,
      default: 'chart-bar',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    proto: {
      type: Boolean,
      default: false,
    },
    option: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      chart: null,
      color: getColor(),
      index: {
        selected: 0,
        hovered: 0,
      },
    }
  },
  watch: {
    option: {
      handler(config) {
        this.configChart(config)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart, this.$store.getters.theme)
    },
    configChart(config = this.option) {
      this.chart.showLoading()
      config && Object.keys(config).length > 0 && config.data.length > 0 ? this.drawChart(config) : this.empty()
      this.chart.hideLoading()
    },
    drawChart(config) {
      const option = this.proto ? config : this.chartOptionConfig(config)
      this.chart.clear()
      this.chart.setOption(option, true)
    },
    chartOptionConfig(config) {
      let option = {
        color: getColor(),
        backgroundColor: 'transparent',
        legend: {
          show: config.data.length > 2,
          type: 'scroll',
          bottom: 10,
          left: 'center',
          icon: 'roundRect',
        },
        grid: {
          top: 20,
          left: 10,
          right: 50,
          bottom: config.data.length > 2 ? 50 : 20,
          containLabel: true,
        },
        tooltip: {
          appendToBody: true,
          trigger: 'axis',
          axisPointer: {
            type: 'shadow',
          },
        },
        dataset: {
          source: config.data,
        },
      }
      option = deepMerge(option, this.chartAxisConfig(config))
      option = deepMerge(option, this.chartSeriesConfig(config))
      if (config.data.length === 2) {
        option.tooltip.formatter = (param) => {
          const currentData = param[0]
          return `${currentData.marker} ${currentData.value[0]} ${currentData.value[1]}`
        }
      }
      option = echarts.util.merge(option, config)
      if (this.option.custom) {
        option = echarts.util.merge(option, this.option.custom)
      }
      return option
    },
    chartAxisConfig(config) {
      let axis = {
        xAxis: {
          axisLabel: {
            formatter(param) {
              return param.length > 15 ? param.slice(0, 15) + '...' : param
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          axisLabel: {
            formatter(param) {
              return param.length > 15 ? param.slice(0, 15) + '...' : param
            },
          },
          axisTick: {
            show: false,
          },
        },
      }
      config.axis = config.axis || 1
      if (config.axis === 'x' || config.axis === 1) {
        axis.xAxis.type = 'category'
        axis.yAxis.type = 'value'
      }

      if (config.axis === 'y' || config.axis === 2) {
        axis.xAxis.type = 'value'
        axis.yAxis.type = 'category'
      }
      if (config.hasOwnProperty('axisDefine')) {
        axis = deepMerge(axis, config.axisDefine)
      }
      return axis
    },
    chartSeriesConfig(config) {
      config.type = config.type || 1
      if (config.type === 'bar' || config.type === 1) {
        return this.getBarSeries(config)
      }
      if (config.type === 'bar-stack' || config.type === 2) {
        return this.getBarStackSeries(config)
      }
      if (config.type === 'bar-polar' || config.type === 3) {
        return this.getBarPolarSeries(config)
      }
      if (config.type === 'bar-polar-stack' || config.type === 4) {
        return this.getBarPolarStackSeries(config)
      }
      if (config.type === 'bar-radial' || config.type === 5) {
        return this.getBarRadialSeries(config)
      }
      if (config.type === 'bar-radial-stack' || config.type === 6) {
        return this.getBarRadialStackSeries(config)
      }
      if (config.type === 'bar-line') {
        return this.getBarLineSeries(config)
      }
    },
    getBarSeries(config) {
      const color = getColor()
      const y = config.axis === 'y' || config.axis === 2
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        let seriesItem = {
          type: 'bar',
          barMaxWidth: '100',
          seriesLayoutBy: 'row',
          label: {
            show: true,
            fontSize: 10,
            position: y ? 'right' : 'top',
          },
          itemStyle: {
            borderRadius: y ? [0, 5, 5, 0] : [5, 5, 0, 0],
            color: new echarts.graphic.LinearGradient(
              y ? 1 : 0,
              0,
              0,
              y ? 0 : 1,
              [
                {
                  offset: 0,
                  color: color[index],
                },
                {
                  offset: 1,
                  color: color[index].colorToRgb(0.7),
                },
              ],
              false
            ),
          },
        }
        if (config.hasOwnProperty('seriesDefine')) {
          seriesItem = deepMerge(seriesItem, config.seriesDefine)
        }
        return seriesItem
      })
      return {
        series: series,
      }
    },
    getBarStackSeries(config) {
      const color = getColor()
      const y = config.axis === 'y' || config.axis === 2
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        return {
          type: 'bar',
          barMaxWidth: '100',
          seriesLayoutBy: 'row',
          stack: 'total',
          label: {
            show: true,
            fontSize: 10,
            color: '#fff',
          },
          itemStyle: {
            borderRadius: y ? [0, 5, 5, 0] : [5, 5, 0, 0],
            color: new echarts.graphic.LinearGradient(
              y ? 1 : 0,
              0,
              0,
              y ? 0 : 1,
              [
                {
                  offset: 0,
                  color: color[index],
                },
                {
                  offset: 1,
                  color: color[index].colorToRgb(0.7),
                },
              ],
              false
            ),
          },
        }
      })
      return {
        series: series,
      }
    },
    getBarPolarSeries(config) {
      const dataLength = config.data.length > 2
      const series = Array.from({ length: config.data.length - 1 }, () => {
        return {
          type: 'bar',
          barMaxWidth: '100',
          seriesLayoutBy: 'row',
          coordinateSystem: 'polar',
          label: {
            show: true,
            fontSize: 10,
            color: '#fff',
          },
        }
      })
      return {
        polar: {
          radius: dataLength ? [30, '60%'] : [30, '70%'],
          center: dataLength ? ['50%', '40%'] : ['50%', '50%'],
        },
        angleAxis: {
          max: 4,
          startAngle: 75,
        },
        radiusAxis: {
          type: 'category',
          data: config.data[0],
        },
        xAxis: {
          show: false,
        },
        yAxis: {
          show: false,
        },
        series: series,
      }
    },
    getBarPolarStackSeries(config) {
      const dataLength = config.data.length > 2
      const series = Array.from({ length: config.data.length - 1 }, () => {
        return {
          type: 'bar',
          barMaxWidth: '100',
          seriesLayoutBy: 'row',
          coordinateSystem: 'polar',
          stack: 'total',
          label: {
            show: true,
            fontSize: 10,
            color: '#fff',
          },
        }
      })
      return {
        polar: {
          radius: dataLength ? [30, '60%'] : [30, '70%'],
          center: dataLength ? ['50%', '40%'] : ['50%', '50%'],
        },
        angleAxis: {
          max: 4,
          startAngle: 75,
        },
        radiusAxis: {
          type: 'category',
          data: config.data[0],
        },
        xAxis: {
          show: false,
        },
        yAxis: {
          show: false,
        },
        series: series,
      }
    },
    getBarRadialSeries(config) {
      const dataLength = config.data.length > 2
      const series = Array.from({ length: config.data.length - 1 }, () => {
        return {
          type: 'bar',
          barMaxWidth: '100',
          seriesLayoutBy: 'row',
          coordinateSystem: 'polar',
          label: {
            show: true,
            fontSize: 10,
            color: '#fff',
          },
        }
      })
      return {
        polar: {
          radius: dataLength ? [30, '60%'] : [30, '70%'],
          center: dataLength ? ['50%', '40%'] : ['50%', '50%'],
        },
        radiusAxis: {
          max: 4,
        },
        angleAxis: {
          type: 'category',
          data: config.data[0],
          startAngle: 75,
        },
        xAxis: {
          show: false,
        },
        yAxis: {
          show: false,
        },
        series: series,
      }
    },
    getBarRadialStackSeries(config) {
      const dataLength = config.data.length > 2
      const series = Array.from({ length: config.data.length - 1 }, () => {
        return {
          type: 'bar',
          barMaxWidth: '100',
          seriesLayoutBy: 'row',
          coordinateSystem: 'polar',
          stack: 'total',
          label: {
            show: true,
            fontSize: 10,
            color: '#fff',
          },
        }
      })
      return {
        polar: {
          radius: dataLength ? [30, '60%'] : [30, '70%'],
          center: dataLength ? ['50%', '40%'] : ['50%', '50%'],
        },
        radiusAxis: {
          max: 4,
        },
        angleAxis: {
          type: 'category',
          data: config.data[0],
          startAngle: 75,
        },
        xAxis: {
          show: false,
        },
        yAxis: {
          show: false,
        },
        series: series,
      }
    },
    getBarLineSeries(config) {
      const color = getColor()
      const y = config.axis === 'y' || config.axis === 2
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        return [
          {
            type: 'bar',
            barMaxWidth: '100',
            seriesLayoutBy: 'row',
            label: {
              show: true,
              fontSize: 10,
              position: y ? 'right' : 'top',
            },
            itemStyle: {
              borderRadius: y ? [0, 5, 5, 0] : [5, 5, 0, 0],
              color: new echarts.graphic.LinearGradient(
                y ? 1 : 0,
                0,
                0,
                y ? 0 : 1,
                [
                  {
                    offset: 0,
                    color: color[index],
                  },
                  {
                    offset: 1,
                    color: color[index].colorToRgb(0.7),
                  },
                ],
                false
              ),
            },
          },
          {
            type: 'line',
            seriesLayoutBy: 'row',
            smooth: true,
            showSymbol: false,
            itemStyle: {
              color: color[index],
              borderColor: color[index],
            },
          },
        ]
      })
      return {
        series: series.flat(),
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
