export default {
  resource: {
    infoItem: {
      resourceToken: '资源标识',
      resourceName: '资源名称',
      resourceStatus: '资源状态',
      resourceStatusText: '资源状态',
      authority: '隶属权限',
      authorityText: '隶属权限',
      resourceDescription: '资源描述',
      actionName: '功能名称',
      actionToken: '功能标识',
      actionStatus: '功能状态',
      actionDescription: '功能描述',
    },
    authority: {
      system: '系统管理员',
      running: '运维管理员',
      audit: '系统审计员',
    },
    codeList: {
      resourceStatus: {
        show: '显示',
        hide: '隐藏',
      },
      actionStatus: {
        show: '显示',
        hide: '隐藏',
      },
    },
    header: {
      dialogTitle: '资源管理',
      operation: '操作',
      placeholder: '请选择',
      innerDialogTitle: '功能',
      tipInfo: {
        resourceTokenRepeat: '添加资源标识重复!',
        actionTokenRepeat: '添加功能标识重复！',
      },
    },
    placeholder: {
      keyword: '请输入关键字查询',
      resourceToken: '资源标识',
      resourceName: '资源名称',
      resourceStatus: '资源状态',
      authority: '隶属权限',
      resourceDescription: '资源描述',
    },
  },
}
