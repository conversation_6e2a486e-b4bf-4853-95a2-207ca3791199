<!--
 * @Description: 威胁事件 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <section class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        ref="shellTable"
        v-loading="tableLoading"
        v-el-table-scroll="scrollTable"
        :data="tableData"
        infinite-scroll-disabled="disableScroll"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @selection-change="clickSelectRows"
      >
        <el-table-column type="index" show-overflow-tooltip></el-table-column>
        <el-table-column type="selection" prop="eventId"></el-table-column>
        <el-table-column prop="eventTypeName" :label="$t('event.threat.eventType')" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('event.threat.eventLevel')" show-overflow-tooltip>
          <template slot-scope="scope">
            <level-tag :level="scope.row.eventLevel"></level-tag>
          </template>
        </el-table-column>
        <el-table-column prop="eventDesc" :label="$t('event.threat.eventDesc')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="receiveTime" :label="$t('event.threat.receiveTime')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="eventTime" :label="$t('event.threat.eventTime')" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" width="140">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </section>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import LevelTag from '@comp/LevelTag'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    LevelTag,
  },
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableScroll: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
  },
  computed: {
    disableScroll() {
      return this.tableScroll
    },
  },
  methods: {
    scrollTable() {
      this.$emit('on-scroll')
    },
    clickSelectRows(select) {
      this.$emit('on-select', select)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
  },
}
</script>
