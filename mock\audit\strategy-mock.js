const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    policyId: '@ID',
    orderNumber: /^([0-9]{3,4}-)?[0-9]{7,8}$/,
    policyName: '@NAME',
    outputEventRemark: '@ZIP',
    'state|1': true,
  },
})
// 事件类型
const eventTypeList = [
  {
    label: '事件类型一',
    value: '事件类型一',
  },
  {
    label: '事件类型二',
    value: '事件类型二',
  },
]
// 审计事件类型
const outputEventTypeList = [
  {
    label: '审计事件类型一',
    value: '审计事件类型一',
  },
  {
    label: '审计事件类型二',
    value: '审计事件类型二',
  },
]
// 转发外系统
const isForwardList = [
  {
    label: '转发外系统一',
    value: '转发外系统一',
  },
  {
    label: '转发外系统二',
    value: '转发外系统二',
  },
  {
    label: '转发外系统三',
    value: '转发外系统三',
  },
]
module.exports = [
  {
    url: '/strategy/audit/strategy',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/strategy/audit/strategy/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/audit/strategy',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/audit/startegy/state',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/audit/strategies',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/strategy/audit/strategy/move',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/strategy/audit/combo/event-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: eventTypeList,
      }
    },
  },
  {
    url: '/strategy/audit/combo/audit-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: outputEventTypeList,
      }
    },
  },
  {
    url: '/strategy/audit/combo/forward-strategies',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: isForwardList,
      }
    },
  },
]
