<!--
 * @Description: 监控器展示 - 内存信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-12
 * @Editor:
 * @EditDate: 2021-08-12
-->
<template>
  <line-chart height="300px" proto :width="'100%'" :line-data="memoryOption"></line-chart>
</template>

<script>
import LineChart from '@comp/ChartFactory/common/LineChart.vue'
import { queryMonitorMemory } from '@api/monitor/view-api'
export default {
  components: {
    LineChart,
  },
  props: {
    params: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      memoryOption: {},
    }
  },
  computed: {
    condition() {
      return {
        edId: this.params.edId,
        monitorId: this.params.monitorId,
        startTime: this.params.startTime,
        endTime: this.params.endTime,
      }
    },
  },
  watch: {
    params: {
      handler() {
        this.getMemoryInfo()
      },
      deep: true,
    },
  },
  mounted() {
    this.getMemoryInfo()
  },
  methods: {
    getMemoryInfo(params = this.condition) {
      queryMonitorMemory(params).then((res) => {
        this.memoryOption = this.getLineOption(res)
      })
    },
    getLineOption(data) {
      const option = {
        backgroundColor: 'transparent',
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          subtext: '内存使用率',
        },
        tooltip: {
          trigger: 'axis',
        },
        xAxis: {
          type: 'category',
          data: data.axis,
        },
        yAxis: {
          type: 'value',
        },
        series: {
          type: 'line',
          data: data.data,
        },
      }
      return option
    },
  },
}
</script>
