import request from '@util/request'

// 增加角色信息
export function addInfo(obj) {
  return request({
    url: '/rolemanagement/role',
    method: 'post',
    data: obj || {},
  })
}

// 删除用户信息
export function delInfo(ids) {
  return request({
    url: `/rolemanagement/role/${ids}`,
    method: 'delete',
  })
}

// 修改角色
export function updInfo(obj) {
  return request({
    url: '/rolemanagement/role',
    method: 'put',
    data: obj || {},
  })
}

// 查询获取用户信息
export function getList(param) {
  return request({
    url: '/rolemanagement/roles',
    method: 'get',
    params: param || {},
  })
}

// 获取资源集合
export function getAllResourcesList() {
  return request({
    url: '/rolemanagement/resources-combo',
    method: 'get',
  })
}

// 获取角色详情
export function getRoleInfo(userId) {
  return request({
    url: `/rolemanagement/role/${userId}`,
    method: 'get',
  })
}

// 获取用户集合
export function getUsersCombo() {
  return request({
    url: '/rolemanagement/users-combo',
    method: 'get',
  })
}

// 授权角色
export function grantRole(obj) {
  return request({
    url: '/rolemanagement/role/grant',
    method: 'put',
    data: obj || {},
  })
}

//  查询已授权用户
export function getGrantedUsers(id) {
  return request({
    url: `/rolemanagement/role/users/${id}`,
    method: 'get',
  })
}
