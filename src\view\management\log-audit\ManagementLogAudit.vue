<!--
 * @Description: 日志审计
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model="data.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('management.logAudit.label.operator')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="clickQueryLogAudit"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="clickQueryLogAudit">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" v-debounce="clickLogForward">
            {{ $t('button.forward') }}
          </el-button>
          <el-button v-has="'download'" v-debounce="clickDownloadLogAuditTable">
            {{ $t('button.export.default') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteLogAudit">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.logAudit.name') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="logAuditTableRowChange"
          @selection-change="logAuditTableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="logUser" :label="$t('management.logAudit.label.operator')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="logResource" :label="$t('management.logAudit.label.resource')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="logAction" :label="$t('management.logAudit.label.operation')" sortable></el-table-column>
          <el-table-column prop="logState" :label="$t('management.logAudit.label.result')" sortable>
            <template slot-scope="scope">
              {{ columnText(scope.row.logState, 'resultStatus') }}
            </template>
          </el-table-column>
          <el-table-column prop="logIp" :label="$t('management.logAudit.label.ip')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column prop="logDate" :label="$t('management.logAudit.label.date')" sortable show-overflow-tooltip></el-table-column>
          <el-table-column fixed="right" width="140">
            <template slot-scope="scope">
              <el-button v-has="'update'" class="el-button--blue" @click="clickDetailLogAudit(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteLogAudit(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="logAuditTableSizeChange"
        @current-change="logAuditTableCurrentChange"
      ></el-pagination>
    </footer>
    <log-audit-dialog :visible.sync="dialog.detail.visible" :form-data="dialog.detail.form"></log-audit-dialog>
    <log-forward-dialog :visible.sync="dialog.logForward.visible" :form="dialog.logForward.form" @on-submit="saveLogForward"></log-forward-dialog>
  </div>
</template>

<script>
import LogAuditDialog from './TheLogAuditDialog'
import LogForwardDialog from './TheLogForwardDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import { resultStatus } from '@asset/js/code/option'
import {
  deleteLogAuditData,
  queryLogAuditTableData,
  downloadLogAuditTableData,
  queryLogForwardConfig,
  saveLogForwardConfig,
} from '@api/management/log-audit-api'
import { isEmpty } from '@util/common'

export default {
  name: 'ManagementLogAudit',
  components: {
    LogAuditDialog,
    LogForwardDialog,
  },
  data() {
    return {
      data: {
        loading: false,
        debounce: null,
        table: [],
        selected: [],
        fuzzyField: '',
      },
      pagination: {
        visible: true,
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
      dialog: {
        detail: {
          visible: false,
          form: {},
        },
        logForward: {
          visible: false,
          form: {
            model: [],
          },
        },
      },
      options: {
        resultStatus: resultStatus,
      },
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  mounted() {
    this.initDebounceQuery()
    this.getLogAuditTableData()
    this.getLogForwardConfig()
  },
  methods: {
    clickDownloadLogAuditTable() {
      const ids = this.data.selected.map((item) => item.logId).toString()
      this.downloadLogAuditTable(ids)
    },
    clickDetailLogAudit(row) {
      this.dialog.detail.form = row
      this.dialog.detail.visible = true
    },
    clickDeleteLogAudit(row) {
      this.deleteLogAudit(row.logId)
    },
    clickBatchDeleteLogAudit() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.logId).toString()
        this.deleteLogAudit(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickQueryLogAudit() {
      this.data.debounce()
    },
    logAuditTableRowChange(row) {
      this.pagination.currentRow = row
    },
    clickLogForward() {
      this.getLogForwardConfig()
      this.dialog.logForward.visible = true
    },
    logAuditTableSelectsChange(select) {
      this.data.selected = select
    },
    logAuditTableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.getLogAuditTableData()
    },
    logAuditTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getLogAuditTableData()
    },
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        this.pagination.pageNum = 1
        this.getLogAuditTableData()
      }, 500)
    },
    getLogAuditTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        fuzzyField: this.data.fuzzyField,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryLogAuditTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.visible = true
        this.data.loading = false
      })
    },
    deleteLogAudit(logIds) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteLogAuditData(logIds).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, logIds.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.getLogAuditTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    downloadLogAuditTable(ids) {
      this.data.loading = true
      const params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        logId: ids,
        fuzzyField: this.data.fuzzyField,
      }
      downloadLogAuditTableData(params).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object'
                ? new Blob([res.data], {
                    type: 'application/octet-stream',
                  })
                : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    getLogForwardConfig() {
      queryLogForwardConfig().then((res) => {
        if (!isEmpty(res) || res.length > 0) {
          this.dialog.logForward.form.model = res
        } else {
          this.dialog.logForward.form.model = [{ ip: '', port: '' }]
        }
      })
    },
    saveLogForward(obj) {
      saveLogForwardConfig({ ips: obj }).then((res) => {
        if (res === 'success') {
          prompt({
            i18nCode: 'tip.update.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
