<!--
 * @Description: 仪表盘 - 生成器
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <section class="dashboard-generator-wrapper">
    <el-row v-for="(row, rowIndex) in data" :key="rowIndex" :gutter="10">
      <el-col v-for="(col, colIndex) in row.list" :key="colIndex" :span="col.span" :style="{ height: row.height + 'px' }" class="chunk-wrapper">
        <section
          v-if="row.list[colIndex]['data'] && (row.list[colIndex]['data']['length'] > 0 || Object.keys(row.list[colIndex]['data'])['length'] > 0)"
          class="chunk-non-empty"
        >
          <header class="chunk-header">
            <h2 class="chunk-header-title">
              {{ col.data.title }}
            </h2>
          </header>
          <main ref="chart" v-loading="col.loading" element-loading-background="rgba(0, 0, 0, 0.3)" class="chunk-chart">
            <template v-if="col.data.type === 'line'">
              <line-chart ref="lineChartDom" :line-data="col.data.option" mouse-event></line-chart>
            </template>
            <template v-if="col.data.type === 'pie'">
              <pie-chart ref="pieChartDom" :pie-data="col.data.option" mouse-event></pie-chart>
            </template>
            <template v-if="col.data.type === 'bar'">
              <bar-chart ref="barChartDom" :bar-data="col.data.option" mouse-event></bar-chart>
            </template>
            <template v-if="col.data.type === 'bar-stack'">
              <bar-stack-chart ref="barStackChartDom" :bar-data="col.data.option" mouse-event></bar-stack-chart>
            </template>
          </main>
        </section>
        <section v-else class="chunk-empty">
          <empty-data-chart ref="emptyChartDom" :text="$t('visualization.dashboard.tip.empty.component')"></empty-data-chart>
        </section>
      </el-col>
    </el-row>
  </section>
</template>

<script>
import LineChart from '@comp/ChartFactory/dashboard/LineChart'
import PieChart from '@comp/ChartFactory/dashboard/PieChart'
import BarChart from '@comp/ChartFactory/dashboard/BarChart'
import BarStackChart from '@comp/ChartFactory/dashboard/BarStackChart'
import EmptyDataChart from '@comp/ChartFactory/common/EmptyDataChart'
import { queryChartOptionData } from '@api/visualization/dashboard-api'

export default {
  components: {
    LineChart,
    PieChart,
    BarChart,
    BarStackChart,
    EmptyDataChart,
  },
  props: {
    dashboardData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      visible: false,
    }
  },
  computed: {
    data() {
      this.dashboardData.forEach((layout) => {
        layout.list.forEach(async (item) => {
          if (item.data && item.data.id) {
            item.loading = true
            await this.getChartData(item.data.id).then((res) => {
              item.data.option = res
              item.loading = false
            })
          }
        })
        this.resize()
      })
      return this.dashboardData
    },
  },
  methods: {
    async getChartData(chartId) {
      let data = {}
      await queryChartOptionData(chartId).then((res) => {
        data = res
      })
      return data
    },
    resize() {
      this.$nextTick(() => {
        Object.getOwnPropertyNames(this.$refs).forEach((domKey) => {
          if (domKey.indexOf('ChartDom') > -1) {
            this.$refs[domKey].forEach((chart) => {
              chart.resize()
            })
          }
        })
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.dashboard-generator-wrapper {
  .el-row {
    margin-bottom: 10px;
  }

  .chunk-wrapper {
    .chunk-empty,
    .chunk-non-empty {
      height: 100%;
      border: 1px solid;
      border-radius: 10px;
      text-align: center;
      @include theme('border-color', border-color);
    }

    .chunk-non-empty {
      .chunk-header {
        display: flex;
        height: 30px;
        padding: 5px 10px;
        color: $primary-color;
        justify-content: space-between;

        &-title {
          width: 100%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          text-align: left;
        }
      }

      .chunk-chart {
        height: calc(100% - 40px);
        padding: 5px 10px;
      }
    }

    .chunk-empty {
      &-icon {
        position: relative;
        height: 70%;

        i {
          position: absolute;
          left: 50%;
          top: 50%;
          font-size: 60px;
          transform: translate(-50%, -50%);
        }
      }
    }
  }

  .add-layout-target {
    height: 80px;
    line-height: 80px;
    font: normal bold 20px/80px monospace;
    letter-spacing: 10px;
    background-color: #ccc;
    text-align: center;
    border-radius: 10px;
    cursor: pointer;

    &:hover {
      background-color: #1873d7;
    }
  }
}
</style>
