import request from '@util/request'

// 查询列表
export function queryGeneralLogTable(obj) {
  return request({
    url: '/event/unknownlog/events',
    method: 'get',
    params: obj || {},
  })
}

// 查询列表总数
export function queryGeneralLogTotal(obj) {
  return request({
    url: '/event/unknownlog/total',
    method: 'get',
    params: obj || {},
  })
}

// 查询自定义列
export function queryColumnsData() {
  return request({
    url: '/event/unknownlog/columns',
    method: 'get',
  })
}

// 修改自定义列
export function updateColumnsData(obj) {
  return request({
    url: '/event/unknownlog/columns',
    method: 'put',
    data: obj || {},
  })
}

// 导出列表数据
export function downloadGeneralLogEvent(obj) {
  return request(
    {
      url: '/event/unknownlog/download',
      method: 'get',
      params: obj || {},
    },
    'download',
    '180000'
  )
}

// 查询级别下拉数据
export function querySeverityCombo() {
  return request({
    url: '/event/unknownlog/combo/severity-categories',
    method: 'get',
  })
}

// 查询发生源设备类型下拉数据
export function queryDeviceTypeCombo() {
  return request({
    url: '/event/unknownlog/combo/asset-types',
    method: 'get',
  })
}

// 查询日志模块下拉数据
export function queryFacilityCombo() {
  return request({
    url: '/event/unknownlog/combo/facility-categories',
    method: 'get',
  })
}

// 查询日志模块下解析速率
export function queryRarseRate() {
  return request({
    url: '/event/unknownlog/consumingTime',
    method: 'get',
  })
}

export function queryLogForward() {
  return request({
    url: '/event/unknownlog/queryUnknowlogForwardPolicyById',
    method: 'get',
  })
}
export function updateLogForward(obj) {
  return request({
    url: '/event/unknownlog/updateUnknowLogForwardPolicy',
    method: 'put',
    data: obj || {},
  })
}
export function queryForwardCollectorOptions() {
  return request({
    url: '/event/unknownlog/queryCollectors',
    method: 'get',
  })
}
export function queryForwardServerOptions() {
  return request({
    url: '/event/unknownlog/combo/forward-strategies',
    method: 'get',
  })
}
