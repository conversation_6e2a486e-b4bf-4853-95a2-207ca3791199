<!--
 * @Description: 系统健康状况图表
 * @Version: 3.5
 * @Author:
 * @Date: 2023/03/05
 * @Editor:
 * @EditDate: 2023/03/05
-->
<template>
  <el-container class="widget" :style="{ height: height + 'px' }">
    <el-header class="widget-title" height="30px">
      {{ $t('visualization.compliance.title.systemHealthy') }}
    </el-header>
    <el-main class="widget-main">
      <pie-chart ref="pieChartDom" proto :option="option"></pie-chart>
    </el-main>
  </el-container>
</template>

<script>
import PieChart from '@comp/ChartFactory/forecast/PieChart'
import { getColor } from '@util/effect'
import { querySystemHealthyChart } from '@api/visualization/compliance-api'

export default {
  components: {
    PieChart,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      chartData: [],
      option: {},
      runState: this.$t('monitor.management.status.normal'),
      description: '',
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getSystemHealthyChart()
    },
    chartSeries() {
      this.option = {
        series: [],
        yAxis: [],
      }
      this.chartData.forEach((v, i) => {
        this.option.series.push({
          type: 'pie',
          clockWise: false,
          hoverAnimation: false,
          radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
          center: ['30%', '55%'],
          label: {
            show: false,
          },
          data: [
            {
              value: v.value,
              name: v.name,
            },
            {
              value: 100 - v.value * 1,
              tooltip: {
                show: false,
              },
              itemStyle: {
                color: 'rgba(0,0,0,0)',
              },
            },
          ],
        })
        this.option.series.push({
          name: '',
          type: 'pie',
          silent: true,
          z: 1,
          clockWise: false, // 顺时加载
          hoverAnimation: false, // 鼠标移入变大
          radius: [65 - i * 15 + '%', 57 - i * 15 + '%'],
          center: ['30%', '55%'],
          label: {
            show: false,
          },
          data: [
            {
              value: 7.5,
              itemStyle: {
                color: '#E3F0FF',
              },
            },
            {
              value: 2.5,
              name: '',
              itemStyle: {
                color: 'rgba(0,0,0,0)',
              },
            },
          ],
        })
        this.option.yAxis.push((v.value * 1).toFixed(0) + '%')
      })
    },
    getOption() {
      this.chartSeries()
      return {
        backgroundColor: 'transparent',
        title: {
          text: '' || this.runState,
          subtext: '' || this.description,
          x: '30%', // 与圆环center的x值保持一致
          y: '55%', // 与圆环center的y值保持一致
          itemGap: 5,
          textStyle: {
            fontSize: 20,
            fontWeight: 'bold',
            align: 'center',
            verticalAlign: 'middle',
          },
          subtextStyle: {
            fontSize: 8,
            align: 'center',
            verticalAlign: 'bottom',
          },
          textAlign: 'center',
          left: '30%',
          top: '55%',
          textVerticalAlign: 'middle',
        },
        tooltip: {
          show: true,
          trigger: 'item',
          formatter: '{b}:{d}%',
        },
        legend: {
          show: true,
          top: 'center',
          left: '65%',
          formatter: (name) => {
            let p = 0
            this.chartData.forEach((item) => {
              if (item.name === name) {
                p = (item.value * 1).toFixed(0)
              }
            })
            return `${name} ${p}%`
          },
        },
        color: getColor(),
        grid: {
          top: '20%',
          bottom: '64%',
          left: '30%',
          containLabel: false,
        },
        yAxis: [
          {
            type: 'category',
            inverse: true,
            axisLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            // ,
            // axisLabel: {
            //     interval: 0,
            //     inside: true,
            //     textStyle: {
            //         fontSize: 12
            //     },
            //     show: true
            // },
            // data: this.option.yAxis
          },
        ],
        xAxis: [
          {
            show: false,
          },
        ],
        series: this.option.series,
      }
    },
    getSystemHealthyChart() {
      querySystemHealthyChart().then((res) => {
        if (res) {
          this.runState = res.runState
          this.description = res.description
          this.chartData = res.usage
          this.option = this.getOption()
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.widget {
  .el-header {
    padding: 0 2px;
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10;
  }
}
</style>
