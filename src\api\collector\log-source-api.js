import request from '@util/request'

// 查询日志源设备列表
export function queryLogSourcesList(obj) {
  return request({
    url: '/collector/logsource/logSources',
    method: 'get',
    params: obj || {},
  })
}

// 删除日志源设备
export function deleteLogSource(id) {
  return request({
    url: `/collector/logsource/logSource/${id}`,
    method: 'delete',
  })
}

// 添加日志源设备
export function addLogSource(obj) {
  return request({
    url: '/collector/logsource/logSource',
    method: 'post',
    data: obj || {},
  })
}

// 修改日志源设备
export function updateLogSource(obj) {
  return request({
    url: '/collector/logsource/logSource',
    method: 'put',
    data: obj || {},
  })
}

// 查询厂商下拉数据
export function queryManufactCombo() {
  return request({
    url: '/collector/logsource/combo/manufact',
    method: 'get',
  })
}

// 查询设备类别下拉数据
export function queryCategoryCombo() {
  return request({
    url: '/collector/logsource/combo/category',
    method: 'get',
  })
}
