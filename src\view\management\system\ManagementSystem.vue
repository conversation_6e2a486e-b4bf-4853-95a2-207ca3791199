<!--
 * @Description: 系统管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div>
    <el-tabs v-model="tab.name" type="card" @tab-click="clickTabSwitch">
      <el-tab-pane :label="$t('management.system.tab.basic')" name="basic">
        <basic-info
          :form-data="data.form.basic"
          @on-restart="restartDevice"
          @on-restore="restoreDevice"
          @on-shutdown="shutdownDevice"
          @on-toggle-ssh="toggleSshStatus"
        ></basic-info>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.system')" name="system">
        <system-config :form-data="data.form.system" ref="systemConfig"></system-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.title.email')" name="email">
        <email-config
          :form-data="data.form.email"
          @on-test="clickTestEmailServeConfig"
          @on-save="clickSaveEmailServeConfig"
          @on-reset="clickResetEmailServeConfig"
        ></email-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.title.timing')" name="reviseTime">
        <revise-time-config
          :form-data="data.form.reviseTime"
          @on-test="clickTestReviseTimeConfig"
          @on-save="clickSaveReviseTimeConfig"
          @on-reset="clickResetReviseTimeConfig"
        ></revise-time-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.database')" name="database">
        <database-config :form-data="data.form.database" :table-data="data.table.database" @on-save="clickSaveDatabaseConfig"></database-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.backup')" name="backup">
        <backup-config
          :form-data="data.form.backup"
          :table-data="data.table.dataBackup"
          @on-save="clickSaveBackupConfig"
          @on-reset="clickResetBackupConfig"
          @on-recover="clickRecoverBackupData"
        ></backup-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.snapshot')" name="snapshot">
        <snapshot-config
          :form-data="data.form.snapshot"
          :table-data="data.table.snapshot"
          :loading="data.loading.snapshotRecovery"
          @on-save="clickSaveSnapshotConfig"
          @on-reset="clickResetSnapshotConfig"
          @on-execute="clickExecuteSnapshot"
          @on-upload="clickUploadSnapshot"
          @on-download="clickDownloadSnapshot"
          @on-delete="clickDeleteSnapshot"
          @on-recovery="clickRecoverySnapshot"
        ></snapshot-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.license')" name="license">
        <license-config :table-data="data.table.license" @on-upload="clickSubmitUploadFile" @on-download="clickDownloadLicense"></license-config>
      </el-tab-pane>
      <!-- <el-tab-pane :label="$t('management.system.tab.threat')" name="threat">
                <threat-config
                    :upload-data="data.form.uploads"
                    :form-data="data.form.threat"
                    :loading="data.form.uploads.loading"
                    @on-save="clickSaveThreatConfig"
                    @on-reset="clickResetThereatConfig"
                    @on-submit="clickSubmitUpload"
                    @reset-upload="clickRestUpload">
                </threat-config>
            </el-tab-pane> -->
      <el-tab-pane :label="$t('management.system.tab.systemAlarm')" name="sysAlarm">
        <sys-alarm-notice :form-data="data.form.sysAlarm" @on-save="clickSaveSysAlarmNotice"></sys-alarm-notice>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.imageFlow')" name="imageFlow">
        <image-flow-config></image-flow-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.system.tab.desensitization')" name="desensitization">
        <desensitization-config></desensitization-config>
      </el-tab-pane>
      <!-- <el-tab-pane label="流量采集" name="flowGather">
        <flow-gather-config></flow-gather-config>
      </el-tab-pane> -->
      <el-tab-pane :label="$t('management.system.tab.webcert')" name="webCert">
        <web-cert-config></web-cert-config>
      </el-tab-pane>
    </el-tabs>
    <backup-upload-dialog
      :visible.sync="dialog.upload.visible"
      :title="dialog.upload.title"
      :form="dialog.upload"
      :width="'35%'"
      @on-submit="clickBackupUpload"
    ></backup-upload-dialog>
  </div>
</template>

<script>
import BasicInfo from './TheBasicInfo'
import SystemConfig from './TheDefaultConfig'
import ImageFlowConfig from './TheImageFlowConfig'
import FlowGatherConfig from './TheFlowGatherConfig'
import DesensitizationConfig from './TheDesensitizationConfig'
import EmailConfig from './TheEmailConfig'
import ReviseTimeConfig from './TheReviseTimeConfig'
import DatabaseConfig from './TheDatabaseConfig'
import LicenseConfig from './TheLicenseConfig'
import BackupConfig from './TheBackupConfig'
import SnapshotConfig from './TheSnapshotConfig'
// import ThreatConfig from './TheThreat'
import SysAlarmNotice from './TheSysAlarmNotice'
import BackupUploadDialog from './TheBackupUploadDialog'
import WebCertConfig from './TheWebCertConfig'
import { prompt } from '@util/prompt'
import {
  queryBasicInfoData,
  queryRestartDeviceData,
  queryShutdownDeviceData,
  queryRestoreDeviceData,
  queryEmailServeConfigData,
  queryReviseTimeConfigData,
  queryDatabaseSafeguardData,
  queryDatabaseSafeguardTableData,
  queryLicenseListData,
  queryDataBackupData,
  queryDataBackupTableData,
  querySSHStatus,
  startSSHStatus,
  stopSSHStatus,
  resetEmailServeConfigData,
  resetReviseTimeConfigData,
  resetDataBackupData,
  saveEmailServeConfigData,
  saveReviseTimeConfigData,
  saveDatabaseSafeguardData,
  saveDataBackupData,
  testEmailServeConfigData,
  testReviseTimeConfigData,
  uploadLicenseData,
  downloadLicense,
  saveDataThreatData,
  resetDataThreatData,
  queryDataThreatData,
  uploadThreatData,
  querySysAlarmNotice,
  saveSysAlarmNotice,
  querySnapshotTaskData,
  resetSnapshotTaskData,
  saveSnapshotTaskData,
  addSnapshotData,
  uploadSnapshot,
  downloadSnapshot,
  delSnapshot,
  recoverySnapshot,
  querySnapshotTableData,
  uploadBackupData,
} from '@api/management/system-api'

export default {
  name: 'ManagementSystem',
  components: {
    BasicInfo,
    SystemConfig,
    EmailConfig,
    ReviseTimeConfig,
    DatabaseConfig,
    LicenseConfig,
    WebCertConfig,
    BackupConfig,
    SnapshotConfig,
    // ThreatConfig,
    SysAlarmNotice,
    BackupUploadDialog,
    ImageFlowConfig,
    // FlowGatherConfig,
    DesensitizationConfig,
  },
  data() {
    return {
      tab: {
        name: 'basic',
      },
      data: {
        table: {
          database: [],
          license: [],
          dataBackup: [],
          snapshot: [],
        },
        form: {
          basic: {
            systemName: '',
            systemVersion: '',
            systemBuild: '',
            systemModel: '',
            sshEnable: '',
          },
          system: {
            oldPassword: '',
            defaultPassword: '',
            captcha: '',
            accountLockEnable: true,
            accountLockTime: 0,
            accountLockCnt: 0,
            accountAutoUnlockEnable: true,
            accountAutoUnlockTime: 0,
            accessTokenValidTime: 0,
            accountValidEnable: true,
            passwordOverdueEnable: true,
            passwordOverdueTime: 0,
            currentPassword: '',
            systemName: '',
            systemLogo: '',
            syslogPort: '',
            webPort: '',
          },
          email: {
            sendMailServer: '',
            sendPort: '',
            senderAddress: '',
            serverAuth: true,
            username: '',
            password: '',
            ssl: true,
            test: null,
            loading: false,
          },
          reviseTime: {
            timingMode: 0,
            centerSeverTime: '',
            configCenterSeverTime: '',
            ntpSeverConfig: '',
            autoValidate: true,
            settingCycle: 'month',
            settingCycleValue: 1,
            settingTime: '00:00:00',
            test: null,
            loading: false,
          },
          database: {
            safeguard: '',
            dataRetainTime: '',
          },
          license: {},
          backup: {
            cycle: '',
            time: '',
            timeValue: '',
            ip: '',
            account: '',
            password: '',
            path: '',
          },
          snapshot: {
            cycle: '',
            time: '',
            timeValue: '',
          },
          threat: {
            // id: "",
            // cycleType: "",
            // time: "",
            // period: "",
            // url: "",
            status: '',
          },
          uploads: {
            loading: false,
            files: [],
            header: {
              // 上传头部信息
              'Content-Type': 'multipart/form-data',
            },
            uploadFile: {},
          },
          sysAlarm: {
            isMail: '0',
            mailTo: '',
            isSound: '0',
            isSnmp: '0',
            snmpForwardServer: '',
            isSms: '0',
            mobileUrl: '',
            mobileEcName: '',
            mobileApId: '',
            mobileSecretKey: '',
            mobileMobiles: '',
            mobileSign: '',
            mobileAddSerial: '',
          },
        },
        loading: {
          snapshotRecovery: false,
        },
      },
      dialog: {
        upload: {
          title: this.$t('management.system.upload.title'),
          visible: false,
          header: { 'Content-Type': 'multipart/form-data' },
          files: [], // 上传文件个数
          templateType: '', // 导入模板类型
          rules: {
            files: [
              {
                required: true,
                message: this.$t('validate.choose'),
                trigger: 'change',
              },
            ],
          },
        },
      },
    }
  },
  mounted() {
    this.tabLoadData(this.tab.name)
  },
  methods: {
    tabLoadData(name) {
      switch (name) {
        case 'basic':
          this.getBasicInfo()
          this.getSSHStatus()
          break
        case 'system':
          this.$refs.systemConfig.init()
          break
        case 'email':
          this.getEmailServeConfig()
          break
        case 'reviseTime':
          this.getReviseTimeConfig()
          break
        case 'database':
          this.getDatabaseSafeguard()
          this.getDatabaseSafeguardTable()
          break
        case 'license':
          this.getLicenseList()
          break
        case 'backup':
          this.getDataBackup()
          this.getDataBackupTable()
          break
        case 'snapshot':
          this.getSnapshotTask()
          this.getSnapshotTable()
          break
        case 'threat':
          this.getDataThere()
          break
        case 'sysAlarm':
          this.getSysAlarmNotice()
          break
        default:
          break
      }
    },
    clickTabSwitch(tab) {
      this.tabLoadData(tab.name)
    },
    clickTestEmailServeConfig(params) {
      this.testEmailServeConfig(params)
    },
    clickTestReviseTimeConfig(params) {
      this.testReviseTimeConfig(params)
    },
    clickSaveEmailServeConfig(params) {
      this.$confirm(!this.data.form.email.test ? this.$t('management.system.tip.test') : this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.saveEmailServeConfig(params)
      })
    },
    clickSaveReviseTimeConfig(params) {
      this.$confirm(
        !this.data.form.reviseTime.test && params.timingMode === 1 ? this.$t('management.system.tip.test') : this.$t('tip.confirm.save'),
        this.$t('tip.confirm.tip'),
        {
          closeOnClickModal: false,
        }
      ).then(() => {
        this.saveReviseTimeConfig(params)
      })
    },
    clickResetEmailServeConfig() {
      this.resetEmailServeConfig()
    },
    clickResetReviseTimeConfig() {
      this.resetReviseTimeConfig()
    },
    clickSaveDatabaseConfig(params) {
      this.saveDatabaseSafeguard(params)
    },
    clickSubmitUploadFile(params) {
      this.uploadLicense(params)
    },
    clickDownloadLicense() {
      this.downloadLicense()
    },
    clickResetBackupConfig() {
      this.resetDataBackup()
    },
    clickSaveBackupConfig(params) {
      this.saveDataBackup(params)
    },
    clickResetSnapshotConfig() {
      this.resetSnapshotTask()
    },
    clickSaveSnapshotConfig(params) {
      this.saveSnapshotTask(params)
    },
    clickExecuteSnapshot() {
      this.executeSnapshot()
    },
    clickUploadSnapshot(params) {
      this.uploadSnapshot(params)
    },
    clickDownloadSnapshot(row) {
      this.downloadSnapshot(row)
    },
    clickDeleteSnapshot(row) {
      this.deleteSnapshot(row)
    },
    clickRecoverySnapshot(row) {
      this.recoverySnapshot(row)
    },
    clickResetThereatConfig() {
      this.resetDataThreat()
    },
    clickSaveThreatConfig(params) {
      this.saveDataThreat(params)
    },
    clickSaveSysAlarmNotice(params) {
      this.saveSysAlarmNotice(params)
    },
    clickRestUpload(FormData) {
      FormData.clearFiles()
      FormData.fileList.length = 0
      prompt({
        i18nCode: 'tip.reset.success',
        type: 'success',
      })
    },
    clickRecoverBackupData() {
      this.dialog.upload.files = []
      this.dialog.upload.importRule = '1'
      this.dialog.upload.templateType = ''
      this.dialog.upload.visible = true
    },
    clickBackupUpload(formData) {
      const loading = this.$loading({
        lock: true,
        fullscreen: true,
        text: '文件正在上传，请稍等...',
        background: 'rgba(0, 0, 0, 0.7)',
      })
      uploadBackupData(formData)
        .then((res) => {
          if (res === 1) {
            prompt({
              i18nCode: this.$t('management.system.upload.successUpload', [res]),
              type: 'success',
            })
            this.getDataBackupTable()
          } else {
            prompt({
              i18nCode: 'tip.import.error',
              type: 'error',
            })
          }
          loading.close()
        })
        .catch((e) => {
          loading.close()
          console.error(e)
        })
    },
    getBasicInfo() {
      queryBasicInfoData().then((res) => {
        this.data.form.basic.systemName = res.systemName
        this.data.form.basic.systemVersion = res.systemVersion
        this.data.form.basic.systemBuild = res.systemBuild
        this.data.form.basic.systemModel = res.systemModel
      })
    },
    getSSHStatus() {
      querySSHStatus().then((res) => {
        if (res === '0') {
          this.data.form.basic.sshEnable = '0'
        } else {
          this.data.form.basic.sshEnable = '1'
        }
      })
    },
    restartDevice() {
      queryRestartDeviceData().then((res) => {
        if (res) {
          this.getBasicInfo()
          prompt({
            i18nCode: 'tip.restart.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.restart.error',
            type: 'error',
          })
        }
      })
    },
    shutdownDevice() {
      queryShutdownDeviceData().then((res) => {
        if (res) {
          this.getBasicInfo()
          prompt({
            i18nCode: 'tip.shutdown.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.shutdown.error',
            type: 'error',
          })
        }
      })
    },
    restoreDevice() {
      queryRestoreDeviceData().then((res) => {
        if (res) {
          this.getBasicInfo()
          prompt({
            i18nCode: 'tip.restore.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.restore.error',
            type: 'error',
          })
        }
      })
    },
    toggleSshStatus(status) {
      if (status === '0') {
        stopSSHStatus().then((res) => {
          if (res === '0') {
            this.getSSHStatus()
            prompt({
              i18nCode: 'tip.disable.success',
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.disable.error',
              type: 'error',
            })
          }
        })
      } else {
        startSSHStatus().then((res) => {
          if (res === '0') {
            this.getSSHStatus()
            prompt({
              i18nCode: 'tip.enable.success',
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.enable.error',
              type: 'error',
            })
          }
        })
      }
    },
    getEmailServeConfig() {
      queryEmailServeConfigData().then((res) => {
        this.data.form.email.sendMailServer = res.sendMailServer
        this.data.form.email.sendPort = res.sendPort
        this.data.form.email.senderAddress = res.senderAddress
        this.data.form.email.serverAuth = res.serverAuth
        this.data.form.email.username = res.username
        this.data.form.email.password = res.password
        this.data.form.email.ssl = res.ssl
      })
    },
    resetEmailServeConfig() {
      resetEmailServeConfigData().then((res) => {
        if (res === 1) {
          this.getEmailServeConfig()
          prompt({
            i18nCode: 'tip.reset.success',
            type: 'success',
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.reset.none',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.reset.error',
            type: 'error',
          })
        }
      })
    },
    saveEmailServeConfig(obj) {
      saveEmailServeConfigData(obj).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    testEmailServeConfig(obj) {
      this.data.form.email.loading = true
      testEmailServeConfigData(obj).then((res) => {
        this.data.form.email.test = res
        this.data.form.email.loading = false
        if (res) {
          prompt({
            i18nCode: 'tip.test.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.test.error',
            type: 'error',
          })
        }
      })
    },
    getReviseTimeConfig() {
      queryReviseTimeConfigData().then((res) => {
        this.data.form.reviseTime.timingMode = res.timingMode
        this.data.form.reviseTime.centerSeverTime = res.centerSeverTime
        if (res.timingMode === 0) {
          this.data.form.reviseTime.configCenterSeverTime = res.configCenterSeverTime
        }
        if (res.timingMode === 1) {
          this.data.form.reviseTime.ntpSeverConfig = res.ntpSeverConfig
          this.data.form.reviseTime.autoValidate = res.autoValidate
          this.data.form.reviseTime.settingCycle = res.settingCycle
          this.data.form.reviseTime.settingCycleValue = res.settingCycleValue
          this.data.form.reviseTime.settingTime = res.settingTime
        }
      })
    },
    resetReviseTimeConfig() {
      resetReviseTimeConfigData().then((res) => {
        if (res === 1) {
          this.getReviseTimeConfig()
          prompt({
            i18nCode: 'tip.reset.success',
            type: 'success',
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.reset.none',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.reset.error',
            type: 'error',
          })
        }
      })
    },
    saveReviseTimeConfig(obj) {
      saveReviseTimeConfigData(obj).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    testReviseTimeConfig(obj) {
      this.data.form.reviseTime.loading = true
      testReviseTimeConfigData(obj).then((res) => {
        this.data.form.reviseTime.test = res
        this.data.form.reviseTime.loading = false
        if (res) {
          prompt({
            i18nCode: 'tip.test.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.test.error',
            type: 'error',
          })
        }
      })
    },
    getDatabaseSafeguard() {
      queryDatabaseSafeguardData().then((res) => {
        this.data.form.database.safeguard = res.safeguardCycle
        this.data.form.database.dataRetainTime = res.safeguardMonth
      })
    },
    saveDatabaseSafeguard(obj) {
      saveDatabaseSafeguardData(obj).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    getDatabaseSafeguardTable() {
      queryDatabaseSafeguardTableData().then((res) => {
        this.data.table.database = res
      })
    },
    getLicenseList() {
      queryLicenseListData().then((res) => {
        this.data.table.license = res
      })
    },
    uploadLicense(formData) {
      uploadLicenseData(formData).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.upload.success',
            type: 'success',
          })
          this.getLicenseList()
          this.getLicenseRemainDay()
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.upload.format',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.upload.error',
            type: 'error',
          })
        }
      })
    },
    downloadLicense() {
      downloadLicense().then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    getDataBackup() {
      queryDataBackupData().then((res) => {
        this.data.form.backup.type = res.type ? String(res.type) : '1'
        this.data.form.backup.cycle = res.backupCycle ? res.backupCycle : 'day'
        this.data.form.backup.time = res.backupTime
        this.data.form.backup.timeValue = res.backupTimeValue
        this.data.form.backup.ip = res.ip
        this.data.form.backup.account = res.account
        this.data.form.backup.password = res.password
        this.data.form.backup.path = res.path
      })
    },
    resetDataBackup() {
      resetDataBackupData().then((res) => {
        if (res === 1) {
          this.getDataBackup()
          prompt({
            i18nCode: 'tip.reset.success',
            type: 'success',
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.reset.none',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.reset.error',
            type: 'error',
          })
        }
      })
    },
    saveDataBackup(obj) {
      saveDataBackupData(obj).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
          this.getDataBackupTable()
        }
        if (res === 0) {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    getSnapshotTask() {
      querySnapshotTaskData().then((res) => {
        this.data.form.snapshot.cycle = res.backupCycle ? res.backupCycle : 'day'
        this.data.form.snapshot.time = res.backupTime
        this.data.form.snapshot.timeValue = res.backupTimeValue
      })
    },
    resetSnapshotTask() {
      resetSnapshotTaskData().then((res) => {
        if (res === 1) {
          this.getSnapshotTask()
          prompt({
            i18nCode: 'tip.reset.success',
            type: 'success',
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.reset.none',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.reset.error',
            type: 'error',
          })
        }
      })
    },
    saveSnapshotTask(obj) {
      saveSnapshotTaskData(obj).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    getSnapshotTable() {
      querySnapshotTableData().then((res) => {
        this.data.table.snapshot = res
      })
    },
    executeSnapshot() {
      addSnapshotData().then((res) => {
        if (res === 'success') {
          prompt({
            i18nCode: 'tip.execute.success',
            type: 'success',
          })
          this.getSnapshotTable()
        } else {
          prompt({
            i18nCode: 'tip.execute.error',
            type: 'error',
          })
        }
      })
    },
    uploadSnapshot(formData) {
      uploadSnapshot(formData).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.upload.success',
            type: 'success',
          })
          this.getSnapshotTable()
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.upload.dunplicate',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.upload.format',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.upload.error',
            type: 'error',
          })
        }
      })
    },
    downloadSnapshot(obj) {
      downloadSnapshot(obj.id).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    recoverySnapshot(obj) {
      this.data.loading.snapshotRecovery = true
      recoverySnapshot(obj.id)
        .then((res) => {
          this.data.loading.snapshotRecovery = false
          if (res === 'success') {
            prompt({
              i18nCode: 'tip.recovery.success',
              type: 'success',
            })
            prompt({
              i18nCode: 'tip.recovery.process',
              type: 'warning',
            })
            this.getSnapshotTable()
          } else if (res === 'error_file') {
            prompt({
              i18nCode: 'tip.recovery.errorfile',
              type: 'error',
            })
          } else if (res === 'error_version') {
            prompt({
              i18nCode: 'tip.recovery.errorversion',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.recovery.error',
              type: 'error',
            })
          }
        })
        .catch((error) => {
          this.data.loading.snapshotRecovery = false
          prompt({
            i18nCode: 'tip.recovery.error',
            type: 'error',
          })
          console.error(error)
        })
    },
    deleteSnapshot(obj) {
      delSnapshot(obj.id).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.delete.success',
            type: 'success',
          })
          this.getSnapshotTable()
        } else {
          prompt({
            i18nCode: 'tip.delete.error',
            type: 'error',
          })
        }
      })
    },
    getDataThere() {
      queryDataThreatData().then((res) => {
        if (res.status === '0') {
          this.data.form.threat.status = true
        } else {
          this.data.form.threat.status = false
        }
        // this.data.form.threat.id = res.id;
        // this.data.form.threat.cycleType = res.cycleType ? res.cycleType : "day";
        // this.data.form.threat.time = res.time;
        // this.data.form.threat.period = res.period;
        // this.data.form.threat.url = res.url;
      })
    },
    resetDataThreat() {
      resetDataThreatData().then((res) => {
        if (res === 1) {
          // this.getDataThere();
          prompt({
            i18nCode: 'tip.reset.success',
            type: 'success',
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.reset.none',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.reset.error',
            type: 'error',
          })
        }
      })
    },
    saveDataThreat(obj) {
      saveDataThreatData(obj).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    getDataBackupTable() {
      queryDataBackupTableData().then((res) => {
        this.data.table.dataBackup = res
      })
    },
    clickSubmitUpload(formData) {
      this.data.form.uploads.loading = true
      uploadThreatData(formData)
        .then((res) => {
          this.data.form.uploads.loading = false
          if (res === 1) {
            prompt({
              i18nCode: 'management.system.threat.upload.successUpload',
              type: 'success',
            })
          } else if (res === 2) {
            prompt({
              i18nCode: 'management.system.threat.emptyError',
              type: 'error',
            })
          } else if (res === 3) {
            prompt({
              i18nCode: 'management.system.threat.structureError',
              type: 'error',
            })
          } else if (res === 4) {
            prompt({
              i18nCode: 'management.system.threat.typeError',
              type: 'error',
            })
          } else if (res === 5) {
            prompt({
              i18nCode: 'management.system.threat.nameError',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
    getSysAlarmNotice() {
      querySysAlarmNotice().then((res) => {
        this.data.form.sysAlarm = res
      })
    },
    saveSysAlarmNotice(obj) {
      saveSysAlarmNotice(obj).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-tabs {
  height: 100%;

  ::v-deep .el-tabs__content {
    height: calc(100% - 60px);

    .el-tab-pane {
      height: 100%;
      overflow: auto;

      .tab-context-wrapper {
        height: 90%;
        overflow: auto;

        .el-form {
          padding-top: 20px;

          .el-form-item {
            margin: 10px 0;
          }

          .item-flex {
            display: flex;
          }

          .el-input__inner {
            text-align: left;
          }

          .el-input-number {
            .el-input__inner {
              text-align: center;
            }
          }
        }

        .el-divider {
          height: 5px;
          @include theme('background-color', main-bg-color);
        }

        .el-table {
          border-top: 1px solid;
          @include theme('border-color', table-body-title-border-color);
        }

        h2 {
          padding-left: 30px;
          margin-bottom: 20px;
        }

        .tab-footer-button {
          position: absolute;
          right: 5%;
          bottom: 5%;
        }

        .header-button-upload {
          display: inline;
          .el-upload {
            width: 100%;
          }
        }
      }
    }
  }
}
</style>
