<!--
 * @Description: 仪表盘
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <el-container>
    <el-header height="32px" class="dashboard-header">
      <section class="dashboard-header-name">
        <template v-if="data.names.length > 0 && active.state === 'edit'">
          <h1>{{ $t('visualization.dashboard.dropdown.name') }}</h1>
          <el-input ref="dashboardNameInput" v-model="active.dashboard.dashboardName" maxlength="16" show-word-limit size="mini"></el-input>
        </template>
      </section>
      <section class="dashboard-header-operation">
        <template v-if="data.names.length > 0">
          <el-dropdown trigger="click" placement="bottom" @command="changeDashboardName">
            <el-button>
              {{ activeDashboardName }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in data.names" :key="item.dashboardId" :command="item">
                {{ item.dashboardName }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown trigger="click" placement="bottom" @command="changeDashboardRefreshTime">
            <el-button>
              {{ activeRefreshTime }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in data.times" :key="item.time" :command="item">
                {{ item.time === 0 ? item.label : item.time + item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown trigger="click" placement="bottom" @command="changeDashboardOperation">
            <el-button>
              {{ $t('visualization.dashboard.dropdown.operation') }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-for="item in data.operation" :key="item.state" :command="item.state">
                {{ item.label }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-button v-if="active.state === 'edit'" class="header-button" @click="clickSaveDashboard">
            {{ $t('button.save') }}
          </el-button>
          <el-button v-if="active.state === 'edit'" class="header-button" @click="clickDeleteDashboard">
            {{ $t('button.delete') }}
          </el-button>
        </template>
      </section>
    </el-header>
    <el-main class="dashboard-main">
      <template v-if="data.names.length === 0">
        <h2 class="dashboard-empty" @click="clickVisibleDashboard">
          {{ $t('visualization.dashboard.tip.empty.dashboard') }}
        </h2>
      </template>
      <template v-else>
        <dashboard-designer v-if="active.state === 'edit'" :dashboard-data.sync="data.dashboard"></dashboard-designer>
        <dashboard-generator v-if="active.state === 'show'" :dashboard-data="data.dashboard"></dashboard-generator>
      </template>
    </el-main>
    <layout-dialog
      :visible.sync="visible"
      :title="$t('visualization.dashboard.dropdown.create')"
      :width="'60%'"
      has-name
      @on-submit="clickSubmitCreateDashboard"
    ></layout-dialog>
  </el-container>
</template>

<script>
import DashboardDesigner from './DashboardDesigner'
import DashboardGenerator from './DashboardGenerator'
import LayoutDialog from './DashboardLayoutDialog'
import { prompt } from '@util/prompt'
import {
  addDashboardData,
  deleteDashboardData,
  queryDashboardNameData,
  queryDashboardData,
  updateDashboardData,
} from '@api/visualization/dashboard-api'

export default {
  name: 'VisualizationDashboard',
  components: {
    DashboardDesigner,
    DashboardGenerator,
    LayoutDialog,
  },
  data() {
    return {
      data: {
        names: [],
        times: [
          {
            time: 0,
            label: this.$t('visualization.dashboard.dropdown.manual'),
          },
          {
            time: 1,
            label: this.$t('visualization.dashboard.dropdown.minute'),
          },
          {
            time: 3,
            label: this.$t('visualization.dashboard.dropdown.minute'),
          },
          {
            time: 5,
            label: this.$t('visualization.dashboard.dropdown.minute'),
          },
          {
            time: 10,
            label: this.$t('visualization.dashboard.dropdown.minute'),
          },
        ],
        operation: [
          {
            state: 'show',
            label: this.$t('visualization.dashboard.dropdown.create'),
          },
          {
            state: 'edit',
            label: this.$t('visualization.dashboard.dropdown.edit'),
          },
        ],
        dashboard: [],
      },
      visible: false,
      active: {
        dashboard: {},
        state: 'show',
      },
      interval: {
        label: '',
        duration: -1,
        timer: null,
      },
    }
  },
  computed: {
    activeDashboardName() {
      return this.active.dashboard.dashboardName ? this.active.dashboard.dashboardName : this.$t('visualization.dashboard.tip.empty.dashboard')
    },
    activeRefreshTime() {
      return this.interval.duration === -1
        ? this.$t('visualization.dashboard.dropdown.refresh')
        : this.interval.duration === 0
        ? this.interval.label
        : this.interval.duration + this.interval.label
    },
  },
  mounted() {
    this.getDashboardName()
  },
  destroyed() {
    this.handleClearInterval()
  },
  methods: {
    changeDashboardName(command) {
      this.active.dashboard = command
      this.getDashboard(command['dashboardId'])
      this.active.state = 'show'
    },
    changeDashboardRefreshTime(command) {
      this.active.state = 'show'
      this.handleClearInterval()
      this.interval.label = command.label
      this.interval.duration = command.time
      const refresh = () => {
        this.getDashboard(this.active.dashboard['dashboardId'])
      }
      if (command.time === 0) {
        refresh()
      } else {
        this.interval.timer = setInterval(refresh, this.interval.duration * 60000)
      }
    },
    changeDashboardOperation(command) {
      if (command === 'show') {
        this.clickVisibleDashboard()
      }
      if (command === 'edit') {
        this.active.state = 'edit'
      }
    },
    clickVisibleDashboard() {
      this.visible = true
    },
    clickSubmitCreateDashboard(dashboard) {
      this.addDashboard(dashboard)
    },
    clickSaveDashboard() {
      if (this.active.dashboard.dashboardName !== '') {
        this.updateDashboard({
          dashboardId: this.active.dashboard.dashboardId,
          dashboardName: this.active.dashboard.dashboardName,
          dashboardData: this.data.dashboard,
        })
      } else {
        this.$refs.dashboardNameInput.focus()
        prompt({
          i18nCode: 'visualization.dashboard.tip.name',
          type: 'warning',
        })
      }
    },
    clickDeleteDashboard() {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.deleteDashboard(this.active.dashboard.dashboardId)
      })
    },
    handleClearInterval() {
      clearInterval(this.interval.timer)
      this.interval.timer = null
    },
    getDashboardName() {
      queryDashboardNameData().then((res) => {
        this.data.names = res
        if (res.length > 0) {
          this.active.dashboard = this.data.names[0]
          this.active.state = 'show'
          this.getDashboard(this.active.dashboard['dashboardId'])
        }
      })
    },
    addDashboard(obj) {
      addDashboardData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.create.success',
              type: 'success',
            },
            () => {
              this.getDashboardName()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.create.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.create.error',
            type: 'error',
          })
        }
      })
    },
    deleteDashboard(dashboardId) {
      deleteDashboardData(dashboardId).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.delete.success',
              type: 'success',
            },
            () => {
              this.getDashboardName()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.delete.error',
            type: 'error',
          })
        }
      })
    },
    updateDashboard(obj) {
      updateDashboardData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getDashboardName()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeatName',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    getDashboard(dashboardId) {
      queryDashboardData(dashboardId).then((res) => {
        this.data.dashboard = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-container {
  .dashboard-header {
    display: flex;
    justify-content: space-between;

    &-name {
      display: flex;
      align-items: center;

      h1 {
        width: 120px;
        height: 100%;
        line-height: 32px;
      }
    }

    &-operation {
      .header-button {
        margin: 0 10px;
      }

      .el-dropdown {
        margin: 0 10px;
      }
    }
  }

  .dashboard-main {
    position: relative;

    .dashboard-empty {
      position: absolute;
      left: 50%;
      top: 50%;
      padding: 20px 50px;
      font-size: 30px;
      cursor: pointer;
      transform: translate(-50%, -200%);

      &:hover {
        color: #1873d7;
        border: 1px dashed #1873d7;
        border-radius: 10px;
        box-sizing: border-box;
      }
    }
  }
}
</style>
