export default {
  behaviorStrategy: {
    title: 'Abnormal Behavior Strategy',
    label: {
      legalIp: 'Legal IP',
      systemName: 'Information System Name',
      systemIp: 'Information System IP',
      abnormalAction: 'Abnormal Behavior',
      abnormalActionStr: 'Abnormal Behavior',
      status: 'Status',
      timeRange: 'Detection Time',
      startTime: 'Start Time',
      endTime: 'End Time',
      externalSystem: 'Forward to External System',
      externalMail: 'Forward to Email',
    },
    placeholder: {
      legalIp: 'Legal IP',
      systemName: 'Information System Name',
      systemIp: 'Information System IP',
      abnormalAction: 'Abnormal Behavior',
      status: 'Status',
      startTime: 'Start Time',
      endTime: 'End Time',
      fuzzyField: 'Information System Name/IP',
    },
  },
}
