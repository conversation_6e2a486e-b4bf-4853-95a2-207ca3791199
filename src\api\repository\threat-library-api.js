import request from '@util/request'

export function queryThreatInfoTableData(obj) {
  return request({
    url: `/threatinfo/list`,
    method: 'get',
    params: obj || {},
  })
}

export function queryThreatInfoTotalData(obj) {
  return request({
    url: `/threatinfo/list/total`,
    method: 'get',
    params: obj || {},
  })
}

export function queryThreatInfoDetailData(obj) {
  return request({
    url: `/threatinfo/detail/${obj.threatId}`,
    method: 'get',
    params: {
      timestamp: obj.timestamp,
    },
  })
}
