<!--
 * @Description: 监控器展示 - 故障事件组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-16
 * @Editor:
 * @EditDate: 2021-08-16
-->
<template>
  <div class="router-wrap-table">
    <main class="table-body-main">
      <el-table
        v-loading="table.loading"
        :data="table.data"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="320"
        @selection-change="tableSelectsChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column
          v-for="(item, index) in tableColoums"
          :key="index"
          :prop="item"
          :label="$t(`monitor.management.view.fault.${item}`)"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <p v-if="item === 'currentStatus'">
              {{ scope.row[item] === 1 ? $t('monitor.management.status.normal') : $t('monitor.management.status.abnormal') }}
            </p>
            <p v-else>
              {{ scope.row[item] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="80">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetailButton(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
    <fault-detail-info :visible.sync="dialog.visible" :title-name="title" :model="dialog.model" :columns="dialog.columns"></fault-detail-info>
  </div>
</template>

<script>
import FaultDetailInfo from './FaultDetailInfo'
import { queryMonitorFaultTable } from '@api/monitor/view-api'
export default {
  components: {
    FaultDetailInfo,
  },
  props: {
    params: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      title: this.$t('monitor.management.view.fault.title'),
      table: {
        loading: false,
        data: [],
        selected: [],
        debounce: null,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      tableColoums: ['faultName', 'currentStatus', 'faultClassName', 'edName', 'domaName'],
      dialog: {
        visible: false,
        model: {},
        columns: [
          { key: 'faultName', label: this.$t('monitor.management.view.fault.faultName') },
          { key: 'currentStatus', label: this.$t('monitor.management.view.fault.currentStatus') },
          { key: 'edName', label: this.$t('monitor.management.view.fault.edName') },
          { key: 'domaName', label: this.$t('monitor.management.view.fault.domaName') },
          { key: 'faultClassName', label: this.$t('monitor.management.view.fault.faultClassName') },
          { key: 'faultLevel', label: this.$t('monitor.management.view.fault.faultLevel') },
          { key: 'enterDate', label: this.$t('monitor.management.view.fault.enterDate') },
          { key: 'faultModule', label: this.$t('monitor.management.view.fault.faultModule') },
          { key: 'faultSolution', label: this.$t('monitor.management.view.fault.faultSolution') },
        ],
      },
    }
  },
  watch: {
    params: {
      handler() {
        this.changeQueryTable()
      },
      deep: true,
    },
  },
  mounted() {
    this.changeQueryTable()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      this.queryTableData()
    },
    tableSelectsChange(select) {
      this.table.selected = select
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    clickDetailButton(row) {
      this.dialog.visible = true
      this.dialog.model = row
    },
    queryTableData() {
      const params = Object.assign({}, this.params, {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      })
      this.table.loading = true
      this.pagination.visible = false
      queryMonitorFaultTable(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
  },
}
</script>
