/**
 * @func 防抖函数
 * @param {function} func - 想防止抖动的函数
 * @param {number} wait - 等待时间(ms)
 * @param {boolean} immediate - 是否立即调用
 * <AUTHOR> @date 2020/9/18
 */
export function debounce(func, wait = 500, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp
    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) {
          context = args = null
        }
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * @func 添加水印
 * @param {string} context - 水印的内容
 * @param {dom} parentNode - 添加的父级元素
 * @param {string} font - 字体样式
 * @param {string} textColor - 字体颜色
 * <AUTHOR> @date 2020/12/29
 */
export function watermark(context, parentNode, font, textColor) {
  const canvas = document.createElement('canvas')
  const ctx = canvas.getContext('2d')
  parentNode.appendChild(canvas)
  canvas.width = 200
  canvas.height = 150
  canvas.style.display = 'none'
  ctx.rotate((-20 * Math.PI) / 180)
  ctx.font = font || '16px Microsoft JhengHei'
  ctx.fillStyle = textColor || 'rgba(180, 180, 180, 0.5)'
  ctx.textAlign = 'left'
  ctx.textBaseline = 'Middle'
  ctx.fillText(context, canvas.width / 10, canvas.height / 2)
  parentNode.style.backgroundImage = `url("${canvas.toDataURL('image/png')}")`
}

/**
 * @func 获取颜色值
 * @return {array}
 * @note - echarts取色值
 * <AUTHOR> @date 2019/6/29
 */
export function getColor() {
  return [
    '#37a2da', // rgba(55, 162, 218, 1)
    // "#ffb136",
    // "#e74a25",
    // "#00bbd9",
    '#ffdb5c', // rgba(255, 219, 92, 1)
    '#fb7293', // rgba(251, 114, 147, 1)
    '#a7d691', // rgba(167, 214, 145, 1)
    '#9d96f5', // rgba(157, 150, 245, 1)
    '#67e0e3', // rgba(103, 224, 227, 1)
    '#ff9f7f', // rgba(255, 159, 127, 1)
    '#32c5e9', // rgba(50, 197, 233, 1)
    '#fdfa4e', // rgba(253, 250, 78, 1)
    '#ee6666', // rgba(238, 102, 102, 1)
    '#8378ea', // rgba(131, 120, 234, 1)
    '#ffc227', // rgba(255, 194, 39, 1)
    '#8fcde5', // rgba(143, 205, 229, 1)
    '#91cc75', // rgba(145, 204, 117, 1)
    '#e062ae', // rgba(224, 98, 174, 1)
    '#96bfff', // rgba(150, 191, 255, 1)
    '#fd9d75', // rgba(253, 157, 117, 1)
    '#73c0de', // rgba(115, 192, 222, 1)
    '#fbd379', // rgba(251, 211, 121, 1)
    '#62b58e', // rgba(98, 181, 142, 1)
    '#9a60b4', // rgba(241, 133, 133, 1)
    '#f18585', // rgba(241, 133, 133, 1)
    '#768dd1', // rgba(118, 141, 209, 1)
    '#fac858', // rgba(250, 200, 88, 1)
    '#92e1ff', // rgba(167, 214, 145, 1)
    '#a7d691', // rgba(167, 214, 145, 1)
    '#e690d1', // rgba(230, 144, 209, 1)
    '#3ba272', // rgba(59, 162, 114, 1)
    '#fd5151', // rgba(253, 81, 81, 1)
    '#5470c6', // rgba(84, 112, 198, 1)
    '#fc8452', // rgba(252, 132, 82, 1)
    '#5ed7d3', // rgba(94, 215, 211, 1)
    '#febe13', // rgba(254, 190, 19, 1)
    '#ea7ccc', // rgba(234, 124, 204, 1)
    '#85f67a', // rgba(133, 246, 122, 1)
  ]
}
