export default {
  strategy: {
    title: 'Alarm Strategy',
    table: {
      strategyName: 'Strategy Name',
      description: 'Description',
      createTime: 'Creation Time',
      state: 'Usage Status',
      handle: 'Operation',
    },
    label: {
      alarmName: 'Audit Event Name Contains',
      targetIp: 'Target IP Contains',
      alarmType: 'Audit Type',
      alarmLevel: 'Audit Event Level',
      systemType: 'Log Source Device',
      snmpForwardServer: 'Forward to External System',
      mailTo: 'Forward to Email',
      description: 'Remarks',
    },
    placeholder: {
      inputSearch: 'Strategy Name/Description',
    },
  },
}
