import request from '@util/request'

// 查询审计事件列表
export function queryTableData(obj) {
  return request({
    url: '/event/audit/events',
    method: 'get',
    params: obj || {},
  })
}

// 查询审计类型下拉框
export function queryAuditType() {
  return request({
    url: '/event/audit/combo/audit-type',
    method: 'get',
  })
}

// 查询审计策略下拉框
export function queryStrategy() {
  return request({
    url: '/event/audit/combo/audit-strategy',
    method: 'get',
  })
}

// 查询审计人员下拉框
export function queryUser() {
  return request({
    url: '/event/audit/combo/audit-user',
    method: 'get',
  })
}

// 查询某审计事件下安全事件列表
export function queryData(id) {
  return request({
    url: '/event/audit/events/security',
    method: 'get',
    params: id || '',
  })
}

// 查询某审计事件下关联事件列表
export function queryRelevance(id) {
  return request({
    url: '/event/audit/events/associated',
    method: 'get',
    params: id || '',
  })
}

// 查询某审计事件威胁事件列表
export function queryThreat(id) {
  return request({
    url: '/event/audit/events/apt',
    method: 'get',
    params: id || '',
  })
}

// 查询审计事件自定义列
export function queryCustom() {
  return request({
    url: '/event/audit/columns',
    method: 'get',
  })
}

// 修改审计事件自定义列
export function updateCustom(obj) {
  return request({
    url: '/event/audit/columns',
    method: 'put',
    data: obj || [],
  })
}

// 导出审计事件
export function exportAudit(obj) {
  return request(
    {
      url: `/event/audit/download`,
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

// 查询原始日志
export function queryLog(obj) {
  return request({
    url: '/event/audit/events/original',
    method: 'get',
    params: obj || {},
  })
}
// 查询详情
export function queryDetails(id, time) {
  return request({
    url: `/event/audit/event/${id}/${time}`,
    method: 'get',
  })
}
// 查询审计事件总数
export function queryTotal(param) {
  return request({
    url: `/event/audit/events/total`,
    method: 'get',
    params: param || {},
  })
}
// 查询安全事件朔源总数
export function queryTotalSafe(param) {
  return request({
    url: `/event/audit/events/security/total`,
    method: 'get',
    params: param || {},
  })
}
// 查询关联事件朔源总数
export function queryTotalRelevance(param) {
  return request({
    url: `/event/audit/events/associated/total`,
    method: 'get',
    params: param || {},
  })
}
// 查询威胁事件朔源总数
export function queryTotalThreat(param) {
  return request({
    url: `/event/audit/events/apt/total`,
    method: 'get',
    params: param || {},
  })
}
// 查询原始日志总数
export function queryTotalLog(param) {
  return request({
    url: `/event/audit/events/original/total`,
    method: 'get',
    params: param || {},
  })
}
