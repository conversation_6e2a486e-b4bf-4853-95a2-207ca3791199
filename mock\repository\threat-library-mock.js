const { createMockTable } = require('../util')

const detail = {
  threatId: '@ID',
  threatItem: '@IP',
  'category|1': ['ip', 'domain', 'url', 'email', 'hash'],
  total: '21',
  'threatLevel|1': ['一般', '低级', '中级', '高级', '严重'],
  firstTime: '@DATETIME',
  lastTime: '@DATETIME',
  timestamp: '@DATETIME',
  country: '@REGION',
  province: '@PROVINCE',
  city: '@CITY',
  organization: '@PROTOCOL',
  operator: '@NAME',
  code: 'DE',
  'longitude|0-180.2': 1,
  'latitude|0-90.2': 1,
  hashType: '@NAME',
  objectType: '1',
  record: 1,
  actions: 'malware',
  malwares: '',
  attackInProtocol: 'malware',
  activeTime: '@DATETIME',
  confidence: '高',
  channel: 'ip_apt',
}

const tempList = () => {
  return createMockTable(detail, 20)
}

module.exports = [
  {
    url: '/threatinfo/list/total',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: /^(?!0)\d{3,5}/,
      }
    },
  },
  {
    url: '/threatinfo/list',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: tempList,
      }
    },
  },
  {
    url: '/threatinfo/detail/[a-zA-Z0-9]+',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: detail,
      }
    },
  },
]
