<!--
 * @Description: 网络管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model.trim="queryInput"
              :placeholder="$t('tip.placeholder.query', [$t('asset.networkManagement.name')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQueryEvent('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button" @click="inputQueryEvent('e')">
            <el-button v-has="'query'">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAdd">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDelete">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('asset.networkManagement.net') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="Table"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="TableRowChange"
          @selection-change="TableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="name" :label="$t('asset.networkManagement.name')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="domainName" :label="$t('asset.networkManagement.domainName')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="allIpv" :label="$t('asset.networkManagement.allIpv')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="remark" :label="$t('asset.networkManagement.remark')" show-overflow-tooltip></el-table-column>
          <el-table-column fixed="right" width="200">
            <template slot-scope="scope">
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加弹窗-->
    <Au-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form"
      :width="'35%'"
      @on-submit="clickSubmitAdd"
    ></Au-dialog>
    <!--修改弹窗-->
    <Au-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form="dialog.form"
      :width="'35%'"
      @on-submit="clickSubmitUpdate"
    ></Au-dialog>
  </div>
</template>
<script>
import AuDialog from './NetManagementAU'
import { prompt } from '@util/prompt'
import { deleteData, queryTableData } from '@api/asset/network-management-api'
import { debounce } from '@util/effect'

export default {
  name: 'AssetNetworkManagement',
  components: {
    AuDialog,
  },
  data() {
    return {
      queryInput: '', // 搜索框内容
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
        visible: true,
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.add', [this.$t('asset.networkManagement.net')]),
          update: this.$t('dialog.title.update', [this.$t('asset.networkManagement.net')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false,
          update: false,
        },
        form: {
          model: {
            // 弹出表单绑定值
            domainName: 'IPv4',
            name: '',
            startIpv: '',
            endIpv: '',
            remark: '',
            id: '',
          },
          info: {
            // 弹出表单信息
            domainName: {
              key: 'domainName',
              label: this.$t('asset.networkManagement.domainName'),
            },
            name: {
              key: 'name',
              label: this.$t('asset.networkManagement.name'),
            },
            startIpv: {
              key: 'startIpv',
              label: this.$t('asset.networkManagement.startIP'),
            },
            endIpv: {
              key: 'endIpv',
              label: this.$t('asset.networkManagement.endIP'),
            },
            remark: {
              key: 'remark',
              label: this.$t('asset.networkManagement.remark'),
            },
          },
        },
      },
      queryDebounce: null,
    }
  },
  mounted() {
    // 查询列表
    this.getTableData()
    this.initDebounce()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          inputVal: this.queryInput,
        }
        this.getTableData(params)
      }, 500)
    },
    // 查询列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryTableData(params).then((res) => {
        if (res) {
          this.data.table = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 删除
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteData(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.inputQueryEvent()
              }
            )
          } else if (res === 5) {
            prompt({
              i18nCode: 'tip.delete.running',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 清空表单数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        domainName: 'IPv4',
        name: '',
        startIpv: '',
        endIpv: '',
        remark: '',
        id: '',
      }
    },
    // 点击添加操作并弹出对话框操作
    clickAdd() {
      this.clearDialogFormModel()
      this.dialog.visible.add = true
    },
    // 提交添加表单操作
    clickSubmitAdd(formModel) {
      if (formModel === 'true') {
        this.queryInput = ''
        this.getTableData()
      }
    },
    // 点击删除操作
    clickDelete(row) {
      this.delete(row.id)
    },
    // 点击批量删除操作
    clickBatchDelete() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.id).toString()
        this.delete(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击修改并弹出对话框操作
    clickUpdate(row) {
      this.TableRowChange(row)
      this.clearDialogFormModel()
      this.dialog.form.model = {
        domainName: row.domainName,
        name: row.name,
        startIpv: row.startIpv,
        endIpv: row.endIpv,
        remark: row.remark,
        id: row.id,
      }
      this.dialog.visible.update = true
    },
    // 提交修改表单操作
    clickSubmitUpdate(formModel) {
      if (formModel === 'true') {
        this.inputQueryEvent()
      }
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.inputQueryEvent('e')
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQueryEvent()
    },
    // 列表多选改变
    TableSelectsChange(select) {
      this.data.selected = select
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 输入框搜索方法
    inputQueryEvent(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
  },
}
</script>
