<!--
 * @Description: 代理服务器 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-16
 * @Editor:
 * @EditDate: 2021-07-16
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ tableTitle }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
      >
        <el-table-column v-for="(item, key) in columns" :key="key" :prop="item" :label="$t(`management.proxy.label.${item}`)" show-overflow-tooltip>
          <template slot-scope="scope">
            <p v-if="item === 'status'">
              {{ scope.row[item] === 1 ? $t('code.status.on') : $t('code.status.off') }}
            </p>
            <p v-else>
              {{ scope.row[item] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="operationState" :label="$t('management.proxy.label.operationstatus')">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.operationState" :active-value="1" :inactive-value="0" @change="toggleStatus(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column width="200" align="right">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.isCenter !== 1 && scope.row.status === 1 && scope.row.operationState === 1"
              v-has="'handle'"
              class="el-button--blue"
              @click="clickNetworkConfig(scope.row)"
            >
              {{ $t('button.networkConfig') }}
            </el-button>
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    tableTitle: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      columns: ['ip', 'status', 'updateTime'],
    }
  },
  methods: {
    clickNetworkConfig(row) {
      this.$emit('on-network-config', row)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    toggleStatus(row) {
      const toggleObj = Object.assign({}, row)
      row.operationState = row.operationState === 1 ? 0 : 1
      this.$emit('on-toggle-status', toggleObj)
    },
  },
}
</script>
