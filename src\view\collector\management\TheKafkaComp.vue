<!--
 * @Description: 采集器管理 - Kafka连接
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-04-18
 * @Editor:
 * @EditDate: 2022-04-18
-->
<template>
  <el-form ref="kafkaForm" :model="form.model" :rules="rules" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="propertyKind" :label="form.info.propertyKind.label">
          <el-cascader
            ref="cascader"
            v-model="form.model.propertyKind"
            filterable
            clearable
            :options="deviceTypeOption"
            :placeholder="$t('collector.management.placeholder.propertyKind')"
            :props="{ expandTrigger: 'hover' }"
          ></el-cascader>
          &nbsp;&nbsp;
          <i class="el-icon-s-operation icon-log-source" :title="$t('collector.management.dialog.title.logSourceAdd')" @click="clickAddLogSource"></i>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="topic" :label="form.info.topic.label">
          <el-input v-model.trim="form.model.topic" maxlength="100"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item prop="kafkaAddress" :label="form.info.kafkaAddress.label">
          <el-input v-model.trim="form.model.kafkaAddress"></el-input>
        </el-form-item>
      </el-col>
      <span class="tip-address">注：输入格式为ip:端口，多个设备之间“,”分隔</span>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item :prop="form.info.userName.key" :label="form.info.userName.label">
          <el-input v-model.trim="form.model.userName" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :prop="form.info.password.key" :label="form.info.password.label">
          <el-input v-model.trim="form.model.password" :show-password="true" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <!--<el-row
            v-for="(item,index) in columnOption[3]"
            :key="index">
            <el-col
                v-for="(itemCol,colIndex) in item"
                :key="colIndex"
                :span="12">
                <el-form-item
                    :prop="itemCol.key"
                    :label="itemCol.label">
                    <el-input
                        v-model.trim="form.model[itemCol.key]"
                        :show-password="itemCol.key==='password'? true: false">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>-->
    <el-row>
      <el-col :span="12">
        <el-form-item prop="strategy" :label="form.info.strategy.label">
          <el-select v-model="form.model.strategy" filterable clearable :placeholder="$t('collector.management.placeholder.strategy')">
            <el-option v-for="item in filterOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="sourcePage === 'add'" :span="12">
        <el-form-item prop="isAsset" :label="form.info.isAsset.label">
          <el-checkbox v-model="form.model.isAsset" :checked="form.model.isAsset === 1 ? true : false" :true-label="1" :false-label="0"></el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  props: {
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    deviceTypeOption: {
      required: true,
      type: Array,
    },
    filterOption: {
      required: true,
      type: Array,
    },
    sourcePage: {
      type: String,
      default: 'add',
    },
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  methods: {
    validateForm() {
      let validate = false
      this.$refs.kafkaForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    clickAddLogSource() {
      this.$emit('on-add-logsource')
    },
  },
}
</script>
<style lang="scss" scoped>
.el-dialog__body {
  .tip-address {
    line-height: 32px;
    padding-right: 12px;
    vertical-align: middle;
    font-size: 12px;
    color: #1873d7;
  }
}
::v-deep.icon-log-source {
  font-size: 16px;
  vertical-align: middle;
  color: #1873d7;
}
</style>
