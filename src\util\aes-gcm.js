import crypto from 'crypto'
const iv = '01234ABC56789DEF'
// npm install crypto --save-dev

export default {
  // 加密
  encrypt(word, aesKey) {
    const iv = crypto.randomBytes(12)
    // const iv = [190, 200, 137, 54, 18, 169, 129, 46, 171, 214, 181, 235, 240, 9];
    // const iv = Buffer.from("01234ABC5678");
    const cipher = crypto.createCipheriv('aes-256-gcm', 'd3+KZq3lsdy5Hgd1VRVQFUhA9jU0PaBH', iv)
    const encrypted = cipher.update(word, 'utf8')
    const end = cipher.final()
    const tag = cipher.getAuthTag()
    const res = Buffer.concat([encrypted, end, tag])
    return res.toString('base64')
  },
  decrypt(word, aesKey) {
    if (!word) {
      return ''
    }
    const md5 = crypto.createHash('md5')
    const result = md5.update(aesKey).digest()
    const decipher = crypto.createDecipheriv('aes-128-gcm', result, iv)
    const b = Buffer.from(word, 'base64')
    decipher.setAuthTag(b.subarray(b.length - 16))
    const str = decipher.update(Buffer.from(b.subarray(0, b.length - 16), 'hex'))
    const fin = decipher.final()
    const decryptedStr = new TextDecoder('utf8').decode(Buffer.concat([str, fin]))
    try {
      return JSON.parse(decryptedStr)
    } catch (e) {
      return decryptedStr
    }
  },
  // bcd-256-gcm加密
  aesCbcEncryptToBase64(data, key) {
    const iv = this.generateRandomInitvector()
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv)
    let encryptedBase64 = ''
    cipher.setEncoding('base64')
    cipher.on('data', (chunk) => (encryptedBase64 += chunk))
    cipher.on('end', () => {
      // do nothing console.log(encryptedBase64);
      // Prints: some clear text data
    })
    cipher.write(data)
    cipher.end()
    var ivBase64 = this.base64Encoding(iv)
    return ivBase64 + ':' + encryptedBase64
  },
  // bcd-256-gcm解密
  aesCbcDecryptFromBase64(data, key) {
    var dataSplit = data.split(':')
    var iv = this.base64Decoding(dataSplit[0])
    var ciphertext = dataSplit[1]
    const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv)
    let decrypted = ''
    decipher.on('readable', () => {
      while ((chunk = decipher.read()) !== null) {
        decrypted += chunk.toString('utf8')
      }
    })
    decipher.on('end', () => {
      // do nothing console.log(decrypted);
    })
    decipher.write(ciphertext, 'base64')
    decipher.end()
    return decrypted
  },
  generateRandomInitvector() {
    return crypto.randomBytes(16)
  },
  base64Encoding(input) {
    return input.toString('base64')
  },
  base64Decoding(input) {
    return Buffer.from(input, 'base64')
  },
}
