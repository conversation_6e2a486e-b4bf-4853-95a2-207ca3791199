export default {
  strategy: {
    header: 'Collection Filter Strategy',
    table: {
      policyName: 'Strategy Name',
      level: 'Log Level',
      eventTypeName: 'Event Type',
      srcIpv: 'Source IP',
      dstIpv: 'Destination IP',
      describe: 'Description',
      handle: 'Operation',
    },
    dialog: {
      title: {
        add: 'Add Collection Filter Strategy',
        update: 'Update Collection Filter Strategy',
      },
      columns: {
        policyName: 'Strategy Name',
        level: 'Log Level',
        eventType: 'Event Type',
        srcIpv: 'Source IP',
        dstIpv: 'Destination IP',
        describe: 'Description',
      },
    },
    placeholder: {
      input: 'Strategy Name',
      policyName: 'Strategy Name',
      level: 'Log Level',
      eventType: 'Event Type',
      srcStartIP: 'Source Start IP',
      srcEndIP: 'Source End IP',
      dstStartIP: 'Destination Start IP',
      dstEndIP: 'Destination End IP',
    },
    level: {
      none: 'No risk level',
    },
  },
}
