<!--
 * @Description: 关联事件 - 自定义弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model">
      <el-form-item>
        <el-checkbox v-model="form.model.checkAll" :indeterminate="form.model.isIndeterminate" @change="handleCheckAllChange">
          {{ $t('button.checkedAll') }}
        </el-checkbox>
        <div style="margin: 15px 0;"></div>
        <el-checkbox-group v-model="form.model.checkList" @change="handleCheckedChange">
          <el-row>
            <el-col v-for="(item, index) in columns" :key="index" :span="6">
              <el-checkbox :label="item.key">
                {{ item.label }}
              </el-checkbox>
            </el-col>
          </el-row>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      required: true,
      type: Object,
    },
    columns: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      option: ['eventCategoryName', 'eventTypeName', 'policyName', 'level', 'createDate', 'updateDate', 'count'],
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      if (this.form.model.checkList && this.form.model.checkList.length > 0) {
        this.$refs.formTemplate.validate((valid) => {
          if (valid) {
            // 给父级调用数据
            this.$emit('on-submit', this.form.model)
            this.clickCancelDialog()
          } else {
            prompt(
              {
                i18nCode: 'validate.form.warning',
                type: 'warning',
              },
              () => {
                return false
              }
            )
          }
        })
      } else {
        prompt(
          {
            i18nCode: 'validate.form.lessOne',
            type: 'warning',
          },
          () => {
            return false
          }
        )
      }
      this.$refs.dialogTemplate.end()
    },
    handleCheckAllChange(val) {
      this.form.model.checkList = val ? this.option : []
      this.form.model.isIndeterminate = false
    },
    handleCheckedChange(val) {
      this.form.model.checkAll = this.option.length === val.length
      this.form.model.isIndeterminate = val.length > 0 && val.length < this.option.length
    },
  },
}
</script>
