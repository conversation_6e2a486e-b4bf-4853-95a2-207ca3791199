export default {
  validate: {
    upload: {
      empty: 'Please upload a file',
      license: 'Only lic or license files can be uploaded',
      p12webcert: 'Only p12 files can be uploaded',
      excel: 'Only xlsx or xls files can be uploaded',
      word: 'Only doc or docx files can be uploaded',
      ppt: 'Only ppt or pptx files can be uploaded',
      pdf: 'Only pdf files can be uploaded',
      sql: 'Only sql files can be uploaded',
      zip: 'Only zip files can be uploaded',
    },
    data: {
      empty: 'No data available',
      nomore: 'No more data...',
    },
    empty: 'This field cannot be empty',
    length: {
      space: 'Input cannot contain spaces',
      maxTen: 'Input length cannot exceed 10 characters',
    },
    comm: {
      url: 'The URL you entered is invalid',
      email: 'The email you entered is invalid',
      cellphone: 'The mobile phone number you entered is invalid',
      telephone: 'The telephone number you entered is invalid',
      port: 'The port number you entered is invalid',
      pwd: 'Only letters, numbers, and symbols are allowed',
    },
    mac: {
      incorrect: 'This MAC address is invalid',
      checkRange: 'Invalid MAC address detected in input',
    },
    ip: {
      compare: 'End IP should be greater than start IP',
      incorrect: 'IP address is invalid',
      segment: 'IP segments are different',
      range: 'Port format is incorrect, only integers between 1 and 65535 are allowed',
      domain: 'Input IP or domain name is invalid',
      empty: 'IP cannot be empty',
      error: 'Please enter valid IPs of the same type!',
      checkRange: 'Invalid IP detected in input, or IP range types are not uniform',
      rangeExist: 'Start IP and end IP must both exist',
    },
    port: {
      incorrect: 'Port format is invalid',
    },
    address: {
      incorrect: 'Address format is invalid',
    },
    form: {
      empty: '{0} cannot be empty',
      error: 'Form validation failed',
      warning: 'Form validation did not pass',
      lessOne: 'Please select at least one item',
    },
    date: {
      compare: 'End time should be greater than start time',
    },
    username: {
      empty: 'Username cannot be empty',
      rule: 'Only letters, numbers, and "_", "-", "." symbols are allowed, and "_", "-", "." can only be in the middle position',
    },
    password: {
      empty: 'Password cannot be empty',
      rule: 'Password must include at least one uppercase letter, one lowercase letter, one number, and one special character',
      size: 'Password must be between 8 and 20 characters',
      sizemin8: 'Password must be at least 8 characters',
      old: {
        empty: 'Please enter old password',
      },
      new: {
        empty: 'Please enter new password',
        compare: 'New password cannot be the same as old password',
      },
      confirm: {
        empty: 'Please confirm new password',
        compare: 'The two passwords do not match',
      },
    },
    captcha: {
      empty: 'Verification code cannot be empty',
      rule: 'Only letters and numbers are allowed',
    },
    choose: 'Please select before submitting',
    onlyNumber: 'Only numbers can be entered',
    none: 'This field cannot be empty or contain spaces',
    number: {
      compare: 'End number should be greater than start number',
    },
    nameInput: {
      rule: 'Only Chinese characters, letters, numbers, "_", "-", "." symbols are allowed, and "_", "-", and "." can only be in the middle position.',
    },
    telephone: 'The phone format entered is invalid',
    email: 'The email entered is invalid',
    faxNo: 'The fax number entered is invalid',
    monitor: {
      pollDate: 'Please enter a value between 3 and 60',
      useRate: 'Please enter a value between 1 and 100',
      times: 'Please enter a value between 1 and 9',
      port: 'Port format is incorrect, only integers between 1 and 65535 are allowed.',
    },
    chooseItem: {
      empty: 'Selected information item cannot be empty',
    },
    filterCondition: 'Please select at least one collector filter condition',
  },
}
