const { TableMock } = require('../util')

// 属性列表
const tableData = new TableMock({
  total: 200,
  template: {
    assetClass: '1',
    assetClassName: '@CNAME',
    assetType: '1',
    assetTypeName: '@CNAME',
    componentType: 5,
    id: '@ID',
    length: 10,
    multiple: 2,
    name: '@NAME',
    remark: '@ZIP',
    required: 2,
  },
})

// 资产类型下拉框
const typeList = [
  {
    children: [
      { value: '172787409065345024', label: 'bigdatasss', type: '1', children: null },
      { value: '1046', label: '防病毒网关', type: '1', children: null },
      { value: '170532302286225408', label: '二层交换机', type: '1', children: null },
      { value: '1045', label: '光纤交换机', type: '1', children: null },
      { value: '110', label: '身份认证网关', type: '1', children: null },
      { value: '109', label: '流量控制', type: '1', children: null },
      { value: '108', label: 'UTM', type: '1', children: null },
      { value: '107', label: '路由器', type: '1', children: null },
      { value: '106', label: 'VPN', type: '1', children: null },
      { value: '104', label: '集线器', type: '1', children: null },
      { value: '103', label: '三层交换机', type: '1', children: null },
      { value: '102', label: '核心交换机', type: '1', children: null },
    ],
    label: '网络设备',
    type: null,
    value: '1',
  },
  {
    children: [
      { value: '171200221789814784', label: 'w', type: '2', children: null },
      { value: '506', label: '漏洞扫描', type: '2', children: null },
      { value: '170537078168223744', label: 'sss', type: '2', children: null },
      { value: '164144437155332096', label: '阿迪斯', type: '2', children: null },
      { value: '105', label: '防火墙', type: '2', children: null },
      { value: '205', label: '邮件监控系统', type: '2', children: null },
      { value: '204', label: '网站安全检测系统', type: '2', children: null },
      { value: '203', label: '网络审计系统', type: '2', children: null },
      { value: '202', label: '病毒监测系统', type: '2', children: null },
      { value: '201', label: '入侵检测系统IDS', type: '2', children: null },
    ],
    label: '安全设备',
    type: null,
    value: '2',
  },
  {
    children: [
      { value: '170902842591150080', label: 'wef', type: '3', children: null },
      { value: '307', label: 'KVM控制台', type: '3', children: null },
      { value: '306', label: '存储设备', type: '3', children: null },
      { value: '305', label: '数据采集服务器', type: '3', children: null },
      { value: '304', label: '数据库服务器', type: '3', children: null },
      { value: '303', label: '应用服务器', type: '3', children: null },
      { value: '302', label: '监控设备管理中心', type: '3', children: null },
      { value: '301', label: '监控设备控制台', type: '3', children: null },
      { value: '500', label: '工作站', type: '3', children: null },
      { value: '300', label: '服务器-未知', type: '3', children: null },
    ],
    label: '服务器',
    type: null,
    value: '3',
  },
  {
    children: [
      { value: '170902787733848064', label: 'test', type: '4', children: null },
      { value: '403', label: 'LED显示屏幕', type: '4', children: null },
      { value: '402', label: '液晶屏幕', type: '4', children: null },
      { value: '401', label: '显示操作终端', type: '4', children: null },
    ],
    label: '终端',
    type: null,
    value: '4',
  },
  {
    children: [
      { value: '173116333351763968', label: '重构资产扩展属性用', type: '5', children: null },
      { value: '505', label: '复印机', type: '5', children: null },
      { value: '504', label: '碎纸机', type: '5', children: null },
      { value: '503', label: '打印机', type: '5', children: null },
      { value: '502', label: '个人办公终端', type: '5', children: null },
      { value: '501', label: '未知设备', type: '5', children: null },
    ],
    label: '其他设备',
    type: null,
    value: '5',
  },
]

// 字典列表
const dicList = [
  {
    id: '173735399644987392',
    inputVisible: false,
    name: '1231',
    propertyId: '173117246665654272',
    usage: false,
  },
  {
    id: '173735216102244353',
    inputVisible: false,
    name: '1233',
    propertyId: '173117246665654272',
    usage: false,
  },
  {
    id: '173734739662864384',
    inputVisible: false,
    name: '测试字典',
    propertyId: '173117246665654272',
    usage: true,
  },
]

module.exports = [
  // 添加属性
  {
    url: '/assetproperties/property',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  // 删除属性
  {
    url: `/assetproperties/property/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 修改属性
  {
    url: '/assetproperties/property',
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  // 修改资产字典
  {
    url: '/assetproperties/dictionaries',
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  // 查询属性列表
  {
    url: '/assetproperties/properties',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  // 查询属性下的资产字典
  {
    url: '/assetproperties/dictionaries/[A-Za-z0-9]',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: dicList,
      }
    },
  },
  // 查询资产分类下拉框
  {
    url: '/assetproperties/types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: typeList,
      }
    },
  },
]
