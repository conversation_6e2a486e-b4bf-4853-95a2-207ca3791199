export default {
  management: {
    header: 'Collector Management',
    table: {
      collectorName: 'Collector Name',
      ip: 'Collection Address',
      protName: 'Access Method',
      agentIp: 'Proxy Server',
      agentStatus: 'Proxy Status',
      runState: 'Usage Status',
      run: 'Enable/Disable',
      handle: 'Operation',
      typeName: 'Log Source Device',
      fuzzyField: 'Collector Name/Log Source Device',
    },
    dialog: {
      title: {
        add: 'Add Collector Management',
        update: 'Update Collector Management',
        logSourceAdd: 'Add Log Source Type',
      },
      exceed: 'Current limit is 1 file, please delete before uploading',
    },
    placeholder: {
      input: 'Collector Name/Collection Address/Access Method',
      innerType: 'Access Method',
      agentIp: 'Proxy Server',
      logType: 'Log Type',
      runState: 'Running Status',
      run: 'Enable',
      propertyKind: 'Log Source Device',
      strategy: 'Filter Strategy',
      dbType: 'Database Type',
      grabCycle: 'Fetch Time',
      codeWay: 'Encoding Format',
      logFile: 'Log File',
    },
    label: {
      runSuccess: 'Please select content for batch enabling',
      runError: 'Please select content for batch stopping',
      runState: {
        on: 'Running',
        off: 'Paused',
      },
      agentStatus: {
        on: 'Online',
        off: 'Offline',
      },
      useState: {
        on: 'Enabled',
        off: 'Disabled',
      },
    },
    error: {
      collectorName: 'Collector name cannot be in IP format',
    },
  },
}
