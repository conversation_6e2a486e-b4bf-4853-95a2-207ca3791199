<template>
  <el-container class="widget" style="height: 350px;">
    <el-header class="widget-header">
      <div class="widget-header-title">
        {{ $t('visualization.compliance.title.logSourceType') }}
      </div>
    </el-header>
    <el-main class="widget-main">
      <pie-chart v-if="chartData.length > 0" ref="pieChartDom" proto :option="option"></pie-chart>
      <section v-else class="chunk-empty">
        <empty-data-chart ref="emptyChartDom" :text="$t('visualization.compliance.empty.component')"></empty-data-chart>
      </section>
    </el-main>
  </el-container>
</template>

<script>
import PieChart from '@comp/ChartFactory/forecast/NestPieChart'
import EmptyDataChart from '@comp/ChartFactory/common/EmptyDataChart'
import { queryLogSourceTypeChartData } from '@api/visualization/compliance-api'

export default {
  components: {
    Pie<PERSON><PERSON>,
    EmptyDataChart,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      chartData: [],
      option: {},
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getLogSourceTypeChart()
    },
    getPieOption(data = this.data) {
      let option = {}
      if (data.length > 0) {
        option = {
          color: this.color,
          backgroundColor: 'transparent',
          tooltip: {
            trigger: 'item',
            confine: true,
          },
          grid: {
            right: 0,
            bottom: 0,
          },
          series: [
            {
              type: 'pie',
              selectedMode: 'single',
              radius: [0, '40%'],
              center: ['50%', '40%'],
              label: {
                normal: {
                  position: 'inner',
                  fontSize: 10,
                },
              },
              labelLine: {
                normal: {
                  show: false,
                },
              },
              data: data[0],
            },
            {
              type: 'pie',
              radius: ['60%', '80%'],
              center: ['50%', '40%'],
              data: data[1],
              label: {
                textBorderColor: '#000',
              },
            },
          ],
        }
      }
      return option
    },
    getLogSourceTypeChart() {
      queryLogSourceTypeChartData().then((res) => {
        this.chartData = res
        this.option = this.getPieOption(res)
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.chunk-empty {
  height: 100%;
}
.widget {
  height: 100%;
  display: block;
  &-header {
    height: 30px;
    .el-link:hover {
      color: #06699c;
    }
    &-title {
      padding-left: 2px !important;
    }
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10px;
  }
  ::v-deep .el-table__header-wrapper {
    width: 99%;
  }
}
</style>
