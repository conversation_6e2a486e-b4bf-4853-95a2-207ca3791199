<!--
 * @Description: 预测信息- 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-26
 * @Editor:
 * @EditDate: 2021-10-26
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model="filterCondition.form.keyword"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('forecast.forecastInfo.placeholder.labelName')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select v-model="filterCondition.form.type" clearable :placeholder="$t('forecast.forecastInfo.label.type')" @change="changeType">
                <el-option v-for="item in options.type" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>

            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.label"
                clearable
                :placeholder="$t('forecast.forecastInfo.label.lableName')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.label" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col align="right" :offset="10" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
import { queryComboLabel } from '@api/forecast/forecast-info-api'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      options: {
        type: [
          { value: 'total', label: '总体' },
          { value: 'eventType', label: '事件类型' },
          { value: 'srcIp', label: '源IP' },
          { value: 'dstIp', label: '目的IP' },
          { value: 'fromIp', label: '采集器IP' },
        ],
        label: [],
      },
    }
  },
  watch: {
    condition(nVal) {
      this.filterCondition = nVal
    },
    filterCondition(nVal) {
      this.$emit('update:condition', nVal)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.filterCondition.form.keyword = ''
      this.changeQueryCondition()
    },
    resetQuery() {
      this.filterCondition.form = {
        keyword: '',
        type: '',
        label: '',
      }
      this.changeQueryCondition()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
      this.changeQueryCondition()
    },
    async changeType(type) {
      await this.getComboLabel(type)
      this.filterCondition.form.label = ''
      this.changeQueryCondition()
    },
    getComboLabel(type) {
      queryComboLabel(type).then((res) => {
        this.options.label = res
      })
    },
  },
}
</script>

<style scoped></style>
