<!--
 * @Description: 告警列表 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" :loading="loading" @on-close="clickCancelDialog">
    <!--tab栏-->
    <el-tabs ref="cardTemplate" v-model="activeName" type="card" @tab-remove="removeTab" @tab-click="handleClick">
      <!--审计事件-->
      <el-tab-pane v-loading="auditLoading" name="first" :label="$t('alarm.table.panel.detail')">
        <section>
          <el-form :model="form.model" label-width="120px">
            <el-row>
              <el-col v-for="(item, index) in options.auditColumnOption" :key="index" :span="12">
                <el-form-item :label="item.label">
                  <template>
                    <level-tag v-if="item.key === 'level'" :level="form.model[item.key]"></level-tag>
                    <p v-else-if="item.key === 'state'">
                      {{ form.model[item.key] === 1 ? $t('alarm.table.state.done') : $t('alarm.table.state.pending') }}
                    </p>
                    <p v-else>
                      {{ form.model[item.key] }}
                    </p>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="24">
                <el-form-item :label="form.info.reason.label">
                  {{ form.model.reason }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </section>
      </el-tab-pane>
      <!--安全事件/关联事件/威胁事件-->
      <el-tab-pane name="second" :label="$t('alarm.table.panel.source')">
        <section class="router-wrap-table">
          <section class="table-body">
            <el-table
              v-if="sourceEventType === '0'"
              v-el-table-scroll="scrollEventTable"
              v-loading="event.loading"
              :data="event.tableData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              infinite-scroll-disabled="disableScroll"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @row-dblclick="queryOriginalLog"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column v-for="(item, index) in options.safeColumnOption" :key="index" :prop="item.key" :label="item.label">
                <template slot-scope="scope">
                  <level-tag v-if="item.key === 'level'" :level="scope.row[item.key]"></level-tag>
                  <p v-else>
                    {{ scope.row[item.key] }}
                  </p>
                </template>
              </el-table-column>
            </el-table>
            <el-table
              v-if="sourceEventType === '1'"
              v-el-table-scroll="scrollEventTable"
              v-loading="event.loading"
              :data="event.tableData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              infinite-scroll-disabled="disableScroll"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @row-dblclick="queryOriginalLog"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column v-for="(item, index) in options.relevanceColumnOption" :key="index" :prop="item.key" :label="item.label">
                <template slot-scope="scope">
                  <level-tag v-if="item.key === 'level'" :level="scope.row[item.key]"></level-tag>
                  <p v-else>
                    {{ scope.row[item.key] }}
                  </p>
                </template>
              </el-table-column>
            </el-table>
            <el-table
              v-if="sourceEventType === '2'"
              v-el-table-scroll="scrollEventTable"
              v-loading="event.loading"
              :data="event.tableData"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              infinite-scroll-disabled="disableScroll"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              @row-dblclick="queryOriginalLog"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column v-for="(item, index) in options.threatColumnOption" :key="index" :prop="item.key" :label="item.label">
                <template slot-scope="scope">
                  <level-tag v-if="item.key === 'eventLevel'" :level="scope.row[item.key]"></level-tag>
                  <p v-else>
                    {{ scope.row[item.key] }}
                  </p>
                </template>
              </el-table-column>
            </el-table>
          </section>
          <footer class="table-footer infinite-scroll">
            <section class="infinite-scroll-nomore">
              <span v-show="event.nomore">{{ $t('validate.data.nomore') }}</span>
              <i v-show="event.totalLoading" class="el-icon-loading"></i>
            </section>
            <section v-show="!event.totalLoading" class="infinite-scroll-total">
              <b>{{ $t('event.original.total') + ':' }}</b>
              <span>{{ event.total }}</span>
            </section>
          </footer>
        </section>
      </el-tab-pane>
      <!--原始日志溯源-->
      <template v-if="original.show">
        <el-tab-pane name="third" :label="$t('alarm.table.panel.original')" closable>
          <section class="router-wrap-table">
            <section class="table-body">
              <el-table
                v-loading="original.loading"
                v-el-table-scroll="scrollRawLogTable"
                :data="original.rawLogTable"
                class="flag"
                element-loading-background="rgba(0, 0, 0, 0.3)"
                infinite-scroll-disabled="disableOriginal"
                size="mini"
                highlight-current-row
                tooltip-effect="light"
                height="100%"
              >
                <el-table-column width="50" type="index"></el-table-column>
                <el-table-column
                  prop="type2Name"
                  :label="$t('alarm.table.detail.detailOriginalColumn.type2Name')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  prop="eventName"
                  :label="$t('alarm.table.detail.detailOriginalColumn.eventName')"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column prop="eventCategoryName" :label="$t('alarm.table.detail.detailOriginalColumn.eventCategoryName')"></el-table-column>
                <el-table-column prop="level" :label="$t('alarm.table.detail.detailOriginalColumn.level')">
                  <template slot-scope="scope">
                    <level-tag :level="scope.row.level"></level-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="sourceIp" :label="$t('alarm.table.detail.detailOriginalColumn.srcIp')"></el-table-column>
                <el-table-column prop="targetIp" :label="$t('alarm.table.detail.detailOriginalColumn.dstIp')"></el-table-column>
                <el-table-column prop="time" :label="$t('alarm.table.detail.detailOriginalColumn.dateTime')"></el-table-column>
                <el-table-column fixed="right" width="80">
                  <template slot-scope="scope">
                    <el-button class="el-button--blue" @click="clickDetailDrawer(scope.row)">
                      {{ $t('button.detail') }}
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </section>
            <footer class="table-footer infinite-scroll">
              <section class="infinite-scroll-nomore">
                <span v-show="original.nomore">{{ $t('validate.data.nomore') }}</span>
                <i v-show="original.totalLoading" class="el-icon-loading"></i>
              </section>
              <section v-show="!original.totalLoading" class="infinite-scroll-total">
                <b>{{ $t('event.original.total') + ':' }}</b>
                <span>{{ original.total }}</span>
              </section>
            </footer>
          </section>
        </el-tab-pane>
      </template>
    </el-tabs>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import CustomDialog from '@comp/CustomDialog'
import levelTag from '@comp/LevelTag'
import { debounce } from '@/util/effect'
import {
  querySafeDetailData,
  queryRelevanceDetailData,
  queryThreatDetailData,
  querySecurityTotal,
  queryRelevanceTotal,
  queryThreatTotal,
  queryRawLog,
  queryRawLogTotal,
} from '@api/alarm/table-api'

export default {
  components: {
    CustomDialog,
    levelTag,
  },
  directives: {
    elTableScroll,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    form: {
      required: true,
      type: Object,
    },
    width: {
      type: String,
      default: '1200',
    },
    actions: {
      type: Boolean,
      default: true,
    },
    sourceEventType: {
      type: String,
      default: '0',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    auditLoading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      // 默认激活第一个tab页
      activeName: 'first',
      // 安全事件，关联事件，审计事件列
      options: {
        auditColumnOption: [
          {
            key: 'name',
            label: this.$t('alarm.table.label.name'),
          },
          {
            key: 'level',
            label: this.$t('alarm.table.label.level'),
          },
          {
            key: 'auditTypeName',
            label: this.$t('alarm.table.label.auditTypeName'),
          },
          {
            key: 'alarmStrategyName',
            label: this.$t('alarm.table.label.alarmStrategyName'),
          },
          {
            key: 'state',
            label: this.$t('alarm.table.label.state'),
          },
          {
            key: 'total',
            label: this.$t('alarm.table.label.total'),
          },
          {
            key: 'createTime',
            label: this.$t('alarm.table.label.createTime'),
          },
          {
            key: 'updateTime',
            label: this.$t('alarm.table.label.updateTime'),
          },
        ],
        safeColumnOption: [
          {
            key: 'alarmCategoryName',
            label: this.$t('alarm.table.detail.detailSafeColumn.alarmCategoryName'),
          },
          {
            key: 'alarmTypeName',
            label: this.$t('alarm.table.detail.detailSafeColumn.alarmTypeName'),
          },
          {
            key: 'level',
            label: this.$t('alarm.table.detail.detailSafeColumn.level'),
          },
          {
            key: 'deviceTypeName',
            label: this.$t('alarm.table.detail.detailSafeColumn.deviceTypeName'),
          },
          {
            key: 'count',
            label: this.$t('alarm.table.detail.detailSafeColumn.count'),
          },
          {
            key: 'aggrStartDate',
            label: this.$t('alarm.table.detail.detailSafeColumn.aggrStartDate'),
          },
        ],
        relevanceColumnOption: [
          {
            key: 'eventTypeName',
            label: this.$t('alarm.table.detail.detailRelevanceColumn.eventTypeName'),
          },
          {
            key: 'policyName',
            label: this.$t('alarm.table.detail.detailRelevanceColumn.policyName'),
          },
          {
            key: 'level',
            label: this.$t('alarm.table.detail.detailRelevanceColumn.level'),
          },
          {
            key: 'createDate',
            label: this.$t('alarm.table.detail.detailRelevanceColumn.createDate'),
          },
          {
            key: 'updateDate',
            label: this.$t('alarm.table.detail.detailRelevanceColumn.updateDate'),
          },
          {
            key: 'count',
            label: this.$t('alarm.table.detail.detailRelevanceColumn.count'),
          },
        ],
        threatColumnOption: [
          {
            key: 'eventTypeName',
            label: this.$t('alarm.table.detail.detailThreatColumn.eventTypeName'),
          },
          {
            key: 'eventLevel',
            label: this.$t('alarm.table.detail.detailThreatColumn.eventLevel'),
          },
          {
            key: 'eventDesc',
            label: this.$t('alarm.table.detail.detailThreatColumn.eventDesc'),
          },
          {
            key: 'receiveTime',
            label: this.$t('alarm.table.detail.detailThreatColumn.receiveTime'),
          },
          {
            key: 'eventTime',
            label: this.$t('alarm.table.detail.detailThreatColumn.eventTime'),
          },
        ],
      },
      // 原始日志
      original: {
        rawLogTable: [],
        loading: false,
        show: false,
        nomore: false,
        total: 0,
        pageNum: 2,
        totalLoading: false,
        scroll: true,
      },
      // 安全事件或者关联事件
      event: {
        tableData: [],
        loading: false,
        nomore: false,
        totalLoading: false,
        scroll: true,
        total: 0,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      }, // 分页信息
      temParams: {}, // 查询中间信息
      debounce: {
        click: null,
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
    disableScroll() {
      return this.event.scroll
    },
    disableOriginal() {
      return this.original.scroll
    },
  },
  watch: {
    visible(nVal) {
      if (nVal) this.initDebounce()
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initDebounce() {
      this.debounce.click = debounce(() => {
        this.getEventDetailData(this.handleParams())
        this.getEventDetailTotal(this.handleParams())
      }, 200)
    },

    // 关闭原始日志选项卡,并清空数据
    removeTab() {
      this.activeName = 'second'
      this.temParams = {}
      this.original = {
        rawLogTable: [],
        loading: false,
        show: false,
        nomore: false,
        total: 0,
        pageNum: 2,
        totalLoading: false,
        scroll: true,
      }
    },
    // 关闭弹框
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
      this.activeName = 'first'
      this.temParams = {}
      this.original = {
        rawLogTable: [],
        loading: false,
        show: false,
        nomore: false,
        total: 0,
        pageNum: 2,
        totalLoading: false,
        scroll: true,
      }
      this.event = {
        tableData: [],
        loading: false,
        nomore: false,
        totalLoading: false,
        scroll: true,
        total: 0,
      }
    },
    // 滚动加载安全事件关联事件
    scrollEventTable() {
      const lastRow = this.event.tableData[this.event.tableData.length - 1]
      let params = {
        pageSize: this.pagination.pageSize,
        timestamp: lastRow.aggrStartDate,
        sourceId: lastRow.eventId,
        id: this.form.model.auditEventId,
      }
      if (this.sourceEventType === '2') {
        params = Object.assign(params, { timestamp: lastRow.receiveTime })
      }
      this.getEventDetailData(params)
    },
    // 点击安全事件/关联/威胁选项卡
    handleClick({ name }) {
      const revertThird = () => {
        this.original = {
          rawLogTable: [],
          loading: false,
          show: false,
          nomore: false,
          total: 0,
          pageNum: 2,
          totalLoading: false,
          scroll: true,
        }
      }
      if (name === 'first') {
        revertThird()
        this.event = {
          tableData: [],
          loading: false,
          nomore: false,
          totalLoading: false,
          scroll: true,
          total: 0,
        }
      }
      if (name === 'second') {
        revertThird()
        if (this.event.tableData.length > 0) {
          return
        }
        this.event.tableData = []
        this.debounce.click()
      }
    },
    // 查询查询安全/关联事件/威胁事件数据
    getEventDetailData(params = {}) {
      this.event.scroll = true
      this.event.loading = true
      if (this.sourceEventType === '0') {
        querySafeDetailData(params).then((res) => {
          this.renderTableData(res)
        })
      } else if (this.sourceEventType === '1') {
        queryRelevanceDetailData(params).then((res) => {
          this.renderTableData(res)
        })
      } else {
        queryThreatDetailData(params).then((res) => {
          this.renderTableData(res)
        })
      }
    },
    renderTableData(res) {
      if (res.length < this.pagination.pageSize) {
        this.event.tableData.push(...res)
        this.event.scroll = true
        if (this.event.tableData.length > this.pagination.pageSize) {
          this.event.nomore = true
        }
      } else {
        this.event.tableData.push(...res)
        this.event.scroll = false
      }
      this.event.loading = false
    },
    // 查询查询安全/关联事件/威胁事件总数
    getEventDetailTotal(params = {}) {
      this.event.totalLoading = true
      if (this.sourceEventType === '0') {
        querySecurityTotal(params).then((res) => {
          this.event.total = res
          this.event.totalLoading = false
        })
      } else if (this.sourceEventType === '1') {
        queryRelevanceTotal(params).then((res) => {
          this.event.total = res
          this.event.totalLoading = false
        })
      } else {
        queryThreatTotal(params).then((res) => {
          this.event.total = res
          this.event.totalLoading = false
        })
      }
    },
    // 构建查询安全/关联事件查询条件
    handleParams() {
      return Object.assign(
        {},
        {
          pageSize: this.pagination.pageSize,
          id: this.form.model.auditEventId,
          timestamp: this.form.model.createTime,
        }
      )
    },
    // 点击溯源原始日志,跳转选项卡
    queryOriginalLog(row, action) {
      // 点击每一条时,刷新日志溯源选项卡,并清空列表
      this.original.show = false
      this.original.rawLogTable = []
      let params = {}
      this.original.show = true
      if (this.sourceEventType === '0') {
        const { eventId, aggrStartDate, aggrEndDate } = row
        params = Object.assign(
          {},
          {
            eventId,
            aggrStartDate,
            aggrEndDate,
            pageSize: this.pagination.pageSize,
            sourceEventType: '0',
          }
        )
      } else if (this.sourceEventType === '1') {
        const { eventId, createDate, updateDate } = row
        params = Object.assign(
          {},
          {
            eventId,
            createDate,
            updateDate,
            pageSize: this.pagination.pageSize,
            pageNum: 1,
            sourceEventType: '1',
          }
        )
      } else {
        const { originalId, receiveTime } = row
        params = Object.assign(
          {},
          {
            eventId: originalId,
            updateDate: receiveTime,
            pageSize: this.pagination.pageSize,
            pageNum: 1,
            sourceEventType: '2',
          }
        )
      }
      this.getOriginalLog(params)
      this.getOriginalLogTotal(params)
      this.temParams = params
      this.$nextTick(() => {
        this.activeName = 'third'
      })
    },
    // 滚动加载原始日志
    scrollRawLogTable() {
      const lastRow = this.original.rawLogTable[this.original.rawLogTable.length - 1]
      let params = {}
      if (this.sourceEventType === '0') {
        params = {
          eventId: this.temParams.eventId,
          aggrStartDate: this.temParams.aggrStartDate,
          aggrEndDate: this.temParams.aggrEndDate,
          pageSize: this.pagination.pageSize,
          originalId: lastRow.id,
          timestamp: lastRow.timestamp,
          sourceEventType: '0',
        }
      } else if (this.sourceEventType === '1') {
        params = {
          eventId: this.temParams.eventId,
          createDate: this.temParams.createDate,
          updateDate: this.temParams.updateDate,
          pageSize: this.pagination.pageSize,
          pageNum: this.original.pageNum++,
          originalId: lastRow.id,
          timestamp: lastRow.timestamp,
          sourceEventType: '1',
        }
      } else {
        params = {
          eventId: this.temParams.eventId,
          updateDate: this.temParams.receiveTime,
          pageSize: this.pagination.pageSize,
          pageNum: this.original.pageNum++,
          originalId: lastRow.id,
          timestamp: lastRow.timestamp,
          sourceEventType: '2',
        }
      }
      this.getOriginalLog(params)
    },
    // 查询原始日志api
    getOriginalLog(params = {}) {
      this.original.scroll = true
      this.original.loading = true
      queryRawLog(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.original.rawLogTable.push(...res)
          this.original.scroll = true
          if (this.original.rawLogTable.length > this.pagination.pageSize) {
            this.original.nomore = true
          }
        } else {
          this.original.rawLogTable.push(...res)
          this.original.scroll = false
        }
        this.original.loading = false
      })
    },
    // 查询原始日志总数api
    getOriginalLogTotal(params = {}) {
      this.original.totalLoading = true
      queryRawLogTotal(params).then((res) => {
        this.original.total = res
        this.original.totalLoading = false
      })
    },
    // 点击原始日志详情抽屉
    clickDetailDrawer(row) {
      this.$emit('drawerShow', row)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep.el-table {
  .cell {
    width: 100%; /*根据自己项目进行定义宽度*/
    overflow: hidden; /*设置超出的部分进行影藏*/
    text-overflow: ellipsis; /*设置超出部分使用省略号*/
    white-space: nowrap; /*设置为单行*/
    padding-right: 0px;
  }
}

::v-deep .el-popover,
.el-popper {
  > p {
    overflow: scroll;
  }
}

::v-deep .el-dialog__body {
  min-height: 500px;
}

::v-deep .table-body {
  min-height: 350px;
  max-height: 350px;
}

::v-deep .router-wrap-table {
  position: relative;

  ::v-deep .table-footer {
    position: absolute;
    right: 10px;
    bottom: 0;
  }
}
</style>
