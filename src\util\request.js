import axios from 'axios'
import store from '@/store'
import router from '@/router'
import i18n from '@/language'
import { prompt } from './prompt'
import ajax from '@asset/js/code/ajax'

/**
 * @func 二次封装axios设置拦截器添加token 响应器解析
 * @param {object} requestBody - 请求体内容
 * @param {string} type - 请求类型 包括default、upload、download
 * @param {string} timeout - 毫秒单位字符串
 * @return {promise} - ajax交互得到的返回值
 * <AUTHOR> @date 2019/5/15
 */
const request = (requestBody, type = 'default', timeout = '40000') => {
  const { NODE_ENV, VUE_APP_IS_MOCK, VUE_APP_BASE_API } = process.env
  let serveBaseURL = VUE_APP_IS_MOCK === 'true' ? '' : VUE_APP_BASE_API
  if (NODE_ENV === 'production') {
    serveBaseURL = ''
  }
  const serviceConfig = {
    baseURL: serveBaseURL,
    withCredentials: false, // 让ajax携带cookie
    headers: {
      /*
       * Content-Type:
       *      default: application/json;charset=utf-8
       *      post: application/x-www-form-urlencoded;charset=UTF-8
       *      upload: multipart/form-data
       * */
      'Content-Type': 'application/json;charset=utf-8',
      locale_language: sessionStorage.getItem('localeLanguage') || 'zh-CN',
    },
  }

  if (process.env.NODE_ENV === 'production') {
    serviceConfig.timeout = timeout // 超时时间
  }

  switch (type) {
    case 'upload':
      serviceConfig.headers['Content-Type'] = 'multipart/form-data'
      serviceConfig['processData'] = false
      serviceConfig['contentType'] = false
      break
    case 'download':
      serviceConfig['responseType'] = 'blob'
      break
    case 'eventSource':
      break
    default:
      break
  }

  const service = axios.create(serviceConfig)

  // 请求request拦截器，拦截config添加token
  service.interceptors.request.use(
    (config) => {
      const token = store.getters.token
      if (token !== '') {
        config.headers['access_token'] = token
      }
      return config
    },
    (error) => {
      prompt({
        i18nCode: 'ajax.interceptors.error',
        type: 'error',
        error: error,
        print: true,
      })
      Promise.reject('response-err:' + error)
    }
  )

  // 响应response拦截器 处理异常状态
  service.interceptors.response.use(
    (response) => {
      const transferCode = response.headers['code'] === undefined ? 200 : Number(response.headers['code'])
      const logout = () => {
        if (process.env.NODE_ENV !== 'production') {
          prompt({
            i18nCode: 'ajax.exception.mock',
            type: 'error',
          })
          router.replace({
            path: '/login',
          })
        } else {
          prompt(
            {
              i18nCode: 'logout.message',
              type: 'error',
            },
            () => {
              if (router.currentRoute.path.indexOf('/login') > -1) {
                location.reload()
              } else {
                store.dispatch('user/reset')
                router.replace({
                  path: '/login',
                })
              }
            }
          )
        }
      }
      const reject = () => {
        const abnormalPrompt = (abnormalCode, type = 'exception', status) => {
          let promptType = ''
          if (response.data.code === 500 || (response.data.code >= 1000 && response.data.code < 2000)) {
            promptType = 'error'
          }

          if (response.data.code >= 2000 && response.data.code < 3000) {
            promptType = 'warning'
          }
          prompt({
            i18nCode: `ajax.${type}.${abnormalCode}`,
            type: promptType,
          })
          return Promise.reject(`response-err-status:${status || ajax[type][abnormalCode]} \nerr-question: ${i18n.t(`ajax.${type}.${abnormalCode}`)}`)
        }
        switch (response.data.code) {
          // 系统异常
          case ajax.exception.system:
            abnormalPrompt('system')
            break
          // 服务故障异常
          case ajax.exception.server:
            abnormalPrompt('server')
            break
          // Session异常
          case ajax.exception.session:
            logout()
            break
          // 访问令牌异常
          case ajax.exception.access:
            logout()
            break
          // 认证异常
          case ajax.exception.certification:
            abnormalPrompt('certification')
            break
          // 访问资源权限异常
          case ajax.exception.auth:
            abnormalPrompt('auth')
            router.replace({
              path: '/401',
            })
            break
          // 访问资源功能权限异常
          case ajax.exception.token:
            abnormalPrompt('token')
            break
          // 参数异常
          case ajax.exception.param:
            abnormalPrompt('param')
            break
          // 幂等性异常
          case ajax.exception.idempotency:
            abnormalPrompt('idempotency')
            break
          // IP禁止访问
          case ajax.exception.ip:
            abnormalPrompt('ip')
            break
          // 上传附件格式校验失败
          case ajax.exception.upload:
            abnormalPrompt('upload')
            break
          // xss脚本攻击
          case ajax.attack.xss:
            abnormalPrompt('xss', 'attack')
            break
          // 其他未知参数直接返回
          default:
            abnormalPrompt('code', 'exception', -1)
            break
        }
      }

      switch (type) {
        case 'upload':
          if (transferCode === ajax.success) {
            return response.data.data
          } else {
            reject()
          }
          break
        case 'download':
          if (transferCode === ajax.success) {
            return {
              data: response.data,
              fileName: decodeURI(response.headers['file-name']),
            }
          } else {
            reject()
          }
          break
        default:
          if (response.data.code === ajax.success) {
            return response.data.data
          } else {
            reject()
          }
          break
      }
    },
    (error) => {
      if (type === 'upload') {
        prompt({
          i18nCode: 'ajax.service.upload',
          type: 'error',
          duration: 2000,
        })
        return Promise.reject(`response-err-status:Upload Error \nerr-question: ${i18n.t('ajax.service.upload')}`)
      }

      // 未连接服务器或者链接服务器超时
      prompt({
        i18nCode: 'ajax.service.timeout',
        type: 'error',
      })

      // 重置用户状态
      store.dispatch('user/reset')

      // 判断当前是否在登录页面
      if (router.currentRoute.path.indexOf('/login') > -1) {
        // 如果当前是登录页面，则刷新页面
        location.reload()
      } else {
        // 如果不是登录页面，则跳转到登录页
        router.replace({
          path: '/login',
        })
      }

      return Promise.reject(`response-err-status:${error} \nerr-question: ${i18n.t('ajax.service.timeout')}`)
    }
  )

  return service(requestBody)
}

export default request
