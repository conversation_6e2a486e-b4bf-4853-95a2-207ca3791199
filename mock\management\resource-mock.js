const { TableMock } = require('../util')

const resource = new TableMock({
  total: 200,
  template: {
    resourceId: '@ID',
    resourceToken: '@NAME',
    resourceName: '@CNAME',
    resourceStatus: '@PICK(["0", "1"])',
    resourceStatusText: function() {
      const resourceStatus = this.resourceStatus
      return resourceStatus === '0' ? '显示' : '隐藏'
    },
    resourceDescription: '@cparagraph(1,1,3)',
    authority: '0,1,2',
    authorityText: '系统管理员,运维管理员,系统审计员',
    actions: [
      {
        actionId: '322068128326769',
        actionName: '恢复',
        actionToken: 'recover',
        actionStatus: '0',
        actionStatusText: '显示',
        actionDescription: '恢复',
      },
      {
        actionId: '322068128326767',
        actionName: '查询',
        actionToken: 'query',
        actionStatus: '0',
        actionStatusText: '显示',
        actionDescription: '查询',
      },
      {
        actionId: '322068128326768',
        actionName: '保存',
        actionToken: 'save',
        actionStatus: '0',
        actionStatusText: '显示',
        actionDescription: '保存',
      },
    ],
    authorities: [0, 1, 2],
  },
})
const actionTokens = [
  {
    value: 'add',
    label: 'add',
  },
  {
    value: 'backups',
    label: 'backups',
  },
  {
    value: 'delete',
    label: 'delete',
  },
  {
    value: 'download',
    label: 'download',
  },
  {
    value: 'find',
    label: 'find',
  },
  {
    value: 'grant',
    label: 'grant',
  },
  {
    value: 'ignore',
    label: 'ignore',
  },
  {
    value: 'lock',
    label: 'lock',
  },
  {
    value: 'login',
    label: 'login',
  },
  {
    value: 'logout',
    label: 'logout',
  },
  {
    value: 'query',
    label: 'query',
  },
  {
    value: 'recover',
    label: 'recover',
  },
  {
    value: 'repeat',
    label: 'repeat',
  },
  {
    value: 'reset',
    label: 'reset',
  },
  {
    value: 'save',
    label: 'save',
  },
  {
    value: 'scan',
    label: 'scan',
  },
  {
    value: 'shut',
    label: 'shut',
  },
  {
    value: 'sort',
    label: 'sort',
  },
  {
    value: 'startup',
    label: 'startup',
  },
  {
    value: 'sync',
    label: 'sync',
  },
  {
    value: 'update',
    label: 'update',
  },
  {
    value: 'upload',
    label: 'upload',
  },
  {
    value: 'workflow',
    label: 'workflow',
  },
]

module.exports = [
  {
    url: '/resourcemanagement/resource',
    type: 'post',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/resourcemanagement/resource',
    type: 'delete',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/resourcemanagement/resource',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/resourcemanagement/resources',
    type: 'get',
    response: (option) => {
      resource.query(option)
      return {
        code: 200,
        data: resource.getMockData(),
      }
    },
  },
  {
    url: '/resourcemanagement/resource',
    type: 'get',
    response: (option) => {
      resource.query(option)
      return {
        code: 200,
        data: resource.getMockData(),
      }
    },
  },
  {
    url: '/resourcemanagement/action-token',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: actionTokens,
      }
    },
  },
]
