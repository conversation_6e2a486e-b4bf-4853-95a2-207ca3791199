export default {
  logSource: {
    title: 'Log Source Type',
    label: {
      manufact: 'Manufacturer',
      categoryName: 'Log Source Category',
      typeName: 'Log Source Type',
      desc: 'Description',
      isAsset: 'Generate Asset',
    },
    placeholder: {
      fuzzyField: 'Log Source Type',
      manufact: 'Manufacturer',
      categoryId: 'Log Source Category',
      categoryName: 'Log Source Category',
      typeName: 'Log Source Type',
      desc: 'Description',
    },
    tip: {
      assetExist: 'Asset corresponding to collector address already exists, are you sure to update the asset type',
    },
  },
}
