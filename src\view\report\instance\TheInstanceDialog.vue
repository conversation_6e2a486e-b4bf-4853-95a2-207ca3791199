<!--
 * @Description: 报表实例 -查看弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemp" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog">
    <html-frame :frame-url="url" :height="'400px'"></html-frame>
    <template slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import HtmlFrame from '@comp/HtmlFrame'

export default {
  components: {
    CustomDialog,
    HtmlFrame,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '600',
    },
    url: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemp.end()
      this.dialogVisible = false
    },
  },
}
</script>
