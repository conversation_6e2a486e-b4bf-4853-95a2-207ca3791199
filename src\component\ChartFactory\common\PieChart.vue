<template>
  <div :id="id" ref="pieChart" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-pie',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    mouseEvent: {
      type: Boolean,
      default: false,
    },
    proto: {
      type: Boolean,
      default: false,
    },
    pieData: {
      type: Object,
      default() {
        return {
          title: '',
          legend: [],
          series: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    pieData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.pieData) {
      if (!data || Object.keys(data).length === 0) {
        data = {
          title: '',
          legend: [],
          series: [],
        }
      }
      this.chart = echarts.init(this.$refs.pieChart, this.$store.getters.theme)
      this.drawChart(data)
    },
    drawChart(data) {
      if (this.proto) {
        this.chart.setOption(data, true)
      } else {
        const option = this.chartOptionConfig(data)
        this.chart.setOption(option, true)
      }
    },
    chartOptionConfig(data) {
      const option = {
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          text: data.title ? data.title : '',
        },
        tooltip: {
          show: false,
        },
        grid: {
          top: '30px',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true,
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: [],
        },
        series: [
          {
            type: 'pie',
            radius: '60%',
            label: {
              show: false,
            },
            data: [],
          },
        ],
      }

      if (this.mouseEvent) {
        this.chartHasMouseEvent(option)
      }

      this.chartSeriesConfig(option, data)
      return option
    },
    chartHasMouseEvent(option) {
      if (option.legend && option.legend.length > 1) {
        option.legend = Object.assign(option.legend, {
          orient: 'horizontal',
          right: '4%',
        })
      }

      option.tooltip = Object.assign(option.tooltip, {
        show: true,
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      })

      return option
    },
    chartSeriesConfig(option, data) {
      data.series.forEach((item, index) => {
        if (this.mouseEvent) {
          option['legend']['data'].push(data.legend[index])
        }
        option.series[0].data.push({
          value: item,
          name: data.legend[index],
        })
      })
    },
    getChart() {
      return this.chart
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
