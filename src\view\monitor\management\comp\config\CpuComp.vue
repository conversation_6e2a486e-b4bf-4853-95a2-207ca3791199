<!--
 * @Description: 监控器配置 - CPU信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-30
 * @Editor:
 * @EditDate: 2021-07-30
-->
<template>
  <section>
    <el-form ref="cpuForm" :model="cpuModel" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="cpuUseRate" :label="$t('monitor.management.config.cpu.cpuUseRate')">
            <el-input v-model="cpuModel.cpuUseRate" maxlength="3" oninput="value=value.replace(/[^0-9]/g,'')" @change="changeUseRate"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="cpuTimes" :label="$t('monitor.management.config.cpu.cpuTimes')">
            <el-input v-model="cpuModel.cpuTimes" maxlength="1" oninput="value=value.replace(/[^0-9]/g,'')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>

<script>
export default {
  props: {
    cpuModel: {
      required: true,
      type: Object,
    },
  },
  data() {
    const cpuUseRate = (rule, value, callback) => {
      if (value !== null && value !== '' && (value < 1 || value > 100)) {
        callback(new Error(this.$t('validate.monitor.useRate')))
      } else {
        callback()
      }
    }
    const cpuTimes = (rule, value, callback) => {
      if (value !== null && value !== '' && (value < 1 || value > 9)) {
        callback(new Error(this.$t('validate.monitor.times')))
      } else {
        callback()
      }
    }
    return {
      rules: {
        cpuUseRate: [
          {
            validator: cpuUseRate,
            trigger: 'blur',
          },
        ],
        cpuTimes: [
          {
            validator: cpuTimes,
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    changeUseRate(value) {
      if (!value.isNotEmpty()) {
        this.cpuModel.cpuTimes = ''
      }
    },
    validateForm() {
      let validate = false
      this.$refs.cpuForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    resetForm() {
      this.$refs.cpuForm.resetFields()
    },
  },
}
</script>
