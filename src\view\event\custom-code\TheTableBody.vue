<!--
 * @Description: 自定义CODE码 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-01-06
 * @Editor:
 * @EditDate: 2022-01-06
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
      >
        <el-table-column
          v-for="(item, key) in columns"
          :key="key"
          :prop="item"
          :label="$t(`event.customCode.label.${item}`)"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column fixed="right" width="210">
          <template slot-scope="scope">
            <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
              {{ $t('button.update') }}
            </el-button>
            <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
              {{ $t('button.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      columns: ['code', 'devTypeName', 'eventTypeName', 'updateTime'],
    }
  },
  methods: {
    clickUpdate(row) {
      this.$emit('on-update', row)
    },
    clickDelete(row) {
      this.$emit('on-delete', row)
    },
  },
}
</script>
