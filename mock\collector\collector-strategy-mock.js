const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    policyId: '@ID',
    policyName: '@NAME',
    level: '@CNAME',
    describe: '@CNAME',
  },
})

const eventTypeList = [
  {
    value: '10',
    label: '拒绝服务',
    type: null,
    children: [
      {
        value: '10001',
        label: '未定义拒绝服务事件',
        type: '10',
        children: null,
      },
      {
        value: '10002',
        label: '畸形Dos数据包',
        type: '10',
        children: null,
      },
    ],
  },
  {
    value: '11',
    label: '扫描探测',
    type: null,
    children: [
      {
        value: '11001',
        label: '未定义探测',
        type: '11',
        children: null,
      },
      {
        value: '11002',
        label: '应用探测',
        type: '11',
        children: null,
      },
    ],
  },
]

module.exports = [
  {
    url: '/collector/strategy/filter/strategy',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/collector/strategy/filter/strategy/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/collector/strategy/filter/strategy',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/collector/strategy/filter/strategies',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/collector/strategy/filter/combo/event-types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: eventTypeList,
      }
    },
  },
]
