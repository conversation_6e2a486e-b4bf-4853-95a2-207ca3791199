import request from '@util/request'

export function addScreenData(obj) {
  return request({
    url: '/visualization/screenmanagement',
    method: 'post',
    data: obj || {},
  })
}

export function deleteScreenData(ids) {
  return request({
    url: `/visualization/screenmanagement/${ids}`,
    method: 'delete',
  })
}

export function updateScreenData(obj) {
  return request({
    url: '/visualization/screenmanagement',
    method: 'put',
    data: obj || {},
  })
}

export function queryScreenTableData(obj) {
  return request({
    url: '/visualization/screenmanagement',
    method: 'get',
    params: obj || {},
  })
}
