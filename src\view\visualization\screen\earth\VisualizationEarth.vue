<!--
 * @Description: 大屏展示 - 3D地球
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <v-scale-screen full-screen>
    <div class="visualization-earth-container">
      <loading v-if="load.flag"></loading>
      <section class="earth-container">
        <section class="earth-outer-ring flash"></section>
        <section class="earth-inner-ring"></section>
        <section class="earth-canvas">
          <global-area :width="'1920px'" :height="'950px'" :global-data="data.earth"></global-area>
        </section>
      </section>
      <section class="main-container">
        <section class="row-header">
          <section class="header-logo">
            <img :src="systemSetting.systemLogo" height="48px" style="padding: 0px 12px;" />
            <h1 style="margin-left: 12px;">{{ systemSetting.systemName }}</h1>
          </section>
          <section class="header-eps">
            <section class="header-eps-top">
              <b class="header-eps-top-number">
                <animated-number :value="data.eps.total" :format-value="formatEPSTotal" :duration="data.eps.interval"></animated-number>
              </b>
              <b class="header-eps-top-letter">EPS</b>
            </section>
            <section class="header-eps-canvas">
              <line-chart :width="'160px'" :height="'50px'" :line-data="data.eps.chartData"></line-chart>
            </section>
          </section>
        </section>
        <section class="row-body">
          <section class="row-body-top">
            <section class="body-top-left">
              <section class="line-chart">
                <section class="line-chart-canvas">
                  <line-scatter-chart :width="'380px'" :height="'220px'" :line-data="data.line"></line-scatter-chart>
                </section>
                <mark class="line-chart-title">{{ $t('visualization.event.security.label.trend') }}</mark>
                <aside class="line-chart-bg"></aside>
              </section>
            </section>
            <section class="body-top-right pie-chart">
              <section class="pie-chart-first">
                <b class="first-number">{{ eventTypeOrder(0).value + '%' }}</b>
                <b class="first-type">{{ eventTypeOrder(0).name }}</b>
              </section>
              <section class="pie-chart-second">
                <b class="second-number">{{ eventTypeOrder(1).value + '%' }}</b>
                <b class="second-type">{{ eventTypeOrder(1).name }}</b>
              </section>
              <section class="pie-chart-third">
                <b class="third-number">{{ eventTypeOrder(2).value + '%' }}</b>
                <b class="third-type">{{ eventTypeOrder(2).name }}</b>
              </section>
              <mark class="pie-chart-title">
                {{ $t('visualization.event.security.label.type') + 'TOP3' }}
              </mark>
              <aside class="pie-chart-bg"></aside>
            </section>
          </section>
          <section class="row-body-bottom">
            <section class="body-top-left bar-chart">
              <section class="bar-chart-canvas">
                <bar-chart :width="'300px'" :height="'220px'" :bar-data="data.bar"></bar-chart>
              </section>
              <mark class="bar-chart-title">
                {{ $t('visualization.event.security.label.name') + 'TOP10' }}
              </mark>
              <aside class="bar-chart-bg"></aside>
              <aside class="bar-chart-decorate"></aside>
            </section>
            <section class="body-top-right radar-chart">
              <section class="radar-chart-canvas">
                <radar-chart :width="'312px'" :height="'312px'" :radar-data="data.radar"></radar-chart>
              </section>
              <aside class="radar-chart-canvas-bg"></aside>
              <mark class="radar-chart-title">{{ $t('visualization.event.security.label.level') }}</mark>
              <aside class="radar-chart-bg"></aside>
              <aside class="radar-chart-decorate"></aside>
            </section>
          </section>
        </section>
        <section class="row-footer">
          <carousel-widget :source-data="data.carousel" :interval="refresh.duration.carousel"></carousel-widget>
        </section>
      </section>
    </div>
  </v-scale-screen>
</template>

<script>
import AnimatedNumber from 'animated-number-vue'
import VScaleScreen from 'v-scale-screen'
import Loading from '@comp/Loading'
import GlobalArea from '@comp/ChartFactory/visualization/GlobalChart'
import BarChart from '@comp/ChartFactory/visualization/BarChart'
import LineChart from '@comp/ChartFactory/visualization/LineChart.vue'
import LineScatterChart from '@comp/ChartFactory/visualization/LineEffectScatterChart'
import RadarChart from '@comp/ChartFactory/visualization/RadarChart'
import CarouselWidget from './TheCarouselWidget'
import {
  queryEPSData,
  queryGlobalData,
  queryLineData,
  queryPieData,
  queryBarData,
  queryRadarData,
  queryCarouselData,
} from '@api/visualization/earth-api'

import { getSystemNameAndLogo } from '@api/layout/layout-api'
export default {
  name: 'VisualizationEarth',
  components: {
    AnimatedNumber,
    Loading,
    GlobalArea,
    BarChart,
    LineChart,
    LineScatterChart,
    RadarChart,
    CarouselWidget,
    VScaleScreen,
  },
  data() {
    return {
      systemSetting: {
        systemLogo: '',
        systemName: this.$store.getters.systemName,
      },
      load: {
        time: 5000,
        flag: true,
      },
      data: {
        eps: {
          chartData: {
            axis: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          },
          total: 0,
          interval: 1000,
        },
        carousel: [],
        earth: [],
        line: {},
        pie: {},
        bar: {},
        radar: {},
      },
      refresh: {
        timer: {
          eps: null,
          earth: null,
          whole: null,
        },
        duration: {
          eps: 5000,
          earth: 90000,
          carousel: 5000,
          whole: 30000,
        },
      },
    }
  },
  computed: {
    eventTypeOrder() {
      return (num) => {
        if (this.data.pie && this.data.pie[num]) {
          return this.data.pie[num] ? this.data.pie[num] : '0'
        } else {
          return {
            value: 0,
            name: this.$t('tip.data.empty'),
          }
        }
      }
    },
  },
  mounted() {
    this.loadData()
  },
  beforeDestroy() {
    this.clearAllInterval()
  },
  methods: {
    loadData() {
      this.loadingTime()
      this.getLogo()
      this.getEPSData()
      this.getCarouselData()
      this.getEarthData()
      this.getLineData()
      this.getPieData()
      this.getBarData()
      this.getRadarData()
      this.refreshData()
    },
    loadingTime() {
      setTimeout(() => {
        this.load.flag = false
        clearInterval(this.refresh.timer.eps)
        this.refreshEPSData()
      }, this.load.time)
    },
    refreshData() {
      this.refreshWholeData()
      // this.refreshEarthData();
    },
    refreshEPSData() {
      this.refresh.timer.eps = setInterval(() => {
        this.getEPSData()
      }, this.refresh.duration.eps)
    },
    refreshWholeData() {
      this.refresh.timer.whole = setInterval(() => {
        this.getLineData()
        this.getPieData()
        this.getBarData()
        this.getRadarData()
        this.getCarouselData()
      }, this.refresh.duration.whole)
    },
    refreshEarthData() {
      this.refresh.timer.earth = setInterval(() => {
        this.getEarthData()
      }, this.refresh.duration.earth)
    },
    formatEPSTotal(value) {
      return `${Number(value).toFixed(0)}`
    },
    clearAllInterval() {
      clearInterval(this.refresh.timer.eps)
      clearInterval(this.refresh.timer.earth)
      clearInterval(this.refresh.timer.whole)
      this.refresh.timer.eps = null
      this.refresh.timer.earth = null
      this.refresh.timer.whole = null
    },
    getLogo() {
      getSystemNameAndLogo().then((res) => {
        this.systemSetting.systemLogo = res.systemLogo
        this.systemSetting.systemName = res.systemName
      })
    },
    getLineData() {
      queryLineData().then((res) => {
        this.data.line = res
      })
    },
    getPieData() {
      queryPieData().then((res) => {
        this.data.pie = res
      })
    },
    getBarData() {
      queryBarData().then((res) => {
        this.data.bar = res
      })
    },
    getRadarData() {
      queryRadarData().then((res) => {
        this.data.radar = res
      })
    },
    getEPSData() {
      queryEPSData().then((res) => {
        this.data.eps.total = res
        this.data.eps.chartData.axis.shift()
        this.data.eps.chartData.axis.push(res)
        this.data.eps.chartData.data.shift()
        this.data.eps.chartData.data.push(res)
      })
    },
    getCarouselData() {
      queryCarouselData().then((res) => {
        this.data.carousel = res
      })
    },
    getEarthData() {
      queryGlobalData().then((res) => {
        this.data.earth = res
      })
    },
  },
}
</script>
<style lang="scss" scoped>
@import './css/visualization-earth';
</style>
