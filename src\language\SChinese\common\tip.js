export default {
  tip: {
    sweet: '温馨提示',
    notice: '消息提醒',
    total: '总计',
    without: '无',
    add: {
      success: '添加成功',
      repeat: '添加重复',
      repeatName: '名称重复!',
      repeatNet: '与已有IP存在重合!',
      error: '添加失败',
      license: '资产已达最大值，无法继续添加',
      IpType: '起止IP类型不一致！',
      aroundError: 'IP范围错误，终止IP应大于起始IP',
      innerType: '该接收方式、采集地址已配置采集器，无需重复配置。',
      maxCount: '超过最大采集器数量',
      innerTypeRepeat: 'Kafka接收方式已存在，不能重复',
      ipAround: 'IP区间不能超过120！',
      errorType: 'IP范围不合法',
      repeatTask: '已有发现该网段的任务',
      ipExist: 'IP已存在',
      licenseLimit: '超过license限制监控器数量',
      assetLimit: '资产信息达到阈值，无法同步添加资产。',
      logSourceExist: '所选日志源不存在，无法添加。',
      fail: '模板显示属性至少输入一条！',
    },
    define: {
      success: '确认成功',
      error: '确认失败',
    },
    delete: {
      success: '删除成功',
      error: '删除失败',
      prompt: '请选择删除内容',
      running: '正在使用中,请停用后再删除',
      use: '正在被使用中,请勿删除',
    },
    ignore: {
      success: '忽略成功',
      error: '忽略失败',
      prompt: '请选择忽略内容',
    },
    correct: {
      success: '加入白名单成功',
      error: '加入白名单失败',
    },
    update: {
      success: '修改成功',
      error: '修改失败',
      null: '不能修改为空!',
      repeat: '修改的内容已存在',
      prompt: '请选择修改内容',
      repeatGroup: '审计组名称重复',
      repeatPerson: '名称已存在',
      running: '使用中的数据不能修改!',
      ipAddress: 'ip地址重复，请重新填写',
      ipRepeat: 'ip地址范围存在重合，请重新填写',
      use: '正在被使用中,请勿修改',
      password: '密码修改成功，请重新登录！',
      passwordComplexError: '密码复杂度不符合系统要求，请重新设置密码！',
      innerType: '该接收方式已有对应ip',
      repeatDic: '字典名称重复',
      ipExist: 'IP已存在',
      ipMonitored: '该资产已经被监控器使用,ip不能修改',
      repeatName: '名称已存在，请重新命名',
      reboot: '配置信息修改系统重启中，请稍后访问。',
      logSourceExist: '所选日志源不存在，无法修改。',
    },
    run: {
      prompt: '请选择运行内容',
    },
    stop: {
      prompt: '请选择停止内容',
      success: '停用成功',
      error: '停用失败',
      existStop: '选择中包含已经停用的记录，请勿重复停用。',
      selectRow: '请选择需要停用的记录',
    },
    start: {
      deletePrompt: '启用状态监控器不允许删除。',
      success: '启用成功',
      error: '启用失败',
      existStart: '选择中包含已经启用的记录，请勿重复启用。',
      selectRow: '请选择需要启用的记录',
    },
    query: {
      success: '查询成功',
      error: '查询失败',
    },
    build: {
      success: '构建成功',
      error: '构建失败',
    },
    config: {
      success: '配置成功',
      error: '配置失败',
    },
    sort: {
      success: '排序成功',
      error: '排序失败',
    },
    upload: {
      success: '上传成功',
      error: '上传失败',
      xml: '请选择xml格式文件',
      typeError: '上传文件格式错误',
      nameError: '上传文件名称错误',
      nameErr: '非法升级包名称',
      baseErr: '非法升级包版本',
      contentErr: '非法升级包内容',
      fail: '升级失败',
      format: '上传文件格式错误',
      back: {
        success: '版本回退成功',
        error: '版本回退失败,请稍后重试',
        none: '无法回退版本',
      },
      running: '系统已在升级，请勿操作',
      successUp: '升级成功！部分服务将重启!请三分钟后使用!',
      dunplicate: '已存在相同记录，请勿重复上传',
    },
    export: {
      success: '导出成功',
      error: '导出失败',
      prompt: '请选择导出内容',
    },
    import: {
      success: '导入成功',
      error: '导入失败',
      license: '资产已达最大值，无法继续导入',
    },
    download: {
      success: '下载成功',
      error: '下载失败',
      prompt: '请选择下载内容',
    },
    clear: {
      success: '清空成功',
      error: '清空失败',
    },
    save: {
      success: '保存成功',
      error: '保存失败',
      onBackup: '数据正在备份中，请稍后。',
    },
    reset: {
      success: '重置成功',
      none: '无备份信息',
      error: '重置失败',
    },
    timing: {
      success: '校时成功',
      error: '校时失败',
    },
    restart: {
      success: '重启成功',
      error: '重启失败',
    },
    restore: {
      success: '恢复出厂设置成功',
      error: '恢复出厂设置失败',
    },
    shutdown: {
      success: '关机成功',
      error: '关机失败',
    },
    create: {
      success: '创建成功',
      repeat: '创建重复',
      error: '创建失败',
    },
    check: {
      success: '校验成功',
      error: '校验失败',
    },
    test: {
      success: '测试成功',
      error: '测试失败',
      tip: '网口正在测试中，请稍后。',
    },
    checkUrl: {
      success: '该域名不是恶意域名',
      error: '该域名是恶意域名',
    },
    operate: {
      success: '{0}成功',
      data: '有数据',
      error: '{0}失败',
      empty: '无数据',
      alert: '请选择要{0}的{1}',
      prompt: '请选择要{0}的内容',
      confirm: '确认{0}吗？',
    },
    component: {
      searchKeywords: '请输入查询关键字',
      choseDate: '选择日期',
      chose: '请选择',
    },
    confirm: {
      tip: '提示',
      submit: '确认提交吗？',
      delete: '确认删除吗？',
      reset: '确认重置吗？',
      save: '确认保存吗？',
      batchDelete: '确认将选中的删除吗？',
      content: '内容',
      error: '提交失败',
      cancel: '取消提交',
      correct: '确认加入白名单吗？',
      batchIgnore: '确认将选中的忽略吗？',
      grant: '确定要授权吗',
      clear: '确定要清空吗',
      restart: '确定要重启吗',
      shutdown: '确定要关机吗',
      restore: '确定要恢复出厂设置吗',
      existAccessmode: '该采集地址已经配置其他接入方式，确定提交吗？',
      existAsset: '该采集地址已配置资产，确定更新资产类型吗？',
      batchStop: '确认将选中的停用吗？',
      batchStart: '确认将选中的启用吗？',
      sslStart: '确定要启用SSH服务吗',
      sslStop: '确定要禁用SSH服务吗',
      allCenterIp: '确定要统一设置中心IP吗？',
      funcStart: '确定启用功能吗？',
      funcStop: '确定停用功能吗？',
    },
    data: {
      empty: '暂无数据',
    },
    toggle: {
      success: '切换成功',
      error: '切换失败',
      run: '运行状态切换成功',
      stop: '运行状态切换失败',
    },
    change: {
      repeat: '资产名称重复',
      success: '转化成功',
      error: '转化失败',
      running: '发现任务正在运行',
      prompt: '请选择要转化的内容',
      number: '资产数量超过license规定数量',
      begin: '开始发现任务',
    },
    find: {
      start: '开始发现',
      repeat: '发现重复',
      success: '发现成功',
      error: '发现失败',
      prompt: '请选择要发现的内容',
    },
    enable: {
      success: '启用成功',
      error: '启用失败',
      prompt: '请选择要启用的内容',
    },
    disable: {
      success: '停用成功',
      error: '停用失败',
      prompt: '请选择要停用的内容',
    },
    status: {
      uninstall: {
        success: '卸载成功',
      },
      recover: {
        success: '恢复成功',
      },
      change: {
        error: '状态切换失败',
      },
      existMonitor: '该代理下已配置监控器信息，不允许卸载。',
      existCollector: '该代理下已配置采集器信息，不允许卸载。',
      existEquipment: '该代理下已配置监控器、采集器信息，不允许卸载。',
    },
    empty: '此项不能为空',
    ip: {
      error: '错误的ip格式',
    },
    port: {
      error: '错误的端口号格式',
    },
    select: {
      all: '全选',
      empty: '请至少选择一项',
      row: '请至少选择一行',
      one: '请选择一条记录',
    },
    placeholder: {
      query: '请输入{0}关键字',
      legalIp: '请输入合法IP',
    },
    execute: {
      success: '执行成功',
      error: '执行失败',
    },
    recovery: {
      success: '恢复成功',
      error: '恢复失败',
      process: '系统恢复过程中，请稍后使用',
      errorfile: '文件无效',
      errorversion: '版本错误',
    },
    copy: {
      success: '复制成功',
      error: '复制失败',
      repeat: '策略名称重复',
      builtIn: '内置策略，不允许复制。',
    },
    detect: {
      success: '检测成功',
      error: '检测失败',
    },
    agent: {
      info: '代理卸载尚未恢复，请稍后再试。',
    },
    send: {
      success: '发送成功',
      error: '发送失败',
      userNotExist: '用户不存在',
      userEmailNotMatch: '用户预留邮箱不匹配',
      serviceUnavailable: '邮件服务不可用',
      codeHasExpired: '验证码已失效',
    },
    expression: {
      repeat: '表达式重复',
    },
  },
}
