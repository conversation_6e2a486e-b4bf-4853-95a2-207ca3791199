<!--
 * @Description: 大屏展示 - 底部轮播组件 - 单个组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <section class="widget-container">
    <section class="widget-sort">
      <mark class="widget-sort-number">{{ orderNumber }}</mark>
      <mark class="widget-sort-letter">{{ orderLetter }}</mark>
    </section>

    <section class="widget-pie">
      <section class="widget-pie-area" :class="levelClassColor(widgetItemData.eventLevel).className">
        {{ levelClassColor(widgetItemData.eventLevel).innerText }}
      </section>
    </section>

    <section class="widget-line">
      <section class="widget-country">
        <b>{{ widgetItemData.eventName }}</b>
      </section>
      <section class="widget-number">
        <b v-for="item in widgetTotal.length - 1" :key="item" :index="item">
          {{ widgetTotal[item] }}
        </b>
      </section>
    </section>
  </section>
</template>

<script>
export default {
  props: {
    widgetItemData: {
      required: true,
      type: Object,
      default: () => {
        return {}
      },
    },
  },
  data() {
    return {
      chart: {
        pie: null,
        line: null,
        bar: null,
      },
    }
  },
  computed: {
    orderNumber() {
      const order = this.widgetItemData.order
      return order ? order.substring(0, order.length - 2) : ''
    },
    orderLetter() {
      const order = this.widgetItemData.order
      return order ? order.substring(order.length - 2, order.length) : ''
    },
    widgetTotal() {
      return this.widgetItemData.eventTotal.toString().padStart(11, '0')
    },
    levelClassColor() {
      return (value) => {
        const levelObj = {}
        switch (value) {
          case this.$t('level.serious'):
          case 0:
          case '0':
            levelObj.className = 'widget-pie--red'
            levelObj.innerText = this.$t('level.serious')
            break
          case this.$t('level.high'):
          case 1:
          case '1':
            levelObj.className = 'widget-pie--orange'
            levelObj.innerText = this.$t('level.high')
            break
          case this.$t('level.middle'):
          case 2:
          case '2':
            levelObj.className = 'widget-pie--yellow'
            levelObj.innerText = this.$t('level.middle')
            break
          case this.$t('level.low'):
          case 3:
          case '3':
            levelObj.className = 'widget-pie--blue'
            levelObj.innerText = this.$t('level.low')
            break
          case this.$t('level.general'):
          case 4:
          case '4':
            levelObj.className = 'widget-pie--green'
            levelObj.innerText = this.$t('level.general')
            break
          default:
            levelObj.className = 'widget-pie--empty'
            levelObj.innerText = this.$t('level.empty')
            break
        }
        return levelObj
      }
    },
  },
}
</script>

<style lang="scss" scoped>
@import '~@style/mixin';

$font-color: #70b4bf;
$number-color: #00132e;

.widget-container {
  display: inline-flex;
  width: 616px;
  height: 120px;
  padding: 5px 0;
  background: url('~@asset/image/visualization/attack-source-main-bg.png') no-repeat;

  section + section {
    margin: 0 5px;
  }

  .widget-sort {
    display: flex;
    width: 50px;
    height: 50px;
    margin: 24px 16px 0 22px;
    align-items: center;
    border-bottom: 1px solid $font-color;

    &-number {
      width: 50%;
      text-align: center;
      font-size: 40px;
      color: $WT;
    }

    &-letter {
      width: 50%;
      padding: 14px 0 0 0;
      font-size: 20px;
      color: $font-color;
    }
  }

  .widget-pie {
    width: 120px;
    height: 100px;
    padding: 0 10px;

    &--red {
      color: $WT;
      background-color: $SERIOUS;
      box-shadow: 0 0 10px 10px $SERIOUS;
    }

    &--orange {
      color: $WT;
      background-color: $HIGH;
      box-shadow: 0 0 10px 10px $HIGH;
    }

    &--yellow {
      color: $WT;
      background-color: $MIDDLE;
      box-shadow: 0 0 10px 10px $MIDDLE;
    }

    &--green {
      color: $WT;
      background-color: $GENERAL;
      box-shadow: 0 0 10px 10px $GENERAL;
    }

    &--blue {
      color: $WT;
      background-color: $LOW;
      box-shadow: 0 0 10px 10px $LOW;
    }

    &-area {
      position: relative;
      left: 12px;
      top: 16px;
      width: 70px;
      height: 70px;
      line-height: 70px;
      font-size: 20px;
      font-weight: 900;
      text-align: center;
      border-radius: 50%;
    }
  }

  .widget-line {
    width: 380px;
    height: 100px;

    .widget-country {
      display: flex;
      padding: 10px;
      align-items: center;
      justify-content: space-between;

      b {
        font-size: 20px;
        color: $WT;
      }
    }

    .widget-number {
      padding: 10px;

      b {
        display: inline-block;
        width: 30px;
        height: 30px;
        margin: 0 2px;
        line-height: 30px;
        font-size: 24px;
        font-weight: bolder;
        text-align: center;
        border-radius: 4px;
        color: $number-color;
        background-color: $font-color;
      }
    }
  }
}

.widget-container + .widget-container {
  margin-left: 12px;
}
</style>
