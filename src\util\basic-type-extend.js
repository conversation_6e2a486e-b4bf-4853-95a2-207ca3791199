/**
 * @explain 该文件为js基本类型的原型扩展方法
 * <AUTHOR> @copyright MIT
 * @date 2019/07/10
 */

Object.assign(HTMLElement.prototype, {
  /**
   * @func 获取该元素的第一个子节点
   * @return {dom}
   * @example el.getFirstChild()
   * <AUTHOR> @date 2019/07/15
   */
  getFirstChild() {
    if (this.firstElementChild) {
      return this.firstElementChild
    } else {
      let node = this.firstChild
      while (node && node.nodeType !== 1) {
        node = node.nextSibling
      }
      return node
    }
  },
  /**
   * @func 获取该元素的最后一个子节点
   * @return {dom}
   * @example el.getLastChild()
   * <AUTHOR> @date 2019/07/15
   */
  getLastChild() {
    if (this.lastElementChild) {
      return this.lastElementChild
    } else {
      let node = this.lastChild
      while (node && node.nodeType !== 1) {
        node = node.previousSibling
      }
      return node
    }
  },
  /**
   * @func 获取该元素所有子节点
   * @return {dom[]}
   * @example el.getChildren()
   * <AUTHOR> @date 2019/07/15
   */
  getChildren() {
    const children = []
    for (let i = this.children.length; i--; ) {
      if (this.children[i].nodeType !== 8) {
        children.unshift(this.children[i])
      }
    }
    return children
  },
  /**
   * @func 获取该元素所有兄弟节点
   * @return {dom[]}
   * @example el.getSiblings()
   * <AUTHOR> @date 2019/07/15
   */
  getSiblings() {
    const children = this.parentNode.childNodes
    const siblingArr = []
    for (const i in children) {
      /*
                nodeType 12种不同的节点类型
                1	Element	                一个元素
                2	Attr	                一个属性
                3	Text	                一个元素的文本内容或属性
                4	CDATASection	        一个文档的CDATA部分（文本将 不会被解析器解析）
                5	Entity	                参考手册实体引用
                6	Entity	                一个实体
                7	ProcessingInstruction	一个处理指令
                8	Comment	                一个注释
                9	Document	            整个文档（DOM树的根节点）
                10	DocumentType	        为文档实体提供接口
                11	DocumentFragment	    表示邻接节点和它们的子树。
                12	Notation	            代表一个符号在DTD中的声明
            */
      if (children[i].nodeType === 1 && children[i] !== this) siblingArr.push(children[i])
    }
    return siblingArr
  },
  /**
   * @func 获取该元素下面滚动条宽度
   * @return {number}
   * @example el.getScrollWidth()
   * <AUTHOR> @date 2019/07/16
   */
  getScrollWidth() {
    const [tempDiv, styles] = [
      document.createElement('div'),
      {
        width: '100px',
        height: '100px',
        overflowY: 'scroll',
      },
    ]
    let scrollbarWidth = ''
    for (const i in styles) tempDiv.style[i] = styles[i]
    this.appendChild(tempDiv)
    scrollbarWidth = tempDiv.offsetWidth - tempDiv.clientWidth
    tempDiv.remove()
    return scrollbarWidth
  },
  /**
   * @func 获取该元素的宽度包含内外边距和边框
   * @return {number}
   * @example el.getOuterWidth()
   * <AUTHOR> @date 2019/07/16
   */
  getOuterWidth() {
    const [marginLeft, marginRight, offsetWidth] = [
      Number.parseFloat(this.css('margin-left')),
      Number.parseFloat(this.css('margin-right')),
      this.offsetWidth,
    ]
    return marginLeft + marginRight + offsetWidth
  },
  /**
   * @func 获取该元素的宽度包含内外边距和边框
   * @return {number}
   * @example el.getOuterHeight()
   * <AUTHOR> @date 2019/07/16
   */
  getOuterHeight() {
    const [marginTop, marginBottom, offsetWidth] = [
      Number.parseFloat(this.css('margin-top')),
      Number.parseFloat(this.css('margin-bottom')),
      this.offsetHeight,
    ]
    return marginTop + marginBottom + offsetWidth
  },
  /**
   * @func 获取该元素的样式或者设置该元素的样式
   * @return {dom}
   * @example get - el.css("width")
   * @example set - 单个el.css("width","200px") 多个el.css({"width":"200px","height":"100px"})
   * <AUTHOR> @date 2019/07/18
   */
  css() {
    let option
    if (arguments.length > 0) {
      option = arguments[0]
      if (arguments.length === 2) {
        option = {}
        option[arguments[0]] = arguments[1]
      }

      if (typeof option === 'string') {
        return window.getComputedStyle(this, null)[option]
      }

      if (typeof option === 'object') {
        for (const key in option) {
          if (Object.prototype.hasOwnProperty.call(option, key)) {
            this.style[key] = option[key]
          }
        }
      }
    }
    return this
  },
  /**
   * @func 获取该元素的属性或者设置该元素的属性
   * @return {dom} - 该元素添加属性之后本身
   * @example get - el.attr("data-value")
   * @example set - el.attr("data-value","请输入初始值")
   * <AUTHOR> @date 2019/07/18
   */
  attr() {
    let option
    if (arguments.length === 2) {
      option = {}
      option[arguments[0]] = arguments[1]
      this.setAttribute(arguments[0], option[arguments[0]])
      return this
    } else if (arguments.length === 1) {
      option = arguments[0]
      return this.getAttribute(option)
    }
  },
  /**
   * @func 删除该元素的属性
   * @return {object}
   * @example el.rmAttr()
   * <AUTHOR> @date 2019/07/20
   */
  rmAttr(prop) {
    return this.attributes.removeNamedItem(prop)
  },
  /**
   * @func 查找该元素是否含有传入的class类名
   * @return {boolean}
   * @example el.hasClass(template-class)
   * <AUTHOR> @date 2019/07/21
   */
  hasClass(cName) {
    // (^|\\s+) 表示什么都没有（起始位置）或者 空白符
    // (\\s+|$) 表示 空白符或者什么都没有（结束位置）
    return !!this.className.match(new RegExp('(^|\\s+)' + cName + '(\\s+|$)'))
  },
  /**
   * @func 为该元素添加class类名
   * @return {dom} - 该元素添加传入class之后本身
   * @example el.addClass(template-class)
   * <AUTHOR> @date 2019/07/21
   */
  addClass(cName) {
    if (this.className === '') {
      this.className = cName
    } else if (!this.hasClass(cName)) {
      this.className += ' ' + cName
    }
    return this
  },
  /**
   * @func 为该元素添加class类名
   * @return {dom} - 该元素删除传入class之后本身
   * @example el.rmClass(template-class)
   * <AUTHOR> @date 2019/07/21
   */
  rmClass(cName) {
    if (this.hasClass(cName)) {
      this.className = this.className.replace(new RegExp('(^|\\s+)' + cName + '(\\s+|$)'), ' ')
      return this
    }
  },
  /**
   * @func 判断该元素是否为空
   * @return {boolean | string} - 为空返回false，不为空返回该元素的值或内容
   * @example el.isNotEmpty()
   * <AUTHOR> @date 2019/07/25
   */
  isNotEmpty() {
    let value
    if (this.localName === 'input' || this.localName === 'textarea') {
      this.value !== null && this.value !== '' ? (value = this.value) : (value = false)
    } else {
      this.innerHTML !== null && this.innerHTML !== '' ? (value = this.innerHTML) : (value = false)
    }
    return value
  },
  /**
   * @func 清空该元素的内容
   * @return {dom} - 清空该元素之后本身
   * @example el.empty()
   * <AUTHOR> @date 2019/07/25
   */
  empty() {
    if (this.localName === 'input' || this.localName === 'textarea') {
      this.value = ''
      this.setAttribute('value', '')
    } else {
      this.innerHTML = ''
    }
    return this
  },
  /**
   * @func 为元素添加动画
   * @param {object} css - 必填 该参数为一个对象 对象中为动画要改变最终的值
   * @param {number} duration = 2000 - 选填 参数为动画的时长 默认不传动画为2s结束
   * @param {string | number | array} effect - 选填 动画的运动过程(easeIn,easeOut,easeInOut,linear) 默认不填为指数减慢
   * @param {string | number | array} fnCallBack - 选填 动画结束之后的回调函数
   * @return {null} - 无返回值
   * @example el.animate({"width":500,"height":300,"opacity":100},5000,1, ()=>{alert("动画结束")})
   * @note  css参数像素的默认加上了px直接传入数值就行，opacity范围不是0-1修改为0-100
   * @note  第三个参数可以传入数字、字符串、数组
   *        内置的数字分别为：1为指数减慢 2为弹性减慢 3为反弹减慢 4为匀速线性
   *        内置的字符串分别为：Exponential为指数减慢 Elastic为弹性减慢 Bounce为反弹减慢 Linear为匀速线性
   *        内置的数组分别为：数组若为["Exponential","easeIn"] 长度为2的可以选择什么类型的什么运动过程的动画
   *                        数组若为["Exponential"] 长度为1 就是运动过程的动画
   * <AUTHOR> @date 2019/07/30
   */
  animate(css, duration = 2000, effect, fnCallBack) {
    // 思路：当前时间*变化量/持续时间+初始值
    const [interval, oBegin, oChange] = [
      16, // 动画间隔
      {}, // 属性的初始值
      {}, // 属性的改变的值
    ]
    let [action, count, times] = [
      null, // 判断传入的运动过程的值
      0, // 判断css改变值是否全部完成
      0, // 动画改变的时间
    ]
    // 动画运动过程函数
    const moveEffect = {
      // 1.线性运动
      Linear(t, b, c, d) {
        return (c * t) / d + b
      },
      // 2.二次方的缓动（t^2）；
      Quadratic: {
        easeIn(t, b, c, d) {
          return c * (t /= d) * t + b
        },
        easeOut(t, b, c, d) {
          return -c * (t /= d) * (t - 2) + b
        },
        easeInOut(t, b, c, d) {
          if ((t /= d / 2) < 1) return (c / 2) * t * t + b
          return (-c / 2) * (--t * (t - 2) - 1) + b
        },
      },
      // 3.三次方的缓动（t^3）
      Cubic: {
        easeIn(t, b, c, d) {
          return c * (t /= d) * t * t + b
        },
        easeOut(t, b, c, d) {
          return c * ((t = t / d - 1) * t * t + 1) + b
        },
        easeInOut(t, b, c, d) {
          if ((t /= d / 2) < 1) return (c / 2) * t * t * t + b
          return (c / 2) * ((t -= 2) * t * t + 2) + b
        },
      },
      // 4.四次方的缓动（t^4）；
      Quartic: {
        easeIn(t, b, c, d) {
          return c * (t /= d) * t * t * t + b
        },
        easeOut(t, b, c, d) {
          return -c * ((t = t / d - 1) * t * t * t - 1) + b
        },
        easeInOut(t, b, c, d) {
          if ((t /= d / 2) < 1) return (c / 2) * t * t * t * t + b
          return (-c / 2) * ((t -= 2) * t * t * t - 2) + b
        },
      },
      // 5.五次方的缓动（t^5）；
      Quintic: {
        easeIn(t, b, c, d) {
          return c * (t /= d) * t * t * t * t + b
        },
        easeOut(t, b, c, d) {
          return c * ((t = t / d - 1) * t * t * t * t + 1) + b
        },
        easeInOut(t, b, c, d) {
          if ((t /= d / 2) < 1) return (c / 2) * t * t * t * t * t + b
          return (c / 2) * ((t -= 2) * t * t * t * t + 2) + b
        },
      },
      // 6.正弦曲线的缓动sin(t)
      Sinusoidal: {
        easeIn(t, b, c, d) {
          return -c * Math.cos((t / d) * (Math.PI / 2)) + c + b
        },
        easeOut(t, b, c, d) {
          return c * Math.sin((t / d) * (Math.PI / 2)) + b
        },
        easeInOut(t, b, c, d) {
          return (-c / 2) * (Math.cos((Math.PI * t) / d) - 1) + b
        },
      },
      // 7.指数曲线的缓动（2^t）；
      Exponential: {
        easeIn(t, b, c, d) {
          return t === 0 ? b : c * Math.pow(2, 10 * (t / d - 1)) + b
        },
        easeOut(t, b, c, d) {
          return t === d ? b + c : c * (-Math.pow(2, (-10 * t) / d) + 1) + b
        },
        easeInOut(t, b, c, d) {
          if (t === 0) return b
          if (t === d) return b + c
          if ((t /= d / 2) < 1) return (c / 2) * Math.pow(2, 10 * (t - 1)) + b
          return (c / 2) * (-Math.pow(2, -10 * --t) + 2) + b
        },
      },
      // 8.圆形曲线的缓动（sqrt(1-t^2)）；
      Circular: {
        easeIn(t, b, c, d) {
          return -c * (Math.sqrt(1 - (t /= d) * t) - 1) + b
        },
        easeOut(t, b, c, d) {
          return c * Math.sqrt(1 - (t = t / d - 1) * t) + b
        },
        easeInOut(t, b, c, d) {
          if ((t /= d / 2) < 1) return (-c / 2) * (Math.sqrt(1 - t * t) - 1) + b
          return (c / 2) * (Math.sqrt(1 - (t -= 2) * t) + 1) + b
        },
      },
      // 9.指数衰减的正弦曲线缓动；
      Elastic: {
        easeIn(t, b, c, d, a, p) {
          let s
          if (t === 0) return b
          if ((t /= d) === 1) return b + c
          if (!p) p = d * 0.3
          if (!a || a < Math.abs(c)) {
            a = c
            s = p / 4
          } else s = (p / (2 * Math.PI)) * Math.asin(c / a)
          return -(a * Math.pow(2, 10 * (t -= 1)) * Math.sin(((t * d - s) * (2 * Math.PI)) / p)) + b
        },
        easeOut(t, b, c, d, a, p) {
          let s
          if (t === 0) return b
          if ((t /= d) === 1) return b + c
          if (!p) p = d * 0.3
          if (!a || a < Math.abs(c)) {
            a = c
            s = p / 4
          } else s = (p / (2 * Math.PI)) * Math.asin(c / a)
          return a * Math.pow(2, -10 * t) * Math.sin(((t * d - s) * (2 * Math.PI)) / p) + c + b
        },
        easeInOut(t, b, c, d, a, p) {
          let s
          if (t === 0) return b
          if ((t /= d / 2) === 2) return b + c
          if (!p) p = d * (0.3 * 1.5)
          if (!a || a < Math.abs(c)) {
            a = c
            s = p / 4
          } else s = (p / (2 * Math.PI)) * Math.asin(c / a)
          if (t < 1) return -0.5 * (a * Math.pow(2, 10 * (t -= 1)) * Math.sin(((t * d - s) * (2 * Math.PI)) / p)) + b
          return a * Math.pow(2, -10 * (t -= 1)) * Math.sin(((t * d - s) * (2 * Math.PI)) / p) * 0.5 + c + b
        },
      },
      // 10.超过范围的三次方缓动（(s+1)*t^3 - s*t^2）；
      Back: {
        easeIn(t, b, c, d, s) {
          if (s === undefined) s = 1.70158
          return c * (t /= d) * t * ((s + 1) * t - s) + b
        },
        easeOut(t, b, c, d, s) {
          if (s === undefined) s = 1.70158
          return c * ((t = t / d - 1) * t * ((s + 1) * t + s) + 1) + b
        },
        easeInOut(t, b, c, d, s) {
          if (s === undefined) s = 1.70158
          if ((t /= d / 2) < 1) return (c / 2) * (t * t * (((s *= 1.525) + 1) * t - s)) + b
          return (c / 2) * ((t -= 2) * t * (((s *= 1.525) + 1) * t + s) + 2) + b
        },
      },
      // 11.指数衰减的反弹缓动。
      Bounce: {
        easeIn(t, b, c, d) {
          return c - moveEffect.Bounce.easeOut(d - t, 0, c, d) + b
        },
        easeOut(t, b, c, d) {
          if ((t /= d) < 1 / 2.75) {
            return c * (7.5625 * t * t) + b
          } else if (t < 2 / 2.75) {
            return c * (7.5625 * (t -= 1.5 / 2.75) * t + 0.75) + b
          } else if (t < 2.5 / 2.75) {
            return c * (7.5625 * (t -= 2.25 / 2.75) * t + 0.9375) + b
          } else {
            return c * (7.5625 * (t -= 2.625 / 2.75) * t + 0.984375) + b
          }
        },
        easeInOut(t, b, c, d) {
          if (t < d / 2) return moveEffect.Bounce.easeIn(t * 2, 0, c, d) * 0.5 + b
          else return moveEffect.Bounce.easeOut(t * 2 - d, 0, c, d) * 0.5 + c * 0.5 + b
        },
      },
    }
    // 动画运动函数
    const move = () => {
      clearInterval(this.timer)
      times += interval
      if (times <= duration) {
        for (const attr in css) {
          const val = action(times, oBegin[attr], oChange[attr], duration)
          if (attr === 'opacity') {
            this.style[attr] = val / 100
            this.style.filter = `alpha(opacity=${val})`
          } else {
            this.style[attr] = val + 'px'
          }
        }
        this.timer = window.setInterval(move, interval)
      } else {
        for (const attr in css) {
          if (attr === 'opacity') {
            this.style[attr] = css[attr] / 100
            this.style.filter = `alpha(opacity=${css[attr]})`
          } else {
            this.style[attr] = css[attr] + 'px'
          }
        }
        // 回调函数
        if (typeof fnCallBack === 'function') {
          fnCallBack.call(this)
        }

        // 动画结束清除定时器和标识
        this.timer = null
        this.rmClass('animating')
      }
    }
    // 添加一个class作为该元素正在做动画过程的标识
    this.addClass('animating')
    // 对传参effect进行判断，并将获得的方法统一赋值给action方法,对传参的判断可以实现js的重载功能。
    if (typeof effect === 'number') {
      // 1 buffer,2 flex,3 bounce 4 linear
      switch (effect) {
        case 1:
          action = moveEffect.Exponential.easeOut
          break
        case 2:
          action = moveEffect.Elastic.easeOut
          break
        case 3:
          action = moveEffect.Bounce.easeOut
          break
        case 4:
          action = moveEffect.Linear
          break
        default:
          break
      }
    } else if (typeof effect === 'string') {
      switch (effect) {
        case 'Exponential':
          action = moveEffect.Exponential.easeOut // 指数
          break
        case 'Elastic':
          action = moveEffect.Elastic.easeOut // 弹性
          break
        case 'Bounce':
          action = moveEffect.Bounce.easeOut // 反弹
          break
        case 'Linear':
          action = moveEffect.Linear // 线性
          break
        default:
          break
      }
    } else if (typeof effect === 'function') {
      // 参数effect是个函数，则默认为这是回调函数，并采用默认的运动效果
      fnCallBack = effect
    } else if (effect instanceof Array) {
      // 输入格式：["Expo","easeOut"]
      if (effect.length === 2) {
        action = moveEffect[effect[0]][effect[1]]
      } else if (effect.length === 1) {
        action = moveEffect.Linear
      }
    } else {
      action = moveEffect.Exponential.easeOut
    }

    // 获取初试值
    for (const attr in css) {
      let begin = 0,
        change = 0
      count = 0
      if (attr === 'opacity') {
        begin = Math.random(parseFloat(this.css(attr)) * 100)
        change = css[attr] - begin
        if (begin === undefined) {
          this.style[attr] = 'alpha(opacity:100)'
          this.style.opacity = 1
        }
      } else if (attr === 'backgroundColor') {
        // TODO:制作背景颜色的渐变
      } else if (attr === 'display') {
        // TODO:制作元素显隐的渐变
      } else {
        begin = parseInt(this.css(attr))
        change = css[attr] - begin
      }
      // 保存
      if (change) {
        count++
        oBegin[attr] = begin
        oChange[attr] = change
      }
    }

    // 判断所有变量是否到齐
    if (count === 0) {
      if (typeof fnCallBack === 'function') {
        fnCallBack.call(this)
      } else {
        return
      }
    }

    move()
  },
  /**
   * @func 显示一个隐藏的元素
   * @return {dom} - 改变之后的该元素
   * @example el.show()
   * <AUTHOR> @date 2019/07/13
   */
  show() {
    let oldDisplay
    const cssDefaultDisplay = (nodeName) => {
      let display = ''
      const tempTag = document.createElement(nodeName)
      document.body.appendChild(tempTag)
      display = tempTag.css('display')
      tempTag.remove()
      return display
    }
    // 如果是style上定义的none 则直接设置成""
    if (this.style.display === 'none') {
      this.style.display = ''
    }
    // 但如果是设置在css里面则不能简单的设置成""的方式
    if (this.style.display === '' && getComputedStyle(this)['display'] === 'none') {
      oldDisplay = cssDefaultDisplay(this.nodeName)
    }

    if (this.style.display === 'none' || this.style.display === '') {
      this.style.display = oldDisplay
    }

    return this
  },
  /**
   * @func 隐藏一个显示的元素
   * @return {dom} - 改变之后的该元素
   * @example el.hide()
   * <AUTHOR> @date 2019/07/14
   */
  hide() {
    if (this.css('display') !== 'none') {
      this.css('display', 'none')
      return this
    }
  },
  /**
   * @func 切换一个元素的显隐
   * @return {dom} - 改变之后的该元素
   * @example el.toggle()
   * <AUTHOR> @date 2019/07/14
   */
  toggle() {
    this.css('display') === 'none' ? this.show() : this.hide()
  },
})

Object.assign(String.prototype, {
  /**
   * @func 判断该字符串是否为空或者为全空格
   * @return {string/boolean} - 为空返回false，不为空返回去掉前后空格的该字串
   * @example str.isNotEmpty()
   * <AUTHOR> @date 2019/08/01
   */
  isNotEmpty() {
    const flag = this === null || typeof this === 'undefined' || this.trim() === ''
    return !flag ? this.trim() : false
  },
  /**
   * @func 判断是否为空
   * @return {string/boolean} - 为空返回true，不为空返回false
   * <AUTHOR> @date 2021/10/20
   */
  isEmpty(obj) {
    if (typeof obj === 'undefined' || obj === null || obj === '') {
      return true
    } else {
      return false
    }
  },
  /**
   * @func 将该颜色值字符串转换为十六进制形式的字符串
   * @return {string} - 颜色的十六进制字符串
   * @example str.colorToHex()
   * <AUTHOR> @date 2019/08/02
   */
  colorToHex() {
    const reg = /^(rgb|RGB)/ // RGB颜色值的正则
    if (reg.test(this)) {
      let strHex = '#'
      const colorArr = this.replace(/(?:\(|\)|rgb|RGB)*/g, '').split(',') // 把RGB的3个数值变成数组
      // 转成16进制
      for (const i in colorArr) {
        let hex = Number(colorArr[i]).toString(16)
        if (hex === '0') hex += hex
        strHex += hex
      }
      return strHex.length === 7 ? strHex : strHex.slice(0, 7)
    } else {
      return String(this)
    }
  },
  /**
   * @func 将该颜色值字符串转换为RGB形式的字符串
   * @return {string} - 颜色的rgb形式字符串
   * @example str.colorToRgb()
   * <AUTHOR> @date 2019/08/02
   */
  colorToRgb(opacity = 1) {
    const reg = /^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/ // 16进制颜色值的正则
    let color = this.toLowerCase() // 把颜色值变成小写
    if (reg.test(color)) {
      // 如果只有三位的值，需变成六位，如：#fff => #ffffff
      if (color.length === 4) {
        let colorNew = '#'
        for (let i = 1; i <= 4; i++) {
          colorNew += color.slice(i, i + 1).concat(color.slice(i, i + 1))
        }
        color = colorNew
      }
      // 处理六位的颜色值，转为RGB
      const colorChange = []
      for (let i = 1; i < 7; i += 2) {
        colorChange.push(parseInt('0x' + color.slice(i, i + 2)))
      }
      return `rgba(${colorChange.join(',')}, ${opacity})`
    } else {
      return color
    }
  },
  /**
   * @func 将该颜色值装换为十六进制的反色返回
   * @return {string} - 颜色的十六进制形式字符串
   * @example str.colorReverse()
   * <AUTHOR> @date 2019/08/02
   */
  colorReverse() {
    const color = '0x' + this.colorToHex().replace(/#/g, '')
    const str = '000000' + (0xffffff - color).toString(16)
    return '#' + str.substring(str.length - 6, str.length)
  },
  /**
   * @func 将字符串按照以下方法加密
   * @return {string} - 加密之后的字符串
   * <AUTHOR> @date 2020/3/24
   */
  encrypt() {
    let tempStr = String.fromCharCode(this.charCodeAt(0) + this.length)
    for (let i = 1; i < this.length; i++) {
      tempStr += String.fromCharCode(this.charCodeAt(i) + this.charCodeAt(i - 1))
    }
    return encodeURIComponent(tempStr)
  },
  /**
   * @func 将字符串按照以下方法解密
   * @return {string} - 解密之后的字符串
   * @note - 这个解密方法只能解密以上方法加密的字符串
   * <AUTHOR> @date 2020/3/24
   */
  decrypt() {
    const str = decodeURIComponent(this)
    let tempStr = String.fromCharCode(str.charCodeAt(0) - str.length)
    for (let i = 1; i < str.length; i++) {
      tempStr += String.fromCharCode(str.charCodeAt(i) - tempStr.charCodeAt(i - 1))
    }
    return tempStr
  },
})

Object.assign(Array.prototype, {
  /**
   * @func 去除该数组重复的元素
   * @param {boolean} type - 是否为数组对象去重
   * @param {string} key - 如果为数组对象去重要声明对象中的key
   * @return {array} - 去重后的数组
   * @example arr.unique()
   * <AUTHOR> @date 2019/08/01
   */
  unique(type = false, key) {
    if (type) {
      const res = []
      const obj = {}
      for (let i = 0; i < this.length; i++) {
        if (!obj[this[i][key]]) {
          res.push(this[i])
          obj[this[i][key]] = true
        }
      }
      return res
    } else {
      return Array.from(new Set([...this]))
    }
  },
})
