<template>
  <div class="loading-wrapper">
    <section class="load-container">
      <section class="load-container-text" :data-text="innerText"></section>
      <aside class="load-container-gradient"></aside>
      <aside class="load-container-spotlight"></aside>
    </section>
  </div>
</template>

<script>
export default {
  name: 'Loading',
  props: {
    innerText: {
      required: false,
      type: String,
      default: 'Loading···',
    },
  },
}
</script>

<style lang="scss" scoped>
body {
  overflow: hidden;
  justify-content: center;
  align-content: center;
  align-items: center;

  .loading-wrapper {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 2000;
    width: 100%;
    height: 100%;
    background-color: #000;

    .load-container {
      position: relative;
      top: 50%;
      margin-top: -150px;
      overflow: hidden;
      filter: contrast(110%) brightness(190%);

      &-text {
        position: relative;
        width: 100%;
        height: 300px;
        margin: 0;
        color: transparent;
        font: 700 150px 'Lato', sans-serif;
        text-transform: uppercase;
        text-align: center;
        background: #000;

        &::before,
        &::after {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          text-align: center;
          color: white;
          filter: blur(0.02em);
          content: attr(data-text);
          pointer-events: none;
        }

        &::after {
          mix-blend-mode: difference;
        }
      }

      &-gradient,
      &-spotlight {
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        pointer-events: none;
      }

      &-gradient {
        background: -webkit-linear-gradient(45deg, #fff, #22a9a5);
        background: linear-gradient(45deg, #fff, #22a9a5);
        mix-blend-mode: multiply;
      }

      &-spotlight {
        top: -100%;
        left: -100%;
        background: radial-gradient(circle, #ffffff, rgba(0, 0, 0, 0) 25%) 0 0/25% 25%,
          radial-gradient(circle, #ffffff, #000000 25%) 50% 50%/12.5% 12.5%;
        animation: light 5s infinite linear;
        mix-blend-mode: color-dodge;
      }

      @keyframes light {
        100% {
          transform: translate3d(50%, 50%, 0);
        }
      }
    }
  }
}
</style>
