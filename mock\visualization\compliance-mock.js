// 日志源资产数
const logSourceData = { rizhiyuan: 60, rizhiyuanMax: 200 }

// 日志总数
const logTotalNumber = { zongshu: 365412 }

// 日志存储空间
const logStorageSpace = { chSize: '305', diskSize: '500' }

// 日志存储时长
const logStorageDuration = { sec: 13 }

// 设备日志数量图
const logNumberChart = [
  [' ', 'aa', 'bb', 'cc', 'dd', 'ee', 'ff', 'gg', 'hh', 'ii', 'jj'],
  ['柱1', 12, 23, 9, 34, 54, 45, 98, 56, 87, 67],
]

// 设备存储时长图
const logDurationChart = [
  [' ', 'aa', 'bb', 'cc', 'dd', 'ee', 'ff', 'gg', 'hh', 'ii', 'jj'],
  ['柱1', 12, 23, 9, 34, 54, 45, 98, 56, 87, 67],
]

// 系统运行状况图
const systemHealthyChart = {
  runState: '异常',
  description: 'CPU异常',
  usage: [
    {
      name: 'CPU',
      value: '0.95',
    },
    {
      name: '内存',
      value: '0.25',
    },
  ],
}

// 日志源资产类型图
const logSourceTypeChart = [
  [
    {
      name: '主机',
      value: 44,
      selected: true,
    },
    {
      name: '网络',
      value: 32,
    },
    {
      name: '安全',
      value: 37,
    },
  ],
  [
    {
      name: '防火墙',
      value: 44,
    },
    {
      name: 'IDS',
      value: 32,
    },
    {
      name: 'Windows',
      value: 37,
    },
  ],
]

// 日志采集趋势
const logNumberTrendChart = [
  [
    ' ',
    '00:00',
    '01:00',
    '02:00',
    '03:00',
    '04:00',
    '05:00',
    '06:00',
    '07:00',
    '08:00',
    '09:00',
    '10:00',
    '11:00',
    '12:00',
    '13:00',
    '14:00',
    '15:00',
    '16:00',
    '17:00',
    '18:00',
    '19:00',
    '20:00',
    '21:00',
    '22:00',
    '23:00',
    '24:00',
  ],
  ['线1', 12, 23, 9, 34, 54, 45, 98, 56, 87, 67, 78, 76, 89, 90, 36, 49, 59, 29, 54, 92, 87, 66, 47, 28],
]

const logNumberTable = [
  {
    count: '9166702',
    label: '绿盟_IDS',
  },
  {
    count: '8917770',
    label: 'IDS',
  },
  {
    count: '5535119',
    label: '防火墙4.2',
  },
  {
    count: '127122',
    label: 'mysql审计日志',
  },
]

const logDurationTable = [
  {
    label: '绿盟_IDS',
    sec: '10',
  },
  {
    label: 'IDS',
    sec: '56',
  },
  {
    label: '防火墙4.2',
    sec: '995',
  },
  {
    label: 'mysql审计日志',
    sec: '32',
  },
]

const logReceivingStatus = [
  {
    label: 'Linux',
    value: '115901',
    status: '1',
  },
  {
    label: 'Microsoft Windows',
    value: '115302',
    status: '1',
  },
  {
    label: '海峡黑盾IPS-G5100E2',
    receivingDuration: 1,
    value: '100021',
    status: '0',
  },
  {
    label: '入侵检测系统(IDS)',
    receivingDuration: 1,
    value: '105002',
    status: '1',
  },
]

const deviceCombo = [
  {
    label: '整体',
    value: '0',
  },
  {
    label: 'Linux',
    value: '115901',
  },
  {
    label: 'Microsoft Windows',
    value: '115302',
  },
  {
    label: '海峡黑盾IPS-G5100E2',
    receivingDuration: 1,
    value: '100021',
  },
  {
    label: '入侵检测系统(IDS)',
    receivingDuration: 1,
    value: '105002',
  },
]

const auditAlarmTrend = [
  ['2023-10', '2023-11'],
  [2, 3],
]

const securityEventCount = [
  [' ', '网络攻击事件', '利用漏洞', '系统状况/配置', '可疑活动', '认证/授权/访问', '病毒/木马', '扫描探测'],
  [' ', '200', '641', '878', '257', '305', '47', '3'],
]

const securityEventTable = [
  {
    label: '网络攻击事件',
    value: '200',
  },
  {
    label: '利用漏洞',
    value: '641',
  },
  {
    label: '系统状况/配置',
    value: '878',
  },
  {
    label: '可疑活动',
    value: '257',
  },
]

module.exports = [
  {
    url: '/hegui/management/rizhiyuanzichanshu',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logSourceData,
      }
    },
  },
  {
    url: '/hegui/management/rizhijieshouzongshu',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logTotalNumber,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhicunchuzhanyongkongjian',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logStorageSpace,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhicunchushichangTotal',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logStorageDuration,
      }
    },
  },
  {
    url: '/hegui/management/rizhijieshouqushi',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logNumberTrendChart,
      }
    },
  },
  {
    url: '/hegui/management/queryXitongzhuangkuang',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: systemHealthyChart,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhishebeishuliangTop10',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logNumberChart,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhishebeishuliangliebiao',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: logNumberTable,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhishebeishichangTop10',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logDurationChart,
      }
    },
  },
  {
    url: '/hegui/management/rizhiyuanzichanleixing',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logSourceTypeChart,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhishebeishichangliebiao',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: logDurationTable,
      }
    },
  },
  {
    url: '/hegui/management/queryRizhijieshouzhuangtai',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logReceivingStatus,
      }
    },
  },
  {
    url: '/hegui/management/device/combo',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: deviceCombo,
      }
    },
  },
  {
    url: '/hegui/management/updateRizhiyuanshichang',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/hegui/management/auditAlarmTread',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: auditAlarmTrend,
      }
    },
  },
  {
    url: '/hegui/management/securityEvent/count',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: securityEventCount,
      }
    },
  },
  {
    url: '/hegui/management/querySecurityeventliebiao',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: securityEventTable,
      }
    },
  },
]
