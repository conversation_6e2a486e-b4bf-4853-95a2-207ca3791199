<!--
 * @Description: 告警策略 - 添加弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.add', [title])"
    :width="width"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="strategyName" :label="form.info.strategyName.label">
            <el-input v-model.trim="form.model.strategyName" maxlength="25"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="alarmName" :label="form.info.alarmName.label">
            <el-input v-model.trim="form.model.alarmName" maxlength="35"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="alarmType" :label="form.info.alarmType.label">
            <el-select v-model="form.model.alarmType" filterable clearable :placeholder="$t('alarm.strategy.label.alarmType')">
              <el-option v-for="item in typeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="alarmLevel" :label="form.info.alarmLevel.label">
            <el-select v-model="form.model.alarmLevel" clearable :placeholder="$t('alarm.strategy.label.alarmLevel')">
              <el-option v-for="item in levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="snmpForwardServer" :label="form.info.snmpForwardServer.label">
            <el-select
              v-model="form.model.snmpForwardServer"
              :placeholder="$t('alarm.strategy.label.snmpForwardServer')"
              clearable
              multiple
              filterable
              collapse-tags
            >
              <el-option v-for="item in forwardOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="mailTo" :label="form.info.mailTo.label">
            <el-input v-model="form.model.mailTo" maxlength="64"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="description" :label="form.info.description.label">
            <el-input v-model="form.model.description" type="textarea" :rows="4"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '800',
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    levelOption: {
      type: Array,
      required: true,
    },
    typeOption: {
      type: Array,
      required: true,
    },
    systemOption: {
      type: Array,
      required: true,
    },
    forwardOption: {
      type: Array,
      required: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.form.model)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
