<!--
 * @Description: 关联策略 - 修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item :prop="form.info.incPolicyName.key" :label="form.info.incPolicyName.label">
            <el-input v-model="form.model.incPolicyName" :disabled="isSysDefault === '1'" maxlength="300"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :prop="form.info.description.key" :label="form.info.description.label">
            <el-input v-model="form.model.description" :disabled="isSysDefault === '1'" type="textarea" :rows="3"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="条件">
        <section class="section-filter-conditions">
          <div class="section-filter-conditions-title">
            <span>
              <el-button v-if="isSysDefault !== '1'" type="text" @click="clickInitializeAndButton">
                {{ $t('event.relevanceStrategy.button.and') }}
              </el-button>
            </span>
            <span>
              <el-button v-if="isSysDefault !== '1'" type="text" @click="clickInitializeOrButton">
                {{ $t('event.relevanceStrategy.button.or') }}
              </el-button>
            </span>
          </div>
          <div class="section-filter-conditions-content">
            <el-tree
              ref="tree"
              :data="filterData"
              node-key="id"
              default-expand-all
              class="section-filter-conditions-content-tree"
              :props="defaultProps"
              :expand-on-click-node="false"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span style="line-height: 30px">{{ node.label }}</span>
                <span class="buttonSpan">
                  <el-button v-if="isSysDefault !== '1'" v-show="data.flag" type="text" size="mini" @click="() => appendOrCondition(data)">
                    <span style="line-height: 30px">
                      {{ $t('event.relevanceStrategy.button.or') }}
                    </span>
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" v-show="data.flag" type="text" size="mini" @click="() => appendAndCondition(data)">
                    <span style="line-height: 30px">
                      {{ $t('event.relevanceStrategy.button.and') }}
                    </span>
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" v-show="data.flag" type="text" size="mini" @click="() => clickConditionButton(node)">
                    <span style="line-height: 30px">{{ $t('event.relevanceStrategy.button.config') }}</span>
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => deleteNode(node, data)">
                    <span style="line-height: 30px">{{ $t('button.delete') }}</span>
                  </el-button>
                </span>
              </span>
            </el-tree>
          </div>
          <el-collapse-transition>
            <section v-if="showCondition" class="section-filter-conditions-condition">
              <div>
                <el-select v-model="condition.colume" placeholder="请选择" clearable @change="(val) => keyChange(val, 'condition')">
                  <el-option v-for="item in keyOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-select v-model="condition.option" placeholder="请选择">
                  <el-option v-for="item in operationOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-input v-if="show.input" v-model="condition.value"></el-input>
                <el-select v-if="show.select" v-model="condition.value" filterable placeholder="请选择">
                  <el-option v-for="item in dynamicOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input v-if="show.checkIp" v-model="condition.value" @input="(val) => ValidateIp(val, 'condition')"></el-input>
                <el-input v-if="show.checkMAC" v-model="condition.value" @input="(val) => ValidateMAC(val, 'condition')"></el-input>
                <el-input v-if="show.checkPort" v-model="condition.value" @input="(val) => ValidatePort(val, 'condition')"></el-input>
                <el-input-number
                  v-if="show.inputNumber"
                  v-model="condition.value"
                  controls-position="right"
                  :max="2147483647"
                  :min="0"
                ></el-input-number>
              </div>
              <div>
                <el-button type="primary" @click="determineButton">
                  {{ $t('button.determine') }}
                </el-button>
              </div>
              <div>
                <el-button @click="clickCancelConditionButton">
                  {{ $t('button.cancel') }}
                </el-button>
              </div>
            </section>
          </el-collapse-transition>
        </section>
        <el-collapse-transition>
          <div v-show="show.filterDataShow" class="el-form-item__error">
            {{ this.$t('tip.empty') }}
          </div>
        </el-collapse-transition>
      </el-form-item>
      <el-form-item label="关联规则">
        <section class="section-filter-rules">
          <div class="section-filter-rules-button">
            <span>
              <el-button v-if="isSysDefault !== '1'" type="text" icon="el-icon-circle-plus-outline" @click="() => addStates()">
                新增关联状态
              </el-button>
            </span>
          </div>
          <el-collapse-transition>
            <section v-if="show.isShowRules" class="section-filter-rules-merge">
              <el-row :gutter="10">
                <el-col :span="5">
                  <el-input-number v-model="ruleConfig.duration" :min="1" :max="86400" placeholder="时间"></el-input-number>
                </el-col>
                <el-col :span="5">
                  <el-input-number v-model="ruleConfig.times" :min="1" :max="2147483647" placeholder="次数"></el-input-number>
                </el-col>
                <el-col :span="8">
                  <el-select v-model="ruleConfig.columns" placeholder="归并字段" multiple collapse-tags>
                    <el-option v-for="item in keyOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-col>
                <el-col :span="6">
                  <el-button type="primary" @click="() => determineRules()">
                    {{ $t('button.determine') }}
                  </el-button>
                  <el-button @click="() => cancelRules()">
                    {{ $t('button.cancel') }}
                  </el-button>
                </el-col>
              </el-row>
            </section>
            <section v-if="show.isShowConfig" class="section-filter-rules-dialog">
              <div>
                <el-select v-model="ruleCondition.colume" placeholder="请选择" clearable @change="(val) => keyChange(val, 'rule')">
                  <el-option v-for="item in keyOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-select v-model="ruleCondition.option" placeholder="请选择">
                  <el-option v-for="item in ruleOperationOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-input v-if="show.ruleInput" v-model="ruleCondition.value"></el-input>
                <el-input v-if="show.ruleValidateIp" v-model="ruleCondition.value" @input="(val) => ValidateIp(val, 'rule')"></el-input>
                <el-input v-if="show.ruleValidateMAC" v-model="ruleCondition.value" @input="(val) => ValidateMAC(val, 'rule')"></el-input>
                <el-input v-if="show.ruleValidatePort" v-model="ruleCondition.value" @input="(val) => ValidatePort(val, 'rule')"></el-input>
                <el-select v-if="show.ruleSelect" v-model="ruleCondition.value" filterable placeholder="请选择">
                  <el-option v-for="item in dynamicRuleOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-input-number
                  v-if="show.ruleInputNumber"
                  v-model="ruleCondition.value"
                  controls-position="right"
                  :max="2147483647"
                  :min="0"
                ></el-input-number>
              </div>
              <div>
                <el-button type="primary" @click="() => determine()">
                  {{ $t('button.determine') }}
                </el-button>
              </div>
              <div>
                <el-button @click="() => cancel()">
                  {{ $t('button.cancel') }}
                </el-button>
              </div>
            </section>
            <section v-if="show.isShowHappen" class="section-filter-rules-happen">
              <div>
                <el-select v-model="isHappen" placeholder="发生/不发生">
                  <el-option v-for="item in happenOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-button type="primary" @click="() => determineHappen()">
                  {{ $t('button.determine') }}
                </el-button>
              </div>
              <div>
                <el-button @click="() => cancelHappen()">
                  {{ $t('button.cancel') }}
                </el-button>
              </div>
            </section>
            <section v-if="show.isShowRelation" class="section-filter-rules-dialog">
              <div>
                <el-select v-model="relation.pre" placeholder="状态1">
                  <el-option v-for="item in preOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-select v-model="relation.operate" placeholder="等于/不等于">
                  <el-option v-for="item in equalOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-select v-model="relation.next" placeholder="状态2">
                  <el-option v-for="item in nextOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </div>
              <div>
                <el-button type="primary" @click="() => determineRelation()">
                  {{ $t('button.determine') }}
                </el-button>
              </div>
              <div>
                <el-button @click="() => cancelRelation()">
                  {{ $t('button.cancel') }}
                </el-button>
              </div>
            </section>
          </el-collapse-transition>
          <div class="section-filter-rules-block">
            <el-tree :data="ruData" node-key="id" :props="defaultProps" default-expand-all :expand-on-click-node="false">
              <div slot-scope="{ node, data }" class="custom-tree-node">
                <span>{{ node.label }}</span>
                <!--状态1，状态2...之后的按钮-->
                <span v-show="data.buttonShow === 1" style="margin-left: 20px">
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => append(node, data, 'and')">
                    && 与
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => append(node, data, 'or')">
                    || 或
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => configRules(node, data, data.id)">
                    配置
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => remove(node, data)">
                    删除
                  </el-button>
                </span>
                <!--1,3,5树的按钮-->
                <span v-show="data.buttonShow === 3" style="margin-left: 20px">
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => append(node, data, 'and')">
                    && 与
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => append(node, data, 'or')">
                    || 或
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => config(node, data, data.id)">
                    配置
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => remove(node, data)">
                    删除
                  </el-button>
                </span>
                <!--连接关系的按钮-->
                <span v-show="data.buttonShow === 2" style="margin-left: 20px">
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => appendRelationAnd(node, data)">
                    && 与
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => configRelation(node, data)">
                    配置
                  </el-button>
                </span>
                <!--连接关系子节点的按钮-->
                <span v-show="data.buttonShow === 4" style="margin-left: 20px">
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => RelationAndConfig(node, data)">
                    配置
                  </el-button>
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => remove(node, data)">
                    删除
                  </el-button>
                </span>
                <!--删除文字类型-->
                <span v-show="data.buttonShow === 5" style="margin-left: 20px">
                  <el-button v-if="isSysDefault !== '1'" type="text" size="mini" @click="() => remove(node, data)">
                    删除
                  </el-button>
                </span>
                <br />
                <span v-show="node.level == 1 && node.data.id % 2 != 0" style="margin-left: 10px">
                  在 {{ node.data.duration }} 秒内，发生 {{ node.data.times }}次
                </span>
                <br />
                <span v-show="node.level == 1 && node.data.id % 2 != 0" style="margin-left: 10px">归并字段：{{ node.data.content }}</span>
                <br />
              </div>
            </el-tree>
          </div>
        </section>
      </el-form-item>
      <el-form-item label="结果">
        <section class="section-result">
          <el-row>
            <el-col :span="12">
              <el-form-item :prop="form.info.incSubCategoryID.key" :label="form.info.incSubCategoryID.label">
                <el-select v-model="form.model.incSubCategoryID" :disabled="isSysDefault === '1'" filterable clearable placeholder="请选择">
                  <el-option v-for="item in relationOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :prop="form.info.eventLevel.key" :label="form.info.eventLevel.label">
                <el-select v-model="form.model.eventLevel" :disabled="isSysDefault === '1'" placeholder="请选择">
                  <el-option v-for="item in levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item :prop="form.info.externalSystem.key" :label="form.info.externalSystem.label">
                <el-select v-model="form.model.externalSystem" clearable filterable multiple collapse-tags style="width:96%" placeholder="请选择">
                  <el-option v-for="item in systemOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </section>
      </el-form-item>
      <el-form-item label="事件文本">
        <section>
          <el-row>
            <el-col :span="24">
              <el-form-item :prop="form.info.eventDesc.key">
                <el-input
                  v-model="form.model.eventDesc"
                  :disabled="isSysDefault === '1'"
                  type="textarea"
                  :rows="5"
                  @keydown.alt.native="altEvent"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-collapse-transition>
            <section>
              <div v-if="show.textCascader" class="event-text">
                <div>
                  <el-cascader
                    :options="TextOption"
                    filterable
                    clearable
                    :props="{ expandTrigger: 'hover' }"
                    @change="textCascaderChange"
                  ></el-cascader>
                </div>
                <div>
                  <el-button @click="clickCancelTextCascader">
                    {{ $t('button.cancel') }}
                  </el-button>
                </div>
              </div>
            </section>
          </el-collapse-transition>
        </section>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import ElCollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import { validateIp, validateMac, validatePort } from '@util/validate'
import { isEmpty } from '@util/common'

export default {
  components: {
    ElCollapseTransition,
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '1000',
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    systemOption: {
      type: Array,
    },
    keyOption: {
      type: Array,
    },
    filData: {
      type: Array,
      default: () => [],
    },
    isSysDefault: {
      type: String,
      default: '1',
    },
    reData: {
      type: Array,
      default: () => [],
    },
    filterId: {
      type: Number,
      default: 0,
    },
    ruParentId: {
      type: Number,
      default: 0,
    },
    ruChildrenId: {
      type: Number,
      default: 0,
    },
    relationOption: {
      type: Array,
    },
    deviceClassOption: {
      type: Array,
    },
    deviceTypeOption: {
      type: Array,
    },
    eventClassOption: {
      type: Array,
    },
    eventTypeOption: {
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      id: this.filterId,
      parentId: this.ruParentId, // 关联规则父级id
      childrenId: this.ruChildrenId, // 关联规则子级id
      ruData: this.reData,
      filterData: this.filData,
      defaultProps: {
        children: 'conditions',
      }, // 修改树默认参数名字
      operationOption: [], // 运算符Option
      ruleOperationOption: [],
      dynamicRuleOption: [],
      dynamicOption: [],
      levelOption: [
        {
          label: this.$t('level.serious'),
          value: '0',
        },
        {
          label: this.$t('level.high'),
          value: '1',
        },
        {
          label: this.$t('level.middle'),
          value: '2',
        },
        {
          label: this.$t('level.low'),
          value: '3',
        },
        {
          label: this.$t('level.general'),
          value: '4',
        },
      ], // 等级Option
      happenOption: [
        {
          label: '发生',
          value: 'and',
        },
        {
          label: '不发生',
          value: 'not',
        },
      ], // 是否发生Option
      equalOption: [
        {
          label: '等于',
          value: 'EQUAL',
        },
        {
          label: '不等于',
          value: 'NOT_EQUAL',
        },
      ], // 是否等于Option
      preOption: [], // 前置字段Option
      nextOption: [], // 后置字段Option
      node: {}, // 存储节点信息,供添加时间次数归并字段使用
      leafNode: {}, // 存储节点信息，添加过滤条件使用
      happenNode: {}, // 存储关联信息节点
      relationNode: {},
      data: {},
      isHappen: 'and', // 是否发生绑定值
      show: {
        isShowRules: false,
        isShowConfig: false,
        isShowHappen: false,
        isShowRelation: false,
        ruDataShow: false,
        filterDataShow: false,
        input: true,
        select: false,
        inputNumber: false,
        checkIp: false,
        checkMAC: false,
        checkPort: false,
        ruleInput: true,
        ruleSelect: false,
        ruleInputNumber: false,
        ruleValidateIp: false,
        ruleValidateMAC: false,
        ruleValidatePort: false,
        textCascader: false,
      },
      showCondition: false,
      condition: {
        colume: '',
        option: '',
        value: '',
      },
      ruleConfig: {
        duration: '',
        times: '',
        columns: [],
      },
      ruleCondition: {
        colume: '',
        option: '',
        value: '',
      },
      relation: {
        pre: '',
        operate: '',
        next: '',
      },
      flag: {
        conditions: '',
        rules: '',
      },
      check: {
        conditionIpv4: true,
        conditionMac: true,
        conditionPort: true,
        ipv4: true,
        mac: true,
        port: true,
      },
      // 文本域级联选择器
      TextOption: [],
      // 归并字段枚举
      enumObj: {
        addr0: '发生源IP 数字',
        addr1: '源IP 数字',
        addr2: '目的IP 数字',
        category: '发生源设备类别',
        code: '关键字',
        device: '发生源设备类型',
        ip0: '发生源IP',
        ip1: '源IP',
        ip2: '目的IP',
        kind: '事件类别',
        level: '事件等级',
        mac1: '源MAC地址',
        mac2: '目的MAC',
        port1: '源端口',
        port2: '目的端口',
        protocol: '协议',
        proType: '日志来源协议',
        type: '事件类型',
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
    filData(nVal) {
      if (nVal?.length > 0) {
        this.convertFilterDataLevelOperations(nVal)
      }
      this.filterData = nVal
    },
    reData(nVal) {
      this.ruData = nVal
    },
    ruChildrenId(nVal) {
      this.childrenId = nVal
    },
    ruParentId(nVal) {
      this.parentId = nVal
    },
    filterId(nVal) {
      this.id = nVal
    },
    filterData: {
      handler(nVal) {
        if (nVal.length !== 0) {
          this.show.filterDataShow = false
        }
      },
      immediate: true,
      deep: true,
    },
    ruData: {
      handler(nVal) {
        this.TextOption = []
        for (let i = 0; i < nVal.length; i++) {
          if (i % 2 === 0) {
            const { id, label } = nVal[i]
            const obj = {
              label: label,
              value: id,
              children: this.keyOption,
            }
            this.TextOption.push(obj)
          }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    // 新增关联状态
    addStates() {
      if (this.ruData.length && this.ruData.length === 9) {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.length',
          type: 'warning',
        })
      } else {
        let newNode = {}
        // 状态长度大于一时加入过度关系relation
        if (this.ruData.length && this.ruData.length > 0) {
          const relation = {
            id: ++this.parentId,
            preStateId: '',
            duration: '',
            logic: 'and',
            conditions: [],
            nextStateId: '',
            label: '',
            buttonShow: 2,
          }
          const index = this.ruData.findIndex((d) => d.id === this.parentId - 1)
          // 状态id
          relation.preStateId = this.ruData[index].id
          relation.label = `状态${(this.ruData[this.ruData.length - 1].id + 1) / 2}之后发生状态${(this.parentId + 2) / 2}`
          this.ruData.push(relation)
          // 按后端要求处理数据
          newNode.relation = relation
        }
        // 新增节点内容
        newNode = {
          id: ++this.parentId, // 唯一标识
          label: '', // 文字描述
          duration: 60, // 时间
          times: 2, // 次数
          columns: [], // 归并字段内容
          filter: {},
          conditions: [],
          content: '',
          buttonShow: 1,
        }
        // 创建状态名
        newNode.label = `状态${(this.parentId + 1) / 2}`
        // 新建的节点加入原始数组
        this.ruData.push(newNode)
      }
    },
    // 新增节点
    append(node, data, logic) {
      if (node.level === 1 && node.data.conditions.length === 1) {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.one',
          type: 'warning',
        })
      } else {
        let filter = {}
        if (logic === 'and') {
          // 新增节点内容
          filter = {
            id: this.childrenId,
            label: '&& 与',
            conditions: [],
            logic: 'and', // 逻辑关系
            buttonShow: 3,
          }
        } else {
          // 新增节点内容
          filter = {
            id: this.childrenId,
            label: '|| 或',
            conditions: [],
            logic: 'or', // 逻辑关系
            buttonShow: 3,
          }
        }
        // 加入原始数组
        data.conditions.push(filter)
        // 子节点自增
        this.childrenId++
      }
    },
    // 移除节点
    remove(node, data) {
      const parent = node.parent
      const conditions = parent.data.conditions || parent.data
      const index = conditions.findIndex((d) => d.id >= data.id)
      if (conditions.length && conditions.length > 1 && index !== 0) {
        if (conditions.length > 2 && conditions.length - 2 > index) {
          if (index !== -1) {
            conditions[index + 1].label = `状态${(conditions[index - 2].id + 1) / 2}之后发生状态${(conditions[index + 2].id + 1) / 2}`
            conditions[index + 1].preStateId = conditions[index - 2].id
            conditions.splice(index, 1)
            conditions.splice(index - 1, 1)
          }
        } else {
          if (node.level !== 1) {
            conditions.splice(index, 1)
          } else {
            conditions.splice(index, 1)
            conditions.splice(index - 1, 1)
            this.parentId = this.parentId - 2
          }
        }
      } else if (index === 0 && conditions.length > 1) {
        if (node.level === 1) {
          conditions.splice(index, 2)
        } else {
          conditions.splice(index, 1)
        }
      } else {
        conditions.splice(index, 1)
      }

      if (this.ruData && this.ruData.length === 0) {
        this.parentId = 0
      }
      this.show.isShowHappen = false
      this.show.isShowRelation = false
      this.show.isShowRules = false
      this.show.isShowConfig = false
    },
    // 显示条件设置
    config(node) {
      this.show.isShowHappen = false
      this.show.isShowRelation = false
      this.show.isShowRules = false
      this.leafNode = node
      this.ruleCondition = {
        colume: '',
        option: '',
        value: '',
      }
      this.ruleOperationOption = []
      this.show.isShowConfig = true
      this.show.ruleInput = true
      this.show.ruleSelect = false
      this.show.ruleInputNumber = false
      this.show.ruleValidateMAC = false
      this.show.ruleValidateIp = false
      this.show.ruleValidatePort = false
    },
    // 确定添加条件设置
    determine() {
      if (!this.ruleCondition.colume.isNotEmpty()) {
        prompt({
          i18nCode: 'validate.chooseItem.empty',
          type: 'warning',
        })
        return false
      }
      if (!this.check.ipv4) {
        prompt({
          i18nCode: 'validate.ip.incorrect',
          type: 'warning',
        })
        return false
      }
      if (!this.check.mac) {
        prompt({
          i18nCode: 'validate.mac.incorrect',
          type: 'warning',
        })
        return false
      }
      if (!this.check.port) {
        prompt({
          i18nCode: 'validate.port.incorrect',
          type: 'warning',
        })
        return false
      }
      for (const [v] of Object.entries(this.ruleCondition)) {
        if (v === '') {
          prompt({
            i18nCode: 'event.relevanceStrategy.tip.empty',
            type: 'warning',
          })
          return null
        }
      }
      const { colume, option, value: newVal } = this.ruleCondition
      let changeValue = ''
      const change = (option) => {
        let label = ''
        switch (option) {
          case 'GREATER_THAN':
            label = '大于'
            break
          case 'LESS_THAN':
            label = '小于'
            break
          case 'GREATER_THAN_EQUAL':
            label = '大于等于'
            break
          case 'LESS_THAN_EQUAL':
            label = '小于等于'
            break
          case 'EQUAL':
            label = '等于'
            break
          case 'NOT_EQUAL':
            label = '不等于'
            break
          case 'match':
            label = '匹配'
            break
          default:
            break
        }
        return label
      }
      const changeColume = (option) => {
        let label = ''
        for (const item of this.keyOption) {
          if (item.value === option) {
            label += item.label
          }
        }
        return label
      }
      if (
        this.flag.rules === '发生源设备类别' ||
        this.flag.rules === '发生源设备类型' ||
        this.flag.rules === '事件类别' ||
        this.flag.rules === '事件类型' ||
        this.flag.rules === '事件等级'
      ) {
        switch (this.flag.rules) {
          case '发生源设备类别':
            for (const { label, value } of this.deviceClassOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '发生源设备类型':
            for (const { label, value } of this.deviceTypeOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '事件类别':
            for (const { label, value } of this.eventClassOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '事件类型':
            for (const { label, value } of this.eventTypeOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '事件等级':
            for (const { label, value } of this.levelOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          default:
            break
        }
      } else {
        changeValue = newVal
      }
      const label = `${changeColume(colume)} ${change(option)} ${changeValue}`
      const child = {
        id: this.childrenId,
        label: label,
        colume: colume,
        option: option,
        value: newVal,
        buttonShow: 5,
      }
      this.leafNode.data.conditions.push(child)
      this.childrenId++
      this.flag.rules = ''
      this.show.isShowConfig = false
      this.check.ipv4 = true
      this.check.mac = true
      this.check.port = true
    },
    // 取消添加条件设置
    cancel() {
      this.ruleCondition = {
        colume: '',
        option: '',
        value: '',
      }
      this.ruleOperationOption = []
      this.show.isShowConfig = false
      this.leafNode = {}
      this.flag.rules = ''
      this.check.ipv4 = true
      this.check.mac = true
      this.check.port = true
    },
    // 显示时间，次数，归并字段
    configRules(node) {
      this.show.isShowHappen = false
      this.show.isShowRelation = false
      this.show.isShowConfig = false
      this.node = node
      this.ruleConfig = {
        duration: node.data.duration,
        times: node.data.times,
        columns: [],
      }
      this.show.isShowRules = true
    },
    // 确定添加时间，次数，归并字段
    determineRules() {
      let str = ''
      for (const item of this.ruleConfig.columns) {
        for (const obj of this.keyOption) {
          if (item === obj.value) {
            str += obj.label + ','
          }
        }
      }
      this.$set(this.node.data, 'duration', this.ruleConfig.duration)
      this.$set(this.node.data, 'times', this.ruleConfig.times)
      this.$set(this.node.data, 'columns', this.ruleConfig.columns)
      this.$set(this.node.data, 'content', str.substr(0, str.length - 1))
      this.show.isShowRules = false
    },
    // 取消添加时间，次数，归并字段
    cancelRules() {
      this.show.isShowRules = false
      this.ruleConfig = {
        duration: '',
        times: '',
        columns: [],
      }
      this.node = {}
    },
    // 关联条件添加与字段
    appendRelationAnd(node, data) {
      if (node.data.conditions.length === 1) {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.one',
          type: 'warning',
        })
      } else {
        const filter = {
          id: this.childrenId,
          label: '&& 与',
          logic: 'and',
          conditions: [],
          buttonShow: 4,
        }
        data.conditions.push(filter)
        this.childrenId++
      }
    },
    // 设置发生还是不发生
    configRelation(node) {
      this.show.isShowRules = false
      this.show.isShowRelation = false
      this.show.isShowConfig = false
      this.isHappen = 'and'
      this.show.isShowHappen = true
      this.happenNode = node
    },
    // 确定发生或者不发生字段
    determineHappen() {
      const index = this.ruData.findIndex((d) => d.id === this.happenNode.data.preStateId)
      const preLabel = this.ruData[index].label
      const nextLabel = this.ruData[index + 2].label
      this.$set(this.happenNode.data, 'logic', this.isHappen)
      this.isHappen === 'and'
        ? this.$set(this.happenNode.data, 'label', `${preLabel}之后发生${nextLabel}`)
        : this.$set(this.happenNode.data, 'label', `${preLabel}之后不发生${nextLabel}`)
      this.show.isShowHappen = false
    },
    // 取消发生或者不发生的选择
    cancelHappen() {
      this.show.isShowHappen = false
      this.happenNode = {}
      this.isHappen = 'and'
    },
    // 设置相关联两个状态的前置字段后后置字段
    RelationAndConfig(node) {
      this.show.isShowHappen = false
      this.show.isShowRules = false
      this.show.isShowConfig = false
      this.relationNode = node
      const parent = node.parent
      const id = parent.data.id
      const nextOption = []
      const preOption = []
      const index = this.ruData.findIndex((d) => d.id === id)
      const preColumns = this.ruData[index - 1].columns
      const nextColumns = this.ruData[index + 1].columns
      if (preColumns.length !== 0) {
        for (const item of preColumns) {
          for (const obj of this.keyOption) {
            if (item === obj.value) {
              preOption.push(obj)
            }
          }
        }
      } else {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.column',
          type: 'warning',
        })
      }
      if (nextColumns.length !== 0) {
        for (const item of nextColumns) {
          for (const obj of this.keyOption) {
            if (item === obj.value) {
              nextOption.push(obj)
            }
          }
        }
      } else {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.column',
          type: 'warning',
        })
      }
      this.preOption = Array.from(new Set(preOption))
      this.nextOption = Array.from(new Set(nextOption))
      this.show.isShowRelation = true
      this.relation = {
        pre: '',
        operate: '',
        next: '',
      }
    },
    determineRelation() {
      let preLabel = ''
      let nextLabel = ''
      let preType = ''
      let nextType = ''
      let operateLabel = ''
      for (const obj of this.preOption) {
        if (this.relation.pre === obj.value) {
          preLabel += obj.label
          preType = obj.type
        }
      }
      for (const obj of this.nextOption) {
        if (this.relation.next === obj.value) {
          nextLabel += obj.label
          nextType = obj.type
        }
      }
      for (const obj of this.equalOption) {
        if (this.relation.operate === obj.value) {
          operateLabel += obj.label
        }
      }
      if (preLabel === '' || nextLabel === '' || operateLabel === '') {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.empty',
          type: 'warning',
        })
        return null
      }
      if (preType !== nextType) {
        prompt({
          i18nCode: 'event.relevanceStrategy.tip.type',
          type: 'warning',
        })
        return
      }
      const content = {
        preColumn: this.relation.pre,
        option: this.relation.operate,
        column: this.relation.next,
        id: this.childrenId,
        label: `${preLabel} ${operateLabel} ${nextLabel}`,
        buttonShow: 5,
      }
      this.show.isShowRelation = false
      this.preOption = []
      this.nextOption = []
      this.relationNode.data.conditions.push(content)
      this.childrenId++
    },
    cancelRelation() {
      this.show.isShowRelation = false
      this.preOption = []
      this.nextOption = []
      this.relation = {
        pre: '',
        operate: '',
        next: '',
      }
    },
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
      this.show.ruDataShow = false
      this.show.filterDataShow = false
      this.show.isShowHappen = false
      this.show.isShowRules = false
      this.show.isShowConfig = false
      this.show.isShowRelation = false
      this.showCondition = false
      this.dynamicOption = []
      this.dynamicRuleOption = []
      this.show.input = true
      this.show.select = false
      this.show.ruleSelect = false
      this.show.ruleInput = true
      this.show.inputNumber = false
      this.show.checkIp = false
      this.show.checkMAC = false
      this.show.checkPort = false
      this.show.ruleInputNumber = false
      this.show.ruleValidateIp = false
      this.show.ruleValidateMAC = false
      this.show.ruleValidatePort = false
      this.show.textCascader = false
      this.flag.conditions = ''
      this.flag.rules = ''
      this.parentId = 0
      this.check = {
        conditionIpv4: true,
        conditionMac: true,
        conditionPort: true,
        ipv4: true,
        mac: true,
        port: true,
      }
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        let flag = 0
        if (this.filterData.length === 0) {
          this.show.filterDataShow = true
          flag = 1
          return null
        }
        if (this.filterData[0]) {
          if (this.filterData[0].conditions.length === 0) {
            prompt({
              i18nCode: 'event.relevanceStrategy.tip.conditions',
              type: 'warning',
            })
            return null
          }
        }
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            // 关联状态未添加与或条件的友好提示
            for (let i = 0; i < this.ruData.length; i++) {
              if (i % 2 === 0) {
                if (this.ruData[i].conditions.length === 0) {
                  prompt({
                    i18nCode: 'event.relevanceStrategy.tip.data',
                    type: 'warning',
                  })
                  flag = 1
                }
              }
            }
            if (flag === 0) {
              for (let i = 0; i < this.ruData.length; i++) {
                if (i % 2 === 0) {
                  this.ruData[i].filter['logic'] = this.ruData[i].conditions[0].logic
                  this.ruData[i].filter['conditions'] = this.ruData[i].conditions[0].conditions
                }
                if (i % 2 !== 0) {
                  this.ruData[i + 1]['relation'] = this.ruData[i]
                }
              }
              if (this.filterData.length !== 0) {
                this.filterData[0]['treeId'] = this.id
              }
              if (this.ruData.length !== 0) {
                this.ruData[0]['childrenId'] = this.childrenId
                this.ruData[0]['parentId'] = this.parentId
              }
              // 重置 filterData，转换 level 字段的操作符
              this.convertFilterDataLevelOperations(this.filterData)

              const states = JSON.parse(JSON.stringify(this.ruData))
              const filter = JSON.parse(JSON.stringify(this.filterData))
              this.$emit('on-submit', this.form.model, states, filter)
              this.clickCancelDialog()
            }
          })
        } else {
          if (this.filterData.length === 0) {
            this.show.filterDataShow = true
          }
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },

    // 新增方法：转换 level 字段的操作符
    convertFilterDataLevelOperations(data) {
      if (!data || data.length === 0) return

      const processConditions = (conditions) => {
        if (!conditions || conditions.length === 0) return

        for (const item of conditions) {
          // 递归处理子条件
          if (item.conditions && item.conditions.length > 0) {
            processConditions(item.conditions)
          }

          // 对 level 字段进行操作符反转处理
          if (item.colume === 'level') {
            switch (item.option) {
              case 'GREATER_THAN':
                item.option = 'LESS_THAN_EQUAL'
                break
              case 'LESS_THAN':
                item.option = 'GREATER_THAN_EQUAL'
                break
              case 'GREATER_THAN_EQUAL':
                item.option = 'LESS_THAN'
                break
              case 'LESS_THAN_EQUAL':
                item.option = 'GREATER_THAN'
                break
              default:
                // 保持 EQUAL 和 NOT_EQUAL 不变
                break
            }
          }
        }
      }

      // 处理每个根条件的子条件
      for (const root of data) {
        if (root.conditions && root.conditions.length > 0) {
          processConditions(root.conditions)
        }
      }
    },

    clickInitializeAndButton() {
      if (this.filterData.length === 0) {
        const newChild = {
          id: this.id++,
          label: '&& 与',
          conditions: [],
          flag: true,
          logic: 'and',
        }
        this.filterData.push(newChild)
      }
    },
    clickInitializeOrButton() {
      if (this.filterData.length === 0) {
        const newChild = {
          id: this.id++,
          label: '|| 或',
          flag: true,
          logic: 'or',
          conditions: [],
        }
        this.filterData.push(newChild)
      }
    },
    clickConditionButton(node) {
      this.showCondition = true
      this.data = node.data
      this.condition = {
        colume: '',
        option: '',
        value: '',
      }
      this.show.input = true
      this.show.select = false
      this.show.inputNumber = false
      this.show.checkIp = false
      this.show.checkMAC = false
      this.show.checkPort = false
      this.operationOption = []
    },
    clickCancelConditionButton() {
      this.showCondition = false
      this.condition = {
        colume: '',
        option: '',
        value: '',
      }
      this.operationOption = []
      this.flag.conditions = ''
      this.check.conditionIpv4 = true
      this.check.conditionMac = true
      this.check.conditionPort = true
    },
    appendAndCondition(data) {
      const newChild = {
        id: this.id++,
        label: '&&与 ',
        conditions: [],
        flag: true,
        logic: 'and',
      }
      data.conditions.push(newChild)
    },
    appendOrCondition(data) {
      const newChild = {
        id: this.id++,
        label: '||或 ',
        conditions: [],
        flag: true,
        logic: 'or',
      }
      data.conditions.push(newChild)
    },
    determineButton() {
      if (!this.condition.colume.isNotEmpty()) {
        prompt({
          i18nCode: 'validate.chooseItem.empty',
          type: 'warning',
        })
        return false
      }
      if (!this.check.conditionIpv4) {
        prompt({
          i18nCode: 'validate.ip.incorrect',
          type: 'warning',
        })
        return false
      }
      if (!this.check.conditionMac) {
        prompt({
          i18nCode: 'validate.mac.incorrect',
          type: 'warning',
        })
        return false
      }
      if (!this.check.conditionPort) {
        prompt({
          i18nCode: 'validate.port.incorrect',
          type: 'warning',
        })
        return false
      }
      for (const [v] of Object.entries(this.condition)) {
        if (v === '') {
          prompt({
            i18nCode: 'event.relevanceStrategy.tip.empty',
            type: 'warning',
          })
          return null
        }
      }
      const { colume, option, value: newVal } = this.condition
      let changeValue = ''
      if (
        this.flag.conditions === '发生源设备类别' ||
        this.flag.conditions === '发生源设备类型' ||
        this.flag.conditions === '事件类别' ||
        this.flag.conditions === '事件类型' ||
        this.flag.conditions === '事件等级'
      ) {
        switch (this.flag.conditions) {
          case '发生源设备类别':
            for (const { label, value } of this.deviceClassOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '发生源设备类型':
            for (const { label, value } of this.deviceTypeOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '事件类别':
            for (const { label, value } of this.eventClassOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '事件类型':
            for (const { label, value } of this.eventTypeOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          case '事件等级':
            for (const { label, value } of this.levelOption) {
              if (newVal === value) {
                changeValue = label
              }
            }
            break
          default:
            break
        }
      } else {
        changeValue = newVal
      }
      const change = (option) => {
        let label = ''
        switch (option) {
          case 'GREATER_THAN':
            label = '大于'
            break
          case 'LESS_THAN':
            label = '小于'
            break
          case 'GREATER_THAN_EQUAL':
            label = '大于等于'
            break
          case 'LESS_THAN_EQUAL':
            label = '小于等于'
            break
          case 'EQUAL':
            label = '等于'
            break
          case 'NOT_EQUAL':
            label = '不等于'
            break
          case 'match':
            label = '匹配'
            break
          default:
            break
        }
        return label
      }
      const changeColume = (option) => {
        let label = ''
        for (const item of this.keyOption) {
          if (item.value === option) {
            label += item.label
          }
        }
        return label
      }
      const label = `${changeColume(colume)} ${change(option)} ${changeValue}`
      const child = Object.assign({}, this.condition, {
        id: this.id++,
        label: label,
        flag: false,
      })
      this.$nextTick(() => {
        this.$refs.tree.append(child, this.data)
      })
      this.showCondition = false
      this.flag.conditions = ''
      this.$forceUpdate()
    },
    deleteNode(node, data) {
      this.showCondition = false
      const parent = node.parent
      const children = parent.data.conditions || parent.data
      const index = children.findIndex((d) => d.id === data.id)
      children.splice(index, 1)
    },
    ValidateIp(val, flag) {
      if (flag === 'condition') {
        this.check.conditionIpv4 = true
        if (!isEmpty(val) && !validateIp(val)) this.check.conditionIpv4 = false
      } else {
        this.check.ipv4 = true
        if (!isEmpty(val) && !validateIp(val)) this.check.ipv4 = false
      }
    },
    ValidateMAC(val, flag) {
      if (flag === 'condition') {
        this.check.conditionMac = true
        if (!isEmpty(val) && !validateMac(val)) this.check.conditionMac = false
      } else {
        this.check.mac = true
        if (!isEmpty(val) && !validateMac(val)) this.check.mac = false
      }
    },
    ValidatePort(val, flag) {
      if (flag === 'condition') {
        this.check.conditionPort = true
        if (!isEmpty(val) && !validatePort(val)) this.check.conditionPort = false
      } else {
        this.check.port = true
        if (!isEmpty(val) && !validatePort(val)) this.check.port = false
      }
    },
    keyChange(val, flag) {
      if (flag === 'condition') {
        if (val.isNotEmpty()) {
          this.condition.option = 'EQUAL'
        } else {
          this.condition.option = ''
        }
        let str = ''
        let str2 = ''
        for (const { value, type, label } of this.keyOption) {
          if (val === value) {
            str = type
            str2 = label
          }
        }
        this.clearConditionValidator()
        if (str === 'string') {
          if (val === 'ip0' || val === 'ip1' || val === 'ip2') {
            this.dynamicOption = []
            this.condition.value = ''
            this.show.input = false
            this.show.select = false
            this.show.inputNumber = false
            this.show.checkIp = true
            this.show.checkMAC = false
            this.show.checkPort = false
          } else if (val === 'mac2' || val === 'mac1') {
            this.dynamicOption = []
            this.condition.value = ''
            this.show.input = false
            this.show.select = false
            this.show.inputNumber = false
            this.show.checkIp = false
            this.show.checkMAC = true
            this.show.checkPort = false
          } else {
            this.dynamicOption = []
            this.condition.value = ''
            this.show.input = true
            this.show.select = false
            this.show.inputNumber = false
            this.show.checkIp = false
            this.show.checkMAC = false
            this.show.checkPort = false
          }
        } else if (str === 'int') {
          if (val === 'port1' || val === 'port2') {
            this.dynamicOption = []
            this.condition.value = ''
            this.show.input = false
            this.show.select = false
            this.show.inputNumber = false
            this.show.checkIp = false
            this.show.checkMAC = false
            this.show.checkPort = true
          } else {
            switch (str2) {
              case '发生源设备类别':
                this.flag.conditions = '发生源设备类别'
                this.dynamicOption = []
                this.condition.value = ''
                this.dynamicOption = this.deviceClassOption
                this.show.input = false
                this.show.select = true
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              case '发生源设备类型':
                this.flag.conditions = '发生源设备类型'
                this.dynamicOption = []
                this.condition.value = ''
                this.dynamicOption = this.deviceTypeOption
                this.show.input = false
                this.show.select = true
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              case '事件类别':
                this.flag.conditions = '事件类别'
                this.dynamicOption = []
                this.condition.value = ''
                this.dynamicOption = this.eventClassOption
                this.show.input = false
                this.show.select = true
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              case '事件类型':
                this.flag.conditions = '事件类型'
                this.dynamicOption = []
                this.condition.value = ''
                this.dynamicOption = this.eventTypeOption
                this.show.input = false
                this.show.select = true
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              case '事件等级':
                this.flag.conditions = '事件等级'
                this.dynamicOption = []
                this.condition.value = ''
                this.dynamicOption = this.levelOption
                this.show.input = false
                this.show.select = true
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              case '源端口':
                this.dynamicOption = []
                this.condition.value = ''
                this.show.input = true
                this.show.select = false
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              case '目的端口':
                this.dynamicOption = []
                this.condition.value = ''
                this.show.input = true
                this.show.select = false
                this.show.inputNumber = false
                this.show.checkIp = false
                this.show.checkMAC = false
                this.show.checkPort = false
                break
              default:
                break
            }
          }
        } else if (str === 'long') {
          this.dynamicOption = []
          this.condition.value = ''
          this.show.input = false
          this.show.select = false
          this.show.inputNumber = true
          this.show.checkIp = false
          this.show.checkMAC = false
          this.show.checkPort = false
        }
        str === 'string'
          ? (this.operationOption = [
              {
                label: '等于',
                value: 'EQUAL',
              },
              {
                label: '不等于',
                value: 'NOT_EQUAL',
              },
              {
                label: '匹配',
                value: 'match',
              },
            ])
          : (this.operationOption = [
              {
                label: '大于',
                value: 'GREATER_THAN',
              },
              {
                label: '小于',
                value: 'LESS_THAN',
              },
              {
                label: '大于等于',
                value: 'GREATER_THAN_EQUAL',
              },
              {
                label: '小于等于',
                value: 'LESS_THAN_EQUAL',
              },
              {
                label: '等于',
                value: 'EQUAL',
              },
              {
                label: '不等于',
                value: 'NOT_EQUAL',
              },
            ])
      } else {
        if (val.isNotEmpty()) {
          this.ruleCondition.option = 'EQUAL'
        } else {
          this.ruleCondition.option = ''
        }
        // 取出选中条件的label(键),type(类型),val(键值)
        // type(类型)
        let str = ''
        let str2 = ''
        for (const { value, type, label } of this.keyOption) {
          if (val === value) {
            str = type
            str2 = label
          }
        }
        this.clearRuleValidator()
        if (str === 'string') {
          if (val === 'ip0' || val === 'ip1' || val === 'ip2') {
            this.ruleCondition.value = ''
            this.dynamicRuleOption = []
            this.show.ruleInput = false
            this.show.ruleSelect = false
            this.show.ruleInputNumber = false
            this.show.ruleValidateMAC = false
            this.show.ruleValidateIp = true
            this.show.ruleValidatePort = false
          } else if (val === 'mac2' || val === 'mac1') {
            this.ruleCondition.value = ''
            this.dynamicRuleOption = []
            this.show.ruleInput = false
            this.show.ruleSelect = false
            this.show.ruleInputNumber = false
            this.show.ruleValidateIp = false
            this.show.ruleValidateMAC = true
            this.show.ruleValidatePort = false
          } else {
            this.ruleCondition.value = ''
            this.dynamicRuleOption = []
            this.show.ruleInput = true
            this.show.ruleSelect = false
            this.show.ruleInputNumber = false
            this.show.ruleValidateIp = false
            this.show.ruleValidateMAC = false
            this.show.ruleValidatePort = false
          }
        } else if (str === 'int') {
          if (val === 'port1' || val === 'port2') {
            this.ruleCondition.value = ''
            this.dynamicRuleOption = []
            this.show.ruleInput = false
            this.show.ruleSelect = false
            this.show.ruleInputNumber = false
            this.show.ruleValidateIp = false
            this.show.ruleValidateMAC = false
            this.show.ruleValidatePort = true
          } else {
            switch (str2) {
              case '发生源设备类别':
                this.flag.rules = '发生源设备类别'
                this.ruleCondition.value = ''
                this.dynamicRuleOption = []
                this.dynamicRuleOption = this.deviceClassOption
                this.show.ruleInput = false
                this.show.ruleSelect = true
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              case '发生源设备类型':
                this.flag.rules = '发生源设备类型'
                this.ruleCondition.value = ''
                this.dynamicRuleOption = []
                this.dynamicRuleOption = this.deviceTypeOption
                this.show.ruleInput = false
                this.show.ruleSelect = true
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              case '事件类别':
                this.flag.rules = '事件类别'
                this.ruleCondition.value = ''
                this.dynamicRuleOption = []
                this.dynamicRuleOption = this.eventClassOption
                this.show.ruleInput = false
                this.show.ruleSelect = true
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              case '事件类型':
                this.flag.rules = '事件类型'
                this.ruleCondition.value = ''
                this.dynamicRuleOption = []
                this.dynamicRuleOption = this.eventTypeOption
                this.show.ruleInput = false
                this.show.ruleSelect = true
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              case '事件等级':
                this.flag.rules = '事件等级'
                this.ruleCondition.value = ''
                this.dynamicRuleOption = []
                this.dynamicRuleOption = this.levelOption
                this.show.ruleInput = false
                this.show.ruleSelect = true
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              case '源端口':
                this.dynamicRuleOption = []
                this.ruleCondition.value = ''
                this.show.ruleInput = true
                this.show.ruleSelect = false
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              case '目的端口':
                this.dynamicRuleOption = []
                this.ruleCondition.value = ''
                this.show.ruleInput = true
                this.show.ruleSelect = false
                this.show.ruleInputNumber = false
                this.show.ruleValidateIp = false
                this.show.ruleValidateMAC = false
                this.show.ruleValidatePort = false
                break
              default:
                break
            }
          }
        } else if (str === 'long') {
          this.ruleCondition.value = ''
          this.dynamicRuleOption = []
          this.show.ruleInput = false
          this.show.ruleSelect = false
          this.show.ruleInputNumber = true
          this.show.ruleValidateIp = false
          this.show.ruleValidateMAC = false
          this.show.ruleValidatePort = false
        }
        str === 'string'
          ? (this.ruleOperationOption = [
              {
                label: '等于',
                value: 'EQUAL',
              },
              {
                label: '不等于',
                value: 'NOT_EQUAL',
              },
              {
                label: '匹配',
                value: 'match',
              },
            ])
          : (this.ruleOperationOption = [
              {
                label: '大于',
                value: 'GREATER_THAN',
              },
              {
                label: '小于',
                value: 'LESS_THAN',
              },
              {
                label: '大于等于',
                value: 'GREATER_THAN_EQUAL',
              },
              {
                label: '小于等于',
                value: 'LESS_THAN_EQUAL',
              },
              {
                label: '等于',
                value: 'EQUAL',
              },
              {
                label: '不等于',
                value: 'NOT_EQUAL',
              },
            ])
      }
    },
    clearConditionValidator() {
      this.check = {
        conditionIpv4: true,
        conditionMac: true,
        conditionPort: true,
      }
    },
    clearRuleValidator() {
      this.check = {
        ipv4: true,
        mac: true,
        port: true,
      }
    },
    altEvent() {
      this.show.textCascader = true
    },
    textCascaderChange(val) {
      const text = this.form.model.eventDesc ? this.form.model.eventDesc : ''
      let str = ''
      const [stateId, value] = val
      str = `{state${stateId}-${value}}`
      this.form.model.eventDesc = text.concat(str)
      this.show.textCascader = false
    },
    clickCancelTextCascader() {
      this.show.textCascader = false
    },
  },
}
</script>
<style lang="scss" scoped>
html[data-theme='light'] *::-webkit-scrollbar-track {
  background-color: $CR;
}

::v-deep .el-tree-node__content {
  width: 90%;
}

.el-dialog .el-dialog__body .el-form-item__content > .el-textarea {
  width: 96%;
}

.el-select-dropdown__item.selected {
  font-weight: 500;
}

.section-filter-rules {
  ::v-deep .el-tree-node__content {
    width: 90%;
    line-height: normal;
    margin-top: 16px;
  }
}

::v-deep .el-tree__empty-text {
  display: none;
}

.section-filter-conditions {
  overflow-y: scroll;
  margin-bottom: 10px;
  height: 200px;
  width: 800px;
  border: 1px solid;
  @include theme('border-color', el-input-border);
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);

  &-title {
    position: relative;

    > span {
      position: absolute;
    }

    span:nth-child(1) {
      top: 0;
      right: 10px;
    }

    span:nth-child(2) {
      top: 0;
      right: 60px;
    }
  }

  &-content {
    width: 100%;
    margin: 30px 0 20px 0;

    &-tree {
      .el-tree-node__content {
        &:hover p {
          @include theme('color', font-color);
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding-right: 8px;

          .buttonSpan {
            margin-right: 60px;
          }

          :nth-child(2) {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }

  &-condition {
    position: relative;
    margin-top: 10px;
    z-index: 9999999;

    div:nth-child(1) {
      z-index: 999999;
      position: absolute;
      width: 160px;
      height: 32px;
      top: 0;
      left: 10px;
    }

    div:nth-child(2) {
      z-index: 99999;
      width: 160px;
      height: 32px;
      top: 0;
      left: 180px;
      position: absolute;
    }

    div:nth-child(3) {
      width: 160px;
      height: 32px;
      left: 360px;
      position: absolute;
    }

    div:nth-child(4) {
      position: absolute;
      left: 550px;
    }

    div:nth-child(5) {
      position: absolute;
      left: 625px;
    }
  }
}

.section-filter-rules {
  overflow-y: scroll;
  width: 800px;
  height: 200px;
  margin-bottom: 10px;
  border: 1px solid;
  @include theme('border-color', el-input-border);
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);

  &-button {
    position: relative;

    > span {
      z-index: 9999;
      position: absolute;
      top: 5px;
      right: 10px;
    }
  }
  &-merge {
    margin: 10px 0 0 10px;
    .el-row {
      width: 86%;
    }
  }
  &-dialog {
    position: relative;
    margin-top: 10px;
    margin-bottom: 40px;
    z-index: 9999999;

    div:nth-child(1) {
      z-index: 999999;
      position: absolute;
      width: 140px;
      height: 32px;
      top: 0;
      left: 5px;
    }

    div:nth-child(2) {
      z-index: 99999;
      width: 160px;
      height: 32px;
      top: 0;
      left: 160px;
      position: absolute;
    }

    div:nth-child(3) {
      width: 205px;
      height: 32px;
      left: 325px;
      position: absolute;
    }

    div:nth-child(4) {
      position: absolute;
      left: 530px;
    }

    div:nth-child(5) {
      position: absolute;
      left: 595px;
    }
  }

  &-happen {
    position: relative;
    margin-top: 10px;
    margin-bottom: 40px;
    z-index: 9999999;

    div:nth-child(1) {
      z-index: 999999;
      position: absolute;
      width: 160px;
      height: 32px;
      top: 0;
      left: 40px;
    }

    div:nth-child(2) {
      z-index: 99999;
      width: 160px;
      height: 32px;
      top: 0;
      left: 250px;
      position: absolute;
    }

    div:nth-child(3) {
      z-index: 99999;
      width: 160px;
      height: 32px;
      left: 315px;
      position: absolute;
    }
  }

  &-block {
    .el-tree {
      margin: 30px 0 20px 0;

      .el-tree-node__content {
        &:hover span {
          @include theme('color', font-color);
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 14px;
          padding-right: 8px;
        }
      }
    }
  }
}

.section-result {
  width: 800px;
  height: 140px;
  border: 1px solid;
  @include theme('border-color', el-input-border);
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);

  > div {
    margin-top: 20px;
  }

  div:nth-child(2) {
    ::v-deep .width-small {
      width: 96%;
    }
  }
}

.el-form {
  div:nth-child(3) {
    > ::v-deep .el-form-item__label {
      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
  }
}

.event-text {
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: flex-start;

  div:nth-child(2) {
    margin-left: 10px;
  }
}

/*.external-system {*/
/*::v-deep .el-select__tags {*/
/*span:nth-child(1) {*/
/*width: 80%;*/
/*white-space: nowrap;*/

/*& + {*/
/*.el-select__input, .is-small {*/
/*display: none;*/
/*}*/
/*}*/
/*}*/
/*}*/
/*}*/
</style>
