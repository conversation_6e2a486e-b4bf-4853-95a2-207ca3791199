<template>
  <v-scale-screen width="1920" height="1080">
    <div class="bigScreen">
      <div class="video-wrapper">
        <video id="earth-video" autoplay loop muted src="@/asset/image/visualization/mantance/ocean.mp4"></video>
      </div>
      <threat-risk-header style="grid-area: header" @change-refresh-dura="changeRefreshDura" @change-date="changeDate"></threat-risk-header>
      <div class="screenContent" style="grid-area: content">
        <threat-deal-block title="日志量TOP10" style="grid-area: chart1; height: 720px; padding-top: 16px">
          <mantance-bar-chart :width="'100%'" :height="'100%'" :series-data="logsTop10"></mantance-bar-chart>
        </threat-deal-block>
        <!-- 性能监控 -->
        <threat-deal-block title="系统状态" :flexContent="true" style="grid-area: chart2; height: 350px; padding-top: 16px">
          <div class="cmdWrapper">
            <progress-chart :width="'70%'" :height="'70%'" :series-data="progressData11"></progress-chart>
          </div>
          <div class="cmdWrapper">
            <progress-chart :width="'70%'" :height="'70%'" :series-data="progressData12"></progress-chart>
          </div>
          <div class="cmdWrapper">
            <progress-chart :width="'70%'" :height="'70%'" :series-data="progressData13"></progress-chart>
          </div>
        </threat-deal-block>
        <threat-deal-block title="日志源存储时长TOP10" style="grid-area: chart3; height: 720px; padding-top: 16px">
          <mantance-bar-chart :width="'100%'" :height="'100%'" :series-data="staticsData"></mantance-bar-chart>
        </threat-deal-block>
        <threat-deal-block title="采集趋势" style="grid-area: chart4; height: 360px;">
          <mantance-line-chart :width="'100%'" :height="'100%'" :series-data="gatherTrendData"></mantance-line-chart>
        </threat-deal-block>
        <threat-deal-block title="发生源类型TOP5" style="grid-area: chart5; height: 230px;">
          <div style="height: 100%; display: flex">
            <div style="flex-basis: 20%">
              <mantance-pie-chart :width="'100%'" height="100%" :series-data="progressData21"></mantance-pie-chart>
            </div>
            <div style="flex-basis: 20%">
              <mantance-pie-chart :width="'100%'" height="100%" :series-data="progressData22"></mantance-pie-chart>
            </div>
            <div style="flex-basis: 20%">
              <mantance-pie-chart :width="'100%'" height="100%" :series-data="progressData23"></mantance-pie-chart>
            </div>
            <div style="flex-basis: 20%;">
              <mantance-pie-chart :width="'100%'" height="100%" :series-data="progressData24"></mantance-pie-chart>
            </div>
            <div style="flex-basis: 20%;">
              <mantance-pie-chart :width="'100%'" height="100%" :series-data="progressData25"></mantance-pie-chart>
            </div>
          </div>
        </threat-deal-block>
      </div>
      <div class="particles-area" id="particles"></div>
    </div>
  </v-scale-screen>
</template>
<script>
import VScaleScreen from 'v-scale-screen'
import ThreatRiskHeader from './threatriskHeader.vue'
import ThreatDealBlock from './threatdealBlock.vue'
import ProgressChart from '@comp/ChartFactory/visualization/ProgressChart'
import MantanceBarChart from '@comp/ChartFactory/visualization/MantanceBarChart'
import MantanceLineChart from '@comp/ChartFactory/visualization/MantanceLineChart'
import MantancePieChart from '@comp/ChartFactory/visualization/MantancePieChart'
import '@util/particles.js'
import particlesConfig from './particlesConfig.json'
import { queryPerformance, queryLogsTop10, queryStorageTop10, querySourceType, queryGatherTrend } from '@api/visualization/mantance-api'
export default {
  name: 'ThreatRisk',
  components: {
    VScaleScreen,
    ThreatRiskHeader,
    ThreatDealBlock,
    ProgressChart,
    MantanceBarChart,
    MantanceLineChart,
    MantancePieChart,
  },
  data() {
    return {
      currentDate: '',
      progressData11: { name: 'CPU', value: 0, color: '#56d64c' },
      progressData12: { name: '磁盘', value: 0, color: '#00c3fd' },
      progressData13: { name: '内存', value: 0, color: '#ff4068' },
      progressData21: { name: '类型1', value: 0, color: '#ff3e66' },
      progressData22: { name: '类型2', value: 0, color: '#7f38ff' },
      progressData23: { name: '类型3', value: 0, color: '#07eaff' },
      progressData24: { name: '类型4', value: 0, color: '#ffb42d' },
      progressData25: { name: '类型5', value: 0, color: '#15c976' },
      staticsData: [],
      logsTop10: [],
      gatherTrendData: [],
      refreshTimer: null,
    }
  },
  computed: {
    queryFilter() {
      return {
        date: this.currentDate,
      }
    },
  },
  watch: {
    currentDate(newVal, oldVal) {
      this.init()
    },
  },
  mounted() {
    // eslint-disable-next-line no-undef
    particlesJS('particles', particlesConfig)
    const today = new Date()
    this.currentDate = today.getFullYear() + '-' + this.fitzero(today.getMonth() + 1) + '-' + this.fitzero(today.getDate())
    // this.init()
  },
  methods: {
    fitzero(num) {
      return num.toString().padStart(2, '0')
    },
    init() {
      this.getPerformance()
      this.getGatherTrend()
      this.getSourceType()
      this.getStorageTop10()
      this.getLogsTop10()
    },
    getPerformance() {
      queryPerformance().then((res) => {
        // 使用toFixed方法将结果保留两位小数，再转换为数值
        this.progressData11.value = parseFloat((res.cpu * 1).toFixed(2))
        this.progressData12.value = parseFloat((res.disk * 1).toFixed(2))
        this.progressData13.value = parseFloat((res.memory * 1).toFixed(2))
      })
    },
    getLogsTop10() {
      queryLogsTop10(this.queryFilter).then((res) => {
        if (res.length > 1) {
          const names = res[0].filter((o) => !!o && o !== ' ')
          const values = res[1].filter((o) => !!o && o !== ' ')
          this.logsTop10 = names
            .map((item, idx) => {
              return { name: item, value: values[idx] }
            })
            .reverse()
        }
      })
    },
    getStorageTop10() {
      queryStorageTop10(this.queryFilter).then((res) => {
        if (res.length > 1) {
          const names = res[0].filter((o) => !!o && o !== ' ')
          const values = res[1].filter((o) => !!o && o !== ' ')
          this.staticsData = names
            .map((item, idx) => {
              return { name: item, value: values[idx] }
            })
            .reverse()
        }
      })
    },
    getSourceType() {
      querySourceType(this.queryFilter).then((res) => {
        res = res.map((o) => {
          return { ...o, name: o.label, value: o.value }
        })
        this.progressData21 = { ...this.progressData21, ...res[0] }
        this.progressData22 = { ...this.progressData22, ...res[1] }
        this.progressData23 = { ...this.progressData23, ...res[2] }
        this.progressData24 = { ...this.progressData24, ...res[3] }
        this.progressData25 = { ...this.progressData25, ...res[4] }
      })
    },
    getGatherTrend() {
      queryGatherTrend(this.queryFilter).then((res) => {
        const times = res[0]
        const logCount = res[1] || []
        const all = logCount.map((item, index) => {
          return { name: times[index], value: item }
        })
        this.gatherTrendData = all.slice(-7)
      })
    },
    changeDate(v) {
      this.currentDate = v
    },
    changeRefreshDura(v) {
      if (v === 0 && this.refreshTimer) {
        clearInterval(this.refreshTimer)
        this.refreshTimer = null
      } else {
        this.refreshTimer = setInterval(() => {
          this.init()
        }, v * 60 * 1000)
      }
    },
  },
}
</script>
<style scoped lang="scss">
.bigScreen {
  background: #000;
  width: 1920px;
  height: 1080px;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  display: grid;
  gap: 24px;
  grid-template-columns: 1fr 2.2fr 1fr;
  grid-template-rows: 64px 1fr;
  grid-template-areas:
    'header header header'
    'content content content';
  align-items: flex-start;
  font-size: 16px;
  .video-wrapper {
    position: absolute;
    width: 1920px;
    overflow: hidden;
    display: grid;
    place-items: center;
    video {
      width: 100%;
      mask-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5));
      position: relative;
      z-index: 2;
    }
  }
  .floor {
    position: absolute;
    z-index: 2;
    bottom: 0px;
  }
}
.screenContent {
  display: grid;
  gap: 12px;
  grid-template-columns: 1fr 3fr 1fr;
  grid-template-rows: 1fr 1fr 1fr;
  grid-template-areas:
    'chart1 chart2 chart3'
    'chart1 chart4 chart3'
    'chart5 chart5 chart5';
  z-index: 3;
  height: 100%;
  .cmdWrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    flex-basis: 33.33%;
  }
}
.particles-area {
  width: 800px;
  height: 700px;
  position: absolute;
  left: 50%;
  margin-left: -400px;
  bottom: 10px;
  z-index: 11;
}
</style>
