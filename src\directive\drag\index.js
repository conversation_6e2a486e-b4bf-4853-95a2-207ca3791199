import Vue from 'vue'

Vue.directive('drag', {
  inserted(el) {
    el.style.cursor = 'move'
    el.style.position = 'absolute'
    el.onmousedown = (e) => {
      const [disX, disY] = [e.pageX - el.offsetLeft, e.pageY - el.offsetTop]
      document.onmousemove = (e) => {
        const [systemHeader, systemMainArea] = [document.querySelector('header'), document.querySelector('.router-container')]
        const [maxX, maxY] = [
          document.body.clientWidth -
            Number.parseInt(el.css('width')) -
            Number.parseInt(systemMainArea.css('margin-left')) -
            Number.parseInt(systemMainArea.css('margin-right')),
          document.body.clientHeight -
            Number.parseInt(el.css('height')) -
            Number.parseInt(systemMainArea.css('margin-top')) -
            Number.parseInt(systemMainArea.css('margin-bottom')) -
            Number.parseInt(systemHeader.css('height')),
        ]
        let [x, y] = [e.pageX - disX, e.pageY - disY]

        if (x < 0) {
          x = 0
        } else if (x > maxX) {
          x = maxX
        }

        if (y < 0) {
          y = 0
        } else if (y > maxY) {
          y = maxY
        }

        el.style.left = `${x}px`
        el.style.top = `${y}px`
      }

      document.onmouseup = (e) => {
        document.onmousemove = document.onmouseup = null
      }
    }
  },
})
