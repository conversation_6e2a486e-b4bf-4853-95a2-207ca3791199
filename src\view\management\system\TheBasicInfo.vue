<!--
 * @Description: 系统管理 - 基本信息
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="tab-context-wrapper">
    <el-row justify="center">
      <el-col :span="4" :offset="6" class="context-center col-border">
        {{ $t('management.system.label.productName') }}
      </el-col>
      <el-col :span="8" class="context-center col-border">
        {{ formData.systemName }}
      </el-col>
      <el-col :span="4" :offset="6" class="context-center col-border">
        {{ $t('management.system.label.productVersion') }}
      </el-col>
      <el-col :span="8" class="context-center col-border">
        {{ formData.systemVersion }}
      </el-col>
      <el-col :span="4" :offset="6" class="context-center col-border">
        {{ $t('management.system.label.buildVersion') }}
      </el-col>
      <el-col :span="8" class="context-center col-border">
        {{ formData.systemBuild }}
      </el-col>
      <el-col :span="4" :offset="6" class="context-center col-border">
        {{ $t('management.system.label.productModel') }}
      </el-col>
      <el-col :span="8" class="context-center col-border">
        {{ formData.systemModel }}
      </el-col>
    </el-row>
    <section class="tab-footer-button">
      <span class="ssh-switch">
        {{ $t('management.system.label.sshService') }}
        <el-switch v-model="formData.sshEnable" active-value="1" inactive-value="0" @change="toggleSSHStatus"></el-switch>
      </span>
      <el-button v-has="'restart'" @click="clickRestartDevice">
        {{ $t('button.restart') }}
      </el-button>
      <el-button v-has="'shutdown'" @click="clickShutdownDevice">
        {{ $t('button.shutdown') }}
      </el-button>
      <el-button v-has="'restoreDevice'" @click="clickRestoreDevice">
        {{ $t('button.restore') }}
      </el-button>
    </section>
  </div>
</template>

<script>
export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  methods: {
    clickRestartDevice() {
      this.$confirm(this.$t('tip.confirm.restart'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-restart')
      })
    },
    clickShutdownDevice() {
      this.$confirm(this.$t('tip.confirm.shutdown'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-shutdown')
      })
    },
    clickRestoreDevice() {
      this.$confirm(this.$t('tip.confirm.restore'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-restore')
      })
    },
    toggleSSHStatus(val) {
      this.formData.sshEnable = val === '1' ? '0' : '1'
      let message = this.$t('tip.confirm.sslStart')
      if (val === '0') {
        message = this.$t('tip.confirm.sslStop')
      }
      this.$confirm(message, this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-toggle-ssh', val)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-row {
  margin-top: 50px;

  .col-border {
    height: 60px;
    line-height: 60px;
    border-left: 1px solid;
    border-top: 1px solid;
    @include theme('border-color', border-color);
  }

  .col-border:nth-child(even) {
    border-right: 1px solid;
    @include theme('border-color', border-color);
  }

  .col-border:last-child,
  .col-border:nth-last-child(2) {
    border-bottom: 1px solid;
    @include theme('border-color', border-color);
  }
}
.ssh-switch {
  margin-right: 12px;
  color: #1873d7;
}
</style>
