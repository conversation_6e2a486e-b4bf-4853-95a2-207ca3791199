import request from '@util/request'

export function queryMonitorBasic(obj) {
  return request({
    url: '/monitormanagement/basic',
    method: 'get',
    params: obj || {},
  })
}

export function queryMonitorCpu(obj) {
  return request({
    url: '/monitormanagement/getCpuStatic',
    method: 'get',
    params: obj || {},
  })
}

export function queryMonitorMemory(obj) {
  return request({
    url: '/monitormanagement/getMemoryStatic',
    method: 'get',
    params: obj || {},
  })
}

export function queryMonitorDisk(obj) {
  return request({
    url: '/monitormanagement/getDiskStatic',
    method: 'get',
    params: obj || {},
  })
}

export function queryCurrentCpu(obj) {
  return request({
    url: '/monitormanagement/perf/currentCpu',
    method: 'get',
    params: obj || {},
  })
}

export function queryCurrentMemory(obj) {
  return request({
    url: '/monitormanagement/perf/currentMemory',
    method: 'get',
    params: obj || {},
  })
}

export function queryCurrentDisk(obj) {
  return request({
    url: '/monitormanagement/perf/currentDisk',
    method: 'get',
    params: obj || {},
  })
}

export function queryMonitorFaultTable(obj) {
  return request({
    url: '/monitormanagement/queryFaultEvents',
    method: 'get',
    params: obj || {},
  })
}

export function queryFaultDetail(obj) {
  return request({
    url: '/monitormanagement/queryFaultEventDetails',
    method: 'get',
    params: obj || {},
  })
}

export function queryMonitorPerfTable(obj) {
  return request({
    url: '/monitormanagement/queryPerformanceEvents',
    method: 'get',
    params: obj || {},
  })
}

export function queryPerfDetail(obj) {
  return request({
    url: '/monitormanagement/queryPerformanceEventDetails',
    method: 'get',
    params: obj || {},
  })
}
