export default {
  management: {
    header: '采集器管理',
    table: {
      collectorName: '采集器名称',
      ip: '采集地址',
      protName: '接入方式',
      agentIp: '代理服务器',
      agentStatus: '代理状态',
      runState: '使用状态',
      run: '启用停用',
      handle: '操作',
      typeName: '日志源设备',
      fuzzyField: '采集器名称/日志源设备',
    }, // 采集器列表语言国际化
    dialog: {
      title: {
        add: '采集器管理添加',
        update: '采集器管理修改',
        logSourceAdd: '日志源类型添加',
      },
      columns: {
        collectorName: '采集器名称',
        innerType: '接入方式',
        agentIp: '代理服务器',
        logType: '日志类型',
        ip: '采集地址',
        kafkaAddress: 'Kafka地址',
        propertyKind: '日志源设备',
        describe: '描述',
        dbType: '数据库类型',
        dbInst: '数据库实例',
        port: '端口',
        userName: '用户名',
        password: '密码',
        domain: '域名',
        strategy: '过滤策略',
        codeWay: '编码格式',
        logFile: '日志文件',
        importConfig: '导入配置',
        historyImport: '历史导入',
        ipAddress: '采集地址',
        fileName: '文件名称',
        createTime: '导入时间',
        directory: '目录',
        topic: '主题',
        isAsset: '生成资产',
      },
      exceed: '当前限制选择 1 个文件，请删除后再上传',
    }, // dialog相关信息语言国际化
    placeholder: {
      input: '采集器名称/采集地址/接入方式',
      innerType: '接入方式',
      agentIp: '代理服务器',
      logType: '日志类型',
      runState: '运行状态',
      run: '启用',
      propertyKind: '日志源设备',
      strategy: '过滤策略',
      dbType: '数据库类型',
      grabCycle: '抓取时间',
      codeWay: '编码格式',
      logFile: '日志文件',
    }, // 占位文字语言国际化
    label: {
      runSuccess: '请选择批量开启内容',
      runError: '请选择批量停止内容',
      runState: {
        on: '正在运行',
        off: '暂停',
      },
      agentStatus: {
        on: '在线',
        off: '下线',
      },
      useState: {
        on: '启用',
        off: '停用',
      },
    },
    error: {
      collectorName: '采集器名称不能为IP格式',
    },
  },
}
