<!--
 * @Description: 日志数量趋势图表
 * @Version: 3.5
 * @Author:
 * @Date: 2023/03/05
 * @Editor:
 * @EditDate: 2023/11/13
-->
<template>
  <el-container class="widget" :style="{ height: height + 'px' }">
    <el-header class="widget-header" height="30px">
      <el-col :span="20" class="widget-header-title">
        {{ $t('visualization.compliance.title.logNumberTrend') }}
      </el-col>
      <el-col :span="4" align="right">
        <el-select v-model="deviceValue" size="mini" @change="changeDevice">
          <el-option v-for="item in options.device" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-col>
      <el-col :span="4" align="right">
        <el-select v-model="cycleValue" size="mini" @change="changeCycle">
          <el-option v-for="item in options.cycle" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-col>
    </el-header>
    <el-main class="widget-main">
      <line-chart ref="" :option="option"></line-chart>
    </el-main>
  </el-container>
</template>

<script>
import LineChart from '@comp/ChartFactory/forecast/LineChart'
import { queryLogNumberTrendChart, queryDeviceCombo } from '@api/visualization/compliance-api'

export default {
  components: {
    LineChart,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      chartData: [],
      cycleValue: 3,
      deviceValue: '',
      options: {
        cycle: [
          { value: 1, label: this.$t('visualization.compliance.cycle.lastDay') },
          { value: 2, label: this.$t('visualization.compliance.cycle.lastWeek') },
          { value: 3, label: this.$t('visualization.compliance.cycle.lastMonth') },
          { value: 4, label: this.$t('visualization.compliance.cycle.lastHalfYear') },
          { value: 5, label: this.$t('visualization.compliance.cycle.lastYear') },
        ],
        device: [],
      },
    }
  },
  computed: {
    option() {
      return {
        type: 'line-stack',
        axis: 'x',
        data: this.chartData,
      }
    },
  },
  mounted() {
    this.loadData()
    this.initOptions()
  },
  methods: {
    loadData() {
      this.getLogNumberTrendChart()
    },
    changeCycle(value) {
      this.cycleValue = value
      this.getLogNumberTrendChart()
    },
    changeDevice(value) {
      this.deviceValue = value
      this.getLogNumberTrendChart()
    },
    getLogNumberTrendChart(
      params = {
        type: this.cycleValue,
        typeId: this.deviceValue,
      }
    ) {
      queryLogNumberTrendChart(params).then((res) => {
        this.chartData = res
      })
    },
    initOptions() {
      this.getDeviceCombo()
    },
    getDeviceCombo() {
      queryDeviceCombo().then((res) => {
        if (Array.isArray(res) && res.length > 0) {
          this.deviceValue = res[0].value
        }
        this.options.device = res
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.widget {
  &-header {
    &-title {
      padding-left: 0 !important;
    }
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10px;
    ::v-deep .el-input--small .el-input__inner {
      height: 26px;
      line-height: 26px;
      .el-input__prefix,
      ::v-deep .el-input__suffix {
        top: 2px;
      }
    }
  }
}
</style>
