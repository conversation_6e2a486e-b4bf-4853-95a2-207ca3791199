import request from '@util/request'

export function addUserData(obj) {
  return request({
    url: '/usermanagement/user',
    method: 'post',
    data: obj || {},
  })
}

export function deleteUserData(ids) {
  return request({
    url: `/usermanagement/userDelete/${ids}`,
    method: 'post',
  })
}

export function updateUserData(obj) {
  return request({
    url: '/usermanagement/userChange',
    method: 'post',
    data: obj || {},
  })
}

export function queryUserTableData(obj) {
  return request({
    url: '/usermanagement/users',
    method: 'get',
    params: obj || {},
  })
}

export function queryUserDetailData(userId) {
  return request({
    url: `/usermanagement/user/${userId}`,
    method: 'get',
  })
}

export function updateUserLockStateData(obj) {
  return request({
    url: '/usermanagement/user/status',
    method: 'post',
    data: obj || {},
  })
}

export function resetPasswordData(id) {
  return request({
    url: '/usermanagement/password',
    method: 'post',
    data: id,
  })
}

export function queryRoleData() {
  return request({
    url: '/rolemanagement/roles',
    method: 'get',
  })
}

export function updateUserGrantRoleData(obj) {
  return request({
    url: '/usermanagement/user/grant',
    method: 'post',
    data: obj || [],
  })
}

export function queryUserGrantRoleData(userId) {
  return request({
    url: `/usermanagement/user/roles/${userId}`,
    method: 'get',
  })
}
