const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    id: '@ID',
    'legalIp|1-10': ['@IP'],
    systemName: '@NAME',
    systemIp: '@IP',
    abnormalAction: '[["10","10001"]]',
    startTime: '@TIME',
    endTime: '@TIME',
  },
})

// 事件类型
const eventTypeList = [
  {
    value: '10',
    label: '拒绝服务',
    children: [
      { value: '10001', label: '未定义拒绝服务事件', type: '10', children: null },
      { value: '10002', label: '畸形Dos数据包', type: '10', children: null },
    ],
  },
  {
    value: '11',
    label: '扫描探测',
    children: [
      { value: '11002', label: '应用探测', type: '11', children: null },
      { value: '11003', label: '主机探测', type: '11', children: null },
    ],
  },
]

// 转发外系统
const externalSystem = [
  { value: '1', label: '转发系统一' },
  { value: '2', label: '转发系统二' },
]

module.exports = [
  {
    url: '/infosystemanomaly/queryConfigs',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/infosystemanomaly/combo/event-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: eventTypeList,
      }
    },
  },
  {
    url: '/infosystemanomaly/addConfig',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/infosystemanomaly/updateConfig',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/infosystemanomaly/deleteConfigs/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/infosystemanomaly/combo/forward-relay-way',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: externalSystem,
      }
    },
  },
]
