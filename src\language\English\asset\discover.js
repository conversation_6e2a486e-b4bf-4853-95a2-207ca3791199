export default {
  discover: {
    asset: 'Asset Discovery',
    findTask: 'Discovery Task',
    taskLog: 'Task Log',
    th: 'Custom Column',
    errorConnect: 'Connection interrupted, progress paused',
    assetCode: 'Asset Code',
    domaName: 'Region',
    assetName: 'Asset Name',
    assetType: 'Asset Type',
    responsiblePerson: 'Person in Charge',
    netWorkId: 'Network Segment ID',
    netWorkName: 'Network Segment',
    assetModel: 'Asset Model',
    manufactor: 'Manufacturer',
    osType: 'Operating System',
    ipvAddress: 'Asset IP',
    memoryInfo: 'Memory Capacity',
    contactPhone: 'Contact Phone',
    email: 'Email Address',
    makerContactPhone: 'Manufacturer Phone',
    assetDesc: 'Remarks',
    securityComponent: 'Security Component Type',
    columns: {
      assetName: 'Asset Name',
      assetType: 'Asset Type',
      netWorkId: 'Network Segment',
      assetModel: 'Asset Model',
      manufactor: 'Manufacturer',
      osType: 'Operating System',
      memoryInfo: 'Memory Capacity',
      responsiblePerson: 'Person in Charge',
      contactPhone: 'Contact Phone',
      email: 'Email Address',
      makerContactPhone: 'Manufacturer Phone',
      assetCode: 'Asset Code',
      assetDesc: 'Remarks',
      ipvAddress: 'Asset IP',
    },
    taskName: 'Task Name',
    progressPercent: 'Status',
    placeholder: {
      startIpv: 'Start Asset IP',
      endIpv: 'End Asset IP',
      keyword: 'Please enter keyword to search',
      taskName: 'Task Name',
      netWorkId: 'Network Segment',
      assetName: 'Asset Name',
      assetType: 'Asset Category',
      assetModel: 'Asset Model',
      manufactor: 'Manufacturer',
      osType: 'Operating System',
      ipvAddress: 'Asset IP',
      memoryInfo: 'Memory Capacity',
      responsiblePerson: 'Person in Charge',
      contactPhone: 'Contact Phone',
      email: 'Email Address',
      makerContactPhone: 'Manufacturer Phone',
      assetCode: 'Asset Code',
      domaName: 'Region',
      assetDesc: 'Remarks',
    },
  },
}
