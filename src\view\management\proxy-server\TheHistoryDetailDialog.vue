<!--
 * @Description: 代理服务器 - 历史详细弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-16
 * @Editor:
 * @EditDate: 2021-07-16
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-form :model="detailModel" label-width="120px">
        <el-row>
          <el-divider content-position="left">{{ $t('management.proxy.detail.basic.title') }}</el-divider>
          <el-col v-for="(item, index) in columnOption.basic" :key="index" :span="8">
            <el-form-item :prop="item" :label="$t(`management.proxy.label.${item}`)">
              <span v-if="item === 'status'">
                {{ detailModel[item] === 1 ? $t('code.status.on') : $t('code.status.off') }}
              </span>
              <span v-else>
                {{ detailModel[item] }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="cpuModel" label-width="120px">
        <el-row>
          <el-divider content-position="left">{{ $t('management.proxy.detail.cpu.title') }}</el-divider>
          <el-col v-for="(item, index) in columnOption.cpu" :key="index" :span="8">
            <el-form-item :prop="item" :label="$t(`management.proxy.detail.cpu.${item}`)">
              {{ cpuModel[item] }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <el-form :model="memModel" label-width="120px">
        <el-row>
          <el-divider content-position="left">{{ $t('management.proxy.detail.mem.title') }}</el-divider>
          <el-col v-for="(item, index) in columnOption.mem" :key="index" :span="8">
            <el-form-item :prop="item" :label="$t(`management.proxy.detail.mem.${item}`)">
              {{ memModel[item] }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form :model="sysModel" label-width="120px">
        <el-row>
          <el-divider content-position="left">{{ $t('management.proxy.detail.sys.title') }}</el-divider>
          <el-col v-for="(item, index) in columnOption.sys" :key="index" :span="8">
            <el-form-item :prop="item" :label="$t(`management.proxy.detail.sys.${item}`)">
              {{ sysModel[item] }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <el-form label-width="120px">
        <el-row>
          <el-divider content-position="left">{{ $t('management.proxy.detail.sysFiles.title') }}</el-divider>
          <el-col :span="24">
            <el-table
              :data="filesModel"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              style="width: 100%"
            >
              <el-table-column
                v-for="(item, key) in columnOption.sysFiles"
                :key="key"
                :prop="item"
                :label="$t(`management.proxy.detail.sysFiles.${item}`)"
                show-overflow-tooltip
              ></el-table-column>
            </el-table>
          </el-col>
        </el-row>
      </el-form>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    detailModel: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      cpuModel: {},
      memModel: {},
      sysModel: {},
      filesModel: [],
      columnOption: {
        basic: ['ip', 'status', 'updateTime'],
        cpu: ['cpuNum', 'sys', 'used', 'wait', 'free'],
        mem: ['total', 'used', 'free', 'usage'],
        sys: ['computerName', 'osName', 'osArch'],
        sysFiles: ['dirName', 'sysTypeName', 'typeName', 'total', 'free', 'used', 'usage'],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.bindingDetail()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    bindingDetail() {
      const metrics = JSON.parse(this.detailModel.metrics)
      this.cpuModel = metrics.cpu
      this.memModel = metrics.mem
      this.sysModel = metrics.sys
      this.filesModel = metrics.sysFiles
    },
  },
}
</script>

<style lang="scss" scoped>
.el-divider__text {
  padding: 0 20px 0 0;
}
.el-divider__text.is-left {
  left: 0px !important;
}
</style>
