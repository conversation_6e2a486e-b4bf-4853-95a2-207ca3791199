<!--
 * @Description: 安全事件 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" :loading="loading" @on-close="clickCancelDialog">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane :label="$t('event.security.panel.detail')" name="first">
        <section>
          <el-form :model="form.model" label-width="120px">
            <el-row>
              <el-col v-for="(itemCol, colIndex) in detailColumnsOption" :key="colIndex" :span="itemCol.key === 'alarmDesc' ? 24 : 12">
                <el-form-item :label="itemCol.label">
                  <template>
                    <level-tag v-if="itemCol.key === 'level'" :level="form.model[itemCol.key]"></level-tag>
                    <p v-else>
                      {{ form.model[itemCol.key] }}
                    </p>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </section>
      </el-tab-pane>
      <el-tab-pane :label="$t('event.security.panel.original')" name="second">
        <section class="router-wrap-table">
          <section class="table-body">
            <el-table
              v-loading="data.loading"
              v-el-table-scroll="scrollOriginalTable"
              :data="data.table"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              infinite-scroll-disabled="disableScroll"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="350"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column prop="type2Name" :label="$t('event.security.detailColumns.type2Name')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="eventName" :label="$t('event.security.detailColumns.eventName')"></el-table-column>
              <el-table-column prop="eventCategoryName" :label="$t('event.security.detailColumns.eventCategoryName')"></el-table-column>
              <el-table-column prop="level" :label="$t('event.security.detailColumns.level')">
                <template slot-scope="scope">
                  <level-tag :level="scope.row.level"></level-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sourceIp" :label="$t('event.security.detailColumns.srcIp')"></el-table-column>
              <el-table-column prop="targetIp" :label="$t('event.security.detailColumns.dstIp')"></el-table-column>
              <el-table-column prop="time" width="140" :label="$t('event.security.detailColumns.dateTime')"></el-table-column>
              <el-table-column width="60">
                <template slot-scope="scope">
                  <el-button class="el-button--blue" @click="clickDetailDrawer(scope.row)">
                    {{ $t('button.detail') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </section>
        <footer class="table-footer infinite-scroll">
          <section class="infinite-scroll-nomore">
            <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
            <i v-show="data.totalLoading" class="el-icon-loading"></i>
          </section>
          <section v-show="!data.totalLoading" class="infinite-scroll-total">
            <b>{{ $t('event.original.total') + ':' }}</b>
            <span>{{ data.total }}</span>
          </section>
        </footer>
      </el-tab-pane>
    </el-tabs>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import CustomDialog from '@comp/CustomDialog'
import levelTag from '@comp/LevelTag'
import { queryOriginalLog, queryOriginalTotal } from '@api/event/security-api'
import { debounce } from '@/util/effect'

export default {
  components: {
    CustomDialog,
    levelTag,
  },
  directives: {
    elTableScroll,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    form: {
      required: true,
      type: Object,
    },
    width: {
      type: String,
      default: '1000',
    },
    actions: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    detailColumnsOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'first', // 默认激活第一个tab栏
      data: {
        loading: false,
        scroll: true,
        table: [],
        nomore: false,
        totalLoading: false,
        total: 0,
      }, // 列表信息
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      }, // 分页信息
      debounce: {
        click: null,
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
    disableScroll() {
      return this.data.scroll
    },
  },
  watch: {
    visible(nVal) {
      if (nVal) this.initDebounce()
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initDebounce() {
      this.debounce.click = debounce(() => {
        this.queryOriginalLogData(this.handleParams())
        this.queryOriginalLogTotal(this.handleParams())
      }, 200)
    },
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
      this.activeName = 'first'
      this.data = {
        loading: false,
        scroll: true,
        table: [],
        nomore: false,
        totalLoading: false,
        total: 0,
      }
    },
    clickDetailDrawer(row) {
      this.$emit('drawerShow', row)
    },
    handleClick({ name }) {
      if (name === 'second') {
        if (this.data.table.length > 0) return
        this.data.table = []
        this.debounce.click()
      } else {
        this.data = {
          loading: false,
          scroll: true,
          table: [],
          nomore: false,
          totalLoading: false,
          total: 0,
        }
      }
    },
    // 滚动加载
    scrollOriginalTable() {
      const lastRow = this.data.table[this.data.table.length - 1]
      let params = {}
      if (lastRow) {
        params = {
          eventId: this.form.model.eventId,
          aggrStartDate: this.form.model.aggrStartDate,
          aggrEndDate: this.form.model.aggrEndDate,
          pageSize: this.pagination.pageSize,
          originalId: lastRow.id,
          timestamp: lastRow.timestamp,
        }
      }
      this.queryOriginalLogData(params)
    },
    // 点击选项卡查询原始日志
    queryOriginalLogData(params = {}) {
      this.data.scroll = true
      this.data.loading = true
      queryOriginalLog(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.push(...res)
          this.data.scroll = true
          if (this.data.table.length > this.pagination.pageSize) {
            this.data.nomore = true
          }
        } else {
          this.data.table.push(...res)
          this.data.scroll = false
        }
        this.data.loading = false
      })
    },
    // 点击选项卡查询总数
    queryOriginalLogTotal(params = {}) {
      this.data.totalLoading = true
      queryOriginalTotal(params).then((res) => {
        this.data.totalLoading = false
        this.data.total = res
      })
    },
    // 处理数据
    handleParams() {
      const { eventId, aggrStartDate, aggrEndDate } = this.form.model
      return Object.assign(
        {},
        {
          eventId,
          aggrStartDate,
          aggrEndDate,
          pageSize: this.pagination.pageSize,
        }
      )
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
::v-deep .el-popover,
.el-popper {
  > p {
    overflow: scroll;
  }
}

::v-deep .el-dialog__body {
  min-height: 500px;
}

::v-deep .table-body {
  min-height: 350px;
  max-height: 350px;
}

::v-deep .router-wrap-table {
  position: relative;

  ::v-deep .table-footer {
    position: absolute;
    right: 10px;
    bottom: 0;
  }
}
</style>
