import request from '@util/request'

// 查询事件特征值列表
export function queryCustomCode(obj) {
  return request({
    url: '/customPattern/code/code-alarm',
    method: 'get',
    params: obj || {},
  })
}

// 添加事件特征值
export function addCustomCode(obj) {
  return request({
    url: '/customPattern/code/add',
    method: 'post',
    data: obj || {},
  })
}

// 修改事件特征值
export function updateCustomCode(obj) {
  return request({
    url: '/customPattern/code/update',
    method: 'put',
    data: obj || {},
  })
}

// 删除事件特征值
export function deleteCustomCode(ids) {
  return request({
    url: `/customPattern/code/del/${ids}`,
    method: 'delete',
  })
}

// 查询设备类型下拉
export function queryDevTypeCombo(obj) {
  return request({
    url: '/customPattern/code/combo/devTypes',
    method: 'get',
    params: obj || {},
  })
}

// 查询事件类型下拉
export function queryEventTypeCombo(obj) {
  return request({
    url: '/customPattern/code/code-alarm/alarm-types',
    method: 'get',
    params: obj || {},
  })
}
