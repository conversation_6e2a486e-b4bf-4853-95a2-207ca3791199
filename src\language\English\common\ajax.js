export default {
  ajax: {
    success: 'Access successful',
    exception: {
      system: 'System exception',
      server: 'Service failure exception',
      session: 'Session exception',
      access: 'Access token exception',
      certification: 'Authentication exception',
      auth: 'Resource access permission exception',
      token: 'Resource function permission exception',
      param: 'The content you entered is incorrect, please check and re-enter',
      idempotency: 'Idempotency exception',
      ip: 'IP access forbidden',
      upload: 'Upload attachment format validation failed',
      code: 'No valid system return code detected',
      mock: 'Missing mock data for corresponding API, please complete',
    },
    attack: {
      xss: 'XSS script attack',
    },
    interaction: {
      error: 'API exception, please check if the frontend and backend APIs are correct',
    },
    interceptors: {
      error: 'Request interceptor interception failed',
    },
    service: {
      upload: 'Failed to upload file, possible reasons: file has been modified or server connection failed',
      timeout: 'Detected that you are not connected to a valid API or the server connection timed out',
    },
  },
}
