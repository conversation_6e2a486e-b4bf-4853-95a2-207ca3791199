<!--以下所有注释不用写 方法名参考以下将demo换做自己业务的单词-->
<template>
  <!--列表页面整体加class为"router-wrap-table" 为列表页面布局-->
  <div class="router-wrap-table">
    <!--列表页面头部加class为"table-header" 为列表头部-->
    <section class="table-header">
      <!--列表页面头部搜索和按钮层加class为"table-header-main"-->
      <section class="table-header-main">
        <!--列表页面头部搜索加class为"table-header-search"-->
        <section class="table-header-search">
          <section v-show="!search.high" v-has="'query'" class="table-header-search-input">
            <el-input
              v-model="search.fuzzyField"
              prefix-icon="soc-icon-search"
              clearable
              :placeholder="$t('tip.placeholder.query')"
              @change="changeQueryDemoTable"
            ></el-input>
          </section>
          <!--高级查询按钮class为"table-header-search-button"-->
          <section class="table-header-search-button">
            <!--查询按钮-->
            <el-button v-if="!search.high" v-has="'query'" @click="changeQueryDemoTable">
              {{ $t('button.query') }}
            </el-button>
            <!--精准查询按钮附加上下箭头-->
            <el-button v-has="'query'" @click="clickHighQueryDemo">
              {{ $t('button.search.exact') }}
              <i :class="search.high ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--列表页面头部加class为"table-header-button" 为列表头部右侧展示的按钮操作-->
        <section class="table-header-button">
          <!--列表页面头部上传按钮加class为"header-button-upload" 为行内块展示按钮-->
          <!--按钮级别的有权限校验 加v-has 值为菜单授权时选择的单词，注意里面单词为字符串-->
          <el-upload
            ref="uploadDemo"
            v-has="'upload'"
            class="header-button-upload"
            action="#"
            :headers="upload.header"
            auto-upload
            :show-file-list="false"
            accept=".xls, .xlsx"
            :file-list="upload.files"
            :on-change="onUploadFileChange"
            :http-request="submitUploadFile"
            :before-upload="beforeUploadValidate"
          >
            <el-button @click="clickUploadDemoTable">
              {{ $t('button.upload') }}
            </el-button>
          </el-upload>
          <el-button v-has="'download'" v-debounce="clickDownloadDemoTable">
            {{ $t('button.download') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAddDemo">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteDemo">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <!--列表页面头部高级搜索层加class为"table-header-extend"-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <section v-show="search.high" class="table-header-query">
            <!--每一行加gutter20像素 每行最多4个组件-->
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input v-model="search.query.form.account" :placeholder="$t('demo.table.eName')" @change="changeQueryDemoTable"></el-input>
              </el-col>
              <el-col :span="5">
                <el-input v-model="search.query.form.name" :placeholder="$t('demo.table.cName')" @change="changeQueryDemoTable"></el-input>
              </el-col>
              <el-col :span="5">
                <el-input v-model="search.query.form.tel" :placeholder="$t('demo.table.telephone')" @change="changeQueryDemoTable"></el-input>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input v-model="search.query.form.phone" :placeholder="$t('demo.table.cellphone')" @change="changeQueryDemoTable"></el-input>
              </el-col>
              <el-col :span="5">
                <el-input v-model="search.query.form.email" :placeholder="$t('demo.table.email')" @change="changeQueryDemoTable"></el-input>
              </el-col>
              <el-col align="right" :span="4" :offset="10">
                <el-button v-has="'query'" @click="changeQueryDemoTable">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="clickResetQueryDemoForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button @click="clickShrinkHighQuery">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </section>
        </el-collapse-transition>
      </section>
    </section>
    <!--列表页面头部加class为"table-body" 为列表主体-->
    <section class="table-body">
      <el-table
        ref="demoTable"
        v-loading="data.loading"
        :data="data.table"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @row-dblclick="dblclickDemoDisplayDetail"
        @current-change="demoTableRowChange"
        @selection-change="demoTableSelectsChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column type="index" :label="$t('demo.table.index')"></el-table-column>
        <el-table-column prop="id" :label="$t('demo.table.userID')" sortable show-overflow-tooltip></el-table-column>
        <el-table-column prop="account" :label="$t('demo.table.eName')" sortable show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('demo.table.cName')">
          <template slot-scope="scope">
            <el-popover trigger="click" placement="right">
              <p>{{ $t('demo.table.cName') }}: {{ scope.row.name }}</p>
              <p>{{ $t('demo.table.address') }}: {{ scope.row.address }}</p>
              <p>{{ $t('demo.table.postcode') }}: {{ scope.row.postcode }}</p>
              <div slot="reference" class="col-inline-block">
                <el-tag>{{ scope.row.name }}</el-tag>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column :label="$t('demo.table.registryTime')" show-overflow-tooltip>
          <template slot-scope="scope">
            <i class="el-icon-time"></i>
            <span>{{ scope.row.time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tel" sortable :label="$t('demo.table.telephone')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="phone" sortable :label="$t('demo.table.cellphone')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="email" sortable :label="$t('demo.table.email')" show-overflow-tooltip></el-table-column>
        <!--添加`fixed="right"`将操作固定在右侧-->
        <el-table-column fixed="right" width="140">
          <!--按钮级别的有权限校验 加v-has 值为菜单授权时选择的单词，注意里面单词为字符串-->
          <template slot-scope="scope">
            <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateDemo(scope.row)">
              {{ $t('button.update') }}
            </el-button>
            <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteDemo(scope.row)">
              {{ $t('button.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </section>
    <!--列表页面头部加class为"table-footer" 为列表翻页-->
    <section class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="demoTableSizeChange"
        @current-change="demoTableCurrentChange"
      ></el-pagination>
    </section>
    <!--添加弹窗-->
    <table-dialog :visible.sync="dialog.visible.add" :title="dialog.title.add" :form="dialog.form" @on-submit="clickSubmitAddDemo"></table-dialog>
    <!--修改弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form="dialog.form"
      @on-submit="clickSubmitUpdateDemo"
    ></table-dialog>
    <!--详情弹窗-->
    <table-dialog :visible.sync="dialog.visible.detail" :title="dialog.title.detail" :action="false" readonly :form="dialog.form"></table-dialog>
  </div>
</template>

<script>
import TableDialog from './TheTableDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import { validateEmail, validateCellphone, validateTelephone } from '@util/validate'
import {
  uploadDemoTableData,
  downloadDemoTableData,
  addDemoData,
  deleteDemoData,
  updateDemoData,
  queryDemoTableData,
} from '@api/demo/table-normal-api'

export default {
  name: 'TableNormal',
  components: {
    TableDialog,
  },
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value === '') {
        // 这个分支代表为空可以过，但是不为空就要校验了
        callback()
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    const validatorCellphone = (rule, value, callback) => {
      if (value === '') {
        callback()
      } else if (!validateCellphone(value)) {
        callback(new Error(this.$t('validate.comm.cellphone')))
      } else {
        callback()
      }
    }
    const validatorTelephone = (rule, value, callback) => {
      if (value === '') {
        callback()
      } else if (!validateTelephone(value)) {
        callback(new Error(this.$t('validate.comm.telephone')))
      } else {
        callback()
      }
    }
    return {
      search: {
        high: false,
        fuzzyField: '',
        query: {
          form: {
            account: '',
            name: '',
            tel: '',
            phone: '',
            email: '',
          },
        },
      },
      data: {
        debounce: null, // 数据列表防抖函数
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
      },
      upload: {
        header: {
          // 上传头部信息
          'Content-Type': 'multipart/form-data',
        },
        files: [], // 上传文件个数
      },
      pagination: {
        visible: true, // 防止页码错误
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.add', [this.$t('demo.table.demo')]),
          update: this.$t('dialog.title.update', [this.$t('demo.table.demo')]),
          detail: this.$t('dialog.title.detail', [this.$t('demo.table.demo')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false,
          update: false,
          detail: false,
        },
        form: {
          model: {
            // 弹出表单绑定值
            account: '',
            name: '',
            tel: '',
            phone: '',
            email: '',
          },
          info: {
            // 弹出表单信息
            account: {
              key: 'account',
              label: this.$t('demo.table.eName'),
              value: '',
            },
            name: {
              key: 'name',
              label: this.$t('demo.table.cName'),
              value: '',
            },
            tel: {
              key: 'tel',
              label: this.$t('demo.table.telephone'),
              value: '',
            },
            phone: {
              key: 'phone',
              label: this.$t('demo.table.cellphone'),
              value: '',
            },
            email: {
              key: 'email',
              label: this.$t('demo.table.email'),
              value: '',
            },
          },
          rules: {
            // 弹出表单的校验规则
            account: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            name: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            tel: [
              {
                validator: validatorTelephone,
                trigger: 'blur',
              },
            ],
            phone: [
              {
                validator: validatorCellphone,
                trigger: 'blur',
              },
            ],
            email: [
              {
                validator: validatorEmail,
                trigger: 'blur',
              },
            ],
          },
        },
      },
    }
  },
  mounted() {
    this.initDebounceQuery()
    this.getDemoTableData()
  },
  methods: {
    // 方法顺序由上到下贯穿
    // 点击上传文件
    clickUploadDemoTable() {
      this.upload.files = []
      this.$refs.uploadDemo.submit()
    },
    // 点击下载Demo列表
    clickDownloadDemoTable() {
      this.downloadDemoTable()
    },
    // 点击添加操作并弹出对话框操作
    clickAddDemo() {
      this.clearDialogFormModel()
      this.dialog.visible.add = true
    },
    // 点击批量删除操作
    clickBatchDeleteDemo() {
      if (this.data.selected.length > 0) {
        // 选中大于等于1时，将每行的id组装成数组并将这个数组转换成字符串进行删除传递
        const ids = this.data.selected.map((item) => item.id).toString()
        this.deleteDemo(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击删除操作
    clickDeleteDemo(row) {
      this.deleteDemo(row.id)
    },
    // 点击修改并弹出对话框操作
    clickUpdateDemo(row) {
      this.demoTableRowChange(row)
      this.clearDialogFormModel()
      this.dialog.form.model = {
        id: row.id,
        account: row.account,
        name: row.name,
        tel: row.tel,
        phone: row.phone,
        email: row.email,
      }
      this.dialog.visible.update = true
    },
    // 点击高级查询
    clickHighQueryDemo() {
      this.search.high = !this.search.high
      this.changeQueryDemoTable()
    },
    // 点击高级查询中的重置
    clickResetQueryDemoForm() {
      this.clearHighQueryForm()
      this.changeQueryDemoTable()
    },
    // 点击高级查询中的收起
    clickShrinkHighQuery() {
      this.clearHighQueryForm()
      this.search.high = false
      this.changeQueryDemoTable()
    },
    // 提交添加表单操作
    clickSubmitAddDemo(formModel) {
      this.addDemo(formModel)
    },
    // 提交修改表单操作
    clickSubmitUpdateDemo(formModel) {
      this.updateDemo(formModel)
    },
    // 查看某一行详情
    dblclickDemoDisplayDetail(row) {
      this.demoTableRowChange(row)
      this.clearDialogFormModel()
      this.dialog.form.model = {
        id: row.id,
        account: row.account,
        name: row.name,
        tel: row.tel,
        phone: row.phone,
        email: row.email,
      }
      this.dialog.visible.detail = true
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    demoTableSizeChange(size) {
      this.pagination.pageSize = size
      this.changeQueryDemoTable()
    },
    // 查看当前页操作 pageNum 改变时会触发
    demoTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryDemoTable()
    },
    // 列表多选改变
    demoTableSelectsChange(select) {
      this.data.selected = select
    },
    // 列表点击之后改变当前行
    demoTableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 清空表单绑定数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        account: '',
        name: '',
        tel: '',
        phone: '',
        email: '',
      }
    },
    // 提交上传
    submitUploadFile(param) {
      if (param.file && this.upload.files.length > 0) {
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.uploadDemoTable(formData)
      }
    },
    // 上传之前的文件校验
    beforeUploadValidate(file) {
      if (this.upload.files.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isXLS = suffix === 'xls'
        const isXLSX = suffix === 'xlsx'
        if (!isXLS && !isXLSX) {
          prompt({
            i18nCode: 'template.upload.excel',
            type: 'warning',
          })
        }
        return isXLS || isXLSX
      }
    },
    // 上传文件改变时调用
    onUploadFileChange(file) {
      this.upload.files.push(file)
    },
    // 清空高级查询中的内容
    clearHighQueryForm() {
      this.search.fuzzyField = ''
      this.search.query.form = {
        account: '',
        name: '',
        tel: '',
        phone: '',
        email: '',
      }
    },
    // 提取查询参数并查询列表
    changeQueryDemoTable() {
      this.data.debounce()
    },
    // 初始化防抖查询，将防抖函数挂载到data属性上
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        if (this.search.high) {
          this.getDemoTableData({
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            account: this.search.query.form.account,
            name: this.search.query.form.name,
            tel: this.search.query.form.tel,
            phone: this.search.query.form.phone,
            email: this.search.query.form.email,
          })
        } else {
          this.getDemoTableData()
        }
      }, 500)
    },
    // 加载所有列表数据
    getDemoTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        fuzzyField: this.search.fuzzyField,
      }
    ) {
      // 隐藏翻页，查出之后在显示，防止翻页组件读取默认pageNum
      this.pagination.visible = false
      // 此处加个列表的loading
      this.data.loading = true
      queryDemoTableData(params).then((res) => {
        // 返回值返填列表数据、列表总量
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.total = res.total
        this.pagination.visible = true
        // 查询结束关闭loading
        this.data.loading = false
      })
    },
    // 上传demo信息
    uploadDemoTable(formData) {
      uploadDemoTableData(formData)
        .then((res) => {
          if (res) {
            this.getDemoTableData()
            prompt({
              i18nCode: 'tip.upload.success',
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.upload.error',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          prompt({
            i18nCode: 'template.upload.empty',
            type: 'error',
          })
          console.error(e)
        })
    },
    // 下载demo信息
    downloadDemoTable(obj) {
      this.data.loading = true
      downloadDemoTableData(obj).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    // demo添加交互操作
    addDemo(obj) {
      addDemoData(obj).then((res) => {
        // 这里根据自己的业务进行操作
        // 例：添加成功返回值为1，重复添加返回值为2 其他失败
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.getDemoTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // demo删除操作
    deleteDemo(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteDemoData(ids).then((res) => {
          // 这里根据自己的业务进行操作
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.getDemoTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // demo更新操作
    updateDemo(obj) {
      updateDemoData(obj).then((res) => {
        // 这里根据自己的业务进行操作
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getDemoTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
