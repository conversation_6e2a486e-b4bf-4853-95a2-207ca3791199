<!--
 * @Description: 事件特征值 - 添加弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-01-06
 * @Editor:
 * @EditDate: 2022-01-06
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.add', [titleName])"
    width="30%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formDom" :model="model" :rules="rules" label-width="110px">
      <el-form-item prop="code" :label="$t('event.customCode.label.code')">
        <el-input v-model.trim="model.code" maxlength="200"></el-input>
      </el-form-item>
      <el-form-item prop="devType" :label="$t('event.customCode.label.devType')">
        <el-select v-model="model.devType" clearable filterable :placeholder="$t('event.customCode.placeholder.devType')">
          <el-option v-for="item in options.devType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="eventType" :label="$t('event.customCode.label.eventType')">
        <el-select v-model="model.eventType" clearable filterable :placeholder="$t('event.customCode.placeholder.eventType')">
          <el-option v-for="item in options.eventType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      rules: {
        code: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        devType: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'change',
          },
        ],
        eventType: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'change',
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formDom.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
  },
}
</script>
