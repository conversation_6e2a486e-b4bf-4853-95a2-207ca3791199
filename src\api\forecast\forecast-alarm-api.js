import request from '@util/request'

// 查询预测告警异常列表
export function queryForecastAbnormalTable(obj) {
  return request({
    url: '/dataAlarm/queryDataAnalysisAnomalyLists',
    method: 'get',
    params: obj || {},
  })
}

// 查询分类信息项
export function queryInfoItemCombo(type) {
  return request({
    url: `/dataAlarm/findLableByType/${type}`,
    method: 'get',
  })
}

// 查询预测分析数据
export function queryAnalysisData(obj) {
  return request({
    url: '/dataAlarm/queryDataAnalysisByTypeAndLable',
    method: 'get',
    params: obj || {},
  })
}

// 查询模型下拉数据
export function queryModelCombo() {
  return request({
    url: '/dataAlarm/queryDataAnalysisAnomalyModel',
    method: 'get',
  })
}
