const tableData = {
  edition: '1.1.1',
  rows: [
    {
      id: '1',
      upContent: '升级',
      upOldEdition: '1.0.1',
      edition: '1.1.1',
      upNewEdition: '1.2.1',
      upTime: '2020/9/16',
      upResult: '成功',
    },
    {
      id: '2',
      upContent: '又升级',
      upOldEdition: '1.1.1',
      edition: '1.2.1',
      upNewEdition: '1.3.1',
      upTime: '2020/9/16',
      upResult: '成功',
    },
  ],
}
module.exports = [
  {
    url: '/systemup/upgrade/file',
    type: 'post',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/systemup/upgrades',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: tableData,
      }
    },
  },
  {
    url: '/systemup/upgrade/file',
    type: 'post',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/systemup/rollback',
    type: 'post',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
]
