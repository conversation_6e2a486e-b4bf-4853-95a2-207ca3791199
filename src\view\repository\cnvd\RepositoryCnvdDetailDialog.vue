<!--
 * @Description: CNVD漏洞库 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog">
    <section>
      <el-form ref="formTemplate" :model="form.model" label-width="120px">
        <el-form ref="formTemplate" :model="form.model" label-width="120px">
          <el-row>
            <el-col v-for="(item, index) in columnTop" :key="index" :span="12">
              <el-form-item :prop="item.key" :label="item.label">
                <template>
                  <level-tag v-if="item.key === 'cnvdLevel'" :level="levelEnum(form.model[item.key])"></level-tag>
                  <p v-else>
                    {{ form.model[item.key] }}
                  </p>
                </template>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-for="(item, index) in columnBottom" :key="index">
            <el-col :span="24">
              <el-form-item :prop="item.key" :label="item.label">
                {{ form.model[item.key] }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-form>
    </section>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import levelTag from '@comp/LevelTag'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
    levelTag,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    form: {
      required: true,
      type: Object,
    },
    width: {
      type: String,
      default: '1000',
    },
    actions: {
      type: Boolean,
      default: false,
    },
    columnTop: {
      type: Array,
    },
    columnBottom: {
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
    levelEnum() {
      return (level) => {
        const levelEnum = {
          严重: '0',
          高: '1',
          中: '2',
          低: '3',
          一般: '4',
        }
        return levelEnum[level]
      }
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
  },
}
</script>
<style></style>
