export default {
  system: {
    title: {
      email: 'Email Service',
      timing: 'Time Synchronization',
      databaseConfig: 'Database Maintenance Configuration',
      databaseTable: 'Database Maintenance List',
      dataBackupTable: 'Data Backup List',
      snapBackupConfig: 'Snapshot Backup Settings',
      snapshotBackupTable: 'Snapshot Backup List',
    },
    tab: {
      system: 'System Configuration',
      basic: 'Basic Information',
      diskSpace: 'Disk Space Settings',
      database: 'Database Maintenance',
      license: 'License Management',
      backup: 'Data Backup',
      snapshot: 'System Snapshot',
      threat: 'Threat Intelligence Settings',
      systemAlarm: 'System Alarm Notification',
      webcert: 'WEB Certificate',
      imageFlow: 'Image Flow Audit Configuration',
      desensitization: 'Original Log Desensitization Configuration',
      flowGather: 'Flow Gather',
    },
    label: {
      localeLanguage: 'System Language',
      productName: 'Product Name',
      productVersion: 'Product Version',
      buildVersion: 'Build Number',
      productModel: 'Product Model',
      systemName: 'System Name',
      sshService: 'SSH Service',
      email: 'Admin Email',
      defaultPassword: 'Default Password',
      captcha: 'Enable Captcha',
      accountLockEnable: 'Account Lock Policy',
      accountLockTime: 'Account Lock Monitoring Time Period',
      accountLockCnt: 'Account Lock Monitoring Count',
      accountAutoUnlockEnable: 'Account Auto Unlock Policy',
      accountAutoUnlockTime: 'Account Auto Unlock Time Period',
      systemTimeout: 'System Timeout',
      accountValidEnable: 'Account Validity Policy',
      passwordOverdueEnable: 'Password Expiry Policy',
      passwordOverdueTime: 'Password Expiry Time',
      sendMailServer: 'Outgoing Mail Server',
      receiveMailServer: 'Incoming Mail Server',
      port: 'Port',
      senderAddress: 'Sender Address',
      serverAuth: 'Server Authentication',
      user: 'Username',
      password: 'Password',
      previousTime: 'Last Sync Time',
      previousMode: 'Last Sync Mode',
      centerSeverTime: 'Central Server Time',
      configCenterSeverTime: 'Set Central Server Time',
      ntpSeverConfig: 'NTP Server Settings',
      autoValidate: 'Auto Validation Function',
      settingCycle: 'Set Cycle',
      settingTime: 'Set Time',
      nextTime: 'Next Sync Time',
      systemCertification: 'System KEY Authentication',
      encryption: 'Encryption Method',
      manualTiming: 'Manual Sync',
      autoTiming: 'Auto Sync',
      cleanHistory: 'Log Disk Space Cleanup History',
      cleanSpace: 'Clean Disk Space',
      spaceSurpass: 'Space Exceeded',
      used: 'Used',
      cleanTime: 'Cleanup Time, Retain',
      warning: 'Warning Issued At',
      monthData: 'Monthly Data',
      safeguard: 'Auto Maintenance At',
      dataRetainTime: 'Data Retention Time',
      time: 'Time',
      description: 'Description',
      result: 'Result',
      license: 'Upload License File',
      machineCode: 'Machine Code',
      backupStrategy: 'Backup Strategy',
      backupMode: 'Backup Mode',
      ftpBackup: 'FTP Backup',
      ip: 'IP Address',
      account: 'Account',
      path: 'Path',
      defaultBackup: 'Default (Daily Backup)',
      customBackup: 'Custom',
      immediateBackup: 'Immediate Backup',
      noBackup: 'No Backup',
      backupWay: 'Backup Method',
      backupCycle: 'Backup Cycle',
      backupTime: 'Backup Time',
      backupRange: 'Backup Range',
      timeRange: 'Time Range',
      all: 'All',
      passwordComplexity: 'Password Complexity',
      upperCase: 'Uppercase Letter',
      lowerCase: 'Lowercase Letter',
      number: 'Number',
      special: 'Special Character',
      minPasswordLength: 'Minimum Password Length',
      chinese: 'Chinese',
      english: 'English',
      systemLogo: 'System Logo',
      syslogPort: 'Syslog Port',
      webPort: 'Web Port',
      flowGather: 'Enable Flow Gather',
      flowGatherNetworkCard: 'Flow Gather Network Card',
      desensitizationSetting: 'Desensitization Setting',
      desensitizeStr: 'Sensitive String',
      desensitizeNewStr: 'Desensitized Result',
      desensitizeStatus: 'Enable Desensitization',
      desensitizeDesc: 'Description',
    },
    tip: {
      localeLanguage: 'Please select system language',
      systemName: 'You can modify the system name',
      email: 'Please enter admin email',
      defaultPassword: 'Please enter default password',
      captcha: 'Enable or disable captcha function',
      test: 'You have not passed the test, are you sure to submit?',
      accountLockEnable: 'Enable or disable account lock function',
      accountLockTime: 'Please enter monitoring time period in seconds',
      accountLockCnt: 'Please enter monitoring count in times',
      accountAutoUnlockEnable: 'Enable or disable account auto unlock function',
      accountAutoUnlockTime: 'Please enter account auto unlock time period in minutes',
      systemTimeoutLockTime: 'Please enter system timeout lock time in minutes',
      accountValidEnable: 'Enable or disable account validity function',
      passwordOverdueEnable: 'Enable or disable password expiry policy',
      passwordOverdueTime: 'Please enter password expiry time in days',
      cleanHistoryMonth: 'Please fill in the number of months of historical log data',
      cleanHistoryDay: 'Please fill in the number of days of historical log data',
      cleanSpace: 'Please fill in the disk usage percentage',
      largeData: '(Not recommended for large data volumes)',
      manualTime: 'Please set the validation time manually',
      autoTime: 'Please select auto sync time',
      passwordComplexity: 'Please set password complexity',
      minPasswordLength: 'Please enter minimum password length',
      flowGather: 'Enable or disable flow gather',
      flowGatherNetworkCard: 'Please select flow gather network card',
      desensitizeStr: 'Please enter sensitive string',
      desensitizeNewStr: 'Please enter desensitized result',
      desensitizeDesc: 'Please enter description',
      webcertP12: 'Only p12 files can be uploaded',
      webcertPassword: 'Please enter certificate password',
    },
    button: {
      testEmail: 'Test Email Service',
      testTiming: 'Test Time Sync',
    },
    threat: {
      emptyError: 'Upload error: this is an empty file',
      structureError: 'Upload error: invalid structure',
      typeError: 'Upload error: invalid type',
      nameError: 'Upload error: invalid name',
      backupCycle: 'Sync Cycle',
      backupTime: 'Sync Time Enabled',
      cuttingTitle: 'Threat Intelligence Upgrade',
      upload: {
        backupTimeText: 'Automatically update at 00:05 every day',
        successUpload: 'Upload successful',
        Submit: 'Submit',
        choice: 'Please select a file to upload',
        title: 'Upload Threat Intelligence',
        titleInput: 'Upload Threat Intelligence File',
        uploadText: 'Uploading, please wait patiently',
      },
    },
    systemAlarm: {
      title: 'Notification Method',
      isMail: 'Email',
      mailTo: 'Email Address',
      snmpForward: 'Notification Server',
      recipientAddress: 'Recipient Address',
      isSound: 'Sound/Light/Electric',
      isSms: 'SMS',
      mobileUrl: 'Gateway Address',
      mobileEcName: 'Enterprise Name',
      mobileApId: 'API Account',
      mobileSecretKey: 'Password',
      mobileMobiles: 'Mobile Number',
      mobileSign: 'Signature Code',
      mobileAddSerial: 'Extension Code',
    },
    upload: {
      title: 'Data Recovery',
      downLoad: 'Download Template',
      chooseFile: 'Please select a file',
      exceed: 'Only 1 file can be selected, please delete before uploading again',
      remindTip: 'When the imported asset name is the same as the system asset name, the imported data shall prevail',
      talkTip: 'When the imported asset name is the same as the system asset name, the system data shall prevail',
      successUpload: 'Recovery operation completed, new records will be added after data recovery!',
    },
  },
}
