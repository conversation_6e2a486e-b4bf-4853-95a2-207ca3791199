import request from '@util/request'

// 查询列表数据
export function queryCnvdTableData(obj) {
  return request({
    url: '/knowledge/vulnerability/cnvd',
    method: 'get',
    params: obj || {},
  })
}

// 查询详情
export function queryCnvdDetail(cnvdId) {
  return request({
    url: `/knowledge/vulnerability/cnvd/${cnvdId}`,
    method: 'get',
  })
}
export function queryTotal(param) {
  return request({
    url: `/knowledge/vulnerability/cnvd/total`,
    method: 'get',
    params: param || {},
  })
}
