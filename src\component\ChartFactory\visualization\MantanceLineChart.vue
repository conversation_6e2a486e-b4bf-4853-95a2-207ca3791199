<template>
  <div :id="id" ref="lineChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    seriesData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    seriesData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart(this.chart)
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.seriesData) {
      const pd = data || this.seriesData
      this.chart = echarts.init(this.$refs.lineChart)
      this.drawChart(pd)
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      const barData = data.map((o) => o.value)
      const barName = data.map((o) => o.name)
      const option = {
        grid: { top: 20, left: 24, right: 64, bottom: 0, containLabel: true },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            lineStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(126,199,255,0)', // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: '#2BB5F6', // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(126,199,255,0)', // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          backgroundColor: '#233653',
          borderColor: '#2BB5F6',
          textStyle: {
            color: '#2BB5F6',
          },
        },
        xAxis: [
          {
            show: true,
            type: 'category',
            boundaryGap: false,
            z: 2,
            scale: true,
            axisTick: { show: false },
            axisLine: { show: true, onZero: false, lineStyle: { color: '#2BB5F6' } },
            axisLabel: {
              color: '#2BB5F6',
              fontSize: 14,
              inside: false,
              rotate: 30, // 添加旋转角度，正值表示逆时针旋转
              margin: 12, // 可选：增加标签与轴线的距离
              formatter: function(value) {
                // 去掉年份和秒数，将 "2025-08-08 05:00:00" 格式化为 "08-08 05:00"
                if (typeof value === 'string') {
                  const match = value.match(/\d{4}-(\d{2}-\d{2} \d{2}:\d{2}):\d{2}/)
                  return match ? match[1] : value
                }
                return value
              },
            },
            data: barName,
          },
        ],
        yAxis: [
          {
            show: false,
            type: 'value',
          },
        ],
        series: [
          {
            name: '数量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 3,
            showSymbol: true,
            lineStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: '#2BB5F6',
                    },
                    {
                      offset: 0.8,
                      color: '#754AC0',
                    },
                  ],
                  false
                ),
                width: 5,
              },
            },
            areaStyle: {
              normal: {
                color: new echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: 'rgba(108,80,243,0.3)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(108,80,243,0)',
                    },
                  ],
                  false
                ),
                shadowColor: 'rgba(108,80,243, 0.9)',
                shadowBlur: 20,
              },
            },
            itemStyle: {
              normal: {
                color: '#fff',
                borderColor: '#fff',
                borderWidth: 6,
              },
            },
            data: barData,
            markPoint: {
              symbolRotate: 180, // 旋转180度，使尖端朝上
              label: {
                show: true,
                color: '#fff',
                offset: [0, 10], // 向上偏移10px
              },
              data: [
                { type: 'max', name: 'Max', itemStyle: { color: '#2BB5F6' } },
                { type: 'min', name: 'Min', itemStyle: { color: 'rgba(108,80,243)' } },
              ],
            },
          },
        ],
      }
      let len = 0
      setInterval(() => {
        if (len === barName.length) {
          len = 0
        }
        this.chart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: len,
        })
        len++
      }, 5000)

      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
