<!--
 * @Description: 资产管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('asset.management.assetName')])"
              prefix-icon="soc-icon-search"
              @change="inputQueryEvent('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="inputQueryEvent('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <span style="display: inline-flex; align-items: center; margin-right: 8px;">
            {{ $t('asset.management.isOnlineCheck') }}：
            <el-switch v-model="aliveCheck" active-color="#13ce66" inactive-color="#dcdfe6" @change="changeAliveCheck"></el-switch>
          </span>

          <el-button v-has="'query'" @click="resetQuery">
            {{ $t('button.reset.default') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAdd">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'upload'" @click="clickUpload">
            {{ $t('button.import') }}
          </el-button>
          <el-button v-has="'download'" v-debounce="clickDownloadTable">
            {{ $t('button.export.default') }}
          </el-button>
          <el-dropdown placement="bottom" trigger="click" @command="handleCommand">
            <el-button v-has="'add'" type="primary">
              {{ $t('button.batchText') }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="add">
                {{ $t('button.batch.add') }}
              </el-dropdown-item>
              <el-dropdown-item command="update">
                {{ $t('button.batch.update') }}
              </el-dropdown-item>
              <el-dropdown-item command="delete" style="color: #e32a0c">
                {{ $t('button.batch.delete') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="10">
                <range-picker
                  v-model="queryInput.ipRange"
                  type="ip"
                  :start-placeholder="$t('asset.management.placeholder.startIP')"
                  :end-placeholder="$t('asset.management.placeholder.endIP')"
                  @change="inputQueryEvent('e')"
                ></range-picker>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.domaId"
                  :placeholder="$t('asset.management.placeholder.domaName')"
                  clearable
                  filterable
                  @change="inputQueryEvent('e')"
                >
                  <el-option v-for="item in dialog.form.domaList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.responsiblePerson"
                  class="width-max"
                  clearable
                  :placeholder="$t('asset.management.placeholder.responsiblePerson')"
                  @change="inputQueryEvent('e')"
                ></el-input>
              </el-col>
              <el-col :span="4" align="right">
                <el-button v-has="'query'" @click="inputQueryEvent('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="seniorQuery"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <section class="tree-box">
        <div>
          <el-tree :data="dialog.form.treeList" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
        </div>
        <div class="table-box">
          <header class="table-body-header">
            <h2 class="table-body-title">
              {{ $t('asset.management.asset') }}
            </h2>
            <el-button v-has="'query'" @click="clickTh">
              {{ $t('button.th') }}
            </el-button>
          </header>
          <main v-loading="data.loading" class="table-body-main">
            <el-table
              ref="assetTable"
              :data="data.table"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              fit
              @current-change="TableRowChange"
              @selection-change="TableSelectsChange"
            >
              <el-table-column type="selection" prop="assetId"></el-table-column>
              <el-table-column
                v-for="(item, key) in dialog.columns.checked"
                :key="key"
                :prop="item"
                :label="$t(`asset.management.${item}`)"
                show-overflow-tooltip
              ></el-table-column>
              <el-table-column align="right" width="320">
                <template slot-scope="scope">
                  <el-button v-if="scope.row.fromCollector === 1" v-has="'query'" class="el-button--blue" @click="clickLogView(scope.row)">
                    {{ $t('button.logView') }}
                  </el-button>
                  <el-button v-has="'query'" class="el-button--blue" @click="dblclickDisplayDetail(scope.row)">
                    {{ $t('button.detail') }}
                  </el-button>
                  <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                    {{ $t('button.update') }}
                  </el-button>
                  <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                    {{ $t('button.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </main>
        </div>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form"
      :width="'70%'"
      @on-submit="clickSubmitAdd"
    ></table-dialog>
    <!--批量添加-->
    <table-dialog
      :visible.sync="dialog.visible.addAll"
      :title="dialog.title.addAll"
      :form="dialog.form"
      :width="'70%'"
      @on-submit="clickSubmitAddAll"
    ></table-dialog>
    <!--批量修改-->
    <table-dialog
      :visible.sync="dialog.visible.updateAll"
      :title="dialog.title.updateAll"
      :form="dialog.form"
      :width="'70%'"
      @on-submit="clickSubmitUpdateAll"
    ></table-dialog>
    <!--修改弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form="dialog.form"
      :width="'70%'"
      @on-submit="clickSubmitUpdate"
    ></table-dialog>
    <!--详情弹窗-->
    <detail-dialog :visible.sync="dialog.visible.detail" :title="dialog.title.detail" :width="'70%'" :form="dialog.form"></detail-dialog>
    <!--自定义列-->
    <ac-dialog
      :visible.sync="dialog.visible.th"
      :title="dialog.title.th"
      :width="'70%'"
      :form="dialog.columns"
      @on-submit="clickSubmitTh"
    ></ac-dialog>
    <!--导入弹窗-->
    <upload-dialog
      :visible.sync="dialog.visible.upload"
      :title="dialog.title.upload"
      :form="dialog.upload"
      :width="'35%'"
      @on-submit="clickSubmitUpload"
    ></upload-dialog>
    <!--查看日志弹框-->
    <log-view-dialog :visible.sync="dialog.view.visible" :model="dialog.view.model" :width="'80%'"></log-view-dialog>
  </div>
</template>
<script>
import TableDialog from './AudDialog'
import DetailDialog from './AssetManagementDetails'
import UploadDialog from './UploadDialog'
import AcDialog from './AssetColumnDialog'
import LogViewDialog from './log-source/TheLogViewDialog'
import RangePicker from '@comp/RangePicker'
import { prompt } from '@util/prompt'
import {
  updateAsset,
  addCustom,
  uploadAsset,
  deleteAssets,
  updateAssets,
  addAsset,
  exportAsset,
  queryCustom,
  queryNet,
  queryType,
  queryTableData,
  queryAssetDomain,
  queryAliveConfig,
  updateAliveConfig,
} from '@api/asset/management-api'
import { debounce } from '@util/effect'
import { isEmpty } from '@util/common'

export default {
  name: 'AssetManagement',
  components: {
    TableDialog,
    AcDialog,
    UploadDialog,
    DetailDialog,
    LogViewDialog,
    RangePicker,
  },
  data() {
    return {
      aliveCheck: false, // 查看资产存活状态配置
      isShow: false,
      defaultProps: {
        // 树形默认值
        children: 'children',
        label: 'label',
      },
      queryInput: {
        ipRange: ['', ''],
        fuzzyField: '',
        domaId: '',
        responsiblePerson: '',
        assetClass: '',
        assetType: '',
        assetId: '',
      }, // 搜索框内容
      queryType: [],
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
        visible: true,
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.add', [this.$t('asset.management.asset')]),
          update: this.$t('dialog.title.update', [this.$t('asset.management.asset')]),
          query: this.$t('dialog.title.query', [this.$t('asset.management.asset')]),
          detail: this.$t('dialog.title.detail', [this.$t('asset.management.asset')]),
          th: this.$t('dialog.title.th', [this.$t('asset.management.asset')]),
          upload: this.$t('dialog.title.upload', [this.$t('asset.management.asset')]),
          addAll: this.$t('dialog.title.addAll', [this.$t('asset.management.asset')]),
          updateAll: this.$t('dialog.title.updateAll', [this.$t('asset.management.asset')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false,
          update: false,
          query: false,
          detail: false,
          th: false,
          upload: false,
          addAll: false,
          updateAll: false,
        },
        form: {
          activeName: 'first',
          add: false,
          addAll: false,
          update: false,
          updateAll: false,
          treeList: [], // 资产类型数据
          netList: [], // 网段数据
          domaList: [],
          customAttr: [], // 自定义属性\
          startIP: {
            label: this.$t('asset.management.startIP'),
            key: 'startIP',
          },
          endIP: {
            label: this.$t('asset.management.endIP'),
            key: 'endIP',
          },
          model: {
            // 弹出表单绑定值
            assetName: '',
            customAttr: [],
            assetType: '',
            values: '',
            netWorkId: '',
            assetModel: '',
            manufactor: '',
            osType: '',
            memoryInfo: '',
            responsiblePerson: '',
            contactPhone: '',
            email: '',
            makerContactPhone: '',
            assetCode: '',
            domaId: '',
            securityComponent: '',
            assetDesc: '',
            ipvAddress: '',
            startIP: '',
            endIP: '',
            assetTypeName: '',
            netWorkName: '',
            assetValue: '0.2',
          },
          info: {
            // 弹出表单信息
            assetName: {
              key: 'assetName',
              label: this.$t('asset.management.assetName'),
              value: '',
            },
            assetTypeName: {
              key: 'assetTypeName',
              label: this.$t('asset.management.assetTypeName'),
              value: '',
            },
            netWorkName: {
              key: 'netWorkName',
              label: this.$t('asset.management.netWorkName'),
              value: '',
            },
            assetModel: {
              key: 'assetModel',
              label: this.$t('asset.management.assetModel'),
              value: '',
            },
            manufactor: {
              key: 'manufactor',
              label: this.$t('asset.management.manufactor'),
              value: '',
            },
            osType: {
              key: 'osType',
              label: this.$t('asset.management.osType'),
              value: '',
            },
            memoryInfo: {
              key: 'memoryInfo',
              label: this.$t('asset.management.memoryInfo'),
              value: '',
            },
            responsiblePerson: {
              key: 'responsiblePerson',
              label: this.$t('asset.management.responsiblePerson'),
              value: '',
            },
            contactPhone: {
              key: 'contactPhone',
              label: this.$t('asset.management.contactPhone'),
              value: '',
            },
            email: {
              key: 'email',
              label: this.$t('asset.management.email'),
              value: '',
            },
            makerContactPhone: {
              key: 'makerContactPhone',
              label: this.$t('asset.management.makerContactPhone'),
              value: '',
            },
            assetCode: {
              key: 'assetCode',
              label: this.$t('asset.management.assetCode'),
              value: '',
            },
            domaName: {
              key: 'domaId',
              label: this.$t('asset.management.domaName'),
              value: '',
            },
            assetDesc: {
              key: 'assetDesc',
              label: this.$t('asset.management.assetDesc'),
              value: '',
            },
            ipvAddress: {
              key: 'ipvAddress',
              label: this.$t('asset.management.ipvAddress'),
              value: '',
            },
            securityComponent: {
              key: 'securityComponent',
              label: this.$t('asset.management.securityComponent'),
              value: '',
            },
            riskRating: {
              key: 'riskRating',
              label: this.$t('asset.management.riskRating'),
              value: '',
            },
            assetValue: {
              key: 'assetValue',
              label: this.$t('asset.management.assetValue'),
              value: '',
            },
          },
        },
        columns: {
          isIndeterminate: true,
          checkAll: false,
          checked: [],
          own: [],
          all: [
            {
              label: this.$t('asset.management.columns.assetName'),
              value: 'assetName',
            },
            {
              label: this.$t('asset.management.columns.assetType'),
              value: 'assetTypeName',
            },
            {
              label: this.$t('asset.management.columns.netWorkId'),
              value: 'netWorkName',
            },
            {
              label: this.$t('asset.management.columns.assetModel'),
              value: 'assetModel',
            },
            {
              label: this.$t('asset.management.columns.manufactor'),
              value: 'manufactor',
            },
            {
              label: this.$t('asset.management.columns.osType'),
              value: 'osType',
            },
            {
              label: this.$t('asset.management.columns.memoryInfo'),
              value: 'memoryInfo',
            },
            {
              label: this.$t('asset.management.columns.responsiblePerson'),
              value: 'responsiblePerson',
            },
            {
              label: this.$t('asset.management.columns.contactPhone'),
              value: 'contactPhone',
            },
            {
              label: this.$t('asset.management.columns.email'),
              value: 'email',
            },
            {
              label: this.$t('asset.management.columns.makerContactPhone'),
              value: 'makerContactPhone',
            },
            {
              label: this.$t('asset.management.columns.assetCode'),
              value: 'assetCode',
            },
            {
              label: this.$t('asset.management.columns.assetDesc'),
              value: 'assetDesc',
            },
            {
              label: this.$t('asset.management.columns.ipvAddress'),
              value: 'ipvAddress',
            },
            {
              label: this.$t('asset.management.columns.domaName'),
              value: 'domaName',
            },
            {
              label: this.$t('asset.management.columns.securityComponent'),
              value: 'securityComponent',
            },
            {
              label: this.$t('asset.management.columns.riskRating'),
              value: 'riskRating',
            },
            {
              label: this.$t('asset.management.columns.useStatusText'),
              value: 'useStatusText',
            },
          ],
        },
        upload: {
          header: {
            // 上传头部信息
            'Content-Type': 'multipart/form-data',
          },
          files: [], // 上传文件个数
          importRule: '1', // 导入规则：1：覆盖规则；2：忽略规则
          templateType: '', // 导入模板类型（1：xlsm  2:xls)
          rules: {
            files: [
              {
                required: true,
                message: this.$t('validate.choose'),
                trigger: 'change',
              },
            ],
            importRule: [
              {
                required: true,
                message: this.$t('validate.choose'),
                trigger: 'blur',
              },
            ],
          },
        },
        view: {
          visible: false,
          model: {},
        },
      },
      queryDebounce: null,
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.pagination.pageNum = 1
        this.queryInput.fuzzyField = route.query.fuzzyField
        let params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
        }
        if (!isEmpty(route.query.fuzzyField)) {
          params = Object.assign(params, {
            fuzzyField: route.query.fuzzyField,
          })
          this.getTableData(params)
        }
        if (!isEmpty(route.query.drillKey)) {
          this.queryInput.assetId = route.query.drillKey
          this.queryInput.fuzzyField = route.query.drillLabel
          params = Object.assign(params, {
            assetId: route.query.drillKey,
          })
          this.getTableData(params)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.initLoadData()
  },
  updated() {
    this.$refs.assetTable.doLayout()
  },
  methods: {
    initLoadData() {
      this.getTreeList()
      this.getNetList()
      this.getDomaList()
      if (Object.keys(this.$route.query).length === 0 || this.$route.query.fuzzyField === '' || this.$route.query.drillKey === '') {
        this.getTableData()
      }
      this.initDebounce()
      this.getAliveConfig()
    },
    getAliveConfig() {
      queryAliveConfig().then((res) => {
        if (res) {
          this.aliveCheck = res.status === '1' ? true : false
        }
        this.getColumn()
      })
    },
    changeAliveCheck(val) {
      updateAliveConfig({ status: val ? 1 : 0 }).then((res) => {
        this.$message.success('更新资产存活启停配置成功')
        this.getColumn()
      })
    },
    initDebounce() {
      this.queryDebounce = debounce(() => {
        this.searchQuery()
      }, 500)
    },
    // 获取table列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.$nextTick(() => {
          this.$refs.assetTable.doLayout()
        })
        this.pagination.visible = true
      })
    },
    // 获取资产类型数据
    getTreeList() {
      queryType().then((res) => {
        this.dialog.form.treeList = res
      })
    },
    // 获取网段数据
    getNetList() {
      queryNet().then((res) => {
        this.dialog.form.netList = res
      })
    },
    // 获取隶属区域数据
    getDomaList() {
      queryAssetDomain().then((res) => {
        this.dialog.form.domaList = res
      })
    },
    // 获取已选自定义列
    getColumn() {
      queryCustom().then((res) => {
        if (res) {
          const allArray = this.dialog.columns.all
            .map((item) => {
              return item.value
            })
            .filter((item) => {
              if (item === 'useStatusText') {
                return this.aliveCheck
              }
              return true
            })
          if (res.length > 0) {
            this.dialog.columns.checked = res.filter((item) => {
              if (item === 'useStatusText') {
                return this.aliveCheck
              }
              return true
            })
            this.dialog.columns.own = res
          } else {
            this.dialog.columns.checked = allArray
            this.dialog.columns.own = allArray
          }
          if (this.dialog.columns.own.length === allArray.length) {
            this.dialog.columns.checkAll = true
            this.dialog.columns.isIndeterminate = false
          } else {
            this.dialog.columns.checkAll = false
            this.dialog.columns.isIndeterminate = true
          }

          if (allArray.length > this.dialog.columns.own.length > 0) {
            this.dialog.columns.isIndeterminate = true
          } else {
            this.dialog.columns.isIndeterminate = false
          }
        } else {
          const allArray = this.dialog.columns.all.map((item) => {
            return item.value
          })
          this.dialog.columns.checked = allArray
          this.dialog.columns.own = allArray
          prompt({
            i18nCode: 'tip.query.error',
            type: 'error',
          })
        }
        this.data.selected = []
        this.searchQuery()
      })
    },
    // 调用导出接口
    downloadTable() {
      this.data.loading = true
      let params = {}
      if (this.isShow) {
        params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          assetIds: this.data.selected.map((item) => item.assetId).toString() || '',
          startIP: this.ipRange(this.queryInput.ipRange),
          domaId: this.queryInput.domaId,
          responsiblePerson: this.queryInput.responsiblePerson,
          assetClass: this.queryInput.assetClass,
          assetType: this.queryInput.assetType,
        }
      } else {
        params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          assetIds: this.data.selected.map((item) => item.assetId).toString() || '',
          fuzzyField: this.queryInput.fuzzyField,
          assetClass: this.queryInput.assetClass,
          assetType: this.queryInput.assetType,
        }
      }
      exportAsset(params).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    // 点击导出列表
    clickDownloadTable() {
      this.downloadTable()
    },
    // 查看某一行详情
    dblclickDisplayDetail(row) {
      this.TableRowChange(row)
      this.clearDialogFormModel()
      this.dialog.form.model = row
      this.dialog.form.activeName = 'first'
      const first = row.assetClass ? row.assetClass.toString() : ''
      const second = row.assetType ? row.assetType.toString() : ''
      this.dialog.form.model.values = [first, second]
      this.dialog.form.model.customAttr = this.dialog.form.model.customAttr.map((item) => {
        // 下拉框单选时 处理数组为字符串
        if (item && item.componentType === 2 && item.multiple === 2) {
          item.values = item.value ? item.value.toString() : ''
        }
        return item
      })
      this.dialog.visible.detail = true
    },
    // 调用添加接口
    add(obj) {
      const params = {
        ...obj.model,
        assetClass: obj.model.values[0],
        assetType: obj.model.values[1],
        customAttr: obj.model.customAttr,
      }
      addAsset(params).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.resetQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else if (res === -1) {
          prompt({
            i18nCode: 'tip.add.license',
            type: 'error',
          })
        } else if (res === 6) {
          prompt({
            i18nCode: 'tip.add.ipExist',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 点击添加操作并弹出对话框操作
    clickAdd() {
      this.clearDialogFormModel()
      this.dialog.visible.add = true
      this.dialog.form.activeName = 'first'
      this.dialog.form.add = true
      this.dialog.form.addAll = false
      this.dialog.form.update = false
      this.dialog.form.updateAll = false
    },
    // 点击批量添加操作并弹出对话框操作
    clickAddAll() {
      this.clearDialogFormModel()
      this.dialog.visible.addAll = true
      this.dialog.form.activeName = 'first'
      this.dialog.form.addAll = true
      this.dialog.form.add = false
      this.dialog.form.update = false
      this.dialog.form.updateAll = false
    },
    // 点击修改并弹出对话框操作
    clickUpdate(row) {
      this.TableRowChange(row)
      this.clearDialogFormModel()
      this.dialog.form.activeName = 'first'
      this.dialog.form.model = row
      const first = row.assetClass ? row.assetClass.toString() : ''
      const second = row.assetType ? row.assetType.toString() : ''
      this.dialog.form.model.values = [first, second]
      this.dialog.form.model.customAttr = this.dialog.form.model.customAttr.map((item) => {
        // 下拉框单选时 处理数组为字符串
        if (item && item.componentType === 2 && item.multiple === 2) {
          item.values = item.value ? item.value.toString() : ''
        }
        return item
      })
      this.dialog.visible.update = true
      this.dialog.form.update = true
      this.dialog.form.add = false
      this.dialog.form.addAll = false
      this.dialog.form.updateAll = false
    },
    // 点击批量修改操作并弹出对话框操作
    clickUpdateAll() {
      if (this.data.selected.length > 0) {
        this.clearDialogFormModel()
        this.dialog.form.activeName = 'first'
        this.dialog.visible.updateAll = true
        this.dialog.form.updateAll = true
        this.dialog.form.add = false
        this.dialog.form.addAll = false
        this.dialog.form.update = false
      } else {
        prompt({
          i18nCode: 'tip.update.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击导入操作并弹出对话框操作
    clickUpload() {
      this.dialog.upload.files = []
      this.dialog.upload.importRule = '1'
      this.dialog.upload.templateType = ''
      this.dialog.visible.upload = true
    },
    // 提交添加表单操作
    clickSubmitAdd(formModel) {
      const params = Object.assign({}, formModel)
      if (params.model.customAttr && params.model.customAttr.length > 0) {
        params.model.customAttr = params.model.customAttr.map((item) => {
          // 下拉框单选时 处理字符串为数组
          if (item.componentType === 2 && item.multiple === 2) {
            item.values = [item.value]
          }
          return item
        })
      }
      this.add(params)
    },
    // 提交批量添加
    clickSubmitAddAll(formModel) {
      if (formModel === 'true') {
        this.resetQuery()
      }
    },
    // 调用批量修改接口
    updateAll(obj) {
      const params = {
        assetCode: obj.model.assetCode,
        assetDesc: obj.model.assetDesc,
        assetIds: obj.model.assetIds,
        assetModel: obj.model.assetModel,
        assetName: obj.model.assetName,
        contactPhone: obj.model.contactPhone,
        domaId: obj.model.domaId,
        email: obj.model.email,
        endIP: obj.model.endIP,
        ipvAddress: obj.model.ipvAddress,
        makerContactPhone: obj.model.makerContactPhone,
        manufactor: obj.model.manufactor,
        memoryInfo: obj.model.memoryInfo,
        netWorkId: obj.model.netWorkId,
        osType: obj.model.osType,
        responsiblePerson: obj.model.responsiblePerson,
        startIP: obj.model.startIP,
        assetClass: obj.model.values[0],
        assetType: obj.model.values[1],
        customAttr: obj.model.customAttr,
        securityComponent: obj.model.securityComponent,
        assetValue: obj.model.assetValue,
      }
      updateAssets(params).then((res) => {
        // 这里根据自己的业务进行操作
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.$emit('on-submit', 'true')
              this.inputQueryEvent()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 提交批量修改
    clickSubmitUpdateAll(formModel) {
      formModel.model.assetIds = this.data.selected.map((item) => item.assetId).toString()
      const params = Object.assign({}, formModel)
      if (params.model.customAttr && params.model.customAttr.length > 0) {
        params.model.customAttr = params.model.customAttr.map((item) => {
          // 下拉框单选时 处理字符串为数组
          if (item && item.componentType === 2 && item.multiple === 2) {
            item.values = [item.value]
          }
          return item
        })
      }
      this.updateAll(params)
    },
    // 调用删除接口
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteAssets(ids).then((res) => {
          // 这里根据自己的业务进行操作
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.inputQueryEvent()
              }
            )
          } else if (res === 8) {
            prompt({
              i18nCode: 'tip.delete.use',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 点击删除操作
    clickDelete(row) {
      this.delete(row.assetId)
    },
    // 点击批量删除操作
    clickBatchDelete() {
      if (this.data.selected.length > 0) {
        // 选中大于等于1时，将每行的id组装成数组并将这个数组转换成字符串进行删除传递
        const ids = this.data.selected.map((item) => item.assetId).toString()
        this.delete(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.inputQueryEvent()
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQueryEvent()
    },
    // 列表多选改变
    TableSelectsChange(select) {
      this.data.selected = select
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 输入框搜索方法
    inputQueryEvent(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    searchQuery() {
      const params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        startIP: this.ipRange(this.queryInput.ipRange),
        domaId: this.queryInput.domaId,
        responsiblePerson: this.queryInput.responsiblePerson,
        assetClass: this.queryInput.assetClass,
        assetType: this.queryInput.assetType,
        assetId: this.queryInput.assetId,
        fuzzyField: this.queryInput.fuzzyField,
      }
      this.data.loading = true
      this.getTableData(params)
    },
    ipRange(ipArr) {
      let ip = ''
      ipArr = ipArr.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipArr.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    // 清空搜索条件
    clearQuery() {
      this.queryInput = {
        ipRange: ['', ''],
        fuzzyField: '',
        domaId: '',
        responsiblePerson: '',
        assetClass: '',
        assetType: '',
      }
    },
    // 重置搜索
    resetQuery() {
      this.pagination.pageNum = 1
      this.clearQuery()
      this.queryDebounce()
    },
    // 点击侧边栏树形控件
    handleNodeClick(data) {
      this.clearQuery()
      this.pagination.pageNum = 1
      if (data.children) {
        this.queryInput.assetClass = data.value
      } else {
        this.queryInput.assetType = data.value
      }
      this.queryDebounce()
    },
    // 点击批量操作下拉菜单
    handleCommand(command) {
      switch (command) {
        case 'add':
          this.clickAddAll()
          break
        case 'update':
          this.clickUpdateAll()
          break
        case 'delete':
          this.clickBatchDelete()
          break
      }
    },
    // 清空弹窗表单绑定数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        // 弹出表单绑定值
        assetName: '',
        customAttr: [],
        assetType: '',
        values: '',
        netWorkId: '',
        assetModel: '',
        manufactor: '',
        osType: '',
        memoryInfo: '',
        responsiblePerson: '',
        contactPhone: '',
        email: '',
        makerContactPhone: '',
        assetCode: '',
        domaId: '',
        assetDesc: '',
        ipvAddress: '',
        startIP: '',
        endIP: '',
        assetTypeName: '',
        netWorkName: '',
        securityComponent: '',
        assetValue: '0.2',
      }
    },
    // 点击自定义列 对话框弹出
    clickTh() {
      this.clearDialogFormModel()
      this.getColumn()
      this.dialog.visible.th = true
    },
    // 提交导入
    clickSubmitUpload(formData) {
      // 调用导入接口
      uploadAsset(formData)
        .then((res) => {
          if (res > 0) {
            prompt({
              i18nCode: this.$t('asset.management.successUpload', [res]),
              type: 'success',
            })
            this.pagination.pageNum = 1
            this.getTableData()
          } else if (res === -1) {
            prompt({
              i18nCode: 'tip.import.license',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.import.error',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
    // 调用修改自定义列接口
    th(obj) {
      const param = obj.own
      addCustom(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getColumn()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.null',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 提交自定义列
    clickSubmitTh(formModel) {
      this.th(formModel)
    },
    // 调用修改接口
    update(obj) {
      const params = {
        ...obj.model,
        assetClass: obj.model.values[0],
        assetType: obj.model.values[1],
        customAttr: obj.model.customAttr,
      }
      updateAsset(params).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.inputQueryEvent()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeatPerson',
            type: 'error',
          })
        } else if (res === 6) {
          prompt({
            i18nCode: 'tip.update.ipExist',
            type: 'error',
          })
        } else if (res === 9) {
          prompt({
            i18nCode: 'tip.update.ipMonitored',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 提交修改表单操作
    clickSubmitUpdate(formModel) {
      const params = Object.assign({}, formModel)
      if (params.model.customAttr && params.model.customAttr.length > 0) {
        params.model.customAttr = params.model.customAttr.map((item) => {
          // 下拉框单选时 处理字符串为数组
          if (item.componentType === 2 && item.multiple === 2) {
            item.values = [item.value]
          }
          return item
        })
      }
      this.update(params)
    },
    clickLogView(row) {
      this.dialog.view.visible = true
      this.dialog.view.model = row
    },
  },
}
</script>

<style lang="scss" scoped>
.tree-box {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
  div.table-box {
    width: 100%;
    overflow: auto;
  }

  .el-tree {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
