<!--
 * @Description: 行为分析策略 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-14
 * @Editor:
 * @EditDate: 2021-10-14
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @selection-change="clickSelectRows"
      >
        <el-table-column type="selection" prop="id"></el-table-column>
        <el-table-column
          v-for="(item, key) in columns"
          :key="key"
          :prop="item"
          :label="$t(`audit.behaviorStrategy.label.${item}`)"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <p v-if="item === 'status'">
              {{ columnText(scope.row[item]) }}
            </p>
            <p v-else>
              {{ scope.row[item] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="status" :label="$t('collector.management.table.run')">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" @change="toggleStatus(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="260">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
            <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
              {{ $t('button.update') }}
            </el-button>
            <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
              {{ $t('button.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      columns: ['systemName', 'systemIp', 'abnormalActionStr', 'startTime', 'endTime'],
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  methods: {
    clickSelectRows(sel) {
      this.$emit('on-select', sel)
    },
    toggleStatus(row) {
      this.$emit('on-toggle-status', row)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    clickUpdate(row) {
      this.$emit('on-update', row)
    },
    clickDelete(row) {
      this.$emit('on-delete', row)
    },
  },
}
</script>

<style scoped></style>
