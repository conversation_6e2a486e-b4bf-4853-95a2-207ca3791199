<template>
  <div :id="id" ref="lineChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    seriesData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    seriesData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart(this.chart)
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.seriesData) {
      const pd = data || this.seriesData
      this.chart = echarts.init(this.$refs.lineChart)
      this.drawChart(pd)
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      const dataValue = data.map((o) => o.value)
      const dataName = data.map((o) => o.name)
      const dataMaxValue = data.map((o) => Math.max(...dataValue))
      const option = {
        grid: { top: 24, bottom: 0, right: 12, left: 0 },
        xAxis: { show: false, type: 'value' },
        yAxis: [
          {
            type: 'category',
            show: true,
            data: dataName,
            inverse: true,
            axisLine: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              show: false,
            },
            axisLabel: {
              interval: 0,
              color: '#fff',
              fontWeight: 'bold',
              align: 'left',
              verticalAlign: 'bottom',
              lineHeight: 48,
              margin: 0,
            },
          },
          {
            type: 'category',
            show: true,
            inverse: true,
            axisTick: 'none',
            axisLine: 'none',
            data: dataValue,
            axisLabel: {
              interval: 0,
              color: '#fff',
              fontWeight: 'bold',
              align: 'right',
              verticalAlign: 'bottom',
              lineHeight: 48,
              margin: 0,
            },
          },
        ],
        series: [
          {
            name: '数据',
            type: 'bar',
            yAxisIndex: 0,
            data: dataValue,
            zlevel: 1,
            barWidth: 12,
            itemStyle: {
              normal: {
                barBorderRadius: 10,
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                  {
                    offset: 0,
                    color: '#385FFC',
                  },
                  {
                    offset: 1,
                    color: '#2FBED3',
                  },
                ]),
              },
            },
          },
          {
            name: '背景',
            type: 'bar',
            yAxisIndex: 1,
            barGap: '-100%',
            data: dataMaxValue,
            barWidth: 10,
            itemStyle: {
              normal: {
                color: 'rgba(24,31,68,1)',
                barBorderRadius: 10,
              },
            },
          },
        ],
      }
      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
