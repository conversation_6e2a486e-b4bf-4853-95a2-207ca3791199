@import 'visualization-earth-animate';
@import '~@/style/country';

$title-color: #70b4bf;
$eps-color: #205589;
$type-color: #174672;

.visualization-earth-container {
  position: relative;
  height: 1080px;
  width: 1920px;
  overflow: hidden;
  background: url('~@asset/image/visualization/display-main-bg.jpg') no-repeat;

  .earth-container {
    position: absolute;
    z-index: 1;
    width: 100%;
    height: 100%;

    .earth-inner-ring {
      position: absolute;
      z-index: 7;
      width: 100%;
      height: 100%;
      background: url('~@asset/image/visualization/earth-inner-ring.png') no-repeat center 88px;
    }

    .earth-outer-ring {
      position: absolute;
      z-index: 8;
      width: 100%;
      height: 100%;
      background: url('~@asset/image/visualization/earth-outer-ring.png') no-repeat center top;
    }

    .earth-canvas {
      position: absolute;
      z-index: 9;
      width: 100%;
      height: 100%;
    }
  }

  .main-container {
    position: absolute;
    z-index: 10;
    width: 100%;
    height: 100%;

    .row-header {
      display: flex;
      height: 100px;
      width: 100%;
      justify-content: space-between;

      .header-logo {
        display: flex;
        width: 430px;
        height: 80%;
        margin-left: 50px;
        align-items: center;
        background: url('~@asset/image/visualization/system-name-bg.png') no-repeat 1px 64px;
        background-size: contain;

        i.soc-icon-logo {
          margin: 10px;
          font-size: 40px;
          color: $WT;
        }

        h1 {
          font-size: 20px;
          white-space: nowrap;
          color: $WT;
        }
      }

      .header-eps {
        width: 180px;
        height: 100%;

        &-top {
          height: 40px;
          padding-top: 10px;
          line-height: 40px;
          white-space: nowrap;

          &-number {
            font-size: 28px;
            color: $title-color;
          }

          &-letter {
            margin-left: 10px;
            font-size: 20px;
            color: $eps-color;
          }
        }

        &-canvas {
          height: 60px;
          margin-top: 10px;
        }
      }
    }

    .row-body {
      width: 100%;
      height: calc(100% - 230px);

      .row-body-top,
      .row-body-bottom {
        display: flex;
        width: 100%;
        height: 50%;
        justify-content: space-between;

        .body-top-left,
        .body-top-right {
          width: 520px;
        }
      }

      .row-body-top {
        .line-chart {
          position: relative;
          height: calc(100% - 120px);

          &-canvas {
            position: absolute;
            left: 40px;
            top: 10px;
            width: 380px;
            height: 220px;
          }

          &-title {
            position: absolute;
            left: 420px;
            top: 0;
            font-size: 18px;
            white-space: nowrap;
            color: $title-color;
          }

          &-bg {
            position: absolute;
            top: 30px;
            right: -44px;
            width: 119px;
            height: 120px;
            background: url('~@asset/image/visualization/attack-trend-decorate.png') no-repeat right center;
          }
        }

        .pie-chart {
          position: relative;
          background: url('~@asset/image/visualization/attack-type-bg.png') no-repeat 50px 10px;

          b {
            display: block;
            padding: 0 5px;
            font-weight: bolder;
          }

          &-first {
            position: absolute;
            left: 225px;
            top: 110px;
            width: 140px;
            height: 140px;
            padding: 45px 0 55px 0;
            text-align: center;
            border-radius: 50%;

            .first-number {
              font-size: 20px;
            }

            .first-type {
              font-size: 14px;
            }
          }

          &-second {
            position: absolute;
            left: 62px;
            top: 20px;
            width: 120px;
            height: 120px;
            padding: 40px 0 49px 0;
            text-align: center;
            border-radius: 50%;

            .second-number {
              font-size: 18px;
            }

            .second-type {
              font-size: 14px;
            }
          }

          &-third {
            position: absolute;
            left: 84px;
            top: 211px;
            width: 100px;
            height: 100px;
            padding: 30px 0 40px 0;
            text-align: center;
            border-radius: 50%;

            .third-number {
              font-size: 16px;
            }

            .third-type {
              font-size: 14px;
            }
          }

          &-first,
          &-second,
          &-third {
            color: $type-color;
          }

          &-title {
            position: absolute;
            left: -100px;
            top: 10px;
            font-size: 18px;
            color: $title-color;
          }

          &-bg {
            position: absolute;
            top: 68px;
            left: -80px;
            width: 160px;
            height: 114px;
          }
        }
      }

      .row-body-bottom {
        .bar-chart {
          position: relative;

          &-canvas {
            position: absolute;
            left: 140px;
            top: 24px;
            width: 330px;
            height: 220px;
          }

          &-title {
            position: absolute;
            top: 270px;
            left: 230px;
            font-size: 18px;
            color: $title-color;
          }

          &-bg {
            position: absolute;
            top: 175px;
            right: -38px;
            width: 407px;
            height: 116px;
            background: url('~@asset/image/visualization/attack-source-border.png') no-repeat right center;
          }

          &-decorate {
            position: absolute;
            top: 144px;
            left: 540px;
            width: 40px;
            height: 42px;
            background: url('~@asset/image/visualization/attack-source-ring.png') no-repeat;
          }
        }

        .radar-chart {
          position: relative;

          &-canvas {
            position: absolute;
            left: 68px;
            top: 60px;
            width: 312px;
            height: 312px;
          }

          &-canvas-bg {
            position: absolute;
            left: 70px;
            top: 60px;
            width: 312px;
            height: 312px;
            background: url('~@asset/image/visualization/attack-level-bg.png') no-repeat;
          }

          &-title {
            position: absolute;
            left: -70px;
            top: 230px;
            font-size: 18px;
            color: $title-color;
          }

          &-bg {
            position: absolute;
            left: -52px;
            top: 162px;
            width: 111px;
            height: 114px;
            background: url('~@asset/image/visualization/attack-level-decorate.png') no-repeat;
          }

          &-decorate {
            position: absolute;
            width: 34px;
            height: 32px;
            top: 144px;
            left: -62px;
            background: url('~@asset/image/visualization/attack-level-ring.png') no-repeat;
          }
        }
      }
    }

    .row-footer {
      height: 130px;
      width: 100%;
      padding: 0 18px;
    }
  }
}
