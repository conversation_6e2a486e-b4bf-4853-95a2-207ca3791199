<!--
 * @Description: 用户管理 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="dialogForm.model" :rules="dialogForm.rules" label-width="25%">
      <el-form-item :label="$t('management.user.label.account')" prop="account">
        <el-input v-model="dialogForm.model.account" :disabled="disabled" maxlength="16" show-word-limit class="width-mini"></el-input>
      </el-form-item>
      <el-form-item :label="$t('time.option.startDate')" prop="startTime">
        <el-date-picker
          v-model="dialogForm.model.startTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          clearable
          :placeholder="$t('time.option.startDate')"
          :picker-options="disablePickerOption.startDate"
          class="width-mini"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('time.option.endDate')" prop="endTime">
        <el-date-picker
          v-model="dialogForm.model.endTime"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          format="yyyy-MM-dd HH:mm:ss"
          clearable
          :placeholder="$t('time.option.endDate')"
          :picker-options="{ disabledDate: disableEndDatePickerOption }"
          class="width-mini"
        ></el-date-picker>
      </el-form-item>
      <el-form-item :label="$t('management.user.label.activeCyclic')">
        <el-time-picker
          v-model="dialogForm.model.activeCyclic"
          value-format="HH:mm:ss"
          format="HH:mm:ss"
          is-range
          :range-separator="$t('time.option.until')"
          :start-placeholder="$t('time.option.startTime')"
          :end-placeholder="$t('time.option.endTime')"
          class="width-mini"
        ></el-time-picker>
      </el-form-item>
      <el-form-item :label="$t('management.user.label.description')" prop="description">
        <el-input v-model="dialogForm.model.description" type="textarea" class="width-mini" maxlength="128"></el-input>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateName } from '@util/validate'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      type: Object,
      default() {
        return {}
      },
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatorAccount = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateName(value, 1)) {
        callback(new Error(this.$t('validate.username.rule')))
      } else {
        callback()
      }
    }

    return {
      dialogVisible: this.visible,
      dialogForm: {
        model: {
          account: '',
          startTime: '',
          endTime: '',
          activeCyclic: ['', ''],
          description: '',
        },
        rules: {
          account: [
            {
              required: true,
              trigger: 'blur',
              validator: validatorAccount,
            },
          ],
          endTime: [
            {
              trigger: 'change',
              validator: (rule, value, callback) => {
                if (this.dialogForm.model.startTime && this.dialogForm.model.startTime !== '' && value && value !== '') {
                  if (new Date(this.dialogForm.model.startTime).getTime() >= new Date(value).getTime()) {
                    callback(new Error(this.$t('validate.date.compare')))
                  } else {
                    callback()
                  }
                } else {
                  callback()
                }
              },
            },
          ],
        },
      },
      disablePickerOption: {
        startDate: {
          disabledDate(date) {
            return date.getTime() <= Date.now() - 24 * 60 * 60 * 1000
          },
        },
      },
    }
  },
  watch: {
    form(form) {
      this.dialogForm.model = {
        account: form.userAccount,
        startTime: form.startValidDate || '',
        endTime: form.endValidDate || '',
        activeCyclic: form.activeCyclic || ['', ''],
        description: form.userDescription,
      }
    },
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.formTemplate.resetFields()
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.dialogForm.model)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    disableEndDatePickerOption(date) {
      if (this.dialogForm.model.startTime && this.dialogForm.model.startTime !== '') {
        return date.getTime() <= new Date(this.dialogForm.model.startTime).getTime() - 24 * 60 * 60 * 1000
      } else {
        return date.getTime() <= Date.now() - 24 * 60 * 60 * 1000
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  min-width: 680px;
}
</style>
