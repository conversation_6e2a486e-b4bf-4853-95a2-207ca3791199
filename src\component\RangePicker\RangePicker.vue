<template>
  <div class="range-picker" :class="{ 'is-active': active, 'is-error': error }">
    <i class="range-picker-icon" :class="inputPrefixIcon"></i>
    <input
      ref="startInput"
      :value="value && value[0]"
      :type="inputType"
      :placeholder="inputStartPlaceholder"
      class="range-picker-input"
      @change="changeRangeValue"
      @input="changeStartValue"
      @focus="focusRangePicker"
      @blur="blurRangePicker"
      @keydown.enter="changeRangeValue"
    />
    <slot name="range-picker-separator">
      <span class="range-picker-separator">
        {{ rangeSeparator }}
      </span>
    </slot>
    <input
      ref="endInput"
      :value="value && value[1]"
      :type="inputType"
      :placeholder="inputEndPlaceholder"
      class="range-picker-input"
      @change="changeRangeValue"
      @input="changeEndValue"
      @focus="focusRangePicker"
      @blur="blurRangePicker"
      @keydown.enter="changeRangeValue"
    />
    <i v-if="clearable" class="range-picker-icon el-icon-circle-close" @click="clickSuffixIcon"></i>
  </div>
</template>

<script>
import { debounce } from '@util/effect'
import { prompt } from '@util/prompt'
import { validateIpv4, validateIpv6, validateIp, validateMac } from '@util/validate'  // 添加validateMac导入

export default {
  props: {
    value: {
      type: Array,
      default: () => [],
    },
    type: {
      type: String,
      default: 'number', // 支持ip、number或mac
    },
    rangeSeparator: {
      type: String,
      default: '-',
    },
    startPlaceholder: {
      type: String,
      default: '',
    },
    endPlaceholder: {
      type: String,
      default: '',
    },
    clearable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      debounce: null,
      active: false,
      error: false,
    }
  },
  computed: {
    inputType() {
      const type = this.type.toLowerCase()
      return type === 'ip' || type === 'mac' ? 'text' : 'number'
    },
    inputPrefixIcon() {
      const type = this.type.toLowerCase()
      if (type === 'ip') return 'soc-icon-ip'
      // 使用默认图标代替mac专用图标
      return 'soc-icon-number'
    },
    inputStartPlaceholder() {
      if (this.startPlaceholder !== '') {
        return this.startPlaceholder
      } else if (this.type === 'number') {
        return '起始数字'
      } else if (this.type === 'ip') {
        return '起始IP'
      } else if (this.type === 'mac') {
        return '起始MAC'
      }
      return this.startPlaceholder
    },
    inputEndPlaceholder() {
      if (this.endPlaceholder !== '') {
        return this.endPlaceholder
      } else if (this.type === 'number') {
        return '终止数字'
      } else if (this.type === 'ip') {
        return '终止IP'
      } else if (this.type === 'mac') {
        return '终止MAC'
      }
      return this.endPlaceholder
    },
  },
  watch: {
    value: {
      handler(value) {
        this.emitInput(value || [])
      },
      immediate: true,
    },
    active(value) {
      if (!value) {
        this.numberValidate()
        this.ipValidate()
      }
    },
  },
  created() {
    this.initDebounce()
  },
  methods: {
    changeStartValue(event) {
      let startValue = event.target.value
      if (this.type.toLowerCase() === 'number') {
        startValue = Number.parseInt(event.target.value)
      }
      this.value[0] = event.target.value === '' ? null : startValue
      this.value[1] = this.value[1] || null
    },
    changeEndValue(event) {
      let endValue = event.target.value
      if (this.type.toLowerCase() === 'number' && event.target.value !== '') {
        endValue = Number.parseInt(event.target.value)
      }
      this.value[0] = this.value[0] || null
      this.value[1] = event.target.value === '' ? null : endValue
    },
    changeRangeValue() {
      this.debounce()
    },
    focusRangePicker() {
      this.inputIsActive(true)
    },
    blurRangePicker() {
      this.inputIsActive(false)
    },
    clickSuffixIcon() {
      this.active = false
      this.error = false
      this.$set(this.value, [])
      this.emitInput([])
      this.$emit('change')
    },
    initDebounce() {
      this.debounce = debounce(() => {
        const [startValue, endValue] = [this.value[0] ? this.value[0] : null, this.value[1] ? this.value[1] : null]
        const type = this.type.toLowerCase()

        if (type === 'number' && !this.active) {
          if (startValue === null || endValue === null || endValue >= startValue) {
            this.$emit('change')
          }
        }

        if (type === 'ip' && !this.active) {
          if (
            (startValue === null && endValue === null) ||
            (startValue === null && validateIp(endValue)) ||
            (endValue === null && validateIp(startValue)) ||
            (validateIpv4(startValue) && validateIpv4(endValue)) ||
            (validateIpv6(startValue) && validateIpv6(endValue))
          ) {
            this.$emit('change')
          }
        }

        if (type === 'mac' && !this.active) {
          if (
            (startValue === null && endValue === null) ||
            (startValue === null && validateMac(endValue)) ||
            (endValue === null && validateMac(startValue)) ||
            (validateMac(startValue) && validateMac(endValue))
          ) {
            this.$emit('change')
          }
        }
      }, 500)
    },
    emitInput(value) {
      this.$emit('input', value)
    },
    inputIsActive(active) {
      this.active = active
    },
    numberValidate() {
      const [startNumber, endNumber] = [this.value[0] ? this.value[0] : null, this.value[1] ? this.value[1] : null]
      if (this.type.toLowerCase() === 'number' && startNumber !== null && endNumber !== null && endNumber < startNumber) {
        prompt({
          i18nCode: 'validate.number.compare',
          type: 'warning',
        })
        this.error = true
      } else {
        this.error = false
      }
    },
    ipValidate() {
      const type = this.type.toLowerCase()
      if (type === 'ip') {
        const [startIp, endIp] = [this.value[0] ? this.value[0] : null, this.value[1] ? this.value[1] : null]
        let validate = null

        if (startIp !== null && endIp === null) {
          validate = validateIp(startIp)
        } else if (startIp === null && endIp !== null) {
          validate = validateIp(endIp)
        } else if (startIp !== null && endIp !== null) {
          validate = (validateIpv4(startIp) && validateIpv4(endIp)) || (validateIpv6(startIp) && validateIpv6(endIp))
        } else {
          validate = true
        }

        if (!validate) {
          prompt({
            i18nCode: 'validate.ip.checkRange',
            type: 'warning',
          })
          this.error = true
        } else {
          this.error = false
        }
      } else if (type === 'mac') {
        const [startMac, endMac] = [this.value[0] ? this.value[0] : null, this.value[1] ? this.value[1] : null]
        let validate = null

        if (startMac !== null && endMac === null) {
          validate = validateMac(startMac)
        } else if (startMac === null && endMac !== null) {
          validate = validateMac(endMac)
        } else if (startMac !== null && endMac !== null) {
          validate = validateMac(startMac) && validateMac(endMac)
        } else {
          validate = true
        }

        if (!validate) {
          prompt({
            i18nCode: 'validate.mac.checkRange',
            type: 'warning',
          })
          this.error = true
        } else {
          this.error = false
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.range-picker {
  display: inline-flex;
  width: 400px;
  height: 32px;
  padding: 3px 10px;
  line-height: 32px;
  align-items: center;
  @include theme('background-color', el-input-bg);
  background-image: none;
  border-radius: 4px;
  border: 1px solid;
  @include theme('border-color', el-input-border);
  box-sizing: border-box;
  color: #606266;
  font-size: inherit;
  outline: none;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);

  &:hover {
    border-color: #c0c4cc;
  }

  &.is-active {
    border-color: #409eff;
  }

  &.is-error {
    border-color: #f56c6c;
  }

  &-input {
    height: 100%;
    margin: 0;
    text-align: center;
    display: inline-block;
    padding: 0;
    width: 40%;
    font-size: 14px;
    line-height: 1;
    appearance: none;
    border: none;
    outline: 0;
    @include theme('color', el-input-color);
    &::-webkit-input-placeholder {
      color: rgba(125, 125, 125, 0.5);
    }
  }

  &-separator {
    width: 10%;
    display: inline-block;
    height: 100%;
    margin: 0;
    padding: 0 5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 24px;
    color: #303133;
    text-align: center;
  }

  &-icon {
    width: 25px;
    height: 100%;
    margin-left: -5px;
    float: left;
    text-align: center;
    line-height: 24px;
    font-size: 14px;
    color: #c0c4cc;
    transition: all 0.3s;

    &.el-icon-circle-close {
      opacity: 0;
      cursor: pointer;

      &:hover {
        opacity: 1;
      }
    }
  }
}
</style>
