<template>
  <div :id="id" ref="lineChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    seriesData: {
      type: Object,
      default() {
        return { name: '', value: 0, color: '#0097ff' }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    seriesData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart(this.chart)
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data) {
      const pd = data || this.seriesData
      this.chart = echarts.init(this.$refs.lineChart)
      this.drawChart(pd)
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.clear()
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      const option = {
        series: [
          {
            type: 'pie',
            clockWise: false,
            radius: ['90%', '96%'],
            itemStyle: {
              normal: { label: { show: false }, labelLine: { show: false }, shadowBlur: 0, shadowColor: '#203665' },
            },
            hoverAnimation: false,
            data: [
              {
                value: 100,
                label: {
                  normal: {
                    rich: {
                      a: { color: data.color, align: 'center', fontSize: 22, lineHeight: 40 },
                      b: { color: data.color, align: 'center', fontSize: 16 },
                    },
                    formatter: function () {
                      return '{a|' + data.value + '\n}' + '{b|' + data.name + '}'
                    },
                    position: 'center',
                    show: true,
                  },
                },
                itemStyle: { normal: { color: data.color, shadowColor: data.color, shadowBlur: 0 } },
              },
              {
                value: 0,
                name: 'invisible',
                itemStyle: {
                  normal: { color: 'rgb(13,44,81)' },
                  emphasis: { color: 'rgb(13,44,81)' },
                },
              },
            ],
          },
        ],
      }
      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
