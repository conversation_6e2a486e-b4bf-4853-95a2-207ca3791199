<!--
 * @Description: 原始日志 - 转发弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-01-11
 * @Editor:
 * @EditDate: 2022-01-11
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.forward', [titleName])"
    width="40%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="forwardDom" :model="form.model" :rules="form.rules" label-width="120px">
      <template>
        <el-form-item :prop="form.info.status.key" :label="form.info.status.label">
          <el-switch v-model="form.model.state" active-value="1" inactive-value="0"></el-switch>
        </el-form-item>
        <el-form-item :prop="form.info.logFormat.key" :label="form.info.logFormat.label">
          <el-autocomplete
            v-model="form.model.logFormat"
            popper-class="custom-autocomplete"
            :placeholder="$t('event.original.placeholder.logFormat')"
            :trigger-on-focus="false"
            :fetch-suggestions="querySearchFields"
            @select="handleSelectField"
          >
            <template slot-scope="{ item }">
              <div class="item">
                {{ item.label }}
              </div>
            </template>
          </el-autocomplete>
        </el-form-item>
        <el-form-item prop="ip0" label="发生源">
          <el-select v-model="form.model.ip0" filterable multiple collapse-tags clearable>
            <el-option v-for="item in collectorServerOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="deviceType" label="设备类型">
          <el-cascader
            ref="cascader"
            v-model="form.model.deviceType"
            filterable
            clearable
            :options="deviceTypeOption"
            :placeholder="$t('event.generalLog.label.deviceName')"
            :props="{ expandTrigger: 'hover' }"
          ></el-cascader>
        </el-form-item>
        <el-form-item :prop="form.info.forwardServer.key" :label="form.info.forwardServer.label">
          <el-select v-model="form.model.forwardId" filterable multiple collapse-tags clearable>
            <el-option v-for="item in forwardServerOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import { queryLogForward } from '@api/event/general-log-api'
const logColumn = [
  {
    label: '设备类别',
    value: '',
    key: 'deviceCategoryName',
    check: false,
  },
  {
    label: '设备类型',
    value: '',
    key: 'deviceTypeName',
    check: false,
  },
  {
    label: '发生源IP',
    value: '',
    key: 'sourceIp',
    check: false,
  },
  {
    label: '日志原文',
    value: '',
    key: 'raw',
    check: false,
  },
]

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    forwardServerOption: {
      required: true,
      type: Array,
    },
    collectorServerOption: {
      required: true,
      type: Array,
    },
    deviceTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      form: {
        model: {
          state: '0',
          logFormat: '',
          propName: '',
          forwardId: '',
          ip0: '',
          deviceType: [],
        },
        info: {
          status: { key: 'status', label: this.$t('event.original.forward.status') },
          logFormat: { key: 'logFormat', label: this.$t('event.original.forward.logFormat') },
          forwardServer: { key: 'forwardId', label: this.$t('event.original.forward.forwardServer') },
        },
        rules: {
          forwardId: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
      conditionExp: '',
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.form.model = {
          state: '0',
          logFormat: '',
          propName: '',
          forwardId: '',
        }
        this.getLogForward()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
    'form.model.logFormat'(value) {
      this.conditionExp = this.form.model.logFormat
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    querySearchFields(inputVal, cb) {
      if (inputVal.lastIndexOf('@') > -1) {
        const searchKey = inputVal.substring(inputVal.lastIndexOf('@') + 1, inputVal.length)
        const attributeFields = logColumn
        const results = searchKey ? attributeFields.filter(this.createFilter(searchKey)) : attributeFields
        // 返回建议列表的数据
        cb(results)
      } else {
        cb([])
      }
    },
    createFilter(queryString) {
      return (restaurant) => {
        return restaurant.label.toLowerCase().indexOf(queryString.toLowerCase()) === 0
      }
    },
    handleSelectField(item) {
      const lastIndex = this.conditionExp.lastIndexOf('@')
      this.form.model.logFormat = this.conditionExp.substring(0, lastIndex + 1).concat(item.label)
    },
    getLogForward() {
      queryLogForward().then((res) => {
        if (!isEmpty(res)) {
          let { ip0, logFormat, deviceType } = JSON.parse(res.filter) || {}
          if (ip0) ip0 = ip0?.split(',')
          if (ip0?.length > 0) {
            ip0 = ip0.filter((item) => {
              return this.collectorServerOption.some((server) => {
                return server.value === item
              })
            })
          }

          // 处理forwardId，确保其中的所有值都存在于forwardServerOption中
          let forwardIdList = res.forwardId?.split(',')
          if (forwardIdList?.length > 0) {
            forwardIdList = forwardIdList.filter((item) => {
              return this.forwardServerOption.some((server) => {
                return server.value === item
              })
            })
          }

          this.form.model = {
            state: res.state?.toString(),
            logFormat: this.parseExpressValue(logFormat),
            forwardId: forwardIdList,
            ip0,
            deviceType: deviceType ? deviceType?.split(',') : [],
          }
        }
      })
    },
    parseExpressValue(logFormat) {
      if (!isEmpty(logFormat)) {
        logColumn.forEach((item) => {
          const key = '|'.concat(item.key, '|')
          if (logFormat.indexOf(key) > -1) {
            logFormat = logFormat.replaceAll(key, '@'.concat(item.label))
          }
        })
      }
      return logFormat
    },
    clickSubmit() {
      this.$refs.forwardDom.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.parseModelFormat(this.form.model)
            this.$emit('on-submit', this.form.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
    parseModelFormat(model) {
      const propArr = []
      let logFormat = model.logFormat
      if (!isEmpty(logFormat)) {
        logColumn.forEach((item) => {
          // @label替换为|key|
          const label = '@'.concat(item.label)
          let i = logFormat.indexOf(label)
          while (i > -1 && i <= logFormat.length) {
            propArr.push({ key: item.key, index: logFormat.indexOf(label) })
            logFormat = logFormat.replace(label, '|'.concat(item.key, '|'))
            i = logFormat.indexOf(label)
          }
        })
      }
      propArr.sort(this.compare('index'))
      const filter = {
        ip0: model.ip0?.toString(),
        logFormat: logFormat,
        propName: propArr.map((item) => item.key)?.toString(),
        deviceType: model.deviceType?.toString(),
      }
      model = Object.assign(model, {
        forwardId: model.forwardId?.toString(),
        filter: JSON.stringify(filter),
      })
      delete model.ip0
      delete model.logFormat
      delete model.propName
      delete model.deviceType
    },
    compare(property) {
      return function(a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value1 - value2
      }
    },
  },
}
</script>

<style scoped lang="scss">
.custom-autocomplete {
  li {
    line-height: normal;
    padding: 7px;
    .item {
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .highlighted .item {
      color: #ddd;
    }
  }
}
</style>
