<!--
 * @Description: 自定义解析
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-12-31
 * @Editor:
 * @EditDate: 2022-12-31
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" :options="options" @on-change="changeQueryTable" @on-add="clickAdd"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-select="clickSelectRows"
      @on-toggle-status="toggleStatus"
      @on-detail="clickDetail"
      @on-delete="clickDelete"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <add-dialog
      :visible.sync="dialog.add.visible"
      :title-name="title"
      :model="dialog.add.model"
      :options="options"
      @on-submit="saveSubmit"
    ></add-dialog>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model" :options="options"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import AddDialog from './TheAddDialog'
import DetailDialog from './TheDetailDialog'
import { prompt } from '@util/prompt'

import {
  queryCustomParseTable,
  addCustomParse,
  deleteCustomParse,
  updateCustomParseStatus,
  queryCustomParseDetail,
  queryDevTypeCombo,
  queryMultiGroupCombo,
  queryEventTypeCombo,
} from '@api/event/custom-parse-api'

export default {
  name: 'CustomParse',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    AddDialog,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('event.customParse.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          patternValue: '',
          patternKey: '',
          status: '',
          devType: '',
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        devType: [],
        multiGroup: [],
        eventType: [],
        status: [
          { value: 1, label: this.$t('code.executeStatus.on') },
          { value: 0, label: this.$t('code.executeStatus.off') },
        ],
        level: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
      },
      dialog: {
        add: {
          visible: false,
          model: {},
        },
        detail: {
          visible: false,
          model: {},
        },
      },
    }
  },
  mounted() {
    this.initOptions()
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') {
        this.pagination.pageNum = 1
      }
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          patternValue: this.query.form.patternValue,
          patternKey: this.query.form.patternKey.toString(),
          status: this.query.form.status,
          devType: this.query.form.devType,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickSelectRows(sel) {
      this.table.selected = sel
    },
    clickAdd() {
      this.dialog.add.visible = true
      this.dialog.add.model = {
        devType: '',
        status: '1',
        message: '',
        points: [],
        pattern: '',
      }
      // this.dialog.add.model.message = "<14>Oct 21 18:47:16 IDS <pre><span style='color: red;'>evLog_daemon</span></pre>: <event evttype=\"ATTACK\" evtid=\"\" level=\"0\" sensorid=\"\" ethid=\"\"> <evtname>HTTP SUN JRE SDK JDK Main-Class manifest  JAR文件栈缓冲区溢出漏洞(CVE-2008-5354)</evtname> <sip>***********</sip> <dip>***********</dip> <sport>10</sport> <dport>10</dport> <repeat>1</repeat> <start_time>2020-10-21 18:47:13</start_time> <end_time>2020-10-21 18:47:13</end_time> <policy>02406,100000110111000000000000,00000110</policy> </event>";
    },
    saveSubmit(obj) {
      this.addCustomParse(obj)
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      queryCustomParseDetail(row.patternId).then((res) => {
        this.dialog.detail.model = res
      })
    },
    clickDelete(row) {
      this.deleteCustomParse(row.patternId)
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    toggleStatus(row) {
      this.updateStatus(row.patternId, row.status)
    },
    initOptions() {
      queryDevTypeCombo().then((res) => {
        this.options.devType = res
      })
      queryMultiGroupCombo().then((res) => {
        this.options.multiGroup = res
      })
      queryEventTypeCombo().then((res) => {
        this.options.eventType = res
      })
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryCustomParseTable(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
    addCustomParse(obj) {
      addCustomParse(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    deleteCustomParse(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteCustomParse(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.query.form = {
                  fuzzyField: '',
                  systemName: '',
                  systemIp: '',
                  abnormalAction: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.table.data.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    updateStatus(id, status) {
      updateCustomParseStatus(id, status).then((res) => {
        if (res) {
          if (status === '1') {
            prompt(
              {
                i18nCode: 'tip.enable.success',
                type: 'success',
              },
              () => {
                this.changeQueryTable()
              }
            )
          } else {
            prompt(
              {
                i18nCode: 'tip.disable.success',
                type: 'success',
              },
              () => {
                this.changeQueryTable()
              }
            )
          }
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
