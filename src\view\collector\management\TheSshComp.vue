<!--
 * @Description: 采集器管理 - SSH连接
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-04-18
 * @Editor:
 * @EditDate: 2022-04-18
-->
<template>
  <el-form ref="sshForm" :model="form.model" :rules="rules" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item :label="form.info.ip.label">
          <div style="display: flex; width: 90%;">
            <el-input v-model.trim="ipTemp"></el-input>
            <el-button @click="addIp" style="margin-left: 10px" type="primary">添加</el-button>
          </div>
          <el-tag v-for="t in ipArray" :key="t" closable @close="deleteIp(t)" style="margin-right: 4px;">
            {{ t }}
          </el-tag>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :prop="form.info.port.key" :label="form.info.port.label">
          <el-input v-model.trim="form.model.port" maxlength="20"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item :prop="form.info.userName.key" :label="form.info.userName.label">
          <el-input v-model.trim="form.model.userName" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item :prop="form.info.password.key" :label="form.info.password.label">
          <el-input v-model.trim="form.model.password" :show-password="true" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
    </el-row>
    <!--<el-row
            v-for="(item,index) in columnOption[0]"
            :key="index">
            <el-col
                v-for="(itemCol,colIndex) in item"
                :key="colIndex"
                :span="12">
                <el-form-item
                    :prop="itemCol.key"
                    :label="itemCol.label">
                    <el-input
                        v-model.trim="form.model[itemCol.key]"
                        :show-password="itemCol.key==='password'? true: false">
                    </el-input>
                </el-form-item>
            </el-col>
        </el-row>-->
    <el-row>
      <el-col :span="12">
        <el-form-item prop="strategy" :label="form.info.strategy.label">
          <el-select v-model="form.model.strategy" filterable clearable :placeholder="$t('collector.management.placeholder.strategy')">
            <el-option v-for="item in filterOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="sourcePage === 'add'" :span="12">
        <el-form-item prop="isAsset" :label="form.info.isAsset.label">
          <el-checkbox v-model="form.model.isAsset" :checked="form.model.isAsset === 1 ? true : false" :true-label="1" :false-label="0"></el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>
<script>
import { validateIp } from '@/util/validate'
import { queryExistAccessmode } from '@api/collector/collector-management-api'

export default {
  props: {
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    filterOption: {
      required: true,
      type: Array,
    },
    sourcePage: {
      type: String,
      default: 'add',
    },
  },
  data: () => ({
    ipArray: [],
    ipTemp: '',
  }),
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    'form.model.ip': {
      handler(val) {
        this.ipArray = val.split(',').filter((item) => item && item !== ' ')
      },
      deep: true,
    },
  },
  mounted() {
    if (this.form.model.ip) {
      this.ipArray = this.form.model.ip.split(',').filter((item) => item && item !== ' ')
    }
  },
  methods: {
    validateForm() {
      let validate = false
      this.$refs.sshForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    addIp() {
      if (this.ipArray.includes(this.ipTemp)) {
        this.$message.error('该IP地址已存在')
        return
      }
      if (this.ipTemp && validateIp(this.ipTemp)) {
        queryExistAccessmode({
          ip: this.ipTemp,
          collectorName: this.form.model.collectorName,
          protId: this.form.model.protId,
          codeWay: this.form.model.codeWay,
          checkedNum: this.ipArray.length,
        }).then((res) => {
          if (res === 4 || res === 5) {
            this.$message.error('该IP地址已有该接入方式')
          } else if (res === 6) {
            this.$message.error('采集地址超出授权点数限制')
          } else {
            this.ipArray.push(this.ipTemp)
            this.ipTemp = ''
            this.form.model.ip = this.ipArray.join(',')
          }
        })
      } else {
        this.$message.error('请输入正确的IP地址')
      }
    },
    deleteIp(tag) {
      const index = this.ipArray.indexOf(tag)
      if (index !== -1) {
        this.ipArray.splice(index, 1)
      }
      this.form.model.ip = this.ipArray.join(',')
    },
  },
}
</script>
