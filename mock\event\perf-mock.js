const { TableMock } = require('../util')

const performanceTable = new TableMock({
  total: 10,
  template: {
    perfId: '@ID',
    perfName: '@CNAME',
    'currentStatus|1': ['0', '1'],
    'perfStatus|1': ['0', '1'],
    'perfClassName|1': ['应用服务性能', '数据库性能', '网络设备性能', '主机性能'],
    edName: '@IP',
    domaName: '@CNAME',
    value: '@Number',
    enterDate: '@DATETIME',
    updateDate: '@DATETIME',
    perfModule: '@TITLE',
    perfSolution: '@DESC',
  },
})

const perfClassList = ['应用服务性能', '数据库性能', '网络设备性能', '主机性能']

const perfClass = [
  { value: '0', label: '应用服务性能' },
  { value: '1', label: '数据库性能' },
  { value: '2', label: '网络设备性能' },
  { value: '3', label: '主机性能' },
]

const perfLevel = [
  { value: '0', label: '严重' },
  { value: '1', label: '高级' },
  { value: '2', label: '中级' },
  { value: '3', label: '低级' },
  { value: '4', label: '一般' },
  { value: '-1', label: '未知' },
]

module.exports = [
  {
    url: '/perfeventmanagement/queryPerformanceEvents',
    type: 'get',
    response: (option) => {
      performanceTable.query(option)
      return {
        code: 200,
        data: performanceTable.getMockData(),
      }
    },
  },
  {
    url: '/perfeventmanagement/combo/perfclass',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: perfClassList,
      }
    },
  },
  {
    url: '/perfeventmanagement/queryPerformanceEventDetails',
    type: 'get',
    response: (option) => {
      performanceTable.query(option)
      return {
        code: 200,
        data: performanceTable.getMockData(),
      }
    },
  },
  {
    url: '/perfeventmanagement/combo/perfclass',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: perfClass,
      }
    },
  },
  {
    url: '/perfeventmanagement/combo/perflevel',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: perfLevel,
      }
    },
  },
]
