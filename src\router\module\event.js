export default [
  {
    name: 'EventOriginal',
    path: '/event/original',
    component: () => import('@view/event/original/EventOriginal'),
  },
  {
    name: 'EventPolymerizationStrategy',
    path: '/event/polymerization-strategy',
    component: () => import('@view/event/polymerization-strategy/EventPolymerizationStrategy'),
  },
  {
    name: 'EventRelevance',
    path: '/event/relevance',
    component: () => import('@view/event/relevance/EventRelevance'),
  },
  {
    name: 'EventRelevanceStrategy',
    path: '/event/relevance-strategy',
    component: () => import('@view/event/relevance-strategy/EventRelevanceStrategy'),
  },
  {
    name: 'EventSecurity',
    path: '/event/security',
    component: () => import('@view/event/security/EventSecurity'),
  },
  {
    name: 'EventOriginalLes',
    path: '/event/original-les',
    component: () => import('@view/event/original-les/EventOriginalLes'),
  },
  {
    name: 'EventThreat',
    path: '/event/threat',
    component: () => import('@view/event/threat/EventThreat'),
  },
  {
    name: 'EventFault',
    path: '/event/fault',
    component: () => import('@view/event/fault/EventFault'),
  },
  {
    name: 'EventPerf',
    path: '/event/performance',
    component: () => import('@view/event/performance/EventPerf'),
  },
  {
    name: 'GeneralLog',
    path: '/event/general-log',
    component: () => import('@view/event/general-log/GeneralLog'),
  },
  {
    name: 'CustomParse',
    path: '/event/custom-parse',
    component: () => import('@view/event/custom-parse/CustomParse'),
  },
  {
    name: 'CustomCode',
    path: '/event/custom-code',
    component: () => import('@view/event/custom-code/CustomCode'),
  },
]
