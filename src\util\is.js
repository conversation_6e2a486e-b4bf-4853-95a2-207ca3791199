export const isServer = typeof window === 'undefined'

export const isClient = !isServer

/**
 * @func 判断值是否非空
 * @param {any} value - 传入的值
 * @return {boolean} 是否非空
 * <AUTHOR> @date 2019/09/10
 */
export function isNotEmpty(value) {
  return value !== null && value !== undefined && value !== ''
}

/**
 * @func 判断值是否为空
 * @param {any} value - 传入的值
 * @return {boolean} 是否为空
 * <AUTHOR> @date 2019/09/10
 */
export function isEmpty(value) {
  return value === null || value === undefined || value === ''
}

/**
 * @func 判断值是否为数组
 * @param {any} value - 传入的值
 * @return {boolean} 是否为数组
 * <AUTHOR> @date 2019/09/10
 */
export function isArray(value) {
  return value.constructor === Array
}

/**
 * @func 判断值是否为对象
 * @param {any} value - 传入的值
 * @return {boolean} 是否为对象
 * <AUTHOR> @date 2019/09/10
 */
export function isObject(value) {
  return value.constructor === Object
}

/**
 * @func 判断值是否为函数
 * @param {any} value - 传入的值
 * @return {boolean} 是否为函数
 * <AUTHOR> @date 2019/09/10
 */
export function isFunction(value) {
  return value.constructor === Function
}

/**
 * @func 判断值是否为字符串
 * @param {any} value - 传入的值
 * @return {boolean} 是否为字符串
 * <AUTHOR> @date 2019/09/10
 */
export function isString(value) {
  return typeof value === 'string' || value.constructor === String
}

/**
 * @func 判断值是否为数字
 * @param {any} value - 传入的值
 * @return {boolean} 是否为数字
 * <AUTHOR> @date 2019/09/10
 */
export function isNumber(value) {
  return typeof value === 'number' || value.constructor === Number
}

/**
 * @func 判断值是否为null
 * @param {any} value - 传入的值
 * @return {boolean} 是否为null
 * <AUTHOR> @date 2019/09/10
 */
export function isNull(value) {
  return Object.prototype.toString.call(value) === '[object Null]'
}

/**
 * @func 判断值是否为undefined
 * @param {any} value - 传入的值
 * @return {boolean} 是否为undefined
 * <AUTHOR> @date 2019/09/10
 */
export function isUndefined(value) {
  return Object.prototype.toString.call(value) === '[object Undefined]'
}

/**
 * @func 判断值是否为symbol
 * @param {any} value - 传入的值
 * @return {boolean} 是否为symbol
 * <AUTHOR> @date 2019/09/10
 */
export function isSymbol(value) {
  return typeof value === 'symbol' || value.constructor === Symbol
}

/**
 * @func 判断两个值相等
 * @param {any} value - 对比值1
 * @param {any} other - 对比值2
 * @return {boolean}
 * <AUTHOR> @date 2023/12/18
 */
export function isEqual(value, other) {
  const typeofInstance = (str) => Object.prototype.toString.call(str).slice(8, -1)

  if (!isObject(value) && !isObject(other)) {
    if (Number.isNaN(value) && Number.isNaN(other)) {
      return true
    }
    return value === other
  }
  if (!isObject(value) || !isObject(other)) {
    return false
  }
  if (typeofInstance(value) !== typeofInstance(other)) {
    return false
  }
  if (value === other) {
    return true
  }
  if (['Array'].includes(typeofInstance(value))) {
    return arrayEquals(value, other)
  }

  if (['Object'].includes(typeofInstance(value))) {
    return objectEquals(value, other)
  }
  if (['Map', 'Set'].includes(typeofInstance(value))) {
    const [arr1, arr2] = [[...value], [...other]]
    return isEqual(arr1, arr2)
  }
  return false
}

/**
 * @func 判断两个对象相同
 * @param {object} object1 - 对象1
 * @param {object} object2 - 对象2
 * @return {boolean}
 * <AUTHOR> @date 2023/12/18
 */
function objectEquals(object1, object2) {
  for (const propName in object1) {
    if (object1.hasOwnProperty(propName) !== object2.hasOwnProperty(propName)) {
      return false
    } else if (typeof object1[propName] !== typeof object2[propName]) {
      return false
    }
  }
  for (const propName in object2) {
    if (object1.hasOwnProperty(propName) !== object2.hasOwnProperty(propName)) {
      return false
    } else if (typeof object1[propName] !== typeof object2[propName]) {
      return false
    }
    if (!object1.hasOwnProperty(propName)) {
      continue
    }
    if (object1[propName] instanceof Array && object2[propName] instanceof Array) {
      if (!arrayEquals(object1[propName], object2[propName])) return false
    } else if (object1[propName] instanceof Object && object2[propName] instanceof Object) {
      if (!objectEquals(object1[propName], object2[propName])) return false
    } else if (object1[propName] !== object2[propName]) {
      return false
    }
  }
  return true
}

/**
 * @func 判断两个对象相同
 * @param {array} array1 - 数组1
 * @param {array} array2 - 数组2
 * @return {boolean}
 * <AUTHOR> @date 2023/12/18
 */
function arrayEquals(array1, array2) {
  if (!array1 || !array2) return false
  if (array1.length !== array2.length) return false
  for (let i = 0, l = array1.length; i < l; i++) {
    if (array1[i] instanceof Array && array2[i] instanceof Array) {
      if (!arrayEquals(array1[i], array2[i])) return false
    } else if (array1[i] instanceof Object && array2[i] instanceof Object) {
      if (!objectEquals(array1[i], array2[i])) return false
    } else if (array1[i] !== array2[i]) {
      return false
    }
  }
  return true
}
