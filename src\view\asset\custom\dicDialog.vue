<!--
 * @Description: 自定义资产属性 - 字典弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :loading="loadingBtn"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form ref="formTemplate" :model="form.model" :label-width="'25%'">
      <template>
        <el-row>
          <el-col :span="16">
            <el-form-item :label="form.info.name.label" :prop="form.info.name.key">
              <el-input
                v-model.trim="form.model.name"
                :placeholder="$t('asset.custom.placeholder.dicName')"
                maxlength="10"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button v-has="'add'" @click="addDic">
              {{ $t('button.add') }}
            </el-button>
          </el-col>
        </el-row>
        <el-row>
          <el-table
            v-loading="form.loading"
            :data="form.model.list"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            tooltip-effect="light"
            fit
          >
            <el-table-column :label="form.info.name.label" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-input
                  v-if="scope.row.inputVisible"
                  v-model.trim="scope.row.name"
                  class="width-mini"
                  maxlength="10"
                  show-word-limit
                  @keyup.enter.native="handleInputConfirm(scope.row)"
                  @blur="handleInputConfirm(scope.row)"
                ></el-input>
                <el-tag v-else disable-transitions>
                  {{ scope.row.name }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column width="160" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                  {{ $t('button.update') }}
                </el-button>
                <el-button v-show="!scope.row.usage" v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                  {{ $t('button.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-row>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { updateDic } from '@api/asset/custom-api'
import { prompt } from '@util/prompt'

export default {
  name: 'DicDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '800',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      num: 0,
      loadingBtn: false,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 添加字典
    addDic() {
      if (this.form.model.name.length > 0) {
        let flag = true
        this.form.model.list.map((item) => {
          if (item && item.name === this.form.model.name) {
            prompt({
              i18nCode: 'tip.update.repeatDic',
              type: 'error',
            })
            flag = false
          }
        })
        if (flag) {
          this.num++
          this.form.model.list.unshift({
            // localId: this.num,
            name: this.form.model.name,
            inputVisible: false,
            propertyId: this.form.model.parentId,
          })
          this.form.model.name = ''
        }
      } else {
        prompt({
          i18nCode: this.$t('asset.custom.placeholder.dicName'),
          type: 'error',
        })
      }
    },
    // 点击修改字典
    clickUpdate(row) {
      this.form.model.list.map((item) => {
        item.inputVisible = false
      })
      const index = this.form.model.list.indexOf(row)
      this.form.model.list[index].inputVisible = true
    },
    // 输入框失去焦点
    handleInputConfirm(row) {
      if (row.name.length > 0) {
        let flag = true
        const index = this.form.model.list.indexOf(row)
        const newList = this.form.model.list.concat()
        newList.splice(row, 1)
        newList.map((item) => {
          if (item && item.name === row.name) {
            // prompt({
            //     i18nCode: "tip.update.repeatDic",
            //     type: "error"
            // });
            flag = false
          }
        })
        if (flag) {
          this.form.model.list[index].inputVisible = false
        }
      } else {
        prompt({
          i18nCode: this.$t('asset.custom.placeholder.dicName'),
          type: 'error',
        })
      }
    },
    // 删除字典
    clickDelete(row) {
      const index = this.form.model.list.indexOf(row)
      this.form.model.list.splice(index, 1)
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      const params = Object.assign({}, this.form.model)
      this.loadingBtn = true
      const obj = {
        parentId: params.parentId,
        dicList: params.list,
      }
      updateDic(obj).then((res) => {
        this.loadingBtn = false
        if (res === 1) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            prompt(
              {
                i18nCode: 'tip.update.success',
                type: 'success',
              },
              () => {
                // 给父级调用数据
                this.$emit('on-submit', true)
                this.clickCancelDialog()
              }
            )
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeatDic',
            type: 'error',
          })
          return false
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-dialog {
  .el-dialog__body {
    height: 450px;
  }
}

::v-deep .el-table {
  height: 330px;
  .el-table__body-wrapper {
    height: 290px;
    overflow-y: scroll;
  }
}
</style>
