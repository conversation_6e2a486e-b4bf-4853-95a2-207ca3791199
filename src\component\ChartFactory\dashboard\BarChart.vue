<template>
  <div :id="id" ref="barChart" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'
import empty from '../mixin/empty'

export default {
  mixins: [resize, empty],
  props: {
    className: {
      type: String,
      default: 'chart-bar',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    mouseEvent: {
      type: Boolean,
      default: false,
    },
    proto: {
      type: Boolean,
      default: false,
    },
    barData: {
      type: Object,
      default() {
        return {
          title: '',
          axis: {
            direction: 'horizontal',
            data: [],
          },
          legend: [],
          series: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    barData: {
      handler(data) {
        this.configChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.barChart, this.$store.getters.theme)
    },
    configChart(data = this.barData) {
      !data || Object.keys(data).length === 0 || data.series.length === 0 ? this.empty() : this.drawChart(data)
    },
    drawChart(data) {
      const option = this.proto ? data : this.chartOptionConfig(data)
      this.chart.clear()
      this.chart.setOption(option, true)
    },
    chartOptionConfig(data) {
      let option = {
        backgroundColor: 'transparent',
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          text: data.title ? data.title : '',
        },
        grid: {
          top: '30px',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true,
        },
        series: [],
      }
      const axis = this.chartAxisConfig(data)
      option = Object.assign(option, axis)

      if (this.mouseEvent) {
        this.chartHasMouseEvent(option, data)
      }

      this.chartSeriesConfig(option, data)
      return option
    },
    chartAxisConfig(data) {
      const [category, value] = [
        [
          {
            type: 'category',
            boundaryGap: true,
            data: data.axis.data,
          },
        ],
        [
          {
            type: 'value',
          },
        ],
      ]
      let axis = {}
      if (data.axis.direction === 'h' || data.axis.direction === 'horizontal') {
        axis = {
          xAxis: category,
          yAxis: value,
        }
      }

      if (data.axis.direction === 'v' || data.axis.direction === 'vertical') {
        axis = {
          xAxis: value,
          yAxis: category,
        }
      }

      return axis
    },
    chartHasMouseEvent(option, data) {
      if (data.legend && data.legend.length > 1) {
        option.legend = {
          type: 'scroll',
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: data.legend,
          right: '4%',
        }
      }

      option.tooltip = {
        trigger: 'axis',
        formatter: '{b}:{c}',
        axisPointer: {
          type: 'shadow',
        },
      }

      return option
    },
    chartSeriesConfig(option, data) {
      const series = (name, data) => {
        return {
          name: name,
          type: 'bar',
          barWidth: 10,
          data: data,
        }
      }

      data.series.forEach((item, index) => {
        option.series.push(series(data.legend[index], item))
      })

      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
