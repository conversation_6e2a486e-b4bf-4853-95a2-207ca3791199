import request from '@util/request'

// 添加属性
export function addAsset(obj) {
  return request({
    url: '/assetproperties/property',
    method: 'post',
    data: obj || {},
  })
}

// 删除属性
export function deleteAssets(ids) {
  return request({
    url: `/assetproperties/property/${ids}`,
    method: 'delete',
  })
}

// 修改属性
export function updateAsset(obj) {
  return request({
    url: `/assetproperties/property`,
    method: 'put',
    data: obj || {},
  })
}

// 修改资产字典
export function updateDic(obj) {
  return request({
    url: `/assetproperties/dictionaries`,
    method: 'put',
    data: obj || {},
  })
}

// 查询属性列表
export function queryTableData(obj) {
  return request({
    url: '/assetproperties/properties',
    method: 'get',
    params: obj || {},
  })
}

// 查询属性下的资产字典
export function queryDic(id) {
  return request({
    url: `/assetproperties/dictionaries/${id}`,
    method: 'get',
  })
}

// 查询资产分类下拉框
export function queryType() {
  return request({
    url: '/assetproperties/types',
    method: 'get',
  })
}

// 查询详情
export function queryDetails(id) {
  return request({
    url: `/assetproperties/properties/${id}`,
    method: 'get',
  })
}
