import request from '@util/request'

// 添加
export function addData(obj) {
  return request({
    url: '/access-control/network',
    method: 'post',
    data: obj || {},
  })
}

// 删除
export function deleteData(ids) {
  return request({
    url: `/access-control/network/${ids}`,
    method: 'delete',
  })
}

// 查询列表
export function queryTableData(obj) {
  return request({
    url: '/access-control/networks',
    method: 'get',
    params: obj || {},
  })
}
