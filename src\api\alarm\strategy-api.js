import request from '@util/request'

export function queryStrategyTableData(obj) {
  return request({
    url: '/strategy/alarm/strategies',
    method: 'get',
    params: obj || {},
  })
}

export function queryAlarmTypeOption(obj) {
  return request({
    url: '/strategy/alarm/combo/audit-types',
    method: 'get',
    params: obj || {},
  })
}

export function querySystemsOption(obj) {
  return request({
    url: '/strategy/alarm/combo/source-device-type',
    method: 'get',
    params: obj || {},
  })
}

export function queryStrategyTableDetail(id) {
  return request({
    url: `/strategy/alarm/strategy/${id}`,
    method: 'get',
  })
}

export function deleteStrategy(id) {
  return request({
    url: `/strategy/alarm/strategy/${id}`,
    method: 'delete',
  })
}

export function addStrategy(obj) {
  return request({
    url: '/strategy/alarm/strategy',
    method: 'post',
    data: obj || {},
  })
}

export function updateStrategy(obj) {
  return request({
    url: '/strategy/alarm/strategy',
    method: 'put',
    data: obj || {},
  })
}

export function updateStrategyStatus(obj) {
  return request({
    url: '/strategy/alarm/strategy/state',
    method: 'put',
    data: obj || {},
  })
}

export function queryForwardCombo(obj) {
  return request({
    url: '/strategy/alarm/combo/forward-strategies',
    method: 'get',
    params: obj || {},
  })
}
