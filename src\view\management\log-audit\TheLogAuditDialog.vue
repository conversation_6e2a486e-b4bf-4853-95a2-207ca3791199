<!--
 * @Description: 日志审计 - 弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.detail', [$t('management.logAudit.name')])"
    :width="width"
    :action="false"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form :model="formData" label-width="25%">
      <el-form-item :label="$t('management.logAudit.label.operator')">
        {{ formData.logUser }}
      </el-form-item>
      <el-form-item :label="$t('management.logAudit.label.resource')">
        {{ formData.logResource }}
      </el-form-item>
      <el-form-item :label="$t('management.logAudit.label.operation')">
        {{ formData.logAction }}
      </el-form-item>
      <el-form-item :label="$t('management.logAudit.label.result')">
        {{ columnText(formData.logState, 'resultStatus') }}
      </el-form-item>
      <el-form-item :label="$t('management.logAudit.label.ip')">
        {{ formData.logIp }}
      </el-form-item>
      <el-form-item :label="$t('management.logAudit.label.date')">
        {{ formData.logDate }}
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { resultStatus } from '@asset/js/code/option'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    width: {
      type: String,
      default: '35%',
    },
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      options: {
        resultStatus: resultStatus,
      },
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.clickCancelDialog()
    },
  },
}
</script>
