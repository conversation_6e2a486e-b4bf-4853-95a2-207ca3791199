<!--
 * @Description: 关联事件
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--页面搜索框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('event.relevance.table.eventName')])"
              clearable
              @change="inputQuery('e')"
              @keyup.enter.native="inputQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--页面按钮区-->
        <section class="table-header-button">
          <el-button v-has="'download'" @click="clickDownloadButton">
            {{ $t('button.export.default') }}
          </el-button>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-select
                  v-model="query.seniorQuery.eventType"
                  clearable
                  filterable
                  :placeholder="$t('event.relevance.table.eventTypeName')"
                  @change="submitQueryForm('e')"
                >
                  <el-option v-for="item in options.eventNameOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="4">
                <el-input
                  v-model.trim="query.seniorQuery.policyName"
                  clearable
                  :placeholder="$t('event.relevance.table.policyName')"
                  @change="submitQueryForm('e')"
                ></el-input>
              </el-col>
              <el-col :span="4">
                <el-select
                  v-model="query.seniorQuery.level"
                  clearable
                  :placeholder="$t('event.relevance.table.eventLevelName')"
                  @change="submitQueryForm('e')"
                >
                  <el-option v-for="item in options.levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="8">
                <el-date-picker
                  v-model="query.seniorQuery.createDate"
                  clearable
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :start-placeholder="$t('event.relevance.time.createDateStart')"
                  :end-placeholder="$t('event.relevance.time.createDateEnd')"
                  @change="submitQueryForm('e')"
                ></el-date-picker>
              </el-col>
              <el-col align="right" :span="4">
                <el-button v-has="'query'" @click="submitQueryForm('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQueryForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--页面主体-->
    <section class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('event.relevance.header') }}
        </h2>
        <el-button @click="clickCustomizeButton">
          {{ $t('button.th') }}
        </el-button>
      </header>
      <main class="table-body-main">
        <el-table
          v-if="show.tableShow"
          v-loading="data.loading"
          v-el-table-scroll="scrollEventRelevanceTable"
          :data="data.table"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="selectsChange"
        >
          <el-table-column width="50" type="index"></el-table-column>
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            v-for="(item, index) in options.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`event.relevance.table.${item}`)"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <level-tag v-if="item === 'level'" :level="scope.row.level"></level-tag>
              <p v-else>
                {{ scope.row[item] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="80">
            <template v-slot="{ row }">
              <el-button class="el-button--blue" @click="clickDetailButton(row)">
                {{ $t('button.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </section>
    <!--页面分页组件-->
    <footer class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
        <i v-show="data.totalLoading" class="el-icon-loading"></i>
      </section>
      <section v-show="!data.totalLoading" class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span>{{ data.total }}</span>
      </section>
    </footer>
    <!--自定义列-->
    <col-dialog
      :visible.sync="dialog.columnDialog.visible"
      :title="dialog.columnDialog.title"
      :form="dialog.columnDialog.form"
      :columns="dialog.columnDialog.columns"
      @on-submit="clickSubmitCustomize"
    ></col-dialog>
    <!--详情弹出框-->
    <detail-dialog
      :visible.sync="dialog.detailDialog.visible"
      :title="dialog.detailDialog.title"
      :form="dialog.detailDialog.form"
      :form-col="dialog.detailDialog.formCol"
      :loading="dialog.detailDialog.loading"
      :width="'70%'"
      @drawerShow="clickDetailDrawer"
    ></detail-dialog>
    <!--抽屉-->
    <drawer :visible.sync="drawer.visible" :loading="drawer.loading" :detail-data="drawer.data"></drawer>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import colDialog from './EventRelevanceCustomizeDialog'
import detailDialog from './EventRelevanceDetailDialog'
import levelTag from '@comp/LevelTag'
import drawer from './EventRelevanceDetailDrawer'
import { debounce } from '@/util/effect'
import { prompt } from '@util/prompt'
import {
  queryRelevanceTableData,
  queryEventType,
  queryColumnsData,
  queryRelevanceTableDetail,
  queryRelevanceTableTotal,
  downloadTableData,
  updateColumnsData,
} from '@api/event/relevance-api'

export default {
  name: 'EventRelevance',
  directives: {
    elTableScroll,
  },
  components: {
    colDialog,
    detailDialog,
    levelTag,
    drawer,
  },
  data() {
    return {
      data: {
        loading: false,
        scroll: true,
        table: [],
        total: 0,
        nomore: false,
        selected: [],
        totalLoading: false,
        debounce: {
          downloadDebounce: null,
          resetQueryDebounce: null,
          query: null,
        },
      }, // 列表数据
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      }, // 分页信息
      show: {
        seniorQueryShow: false,
        tableShow: true,
      }, // 显示隐藏
      dialog: {
        detailDialog: {
          visible: false,
          title: this.$t('event.relevance.dialog.detailTitle'),
          formCol: [
            { key: 'eventTypeName', label: this.$t('event.relevance.table.eventTypeName') },
            { key: 'policyName', label: this.$t('event.relevance.table.policyName') },
            { key: 'level', label: this.$t('event.relevance.table.level') },
            { key: 'createDate', label: this.$t('event.relevance.table.createDate') },
            { key: 'updateDate', label: this.$t('event.relevance.table.updateDate') },
            { key: 'count', label: this.$t('event.relevance.table.count') },
            { key: 'eventDesc', label: this.$t('event.relevance.table.eventDesc') },
          ],
          form: {
            model: {
              eventId: '',
              policyName: '',
              eventTypeName: '',
              level: '',
              createDate: '',
              updateDate: '',
              count: '',
            },
          },
          loading: false,
        },
        columnDialog: {
          visible: false,
          title: this.$t('event.relevance.dialog.colTitle'),
          form: {
            model: {
              checkList: [],
              checkAll: false,
              isIndeterminate: false,
            },
            info: {
              checkList: {
                key: 'checkList',
                label: this.$t('alarm.table.dialog.option'),
              },
            },
          },
          columns: [
            { key: 'eventCategoryName', label: this.$t('event.relevance.table.eventCategoryName') },
            { key: 'eventTypeName', label: this.$t('event.relevance.table.eventTypeName') },
            { key: 'policyName', label: this.$t('event.relevance.table.policyName') },
            { key: 'level', label: this.$t('event.relevance.table.level') },
            { key: 'createDate', label: this.$t('event.relevance.table.createDate') },
            { key: 'updateDate', label: this.$t('event.relevance.table.updateDate') },
            { key: 'count', label: this.$t('event.relevance.table.count') },
          ],
        },
      }, // 弹出框
      query: {
        rawLogParams: {},
        seniorQuery: {
          eventType: '',
          policyName: '',
          level: '',
          createDate: '',
        },
        fuzzyField: '',
        tempParams: {},
      }, // 查询内容
      options: {
        eventNameOption: [],
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        columnOption: ['eventCategoryName', 'eventTypeName', 'policyName', 'level', 'count', 'createDate', 'updateDate'],
      }, // 下拉框option
      drawer: {
        visible: false,
        data: [],
        loading: false,
      },
    }
  },
  computed: {
    disableScroll() {
      return this.data.scroll
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    // 页面初始化方法
    init() {
      this.getRelevanceTable()
      this.initDebounce()
      this.queryColumns()
      this.queryEventTypeName()
      this.queryEventRelevanceTotalData()
    },
    // 初始化防抖方法
    initDebounce() {
      this.initChangeDebounce()
      this.initStaticDebounce()
    },
    // 初始化切换的防抖方法
    initChangeDebounce() {
      this.data.debounce.query = debounce(() => {
        this.data.nomore = false
        this.data.table = []
        let params = {}
        if (this.show.seniorQueryShow) {
          params = Object.assign(
            {},
            {
              ...this.query.seniorQuery,
              createDate: this.query.seniorQuery.createDate === null ? '' : this.query.seniorQuery.createDate.toString(),
              pageSize: this.$store.getters.pageSize,
            }
          )
          this.query.tempParams = params
        } else {
          params = {
            pageSize: this.$store.getters.pageSize,
            fuzzyField: this.query.fuzzyField,
          }
        }
        this.getRelevanceTable(params)
        this.queryEventRelevanceTotalData(params)
      }, 500)
    },
    // 初始化固定的防抖方法
    initStaticDebounce() {
      this.data.debounce.downloadDebounce = debounce(() => {
        let params = {}
        if (this.show.seniorQueryShow) {
          params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            eventId: this.data.selected.length !== 0 ? this.data.selected.map((item) => item.eventId).toString() : null,
            eventType: this.query.seniorQuery.eventType,
            policyName: this.query.seniorQuery.policyName,
            level: this.query.seniorQuery.level,
            createDate: this.query.seniorQuery.createDate === null ? '' : this.query.seniorQuery.createDate.toString(),
          }
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            eventId: this.data.selected.length !== 0 ? this.data.selected.map((item) => item.eventId).toString() : null,
            fuzzyField: this.query.fuzzyField,
          }
        }
        this.downloadApi(params)
      }, 500)
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.data.nomore = false
        this.query.tempParams = {}
        this.data.table = []
        this.data.scroll = true
        this.query.seniorQuery = {
          eventType: '',
          policyName: '',
          level: '',
          createDate: '',
        }
        setTimeout(() => {
          this.getRelevanceTable()
        }, 150)
        this.queryEventRelevanceTotalData()
      }, 500)
    },
    // 下载方法api
    downloadApi(params = {}) {
      this.loading = true
      downloadTableData(params).then((res) => {
        if (res) {
          this.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    // 模糊查询
    inputQuery() {
      this.data.debounce.query()
    },
    // 查询事件总数
    queryEventRelevanceTotalData(
      params = {
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.data.totalLoading = true
      queryRelevanceTableTotal(params).then((res) => {
        this.data.total = res
        this.data.totalLoading = false
      })
    },
    // 滚动查询关联事件
    scrollEventRelevanceTable() {
      const lastRow = this.data.table[this.data.table.length - 1]
      let params = {}
      if (lastRow) {
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.tempParams, {
            pageSize: this.pagination.pageSize,
            eventId: lastRow.eventId,
            timestamp: lastRow.createDate,
          })
        } else {
          params = Object.assign(
            {},
            {
              pageSize: this.pagination.pageSize,
              fuzzyField: this.query.fuzzyField,
              eventId: lastRow.eventId,
              timestamp: lastRow.createDate,
            }
          )
        }
      }
      this.getRelevanceTable(params)
    },
    // 页面初始化调用查询关联事件
    getRelevanceTable(
      params = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.data.scroll = true
      this.data.loading = true
      queryRelevanceTableData(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.push(...res)
          this.data.scroll = true
          if (this.data.table.length > this.pagination.pageSize) {
            this.data.nomore = true
          }
        } else {
          this.data.table.push(...res)
          this.data.scroll = false
        }
        this.data.loading = false
      })
    },
    // 查询事件分类
    queryEventTypeName() {
      queryEventType().then((res) => {
        this.options.eventNameOption = res
      })
    },
    // 查询自定义列
    queryColumns() {
      this.show.tableShow = false
      queryColumnsData().then((res) => {
        if (res.length !== 0) {
          this.options.columnOption = res
        }
        setTimeout(() => {
          this.show.tableShow = true
        }, 100)
      })
    },
    // 点击打开抽屉
    clickDetailDrawer(row) {
      this.drawer.visible = true
      this.drawer.data = row
    },
    // 点击下载按钮
    clickDownloadButton() {
      this.data.debounce.downloadDebounce()
    },
    // 点击向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initChangeDebounce()
      this.resetQueryForm()
    },
    // 点击高级查询按钮
    clickQueryButton() {
      this.query.fuzzyField = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initChangeDebounce()
      this.resetQueryForm()
    },
    // 点击自定义列按钮
    clickSubmitCustomize(formModel) {
      updateColumnsData(formModel.checkList).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.queryColumns()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 点击提交自定义列按钮
    clickCustomizeButton() {
      this.dialog.columnDialog.visible = true
      queryColumnsData().then((res) => {
        if (res.length === 7) {
          this.dialog.columnDialog.form.model.checkList = res
          this.dialog.columnDialog.form.model.checkAll = true
          this.dialog.columnDialog.form.model.isIndeterminate = false
        } else if (res.length === 0) {
          this.dialog.columnDialog.form.model.checkList = [
            'eventCategoryName',
            'eventTypeName',
            'policyName',
            'level',
            'count',
            'createDate',
            'updateDate',
          ]
          this.dialog.columnDialog.form.model.checkAll = true
          this.dialog.columnDialog.form.model.isIndeterminate = false
        } else {
          this.dialog.columnDialog.form.model.checkList = res
          this.dialog.columnDialog.form.model.checkAll = false
          this.dialog.columnDialog.form.model.isIndeterminate = true
        }
      })
    },
    // 点击详情
    clickDetailButton({ eventId, createDate }) {
      this.dialog.detailDialog.loading = true
      queryRelevanceTableDetail(eventId, createDate).then((res) => {
        this.dialog.detailDialog.form.model = res
        this.dialog.detailDialog.loading = false
      })
      this.dialog.detailDialog.visible = true
    },
    // 重置查询信息
    resetQueryForm() {
      this.data.debounce.resetQueryDebounce()
    },
    // 提交高级查询
    submitQueryForm() {
      this.data.debounce.query()
    },
    // 选中列表数据
    selectsChange(select) {
      this.data.selected = select
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
</style>
