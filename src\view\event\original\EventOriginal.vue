<!--
 * @Description: 原始日志
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <section class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="query.high === 'fuzzy'" class="table-header-search-input">
            <el-input
              v-model="query.fuzzyField"
              prefix-icon="soc-icon-search"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('event.original.fuzzyQuery')])"
              @change="changeQueryOriginalLog"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="query.high === 'fuzzy'" v-has="'query'" @click="changeQueryOriginalLog">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-if="query.high === 'exact' || query.high === 'fuzzy'" v-has="'query'" @click="clickExactQuery">
              {{ $t('button.search.exact') }}
              <i :class="query.high === 'exact' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
            <el-button v-if="query.high == 'advance' || query.high === 'fuzzy'" v-has="'query'" @click="clickAdvanceQuery">
              {{ $t('button.search.advance') }}
              <i :class="query.high === 'advance' ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
            <el-select
              v-if="query.high === 'exact'"
              v-model="query.customCondition"
              :placeholder="$t('button.customQuery')"
              clearable
              @change="changeCustomCondition"
            >
              <el-option v-for="item in option.customCondition" :key="item.value" :label="item.label" :value="item.value">
                <span>{{ item.label }}</span>
                <span class="del-custom-item" style="display: inline-block;" @click="deleteCustomCondition(item)">
                  <i class="el-icon-remove" title="删除" style="color: #f56c6c;"></i>
                </span>
              </el-option>
            </el-select>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'handle'" @click="clickLogForward">
            {{ $t('button.forward') }}
          </el-button>
          <el-button v-has="'query'" @click="clickCustomColumn" style="height: 32px;">
            {{ $t('button.th') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <section v-show="query.high === 'exact'" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.form.model.type2Name"
                  clearable
                  :placeholder="$t('event.original.label.type2Name')"
                  maxlength="100"
                  @change="changeQueryOriginalLog"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.form.model.name"
                  clearable
                  filterable
                  :placeholder="$t('event.original.label.logName')"
                  @change="changeQueryOriginalLog()"
                >
                  <el-option v-for="item in option.eventNames" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.form.model.level"
                  clearable
                  :placeholder="$t('event.original.label.level')"
                  @change="changeQueryOriginalLog"
                >
                  <el-option v-for="item in option.level" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-date-picker
                  v-model="query.form.model.date"
                  type="date"
                  value-format="yyyy-MM-dd"
                  :placeholder="$t('event.original.placeholder.logRecieveTime')"
                  @change="changeQueryOriginalLog"
                ></el-date-picker>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="10">
                <range-picker
                  v-model="query.form.model.srcIP"
                  type="ip"
                  :start-placeholder="$t('event.original.placeholder.srcStartIP')"
                  :end-placeholder="$t('event.original.placeholder.srcEndIP')"
                  @change="changeQueryOriginalLog"
                ></range-picker>
              </el-col>
              <el-col :span="10">
                <range-picker
                  v-model="query.form.model.dstIP"
                  type="ip"
                  :start-placeholder="$t('event.original.placeholder.dstStartIP')"
                  :end-placeholder="$t('event.original.placeholder.dstEndIP')"
                  @change="changeQueryOriginalLog"
                ></range-picker>
              </el-col>
            </el-row>
            <!-- <el-row :gutter="20">
              <el-col :span="10">
                <range-picker
                  v-model="query.form.model.srcMac"
                  type="mac"
                  :start-placeholder="$t('event.original.placeholder.srcStartMac')"
                  :end-placeholder="$t('event.original.placeholder.srcEndMac')"
                  @change="changeQueryOriginalLog"
                ></range-picker>
              </el-col>
              <el-col :span="10">
                <range-picker
                  v-model="query.form.model.dstMac"
                  type="mac"
                  :start-placeholder="$t('event.original.placeholder.dstStartMac')"
                  :end-placeholder="$t('event.original.placeholder.dstEndMac')"
                  @change="changeQueryOriginalLog"
                ></range-picker>
              </el-col>
            </el-row> -->
            <el-row :gutter="20">
              <el-col :span="10">
                <range-picker
                  v-model="query.form.model.dstDevice"
                  type="ip"
                  :start-placeholder="$t('event.original.placeholder.fromStartIP')"
                  :end-placeholder="$t('event.original.placeholder.fromEndIP')"
                  @change="changeQueryOriginalLog"
                ></range-picker>
              </el-col>
              <el-col :span="5">
                <el-cascader
                  ref="cascader"
                  v-model="query.form.model.srcDevice"
                  filterable
                  clearable
                  :options="option.srcDevice"
                  :placeholder="$t('event.original.label.srcDevice')"
                  :props="{ expandTrigger: 'hover' }"
                  @change="changeQueryOriginalLog"
                ></el-cascader>
              </el-col>
              <!--                            <el-col :span="5">
                                <el-date-picker
                                    v-model="query.form.model.logTime"
                                    type="date"
                                    value-format="yyyy-MM-dd"
                                    :placeholder="$t('event.original.placeholder.logTime')"
                                    @change="changeQueryOriginalLog">
                                </el-date-picker>
                            </el-col>-->
              <el-col :span="5">
                <el-input
                  v-model.trim="query.form.model.username"
                  clearable
                  :placeholder="$t('event.original.placeholder.username')"
                  maxlength="255"
                  @change="changeQueryOriginalLog"
                ></el-input>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.form.model.targetObject"
                  clearable
                  :placeholder="$t('event.original.placeholder.targetObject')"
                  maxlength="255"
                  @change="changeQueryOriginalLog"
                ></el-input>
              </el-col>
              <el-col align="right" :span="19">
                <el-button v-has="'add'" @click="saveQueryConditions">
                  {{ $t('button.save') }}
                </el-button>
                <el-button v-has="'query'" @click="changeQueryOriginalLog">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="clickResetQueryLogForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button @click="clickShrinkHighQuery">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </section>
        </el-collapse-transition>

        <el-collapse-transition>
          <section v-if="query.high === 'advance'" class="table-header-query">
            <el-row>
              <expression-autocomplete :data="data.advance" @on-change="changeExpressionAutocomplete"></expression-autocomplete>
            </el-row>
          </section>
        </el-collapse-transition>
      </section>
    </section>
    <section class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('event.original.name') }}
        </h2>
        <section class="table-body-button"></section>
      </header>
      <main class="table-body-main">
        <el-table
          v-if="table.keep"
          ref="originalTable"
          v-loading="table.loading"
          v-el-table-scroll="scrollOriginalLogTable"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          :data="data.table.body"
          highlight-current-row
          tooltip-effect="light"
          fit
          height="100%"
          @current-change="originalTableRowChange"
        >
          <el-table-column width="50" type="index"></el-table-column>
          <template v-for="(col, index) in data.table.header">
            <el-table-column v-if="col.check" :key="index" :label="col.label" :prop="col.key" show-overflow-tooltip sortable>
              <template slot-scope="scope">
                <level-tag v-if="col.key === 'level' || col.key === 'levelName'" :level="scope.row[col.key]"></level-tag>
                <p v-else>
                  {{ scope.row[col.key] }}
                </p>
              </template>
            </el-table-column>
          </template>
          <el-table-column width="120" fixed="right">
            <template slot-scope="scope">
              <el-button v-has="'query'" class="el-button--blue" @click="clickOriginalTableDetail(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </section>
    <section class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="table.nomore">{{ $t('validate.data.nomore') }}</span>
      </section>
      <section class="infinite-scroll-total">
        <!--{{ $t('event.original.parseRateDesc', [data.table.parseRate]) }}，-->
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span v-loading="total.loading">{{ data.table.total }}</span>
      </section>
    </section>
    <custom-column
      :visible.sync="dialog.visible.column"
      :title="dialog.title.column"
      :form-data="data.table.header"
      @on-submit="clickSubmitCustomColumn"
    ></custom-column>
    <detail-drawer :visible.sync="dialog.visible.detail" :loading="data.table.loading" :detail-data="data.table.detail"></detail-drawer>
    <log-forward-dialog
      :visible.sync="dialog.forward.visible"
      :title-name="title"
      :forward-server-option="option.forwardServer"
      :collector-server-option="option.collectorServer"
      :event-type-option="option.eventNames"
      :event-level-option="option.level"
      :device-type-option="option.srcDevice"
      @on-submit="updateLogForward"
    ></log-forward-dialog>
    <custom-query-dialog
      :visible.sync="dialog.custom.visible"
      :title="title"
      :custom-name="query.customName"
      @on-submit="clickSubmitCustomQuery"
    ></custom-query-dialog>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import LevelTag from '@comp/LevelTag'
import ExpressionAutocomplete from '@comp/ExpressionAutocomplete'
import RangePicker from '@comp/RangePicker'
import CustomColumn from './TheCustomColumnDialog'
import DetailDrawer from './TheLogDetailDrawer'
import logColumn from '@asset/js/attr/original-log-column'
import LogForwardDialog from './TheLogForwardDialog'
import CustomQueryDialog from './TheCustomQueryDialog'
import { parseTime } from '@util/format'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import { isEmpty } from '@util/common'
import {
  queryOriginalLogColumnData,
  queryOriginalLogTableData,
  queryOriginalLogTotalData,
  queryAssetTypeData,
  updateOriginalLogColumnData,
  queryEventType,
  queryForwardServerCombo,
  queryollectorServerCombo,
  updateLogForward,
  queryRarseRate,
  saveCustomQuery,
  queryCustomQueryCombo,
  queryCustomQueryCondition,
  deleteCustomCondition,
  queryAdvanceCombo,
} from '@api/event/original-api'

export default {
  name: 'EventOriginal',
  directives: {
    elTableScroll,
  },
  components: {
    ExpressionAutocomplete,
    LevelTag,
    RangePicker,
    CustomColumn,
    DetailDrawer,
    LogForwardDialog,
    CustomQueryDialog,
  },
  data() {
    return {
      title: this.$t('event.original.name'),
      total: {
        loading: false,
      },
      table: {
        loading: false,
        keep: true,
        scroll: true,
        nomore: false,
      },
      query: {
        high: 'fuzzy',
        expression: '',
        fuzzyField: '',
        resetQueryDebounce: null,
        form: {
          model: {
            name: '',
            level: '',
            srcDevice: '',
            dstDevice: ['', ''],
            srcIP: ['', ''],
            dstIP: ['', ''],
            date: '',
            // logTime: "",
            username: '',
            targetObject: '',
            requestUrl: '',
            statusCode: '',
          },
        },
        customCondition: '',
        customName: '',
      },
      option: {
        level: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        srcDevice: [],
        eventNames: [],
        forwardServer: [],
        collectorServer: [],
        customCondition: [],
      },
      data: {
        advance: [],
        table: {
          total: 0,
          header: [],
          body: [],
          detail: {},
          loading: false,
          debounce: null,
          parseRate: 0,
        },
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        currentRow: {},
      },
      dialog: {
        visible: {
          column: false,
          detail: false,
        },
        title: {
          column: this.$t('event.original.name') + this.$t('button.th'),
        },
        forward: {
          visible: false,
          model: {},
        },
        custom: {
          visible: false,
        },
      },
      refresh: {
        timer: null,
        duration: 5000,
      },
    }
  },
  computed: {
    defaultTimeRange() {
      const date = new Date()
      const [morning, now] = [parseTime(date.getTime(), '{y}-{m}-{d} 00:00:00'), parseTime(date.getTime())]
      return [morning, now]
    },
    disableScroll() {
      return this.table.scroll
    },
    timeRange() {
      let range = []
      if (this.query.form.model.date !== '' && this.query.form.model.date !== null) {
        range = [`${this.query.form.model.date} 00:00:00`, `${this.query.form.model.date} 23:59:59`]
      }
      return range
    },
  },
  mounted() {
    this.initOption()
    this.initQueryDebounce()
    this.initResetDebounce()
    if (this.$route.query.dstIP || this.$route.query.srcIP) {
      this.query.high = 'exact'
      this.query.form.model.srcIP = [this.$route.query.srcIP || '', '']
      this.query.form.model.dstIP = [this.$route.query.dstIP || '', '']
    }
    this.data.table.debounce()
  },
  methods: {
    initOption() {
      // 事件类型下拉数据
      queryEventType().then((res) => {
        this.option.eventNames = res
      })
      queryForwardServerCombo().then((res) => {
        this.option.forwardServer = res
      })
      queryollectorServerCombo().then((res) => {
        this.option.collectorServer = res.map((item) => {
          return { label: item.collectorName ? `${item.ip}(${item.collectorName})` : item.ip, value: item.devId }
        })
      })
      queryAdvanceCombo().then((res) => {
        this.data.advance = res
      })
      this.queryCustomOption()
      this.getAssetType()
      this.getOriginalLogColumn()
    },
    queryCustomOption() {
      queryCustomQueryCombo().then((res) => {
        this.option.customCondition = res
      })
    },
    initResetDebounce() {
      this.query.resetQueryDebounce = debounce(() => {
        this.table.nomore = false
        this.query.customCondition = ''
        this.query.customName = ''
        this.query.fuzzyField = ''
        this.query.expression = ''
        this.data.table.body = []
        this.query.form.model = {
          name: '',
          level: '',
          srcDevice: '',
          dstDevice: ['', ''],
          srcIP: ['', ''],
          dstIP: ['', ''],
          date: '',
          // logTime: "",
          username: '',
          targetObject: '',
        }
        this.pagination.pageNum = 1
        // setTimeout(() => {
        //     this.getOriginalLogTable();
        //     this.getOriginalLogTotal();
        // }, 150);
      }, 500)
    },
    refreshParseRate() {
      this.refresh.timer = setInterval(() => {
        this.getRarseRate()
      }, this.refresh.duration)
    },
    clearAllInterval() {
      clearInterval(this.refresh.timer)
      this.refresh.timer = null
    },
    clickOriginalTableDetail(row) {
      this.data.table.detail = row
      this.dialog.visible.detail = true
    },
    clickCustomColumn() {
      this.dialog.visible.column = true
    },
    clickSubmitCustomColumn(data) {
      const param = []
      data.forEach((item) => {
        if (item.check) {
          param.push(item.key)
        }
      })
      this.updateOriginalLogColumn(param)
    },
    saveQueryConditions() {
      this.dialog.custom.visible = true
      if (isEmpty(this.query.customCondition)) {
        this.query.customName = ''
      }
    },
    clickSubmitCustomQuery(formModel) {
      const param = {
        timeRange: this.timeRange.toString(),
        type2Name: this.query.form.model.type2Name,
        eventName: this.query.form.model.name,
        level: this.query.form.model.level,
        sourceIp: this.ipRange(this.query.form.model.srcIP),
        targetIp: this.ipRange(this.query.form.model.dstIP),
        fromIp: this.ipRange(this.query.form.model.dstDevice),
        deviceType: this.query.form.model.srcDevice.toString(),
        label: formModel.customQueryName,
        id: isEmpty(this.query.customCondition) ? '' : this.query.customCondition,
        username: this.query.form.model.username,
        targetObject: this.query.form.model.targetObject,
      }
      this.saveCustomQuery(param)
    },
    changeCustomCondition() {
      const params = this.query.customCondition
      if (!isEmpty(params)) {
        this.getCustomQueryCondition()
      } else {
        this.clickResetQueryLogForm()
      }
    },
    clickExactQuery() {
      this.query.fuzzyField = ''
      this.query.high = this.query.high === 'exact' ? 'fuzzy' : 'exact'
      this.clickResetQueryLogForm()
    },
    clickAdvanceQuery() {
      this.query.fuzzyField = ''
      this.query.high = this.query.high === 'advance' ? 'fuzzy' : 'advance'
      this.clickResetQueryLogForm()
    },
    changeQueryOriginalLog() {
      this.data.table.debounce()
    },
    clickResetQueryLogForm() {
      this.query.resetQueryDebounce()
      this.changeQueryOriginalLog()
    },
    clickShrinkHighQuery() {
      this.query.high = 'fuzzy'
      this.clickResetQueryLogForm()
    },
    changeExpressionAutocomplete(keyword) {
      this.query.expression = keyword
      this.changeQueryOriginalLog()
    },
    scrollOriginalLogTable() {
      let param = {
        pageSize: this.pagination.pageSize,
      }
      if (this.query.high === 'exact') {
        param = Object.assign(param, {
          timeRange: this.timeRange.toString(),
          // logTime: this.query.form.model.logTime,
          type2Name: this.query.form.model.type2Name,
          eventName: this.query.form.model.name,
          level: this.query.form.model.level,
          sourceIp: this.ipRange(this.query.form.model.srcIP),
          targetIp: this.ipRange(this.query.form.model.dstIP),
          fromIp: this.ipRange(this.query.form.model.dstDevice),
          deviceType: this.query.form.model.srcDevice.toString(),
          username: this.query.form.model.username,
          targetObject: this.query.form.model.targetObject,
          id: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['id'] : '',
          timestamp: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['timestamp'] : '',
          scrollToken: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['scrollToken'] : '',
        })
      }
      if (this.query.high === 'fuzzy') {
        param = Object.assign(param, {
          fuzzyField: this.query.fuzzyField,
          id: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['id'] : '',
          timestamp: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['timestamp'] : '',
          scrollToken: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['scrollToken'] : '',
        })
      }
      if (this.query.high === 'advance') {
        param = Object.assign(param, {
          expressionCondition: this.query.expression,
          id: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['id'] : '',
          timestamp: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['timestamp'] : '',
          scrollToken: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['scrollToken'] : '',
        })
      }

      this.getOriginalLogTable(param)
    },
    ipRange(ipArr) {
      let ip = ''
      ipArr = ipArr.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipArr.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    originalTableRowChange(row) {
      this.pagination.currentRow = row
    },
    initQueryDebounce() {
      this.data.table.debounce = debounce(() => {
        this.table.nomore = false
        this.data.table.body = []
        let param = {
          pageSize: this.pagination.pageSize,
        }
        if (this.query.high === 'exact') {
          param = Object.assign(param, {
            timeRange: this.timeRange.toString(),
            type2Name: this.query.form.model.type2Name,
            eventName: this.query.form.model.name,
            level: this.query.form.model.level,
            sourceIp: this.ipRange(this.query.form.model.srcIP),
            targetIp: this.ipRange(this.query.form.model.dstIP),
            fromIp: this.ipRange(this.query.form.model.dstDevice),
            deviceType: this.query.form.model.srcDevice.toString(),
            // logTime: this.query.form.model.logTime,
            username: this.query.form.model.username,
            targetObject: this.query.form.model.targetObject,
          })
        }
        if (this.query.high === 'fuzzy') {
          param = Object.assign(param, {
            fuzzyField: this.query.fuzzyField,
          })
        }
        if (this.query.high === 'advance') {
          param = Object.assign(param, {
            expressionCondition: this.query.expression,
            id: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['id'] : '',
            timestamp: this.data.table.body.length > 0 ? this.data.table.body[this.data.table.body.length - 1]['timestamp'] : '',
          })
        }
        console.log('initQueryDebounce')
        this.getOriginalLogTable(param)
        this.getOriginalLogTotal(param)
      }, 500)
    },
    clickLogForward() {
      this.dialog.forward.visible = true
    },
    updateLogForward(formModel) {
      const params = Object.assign({}, formModel)
      updateLogForward(params).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.update.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    getAssetType() {
      queryAssetTypeData().then((res) => {
        this.option.srcDevice = res
      })
    },
    getOriginalLogColumn() {
      queryOriginalLogColumnData().then((res) => {
        if (res && res.constructor === Array) {
          if (res.length > 0) {
            logColumn.forEach((item) => {
              item.check = false
              res.forEach((value) => {
                if (item.key === value) {
                  item.check = true
                }
              })
            })
          } else {
            logColumn[0]['check'] = true
          }
        }
        this.data.table.header = logColumn
      })
    },
    getOriginalLogTable(
      param = {
        pageSize: this.pagination.pageSize,
      }
    ) {
      this.table.scroll = true
      this.table.loading = true
      queryOriginalLogTableData(param).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.body.push(...res)
          this.table.scroll = true
          if (this.data.table.body.length > this.pagination.pageSize) {
            this.table.nomore = true
          }
        } else {
          this.data.table.body.push(...res)
          this.table.scroll = false
        }
        this.table.loading = false
        this.$nextTick(() => {
          this.$refs.originalTable.doLayout()
        })
      })
    },
    getOriginalLogTotal(
      param = {
        pageSize: this.pagination.pageSize,
      }
    ) {
      this.total.loading = true
      queryOriginalLogTotalData(param).then((res) => {
        this.total.loading = false
        this.data.table.total = res
      })
    },
    updateOriginalLogColumn(values) {
      this.table.keep = false
      updateOriginalLogColumnData(values).then((res) => {
        this.table.keep = true
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getOriginalLogExpression()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    getRarseRate() {
      queryRarseRate().then((res) => {
        if (!isNaN(res)) {
          this.data.table.parseRate = res
        }
      })
    },
    saveCustomQuery(obj) {
      saveCustomQuery(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.save.success',
              type: 'success',
            },
            () => {
              this.clickResetQueryLogForm()
              this.queryCustomOption()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeatName',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    getCustomQueryCondition() {
      const params = { id: this.query.customCondition }
      queryCustomQueryCondition(params).then((res) => {
        if (res) {
          this.query.form.model = Object.assign(
            {},
            {
              name: res.eventName,
              type2Name: res.type2Name,
              level: res.level,
              date: res.timeRange !== '' ? res.timeRange.substring(0, 10) : '',
              srcIP: res.sourceIp.split('-'),
              dstIP: res.targetIp.split('-'),
              dstDevice: res.fromIp.split('-'),
              srcDevice: res.deviceType.split(','),
              username: res.username,
              targetObject: res.targetObject,
            }
          )
          this.query.customName = res.label
          this.changeQueryOriginalLog()
        }
      })
    },
    deleteCustomCondition(item) {
      deleteCustomCondition(item.value).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.delete.success',
              type: 'success',
            },
            () => {
              this.queryCustomOption()
              this.query.customCondition = ''
              this.clickResetQueryLogForm()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.delete.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep.infinite-scroll-total {
  .el-loading-spinner {
    .circular {
      height: 25px;
      width: 32px;
      margin-top: 7px;
    }
  }
}

::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}

.router-wrap-table {
  .table-header {
    &-main {
      button + .el-select {
        margin-left: 20px;
        @include theme('background-color', header-search-bg-color);
      }
      .table-header-search-button {
        display: flex;
        flex-wrap: nowrap;
      }
    }
  }
}

.keyword-dropdown-menu {
  &-item {
    display: flex;
    justify-content: space-between;

    span {
      display: inline-block;
      width: auto;
      margin-right: 10px;
    }

    i.el-icon-close {
      height: 0;
      transform: translateY(8px);

      &:hover {
        color: red;
      }
    }
  }
}
</style>
