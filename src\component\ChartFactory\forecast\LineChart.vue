<template>
  <section :id="id" ref="chart" :class="className" :style="{ height: height, width: width }"></section>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import common from '../mixin/common'
import { getColor } from '@util/effect'
import { deepMerge } from '@util/format'

export default {
  mixins: [common],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    proto: {
      type: Boolean,
      default: false,
    },
    option: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      chart: null,
      color: getColor(),
      index: {
        selected: 0,
        hovered: 0,
      },
    }
  },
  watch: {
    option: {
      handler(config) {
        this.configChart(config)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart, this.$store.getters.theme)
    },
    configChart(config = this.option) {
      this.chart.showLoading()
      config && Object.keys(config).length > 0 && config.data.length > 0 ? this.drawChart(config) : this.empty()
      this.chart.hideLoading()
    },
    drawChart(config) {
      const option = this.proto ? config : this.chartOptionConfig(config)
      this.chart.clear()
      this.chart.setOption(option, true)
    },
    chartOptionConfig(config) {
      let option = {
        color: getColor(),
        backgroundColor: 'transparent',
        legend: {
          show: config.data.length > 1,
          type: 'scroll',
          top: 0,
          left: 'center',
        },
        grid: {
          top: config.data.length > 1 ? 50 : 20,
          left: 20,
          right: 20,
          bottom: 10,
          containLabel: true,
        },
        tooltip: {
          appendToBody: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
          },
        },
        dataset: {
          source: config.data,
        },
      }
      option = deepMerge(option, this.chartAxisConfig(config))
      option = deepMerge(option, this.chartSeriesConfig(config))
      if (config.data.length === 2) {
        option.tooltip.formatter = (param) => {
          const currentData = param[0]
          return `${currentData.marker} ${currentData.value[0]} ${currentData.value[1]}`
        }
      }
      if (this.option.custom) {
        option = deepMerge(option, this.option.custom)
      }
      return option
    },
    chartAxisConfig(config) {
      const axis = {
        xAxis: {
          axisLabel: {
            formatter(param) {
              return param.length > 15 ? param.slice(0, 15) + '...' : param
            },
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          axisLabel: {
            formatter(param) {
              return param.length > 15 ? param.slice(0, 15) + '...' : param
            },
          },
        },
      }
      config.axis = config.axis || 1
      if (config.axis === 'x' || config.axis === 1) {
        axis.xAxis.type = 'category'
        axis.yAxis.type = 'value'
      }

      if (config.axis === 'y' || config.axis === 2) {
        axis.xAxis.type = 'value'
        axis.yAxis.type = 'category'
      }

      return axis
    },
    chartSeriesConfig(config) {
      config.type = config.type || 1
      if (config.type === 'line' || config.type === 1) {
        return this.getLineSeries(config)
      }
      if (config.type === 'line-stack' || config.type === 2) {
        return this.getLineStackSeries(config)
      }
      if (config.type === 'line-step' || config.type === 3) {
        return this.getLineStepSeries(config)
      }
      if (config.type === 'line-stack-step' || config.type === 4) {
        return this.getLineStackStepSeries(config)
      }
    },
    getLineSeries(config) {
      const color = getColor()
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        return {
          type: 'line',
          seriesLayoutBy: 'row',
          smooth: true,
          showSymbol: false,
          lineStyle: {
            width: 1,
            color: color[index],
            shadowColor: color[index].colorToRgb(0.6),
            shadowBlur: 2,
          },
          itemStyle: {
            color: color[index],
            borderColor: color[index],
          },
        }
      })
      return {
        series: series,
      }
    },
    getLineStackSeries(config) {
      const color = getColor()
      const y = config.axis === 'y' || config.axis === 2
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        return {
          type: 'line',
          seriesLayoutBy: 'row',
          stack: 'total',
          smooth: true,
          showSymbol: false,
          label: {
            show: true,
            fontSize: 10,
            position: y ? 'right' : 'top',
          },
          lineStyle: {
            width: 1,
            color: color[index],
            shadowColor: color[index].colorToRgb(0.6),
            shadowBlur: 2,
          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                y ? 1 : 0,
                0,
                0,
                y ? 0 : 1,
                [
                  {
                    offset: 0,
                    color: color[index].colorToRgb(0.3),
                  },
                  {
                    offset: 0.8,
                    color: color[index].colorToRgb(0),
                  },
                ],
                false
              ),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10,
            },
          },
          itemStyle: {
            color: color[index],
            borderColor: color[index],
          },
        }
      })
      return {
        series: series,
      }
    },
    getLineStepSeries(config) {
      const color = getColor()
      const y = config.axis === 'y' || config.axis === 2
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        return {
          type: 'line',
          seriesLayoutBy: 'row',
          step: 'end',
          showSymbol: false,
          label: {
            show: true,
            fontSize: 10,
            position: y ? 'right' : 'top',
          },
          lineStyle: {
            width: 1,
            color: color[index],
            shadowColor: color[index].colorToRgb(0.6),
            shadowBlur: 2,
          },
          itemStyle: {
            color: color[index],
            borderColor: color[index],
          },
        }
      })
      return {
        series: series,
      }
    },
    getLineStackStepSeries(config) {
      const color = getColor()
      const y = config.axis === 'y' || config.axis === 2
      const series = Array.from({ length: config.data.length - 1 }, (item, index) => {
        return {
          type: 'line',
          seriesLayoutBy: 'row',
          step: 'end',
          stack: 'total',
          showSymbol: false,
          label: {
            show: true,
            fontSize: 10,
            position: y ? 'right' : 'top',
          },
          lineStyle: {
            width: 1,
            color: color[index],
            shadowColor: color[index].colorToRgb(0.6),
            shadowBlur: 2,
          },
          areaStyle: {
            normal: {
              color: new echarts.graphic.LinearGradient(
                y ? 1 : 0,
                0,
                0,
                y ? 0 : 1,
                [
                  {
                    offset: 0,
                    color: color[index].colorToRgb(0.3),
                  },
                  {
                    offset: 0.8,
                    color: color[index].colorToRgb(0),
                  },
                ],
                false
              ),
              shadowColor: 'rgba(0, 0, 0, 0.1)',
              shadowBlur: 10,
            },
          },
          itemStyle: {
            color: color[index],
            borderColor: color[index],
          },
        }
      })
      return {
        series: series,
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
