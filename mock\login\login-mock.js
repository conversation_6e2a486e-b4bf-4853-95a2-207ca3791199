const accounts = [
  {
    account: '',
    password: '1',
  },
  {
    account: 'admin',
    password: '1',
  },
  {
    account: 'sysmanager',
    password: '1',
  },
  {
    account: 'sysauditor',
    password: '1',
  },
  {
    account: 'init',
    password: '1',
  },
  {
    account: 'reset',
    password: '1',
  },
  {
    account: 'overdue',
    password: '1',
  },
  {
    account: 'license',
    password: '1',
  },
  {
    account: 'administrator',
    password: '1',
  },
]

const buttonAction = [
  'add',
  'backups',
  'build',
  'create',
  'delete',
  'download',
  'find',
  'grant',
  'handle',
  'ignore',
  'lock',
  'login',
  'logout',
  'query',
  'recover',
  'repeat',
  'reset',
  'save',
  'scan',
  'shut',
  'sort',
  'startup',
  'sync',
  'test',
  'update',
  'upload',
  'restart',
  'shutdown',
]

const menuTree = [
  {
    actions: null,
    menuIcon: 'el-icon-star-on',
    menuId: '0',
    menuName: '参考案例',
    menuLocation: '',
    children: [
      {
        actions: null,
        menuIcon: 'el-icon-star-off',
        menuId: '0-1',
        menuName: '列表案例',
        menuLocation: '',
        children: [
          {
            actions: buttonAction,
            menuIcon: 'soc-icon-point',
            menuId: '0-1-1',
            menuName: '标准列表',
            menuLocation: '/demo/table/normal',
            children: null,
          },
          {
            actions: buttonAction,
            menuIcon: 'soc-icon-point',
            menuId: '0-1-2',
            menuName: '树形列表',
            menuLocation: '/demo/table/tree',
            children: null,
          },
          {
            actions: buttonAction,
            menuIcon: 'soc-icon-point',
            menuId: '0-1-3',
            menuName: '详情列表',
            menuLocation: '/demo/table/detail',
            children: null,
          },
        ],
      },
      {
        actions: null,
        menuIcon: 'el-icon-star-off',
        menuId: '0-2',
        menuName: '图表案例',
        menuLocation: '',
        children: [
          {
            actions: ['add', 'delete', 'update', 'query'],
            menuIcon: 'soc-icon-point',
            menuId: '0-2-1',
            menuName: '柱形图表',
            menuLocation: '/demo/chart/bar',
            children: null,
          },
          {
            actions: ['add', 'delete', 'update', 'query'],
            menuIcon: 'soc-icon-point',
            menuId: '0-2-2',
            menuName: '线形图表',
            menuLocation: '/demo/chart/line',
            children: null,
          },
          {
            actions: ['add', 'delete', 'update', 'query'],
            menuIcon: 'soc-icon-point',
            menuId: '0-2-3',
            menuName: '饼形图表',
            menuLocation: '/demo/chart/pie',
            children: null,
          },
          {
            actions: ['add', 'delete', 'update', 'query'],
            menuIcon: 'soc-icon-point',
            menuId: '0-2-4',
            menuName: '关系图表',
            menuLocation: '/demo/chart/graph',
            children: null,
          },
          {
            actions: ['add', 'delete', 'update', 'query'],
            menuIcon: 'soc-icon-point',
            menuId: '0-2-5',
            menuName: '鱼骨图图表',
            menuLocation: '/demo/chart/fishbone',
            children: null,
          },
        ],
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-setting',
    menuId: '1',
    menuName: '系统配置',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-1',
        menuName: '菜单管理',
        menuLocation: '/management/menu',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-2',
        menuName: '资源管理',
        menuLocation: '/management/resource',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-3',
        menuName: '用户管理',
        menuLocation: '/management/user',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-4',
        menuName: '角色管理',
        menuLocation: '/management/role',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-5',
        menuName: '系统管理',
        menuLocation: '/management/system',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-6',
        menuName: '转发服务器',
        menuLocation: '/management/forward-server',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-7',
        menuName: '系统升级管理',
        menuLocation: '/management/system-upgrade',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-8',
        menuName: '网络管理',
        menuLocation: '/management/network',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-9',
        menuName: '日志审计',
        menuLocation: '/management/log-audit',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-10',
        menuName: '日志备份',
        menuLocation: '/management/log-backup',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '1-11',
        menuName: '代理服务器',
        menuLocation: '/management/proxy-server',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-asset',
    menuId: '2',
    menuName: '资产',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '2-0',
        menuName: '区域管理',
        menuLocation: '/asset/area',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '2-1',
        menuName: '资产管理',
        menuLocation: '/asset/management',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '2-2',
        menuName: '资产类型',
        menuLocation: '/asset/type',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '2-3',
        menuName: '自定义资产属性',
        menuLocation: '/asset/custom',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '2-4',
        menuName: '资产发现',
        menuLocation: '/asset/discover',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '2-5',
        menuName: '网络管理',
        menuLocation: '/asset/network-management',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-event',
    menuId: '3',
    menuName: '事件',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-1',
        menuName: '安全事件',
        menuLocation: '/event/security',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-2',
        menuName: '关联事件',
        menuLocation: '/event/relevance',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-3',
        menuName: '聚合策略',
        menuLocation: '/event/polymerization-strategy',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-4',
        menuName: '关联策略',
        menuLocation: '/event/relevance-strategy',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-5',
        menuName: '原始日志',
        menuLocation: '/event/original',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-6',
        menuName: '事件关联图',
        menuLocation: '/event/original-les',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-7',
        menuName: '威胁事件',
        menuLocation: '/event/threat',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-8',
        menuName: '故障事件',
        menuLocation: '/event/fault',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-9',
        menuName: '性能事件',
        menuLocation: '/event/performance',
        children: null,
      },
      {
        actions: ['download', 'add', 'delete', 'update', 'query'],
        menuIcon: 'soc-icon-point',
        menuId: '3-10',
        menuName: '通用日志',
        menuLocation: '/event/general-log',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-11',
        menuName: '自定义解析',
        menuLocation: '/event/custom-parse',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '3-12',
        menuName: '事件特征值',
        menuLocation: '/event/custom-code',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-audit',
    menuId: '4',
    menuName: '审计',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '4-1',
        menuName: '审计事件',
        menuLocation: '/audit/event',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '4-2',
        menuName: '审计策略',
        menuLocation: '/audit/strategy',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '4-3',
        menuName: '审计类型',
        menuLocation: '/audit/type',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '4-4',
        menuName: '审计人员',
        menuLocation: '/audit/person',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '4-5',
        menuName: '行为分析策略',
        menuLocation: '/audit/behavior-strategy',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-waring',
    menuId: '5',
    menuName: '告警',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '5-1',
        menuName: '告警列表',
        menuLocation: '/alarm/table',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '5-2',
        menuName: '告警策略',
        menuLocation: '/alarm/strategy',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '5-3',
        menuName: '系统告警',
        menuLocation: '/alarm/system',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '5-4',
        menuName: '异常行为告警',
        menuLocation: '/alarm/abnormal-behavior',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-collect',
    menuId: '6',
    menuName: '采集',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '6-1',
        menuName: '采集器管理',
        menuLocation: '/collector/management',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '6-2',
        menuName: '采集过滤策略',
        menuLocation: '/collector/strategy',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '6-3',
        menuName: '日志源类型',
        menuLocation: '/collector/log-source',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-repository',
    menuId: '7',
    menuName: '知识库',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '7-1',
        menuName: 'CVE漏洞库',
        menuLocation: '/repository/cve',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '7-2',
        menuName: 'CNVD漏洞库',
        menuLocation: '/repository/cnvd',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '7-3',
        menuName: '威胁情报库',
        menuLocation: '/repository/threat',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-report',
    menuId: '8',
    menuName: '报表',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '8-1',
        menuName: '报表实例',
        menuLocation: '/report/instance',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '8-2',
        menuName: '报表任务',
        menuLocation: '/report/task',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '8-3',
        menuName: '报表管理',
        menuLocation: '/report/show',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-view',
    menuId: '9',
    menuName: '可视化',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '9-1',
        menuName: '大屏管理',
        menuLocation: '/visualization/management',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '9-2',
        menuName: '仪表盘',
        menuLocation: '/visualization/dashboard',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '9-3',
        menuName: '合规审计',
        menuLocation: '/visualization/compliance-audit',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-asset',
    menuId: '10',
    menuName: '监控',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '10-1',
        menuName: '监控器',
        menuLocation: '/monitor/management',
        children: null,
      },
    ],
  },
  {
    actions: null,
    menuIcon: 'soc-icon-asset',
    menuId: '11',
    menuName: '预测',
    menuLocation: '',
    children: [
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '10-1',
        menuName: '预测分析',
        menuLocation: '/forecast/forecast-analysis',
        children: null,
      },
      {
        actions: buttonAction,
        menuIcon: 'soc-icon-point',
        menuId: '10-2',
        menuName: '预测告警',
        menuLocation: '/forecast/forecast-alarm',
        children: null,
      },
    ],
  },
]

module.exports = [
  {
    url: '/authentication/register',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: {
          accessToken: '@GUID',
          publicKey: '@GUID',
        },
      }
    },
  },
  {
    url: '/authentication/captcha',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: false,
      }
    },
  },
  {
    url: '/authentication/login',
    type: 'get',
    response: (option) => {
      const account = option.query.account
      let loginState = 2
      accounts.forEach((item) => {
        if (item.account === account) {
          switch (account) {
            case 'init':
              loginState = 6
              break
            case 'reset':
              loginState = 5
              break
            case 'overdue':
              loginState = 7
              break
            case 'license':
              loginState = 10
              break
            case 'administrator':
              loginState = 11
              break
            default:
              loginState = 1
              break
          }
        }
      })

      return {
        code: 200,
        data: {
          result: loginState,
          message: '0F3CCCF0-4D6921AE-0041907F-0D4F5FC3',
        },
      }
    },
  },
  {
    url: '/menumanagement/menu/navigation',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: menuTree,
      }
    },
  },
  {
    url: '/usermanagement/user/extend/init',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/usermanagement/password',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
]
