<!--
 * @Description: 关联策略 - 复制弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-02-11
 * @Editor:
 * @EditDate: 2022-02-11
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.copy', [title])"
    width="30%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formTemplate" :model="model" :rules="rules" label-width="80px">
      <el-row>
        <el-form-item prop="policyName" :label="$t('event.relevanceStrategy.table.alarmName')">
          <el-input v-model.trim="model.policyName"></el-input>
        </el-form-item>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: <PERSON>olean,
    },
    title: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      rules: {
        policyName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
      },
      model: {
        policyName: '',
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.model.policyName = ''
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
  },
}
</script>
