<!--
 * @Description: 角色管理 - 授权弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :sel="sel"
    :width="width"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmit"
  >
    <section class="table-header">
      <el-input
        v-model.trim="data.filterRole"
        maxlength="32"
        :placeholder="$t('tip.component.searchKeywords')"
        :inline="true"
        clearable
        @change="clickQueryRole"
      >
        <i slot="suffix" class="el-input__icon soc-icon-search" @click="clickQueryRole"></i>
      </el-input>
    </section>
    <section class="table-body">
      <el-table
        ref="userTable"
        v-loading="data.loading"
        :data="data.table"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="300"
        @selection-change="roleTableSelectsChange"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column prop="label" :label="$t('management.role.infoItem.userName')" show-overflow-tooltip></el-table-column>
      </el-table>
    </section>
  </custom-dialog>
</template>
<script>
import CustomDialog from '@comp/CustomDialog'
import { getUsersCombo } from '@api/management/role-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    sel: {
      type: Array,
      default() {
        return []
      },
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      data: {
        filterRole: '',
        table: [],
        allData: [],
        loading: false,
        selected: [],
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
    sel(userId) {
      this.toggleSelection([...userId], 'userTable')
    },
  },
  mounted() {
    this.getRoleTableData()
  },
  methods: {
    getRoleTableData() {
      this.data.loading = true
      getUsersCombo().then((res) => {
        this.data.table = res
        this.data.allData = res
        this.data.loading = false
      })
    },
    toggleSelection(userId, tableRef) {
      this.data.table.forEach((row) => {
        if (userId.indexOf(row.value) >= 0) {
          this.$nextTick(() => {
            this.$refs[tableRef].toggleRowSelection(row)
          })
        }
      })
    },
    clickQueryRole() {
      const [filter, roles] = [this.data.filterRole.toLowerCase(), [...this.data.allData]]
      if (this.data.filterRole && this.data.filterRole.trim() !== '') {
        this.data.table = roles.filter((item) => {
          return Object.keys(item).some((key) => {
            if (key === 'label')
              return (
                String(item[key])
                  .toLowerCase()
                  .indexOf(filter) > -1
              )
          })
        })
      } else {
        this.getRoleTableData()
      }
    },
    clickCancelDialog() {
      this.getRoleTableData()
      this.data.filterRole = ''
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$confirm(this.$t('tip.confirm.grant'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        const users = []
        this.data.selected.forEach((item) => {
          users.push(item.value)
        })
        this.$emit('on-submit', users)
        this.clickCancelDialog()
      })
      this.$refs.dialogTemplate.end()
    },
    roleTableSelectsChange(select) {
      this.data.selected = select
    },
  },
}
</script>
<style lang="scss" scoped>
.soc-icon-search {
  cursor: pointer;
}

::v-deep .el-table__header {
  width: 100%;
}
</style>
