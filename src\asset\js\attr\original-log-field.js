export default [
  {
    value: 'latitude_ip1',
    label: 'latitude_ip1(源区域纬度)',
  },
  {
    value: 'latitude_ip1.keyword',
    label: 'latitude_ip1.keyword(源区域纬度)',
  },
  {
    value: 'longitude_ip1',
    label: 'longitude_ip1(源区域经度)',
  },
  {
    value: 'longitude_ip1.keyword',
    label: 'longitude_ip1.keyword(源区域经度)',
  },
  {
    value: 'addr3',
    label: 'addr3(其他IP)',
  },
  {
    value: 'categoryName',
    label: 'categoryName(设备类别)',
  },
  {
    value: 'categoryName.keyword',
    label: 'categoryName.keyword(设备类别)',
  },
  {
    value: 'cityName_ip1',
    label: 'cityName_ip1(源区域)',
  },
  {
    value: 'cityName_ip1.keyword',
    label: 'cityName_ip1.keyword(源区域)',
  },
  {
    value: 'code',
    label: 'code(特征值)',
  },
  {
    value: 'code.keyword',
    label: 'code.keyword(特征值)',
  },
  {
    value: 'countryLatitude_ip1',
    label: 'countryLatitude_ip1(源国家纬度)',
  },
  {
    value: 'countryLatitude_ip1.keyword',
    label: 'countryLatitude_ip1.keyword(源国家纬度)',
  },
  {
    value: 'countryLongitude_ip1',
    label: 'countryLongitude_ip1(源国家经度)',
  },
  {
    value: 'countryLongitude_ip1.keyword',
    label: 'countryLongitude_ip1.keyword(源国家经度)',
  },
  {
    value: 'countryName_ip1',
    label: 'countryName_ip1(源国家)',
  },
  {
    value: 'countryName_ip1.keyword',
    label: 'countryName_ip1.keyword(源国家)',
  },
  {
    value: 'day',
    label: 'day(日)',
  },
  {
    value: 'day.keyword',
    label: 'day.keyword(日)',
  },
  {
    value: 'deviceName',
    label: 'deviceName(设备类型)',
  },
  {
    value: 'deviceName.keyword',
    label: 'deviceName.keyword(设备类型)',
  },
  {
    value: 'eventName',
    label: 'eventName(事件名称)',
  },
  {
    value: 'eventName.keyword',
    label: 'eventName.keyword(事件名称)',
  },
  {
    value: 'hour',
    label: 'hour(时)',
  },
  {
    value: 'hour.keyword',
    label: 'hour.keyword(时)',
  },
  {
    value: 'ip0',
    label: 'ip0(发生源IP)',
  },
  {
    value: 'ip0.keyword',
    label: 'ip0.keyword(发生源IP)',
  },
  {
    value: 'ip1',
    label: 'ip1(源IP)',
  },
  {
    value: 'ip2',
    label: 'ip2(目的IP)',
  },
  {
    value: 'ipName_ip1',
    label: 'ipName_ip1(源地址)',
  },
  {
    value: 'ipName_ip1.keyword',
    label: 'ipName_ip1.keyword(源地址)',
  },
  {
    value: 'level',
    label: 'level(事件等级)',
  },
  {
    value: 'minute',
    label: 'minute(分)',
  },
  {
    value: 'minute.keyword',
    label: 'minute.keyword(分)',
  },
  {
    value: 'month',
    label: 'month(月)',
  },
  {
    value: 'month.keyword',
    label: 'month.keyword(月)',
  },
  {
    value: 'port1',
    label: 'port1(源端口)',
  },
  {
    value: 'port2',
    label: 'port2(目的端口)',
  },
  {
    value: 'port3',
    label: 'port3(其他端口)',
  },
  {
    value: 'protocol',
    label: 'protocol(协议)',
  },
  {
    value: 'protocol.keyword',
    label: 'protocol.keyword(协议)',
  },
  {
    value: 'raw',
    label: 'raw(日志原文)',
  },
  {
    value: 'receiveProtocol',
    label: 'receiveProtocol(接收协议)',
  },
  {
    value: 'receiveProtocol.keyword',
    label: 'receiveProtocol.keyword(接收协议)',
  },
  {
    value: 'second',
    label: 'second(秒)',
  },
  {
    value: 'second.keyword',
    label: 'second.keyword(秒)',
  },
  {
    value: 'time',
    label: 'time(日志接收时间)',
  },
  {
    value: 'year',
    label: 'year(年)',
  },
  {
    value: 'year.keyword',
    label: 'year.keyword(年)',
  },
]
