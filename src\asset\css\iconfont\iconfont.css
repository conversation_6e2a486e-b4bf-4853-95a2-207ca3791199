@font-face {
  font-family: 'SocIconFont';
  src: url('../../font/soc-webfont.eot?v=4.7.0'); /* IE9+ */
  src: url('../../font/soc-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'),
    /* IE6-IE8 */ url('../../font/soc-webfont.woff?v=4.7.0') format('woff'),
    /* chrome、firefox */ url('../../font/soc-webfont.ttf?v=4.7.0') format('truetype'); /*ttf和otf写一个即可 同样支持chrome、firefox、opera、Safari, Android, iOS 4.2+*/
  font-weight: normal;
  font-style: normal;
}

[class^='soc-icon'],
[class*=' soc-icon'] {
  display: inline-block;
  font: normal normal normal 14px/1 SocIconFont;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased; /*chrome、safari*/
  -moz-osx-font-smoothing: grayscale; /*firefox*/
}

/*添加*/
.soc-icon-add:before {
  content: '\f067';
}

/*修改*/
.soc-icon-edit:before {
  content: '\f040';
}

/*删除*/
.soc-icon-delete:before {
  content: '\f068';
}

/*批量添加*/
.soc-icon-batch-add:before {
  content: '\f196';
}

/*批量修改*/
.soc-icon-batch-edit:before {
  content: '\f044';
}

/*批量删除*/
.soc-icon-batch-delete:before {
  content: '\f014';
}

/*批量终止*/
.soc-icon-batch-stop:before {
  content: '\f04d';
}

/*查询*/
.soc-icon-search:before {
  content: '\f002';
}

/*放大*/
.soc-icon-search-plus:before {
  content: '\f00e';
}

/*缩小*/
.soc-icon-search-minus:before {
  content: '\f010';
}

/*刷新*/
.soc-icon-refresh:before {
  content: '\f021';
}

/*保存*/
.soc-icon-save:before {
  content: '\f0c7';
}

/*提交*/
.soc-icon-submit:before {
  content: '\f011';
}

/*关闭*/
.soc-icon-close:before {
  content: '\f00d';
}

/*锁定*/
.soc-icon-lock:before {
  content: '\f023';
}

/*解锁*/
.soc-icon-unlock:before {
  content: '\f13e';
}

/*下载*/
.soc-icon-download:before {
  content: '\f019';
}

/*上传*/
.soc-icon-upload:before {
  content: '\f093';
}

/*忽略*/
.soc-icon-ignore:before {
  content: '\f1b8';
}

/*批量忽略*/
.soc-icon-batch-ignore:before {
  content: '\f216';
}

/*自动发现*/
.soc-icon-auto-find:before {
  content: '\f14e';
}

/*生成工单*/
.soc-icon-workflow:before {
  content: '\e018';
}

/*停止*/
.soc-icon-stop:before {
  content: '\f04d';
}

/*批量停止*/
.soc-icon-batch-stop:before {
  content: '\f28e';
}

/*导出PDF*/
.soc-icon-export-pdf:before {
  content: '\f1c1';
}

/*导出WORD*/
.soc-icon-export-word:before {
  content: '\f1c2';
}

/*导出EXCEL*/
.soc-icon-export-excel:before {
  content: '\f1c3';
}

/*导出XML*/
.soc-icon-export-xml:before {
  content: '\f1c9';
}

/*导出图片*/
.soc-icon-export-image:before {
  content: '\f1c5';
}

/*导出文件*/
.soc-icon-export-text:before {
  content: '\f0f6';
}

/*配置*/
.soc-icon-config:before {
  content: '\f013';
}

/*同步*/
.soc-icon-sync:before {
  content: '\f1d9';
}

/*撤销*/
.soc-icon-repeat:before {
  content: '\f01e';
}

/*自定义列*/
.soc-icon-th:before {
  content: '\f00a';
}

/*导航*/
.soc-icon-nav:before {
  content: '\f0c9';
}

/*加号*/
.soc-icon-plus:before {
  content: '\f067';
}

/*减号*/
.soc-icon-minus:before {
  content: '\f068';
}

/*统计图-线图*/
.soc-icon-chart-line:before {
  content: '\f201';
}

/*统计图-柱图*/
.soc-icon-chart-bar:before {
  content: '\f080';
}

/*统计图-饼图*/
.soc-icon-chart-pie:before {
  content: '\f200';
}

/*剪切*/
.soc-icon-cut:before {
  content: '\f0c4';
}

/*复制*/
.soc-icon-copy:before {
  content: '\f0c5';
}

/*粘贴*/
.soc-icon-paste:before {
  content: '\f0ea';
}

/*重做*/
.soc-icon-redo:before {
  content: '\f064';
}

/*撤销*/
.soc-icon-undo:before {
  content: '\f112';
}

/*帮助*/
.soc-icon-ask:before {
  content: '\f29c';
}

/*排序*/
.soc-icon-sort:before {
  content: '\f0dc';
}

/*居中排序-上*/
.soc-icon-caret-up:before {
  content: '\f0d8';
}

/*居中排序-下*/
.soc-icon-caret-down:before {
  content: '\f0d7';
}

/*居中排序-左*/
.soc-icon-caret-left:before {
  content: '\f0d9';
}

/*居中排序-右*/
.soc-icon-caret-right:before {
  content: '\f0da';
}

/*排序-上*/
.soc-icon-sort-up:before {
  content: '\f0de';
}

/*排序-下*/
.soc-icon-sort-down:before {
  content: '\f0dd';
}

/*登录*/
.soc-icon-sign-in:before {
  content: '\f090';
}

/*登出(注销)*/
.soc-icon-sign-out:before {
  content: '\f08b';
}

/*邮件*/
.soc-icon-email:before {
  content: '\f003';
}

/*电话*/
.soc-icon-phone:before {
  content: '\f095';
}

/*手机*/
.soc-icon-mobilephone:before {
  content: '\f10b';
}

/*传真*/
.soc-icon-fax:before {
  content: '\f1ac';
}

/*人员*/
.soc-icon-man:before {
  content: '\f007';
}

/*人员组*/
.soc-icon-man-group:before {
  content: '\f0c0';
}

/*人员-添加*/
.soc-icon-man-plus:before {
  content: '\f234';
}

/*人员-删除*/
.soc-icon-man-minus:before {
  content: '\f235';
}

/*主页*/
.soc-icon-home:before {
  content: '\f015';
}

/*运维监控*/
.soc-icon-sys-monitor:before {
  content: '\f108';
}

/*运维监控*/
.soc-icon-sys-config:before {
  content: '\f085';
}

/*综合展示*/
.soc-icon-sys-show:before {
  content: '\f1a5';
}

/*自定义导航*/
.soc-icon-nva-list:before {
  content: '\f00b';
}

/*修改密码*/
.soc-icon-key:before {
  content: '\f084';
}

/*图形展示*/
.soc-icon-graph:before {
  content: '\f0e8';
}

/*列表展示*/
.soc-icon-grid:before {
  content: '\f0ce';
}

/*附件*/
.soc-icon-attachment:before {
  content: '\f0c6';
}

/*向上*/
.soc-icon-arrow-up:before {
  content: '\f062';
}

/*向下*/
.soc-icon-arrow-down:before {
  content: '\f063';
}

/*向左*/
.soc-icon-arrow-left:before {
  content: '\f060';
}

/*向右*/
.soc-icon-arrow-right:before {
  content: '\f061';
}

/*通知*/
.soc-icon-notice:before {
  content: '\e00b';
}

/*消息*/
.soc-icon-message:before {
  content: '\f086';
}

/*提示信息-警告*/
.soc-icon-tip-warning:before {
  content: '\f071';
}

/*提示信息-消息*/
.soc-icon-tip-info:before {
  content: '\f05a';
}

/*提示信息-错误*/
.soc-icon-tip-error:before {
  content: '\f057';
}

/*安全事件*/
.soc-icon-security-event:before {
  content: '\e005';
}

/*故障事件*/
.soc-icon-fault-event:before {
  content: '\e003';
}

/*性能事件*/
.soc-icon-perf-event:before {
  content: '\e004';
}

/*脆弱性事件*/
.soc-icon-vul-event:before {
  content: '\f188';
}

/*配置事件*/
.soc-icon-deploy-event:before {
  content: '\e001';
}

/*显示密码*/
.soc-icon-eye:before {
  content: '\f06e';
}

/*异常密码*/
.soc-icon-eye-close:before {
  content: '\f070';
}

/*状态*/
.soc-icon-status:before {
  content: '\f111';
}

/*时间*/
.soc-icon-clock:before {
  content: '\f017';
}

/*云*/
.soc-icon-cloud:before {
  content: '\f0c2';
}

/*设置*/
.soc-icon-set:before {
  content: '\f0ad';
}

/*提示*/
.soc-icon-tip:before {
  content: '\f0eb';
}

/*等待*/
.soc-icon-spinner:before {
  content: '\f110';
}

/*上移*/
.soc-icon-scroller-top:before {
  content: '\f106';
}

/*下移*/
.soc-icon-scroller-down:before {
  content: '\f107';
}

/*左移*/
.soc-icon-scroller-left:before {
  content: '\f104';
}

/*右移*/
.soc-icon-scroller-right:before {
  content: '\f105';
}

/*上移-全部*/
.soc-icon-scroller-top-all:before {
  content: '\f102';
}

/*下移-全部*/
.soc-icon-scroller-down-all:before {
  content: '\f103';
}

/*左移-全部*/
.soc-icon-scroller-left-all:before {
  content: '\f100';
}

/*右移-全部*/
.soc-icon-scroller-right-all:before {
  content: '\f101';
}

/*时间范围*/
.soc-icon-history:before {
  content: '\f1da';
}

/*自定义菜单*/
.soc-icon-custommenu:before {
  content: '\e00c';
}

/*风险晴雨表-很低*/
.soc-icon-barometer-vl:before {
  content: '\e010';
}

/*风险晴雨表-低*/
.soc-icon-barometer-l:before {
  content: '\e011';
}

/*风险晴雨表-中*/
.soc-icon-barometer-m:before {
  content: '\e012';
}

/*风险晴雨表-高*/
.soc-icon-barometer-h:before {
  content: '\e013';
}

/*风险晴雨表-很高*/
.soc-icon-barometer-vh:before {
  content: '\e014';
}

/*验证码*/
.soc-icon-captcha:before {
  content: '\e017';
}

/*待办工作*/
.soc-icon-todo:before {
  content: '\e019';
}

/*扫描*/
.soc-icon-scan:before {
  content: '\e01a';
}

/*自定义导出*/
.soc-icon-custom-export:before {
  content: '\e01b';
}

/*快捷菜单-外部链接*/
.soc-icon-shortcut-out:before {
  content: '\f0c1';
}

/*移除*/
.soc-icon-remove:before {
  content: '\f056';
}

/*禁止*/
.soc-icon-disabled:before {
  content: '\f05e';
}

/*下划线*/
.soc-icon-underline:before {
  content: '\f0cd';
}

/*粗体*/
.soc-icon-bold:before {
  content: '\f032';
}

/*斜体*/
.soc-icon-italic:before {
  content: '\f033';
}

/*勾选*/
.soc-icon-check:before {
  content: '\f00c';
}

/*字符边框*/
.soc-icon-textborder:before {
  content: '\e021';
}

/*删除线*/
.soc-icon-strikethrough:before {
  content: '\f0cc';
}

/*上角标*/
.soc-icon-supscript:before {
  content: '\f12b';
}

/*下角标*/
.soc-icon-subscript:before {
  content: '\f12c';
}

/*橡皮擦*/
.soc-icon-rubber:before {
  content: '\f12d';
}

/*字体尺寸*/
.soc-icon-fontsize:before {
  content: '\e026';
}

/*字体颜色*/
.soc-icon-fontcolor:before {
  content: '\e027';
}

/*字体类型*/
.soc-icon-fontclass:before {
  content: '\f031';
}

/*颜色*/
.soc-icon-color:before {
  content: '\e022';
}

/*字体背景颜色*/
.soc-icon-gbcolor:before {
  content: '\e025';
}

/*无序列表*/
.soc-icon-ul:before {
  content: '\f0ca';
}

/*有序列表*/
.soc-icon-ol:before {
  content: '\f0cb';
}

/*左缩进*/
.soc-icon-indentleft:before {
  content: '\f03b';
}

/*右缩进*/
.soc-icon-indentright:before {
  content: '\f03c';
}

/*居左对齐*/
.soc-icon-alignleft:before {
  content: '\f036';
}

/*居中对齐*/
.soc-icon-aligncenter:before {
  content: '\f037';
}

/*居右对齐*/
.soc-icon-alignright:before {
  content: '\f038';
}

/*两端对齐*/
.soc-icon-alignjustify:before {
  content: '\f039';
}

/*字母大写*/
.soc-icon-upper:before {
  content: '\e023';
}

/*字母小写*/
.soc-icon-lower:before {
  content: '\e024';
}

/*logo*/
.soc-icon-logo:before {
  content: '\e020';
}

/*用户*/
.soc-icon-user:before {
  content: '\f2c0';
}

/*向左*/
.soc-icon-left:before {
  content: '\f053';
}

/*向右*/
.soc-icon-right:before {
  content: '\f054';
}

/*通知*/
.soc-icon-info1:before {
  content: '\f026';
}

.soc-icon-info2:before {
  content: '\f027';
}

.soc-icon-info3:before {
  content: '\f028';
}

/*用户名*/
.soc-icon-username:before {
  content: '\f2e3';
}

/*密码*/
.soc-icon-password:before {
  content: '\f2e1';
}

/*验证码*/
.soc-icon-vercode:before,
.soc-icon-number:before {
  content: '\f2e2';
}

/*重置*/
.soc-icon-reset:before {
  content: '\f01e';
}

/*网口*/
.soc-icon-network-port:before {
  content: '\f2ee';
}

/*总体态势*/
.soc-icon-menu-overall-situation:before {
  content: '\D038';
}

/*资产态势*/
.soc-icon-menu-asset-situation:before {
  content: '\D036';
}

/*安全态势*/
.soc-icon-menu-security-situation:before {
  content: '\D000';
}

/*运行态势*/
.soc-icon-menu-run-situation:before {
  content: '\D029';
}

/*威胁态势*/
.soc-icon-menu-threat-situation:before {
  content: '\D024';
}

/*弱点态势*/
.soc-icon-menu-weaknesses-situation:before {
  content: '\D017';
}

/*总体安全态势*/
.soc-icon-menu-overall-security-situation:before {
  content: '\D039';
}

/*发现*/
.soc-icon-menu-discover:before {
  content: '\D008';
}

/*导入*/
.soc-icon-menu-import:before {
  content: '\D006';
}

/*运维*/
.soc-icon-menu-operations:before {
  content: '\D030';
}

/*业务*/
.soc-icon-menu-business:before {
  content: '\D027';
}

/*拓扑*/
.soc-icon-menu-topology:before {
  content: '\D022';
}

/*机房*/
.soc-icon-menu-computer-room:before {
  content: '\D011';
}

/*弱点分析*/
.soc-icon-menu-weaknesses-analyze:before {
  content: '\D016';
}

/*安全响应分析*/
.soc-icon-menu-security-response-analyze:before,
.soc-icon-ip:before {
  content: '\D002';
}

/*设置*/
.soc-icon-menu-setting:before {
  content: '\D019';
}

/*报表设置*/
.soc-icon-menu-report-setting:before {
  content: '\D005';
}

/*响应*/
.soc-icon-menu-response:before {
  content: '\D026';
}

/*扫描*/
.soc-icon-menu-scan:before {
  content: '\D018';
}

/*整体风险*/
.soc-icon-menu-overall-risk:before {
  content: '\D031';
}

/*资产风险*/
.soc-icon-menu-asset-risk:before {
  content: '\D035';
}

/*安全域风险*/
.soc-icon-menu-security-domain-risk:before {
  content: '\D003';
}

/*机构风险*/
.soc-icon-menu-institutions-risk:before {
  content: '\D012';
}

/*智能关联*/
.soc-icon-menu-smart-link:before {
  content: '\D033';
}

/*总体*/
.soc-icon-menu-overall:before {
  content: '\D03A';
}

/*工单*/
.soc-icon-menu-work-order:before {
  content: '\D010';
}

/*值班*/
.soc-icon-menu-duty:before {
  content: '\D032';
}

/*邮件*/
.soc-icon-menu-mail:before {
  content: '\D028';
}

/*短信*/
.soc-icon-menu-note:before {
  content: '\D007';
}

/*综合*/
.soc-icon-menu-comprehensive:before {
  content: '\D037';
}

/*资产*/
.soc-icon-menu-assets:before {
  content: '\D034';
}

/*监控*/
.soc-icon-menu-monitor:before {
  content: '\D013';
}

/*弱点*/
.soc-icon-menu-weaknesses:before {
  content: '\D015';
}

/*事件*/
.soc-icon-menu-events:before {
  content: '\D020';
}

/*漏洞*/
.soc-icon-menu-loopholes:before {
  content: '\D014';
}

/*威胁*/
.soc-icon-menu-threats:before {
  content: '\D023';
}

/*案例*/
.soc-icon-menu-cases:before {
  content: '\D004';
}

/*文档*/
.soc-icon-menu-documents:before {
  content: '\D025';
}

/*信息*/
.soc-icon-menu-information:before {
  content: '\D03D';
}

/*日志审计*/
.soc-icon-menu-log-audit:before {
  content: '\D03C';
}

/*用户组管理*/
.soc-icon-menu-user-management:before {
  content: '\D03E';
}

/*菜单配置*/
.soc-icon-menu-configuration:before {
  content: '\D03B';
}

/*菜单管理*/
.soc-icon-menu-management:before {
  content: '\F0CA';
}

/*日志审计*/
.soc-icon-log-audit:before {
  content: '\E001';
}

/*日志查询*/
.soc-icon-log:before {
  content: '\E01E';
}

/*日志备份*/
.soc-icon-log-config:before {
  content: '\E019';
}

/*二级菜单圆点*/
.soc-icon-point:before {
  content: '\D110';
}

/*数据为空*/
.soc-icon-data-empty:before {
  content: '\F2E6';
}

/*警告*/
.soc-icon-alarm:before {
  content: '\F2E7';
}

/*系统警告*/
.soc-icon-system-alarm:before {
  content: '\F2E7';
}

/*视图*/
.soc-icon-view:before {
  content: '\F2EA';
}

/*资产*/
.soc-icon-asset:before {
  content: '\F2ED';
}

/*事件*/
.soc-icon-event:before {
  content: '\F2E9';
}

/*审计*/
.soc-icon-audit:before {
  content: '\F2E8';
}

/*告警*/
.soc-icon-waring:before {
  content: '\D009';
}

/*采集*/
.soc-icon-collect:before {
  content: '\F2E5';
}

/*知识库*/
.soc-icon-repository:before {
  content: '\D011';
}

/*报表*/
.soc-icon-report:before {
  content: '\F2E4';
}

/*系统配置*/
.soc-icon-setting:before {
  content: '\F2EB';
}

/*预测*/
.soc-icon-forecast:before {
  content: '\F0EC';
}

/*情报*/
.soc-icon-threat:before {
  content: '\F132';
}

/*监控*/
.soc-icon-monitor:before {
  content: '\D002';
}

/*监控*/
.soc-icon-monitor:before {
  content: '\D036';
}

.soc-icon-workflow-securityPolicyChange:before {
  content: '\d500';
}

.soc-icon-workflow-assetLending:before {
  content: '\d501';
}

.soc-icon-workflow-configChangeEvent:before {
  content: '\D502';
}

.soc-icon-workflow-configChanges:before {
  content: '\D503';
}

.soc-icon-workflow-inspection:before {
  content: '\D504';
}

.soc-icon-workflow-3rdevent:before {
  content: '\D505';
}

.soc-icon-workflow-assetAllocation:before {
  content: '\D506';
}

.soc-icon-workflow-sysUpdate:before {
  content: '\D507';
}

.soc-icon-workflow-assetAllocationUsed:before {
  content: '\D508';
}

.soc-icon-workflow-dutyChanges:before {
  content: '\D509';
}

.soc-icon-workflow-reject:before {
  content: '\D50A';
}

.soc-icon-workflow-assetToBeRepaired:before {
  content: '\D50B';
}

.soc-icon-workflow-onlineAssetRecovery:before {
  content: '\D50C';
}

.soc-icon-workflow-newAssetStorage:before {
  content: '\D50D';
}

.soc-icon-workflow-deviceApplication:before {
  content: '\D50E';
}

.soc-icon-workflow-generalApply:before {
  content: '\D50F';
}

/*笔形编辑工具*/
.soc-icon-pen:before {
  content: '\D103';
}

/*发现拓扑*/
.soc-icon-find:before {
  content: '\D100';
}

/*移动工具*/
.soc-icon-move:before {
  content: '\D109';
}

/*添加点*/
.soc-icon-add-point:before {
  content: '\D105';
}

/*文本工具*/
.soc-icon-text:before {
  content: '\D108';
}

/*用户手册*/
.soc-icon-help:before {
  content: '\D101';
}

/*退出*/
.soc-icon-exit:before {
  content: '\D107';
}

/*更换背景*/
.soc-icon-back:before {
  content: '\D106';
}

/*回退*/
.soc-icon-undo:before {
  content: '\D104';
}

/*保存*/
.soc-icon-light-save:before {
  content: '\D102';
}

/*========================运维值班表图标=======================*/
.gimap-watch-rightbt:before {
  content: '\f105';
}

.gimap-watch-leftbt:before {
  content: '\f104';
}

.gimap-watch-upbt:before {
  content: '\f106';
}

.gimap-watch-downbt:before {
  content: '\f107';
}

.gimap-bt-leftShrinkIcon:before {
  content: '\f053';
}

.gimap-bt-rightStretchIcon:before {
  content: '\f054';
}

.soc-spin {
  -webkit-animation: soc-spin 2s infinite linear;
  animation: soc-spin 2s infinite linear;
}

.soc-pulse {
  -webkit-animation: soc-spin 1s infinite steps(8);
  animation: soc-spin 1s infinite steps(8);
}

@keyframes soc-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
