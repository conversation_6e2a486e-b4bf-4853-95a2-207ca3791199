<template>
  <div :id="id" ref="lineChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-line',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    lineData: {
      type: Object,
      default() {
        return {
          axis: [],
          data: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    lineData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.lineData) {
      if (data && Object.keys(data).length > 0) {
        this.chart = echarts.init(this.$refs.lineChart)
        this.drawChart(data)
      }
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      let option = { color: ['#7ac6d3'] }
      const [grid, axis, series] = [this.chartGridConfig(), this.chartAxisConfig(data), this.chartSeriesConfig(data)]
      option = Object.assign(option, grid, axis, series)
      return option
    },
    chartGridConfig() {
      return {
        grid: {
          left: '0%',
          top: '5%',
          bottom: '5%',
        },
      }
    },
    chartAxisConfig(data) {
      return {
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: data.axis,
          axisLabel: {
            show: true,
            textStyle: {
              color: '#7ac6d3',
            },
          },
          show: false,
        },
        yAxis: {
          boundaryGap: [0, '50%'],
          type: 'value',
          axisLabel: {
            show: true,
            textStyle: {
              color: '#7ac6d3',
            },
          },
          show: false,
        },
      }
    },
    chartSeriesConfig(data) {
      return {
        series: [
          {
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: '3',
            data: data.data,
          },
        ],
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
