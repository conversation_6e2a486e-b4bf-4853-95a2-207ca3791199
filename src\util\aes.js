import CryptoJS from 'crypto-js'
// npm install crypto-js --save-dev

export default {
  // 随机生成指定数量的32进制key
  generatekey(num) {
    const library = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let key = ''
    for (var i = 0; i < num; i++) {
      const randomPoz = Math.floor(Math.random() * library.length)
      key += library.substring(randomPoz, randomPoz + 1)
    }
    return key
  },
  // 加密
  encrypt(word, keyStr) {
    if (typeof word === 'object') {
      // 对象格式的转成json字符串
      word = JSON.stringify(word)
    }
    keyStr = keyStr || 'CXMGNcYwTrtsadQm' // 判断是否存在ksy，不存在就用定义好的key
    const key = CryptoJS.enc.Utf8.parse(keyStr) // 十六位
    const iv = CryptoJS.enc.Utf8.parse(keyStr) // 十六位
    const encrypted = CryptoJS.AES.encrypt(word, key, {
      iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    return encrypted.ciphertext.toString()
  },
  // 解密
  decrypt(word, keyStr) {
    keyStr = keyStr || 'CXMGNcYwTrtsadQm'
    var key = CryptoJS.enc.Utf8.parse(keyStr)
    var iv = CryptoJS.enc.Utf8.parse(keyStr)
    var encryptedHexStr = CryptoJS.enc.Hex.parse(word)
    var srcs = CryptoJS.enc.Base64.stringify(encryptedHexStr)
    var decrypt = CryptoJS.AES.decrypt(srcs, key, {
      iv: iv,
      mode: CryptoJS.mode.CBC,
      padding: CryptoJS.pad.Pkcs7,
    })
    var decryptedStr = decrypt.toString(CryptoJS.enc.Utf8)
    return decryptedStr.toString()
  },
}
