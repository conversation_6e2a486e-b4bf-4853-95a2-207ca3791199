export default {
  polymerizationStrategy: {
    strategy: '聚合策略',
    table: {
      alarmName: '策略名称',
      matchCriteriaList: '匹配规则',
      alarmTimeout: '最大限定时间(秒)',
      countThreshold: '最大聚合次数',
      eventFrequency: '事件频率(秒/条)',
      handel: '操作',
    },
    label: {
      alarmTimeout: '最大限定时间',
      countThreshold: '最大聚合次数',
      alarmStartTimeout: '最大限定时间(起)',
      alarmEndTimeout: '最大限定时间(止)',
      countStartThreshold: '最大聚合次数(起)',
      countEndThreshold: '最大聚合次数(止)',
      forwardType: '转发类型',
      eventFrequency: '事件频率',
    },
    title: {
      update: '聚合策略修改',
    },
    tip: {
      alarmTimeout: '终止限定时间必须大于起始限定时间',
      countThreshold: '终止聚合次数必须大于起始聚合次数',
    },
    upload: {
      chooseFile: '请选择文件',
      exceed: '当前限制选择 1 个文件，请删除后再上传',
      successUpload: '导入解析规则包成功',
    },
  },
}
