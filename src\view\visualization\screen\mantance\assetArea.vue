<template>
  <div class="assets-wrapper">
    <img class="center-img" src="@/asset/image/visualization/mantance/center.png" alt="" />
    <div class="assets-top-title">
      <div class="charttitle">
        <span>日志源存储时长TOP10</span>
      </div>
    </div>
    <div v-for="(cp, idx) in seriesData" :class="`assets-item a${idx + 1}`">
      <div :class="idx > 4 ? 'asset-item-name reverse' : 'asset-item-name'">{{ cp.name }}</div>
      <el-progress
        :class="idx > 4 ? 'asset-item-progress reverse' : 'asset-item-progress'"
        :percentage="cp.percent"
        :show-text="false"
        define-back-color="#242a37"
        :color="cp.color"></el-progress>
      <div :class="idx > 4 ? 'asset-item-value reverse' : 'asset-item-value'" :style="`color: ${cp.color}`">{{ cp.value }}</div>
    </div>
  </div>
</template>
<script>
import AnimatedNumber from 'animated-number-vue'
export default {
  components: {
    AnimatedNumber,
  },
  props: {
    staticsData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      colorPattern: ['#00fffe', '#ffb42d', '#ff3e66', '#12d202', '#fff800', '#ff5800', '#00ff9a', '#c9ff3e', '#bb00ff', '#caf6ff'],
    }
  },
  computed: {
    seriesData() {
      const all = this.staticsData.reduce((prev, cur) => {
        return prev + cur.value
      }, 0)
      return this.staticsData.map((item, idx) => {
        return {
          ...item,
          percent: Number(((item.value / all) * 100).toFixed(0)),
          color: this.colorPattern[idx],
        }
      })
    },
  },

  mounted() {},
}
</script>
<style lang="scss" scoped>
.assets-wrapper {
  width: 100%;
  height: 100%;
  position: relative;
  display: grid;
  place-items: center;
  .center-img {
    width: 100%;
  }
  .assets-top-title {
    position: absolute;
    top: 64px;
    width: 300px;
    text-align: center;
    .charttitle {
      color: rgb(53, 158, 226);
      font-size: 20px;
      font-weight: 700;
      display: flex;
      justify-content: center;
      align-items: center;
      background-image: url('~@asset/image/visualization/mantance/centerheadertitle.png');
      background-size: contain;
      background-repeat: no-repeat;
      height: 42px;
      background-position: center;
      span {
        position: relative;
        top: -5px;
      }
    }
  }
  .assets-item {
    position: absolute;
    display: flex;
    align-items: center;
    color: #fff;
    font-weight: bold;
    justify-content: space-between;
    width: 150px;
    height: 56px;
    &.reverse {
      justify-content: flex-end;
    }
    .asset-item-name {
      position: absolute;
      top: 12px;
      padding-left: 12px;
      color: #000;
      &.reverse {
        padding-right: 12px;
        padding-left: 0px;
        right: 0px;
      }
    }
    .asset-item-progress {
      width: 75%;
      position: absolute;
      bottom: 12px;
      padding-left: 12px;
      &.reverse {
        right: 0px;
        padding-left: 0px;
        padding-right: 12px;
        ::v-deep .el-progress-bar__inner {
          left: initial;
          right: 0px;
        }
      }
    }
    .asset-item-value {
      position: absolute;
      bottom: 8px;
      right: 2px;
      &.reverse {
        left: 2px;
      }
    }
    &.a1 {
      left: 211px;
      top: 172px;
    }
    &.a2 {
      left: 148px;
      top: 282px;
    }
    &.a3 {
      left: 110px;
      top: 390px;
    }
    &.a4 {
      left: 150px;
      top: 492px;
    }
    &.a5 {
      left: 212px;
      top: 599px;
    }
    &.a6 {
      right: 220px;
      top: 172px;
    }
    &.a7 {
      right: 152px;
      top: 282px;
    }
    &.a8 {
      right: 116px;
      top: 390px;
    }
    &.a9 {
      right: 155px;
      top: 492px;
    }
    &.a10 {
      right: 219px;
      top: 599px;
    }
  }
}
</style>
