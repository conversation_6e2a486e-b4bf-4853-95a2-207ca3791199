export default {
  button: {
    build: 'Build',
    on: 'Enable',
    off: 'Disable',
    detail: 'Details',
    confirm: 'Confirm',
    determine: 'OK',
    cancel: 'Cancel',
    login: 'Login',
    logout: 'Logout',
    add: 'Add',
    change: 'Convert',
    update: 'Update',
    edit: 'Edit',
    delete: 'Delete',
    query: 'Query',
    correct: 'Correct',
    check: 'Manual Detection',
    checkedAll: 'Select All',
    refresh: 'Refresh',
    save: 'Save',
    submit: 'Submit',
    close: 'Close',
    lock: 'Lock',
    unlock: 'Unlock',
    download: 'Download',
    upload: 'Upload',
    packageUpload: 'Parse Package Upload',
    choseFile: 'Choose File',
    import: 'Import',
    sync: 'Sync',
    repeat: 'Undo',
    config: 'Settings',
    recovery: 'Recover',
    dataRecovery: 'Data Recovery',
    stop: 'Stop',
    ignore: 'Ignore',
    scan: 'Scan',
    th: 'Custom Columns',
    nowFind: 'Discover Now',
    find: 'Auto Discover',
    workflow: 'Generate Work Order',
    show: 'Show',
    hide: 'Hide',
    grant: 'Authorize',
    clear: 'Clear',
    title: 'Title',
    return: 'Return',
    previous: 'Previous',
    next: 'Next',
    newIp: 'New IP Range',
    threatenConfig: 'Threat Intelligence Source Configuration',
    look: 'View',
    test: 'Test',
    dictionary: 'Dictionary',
    log: 'Original Text',
    restart: 'Restart',
    restore: 'Factory Reset',
    shutdown: 'Shutdown',
    expression: 'Expression',
    strategyConfig: 'Strategy Configuration',
    handle: 'Handle',
    forward: 'Log Forward',
    copy: 'Copy',
    customQuery: 'Custom Query',
    allConfirm: 'Confirm All',
    insert: 'Add Row',
    back: 'Back',
    search: {
      high: 'Advanced Search',
      exact: 'Exact Query',
      advance: 'Advanced Query',
    },
    reset: {
      default: 'Reset',
      password: 'Reset Password',
    },
    backups: {
      config: 'Backup Settings',
    },
    move: {
      up: 'Move Up',
      down: 'Move Down',
    },
    batchText: 'Batch Operations',
    batch: {
      add: 'Batch Add',
      update: 'Batch Update',
      delete: 'Batch Delete',
      stop: 'Batch Disable',
      grant: 'Batch Authorize',
      ignore: 'Batch Ignore',
      change: 'Batch Convert',
      run: 'Batch Enable',
    },
    export: {
      default: 'Export',
      excel: 'Export EXCEL',
      pdf: 'Export PDF',
      word: 'Export WORD',
      xml: 'Export XML',
      image: 'Export Image',
      text: 'Export File',
      custom: 'Custom Export',
    },
    toggle: {
      run: 'Run',
      stop: 'Pause',
    },
    model: {
      run: 'Model Running',
      stop: 'Model Paused',
      result: 'Model Results',
    },
    view: 'Monitor Display',
    logImport: 'Log Import',
    nowExecute: 'Execute Now',
    detect: 'One-Click Detection',
    networkConfig: 'Configuration',
    centerIp: 'Set Center IP',
    generateParse: 'Generate Parsing',
    logView: 'Logs',
    emailDetermine: 'Send Verification Email',
  },
}
