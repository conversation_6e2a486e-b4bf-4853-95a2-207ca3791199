/**
 * @func 产生区间随机数
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @return {number} 产生的随机数
 * @example
 * <AUTHOR> @date 2020/1/19
 */
export function randomNum(min, max) {
  return Math.floor(Math.random() * (max - min) + min)
}

/**
 * @func 产生任意长度随机字母数字组合
 * @param {boolean} randomFlag - 是否随机产生
 * @param {number} min - 最小值
 * @param {number} max - 最大值
 * @return {string} -
 * @example
 * <AUTHOR> @date 2019/06/12
 */
export function randomWord(randomFlag, min, max) {
  let str = '',
    range = min,
    pos
  const words = [
    '0',
    '1',
    '2',
    '3',
    '4',
    '5',
    '6',
    '7',
    '8',
    '9',
    'a',
    'c',
    'd',
    'e',
    'f',
    'g',
    'h',
    'i',
    'k',
    'l',
    'n',
    'o',
    'p',
    'q',
    'r',
    's',
    't',
    'u',
    'v',
    'w',
    'x',
    'y',
    'z',
    'A',
    'C',
    'D',
    'E',
    'F',
    'G',
    'H',
    'I',
    'K',
    'L',
    'N',
    'O',
    'P',
    'Q',
    'R',
    'S',
    'T',
    'U',
    'V',
    'W',
    'X',
    'Y',
    'Z',
  ]
  if (randomFlag) {
    range = randomNum(min, max)
  }
  for (let i = 0; i < range; i++) {
    pos = Math.round(Math.random() * (words.length - 1))
    str += words[pos]
  }
  return str
}
