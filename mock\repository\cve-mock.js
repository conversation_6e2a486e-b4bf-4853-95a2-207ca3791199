const { TableMock } = require('../util')
const tableData = new TableMock({
  total: 200,
  template: {
    name: '@CNAME',
    votes: '@CNAME',
    phase: '@CNAME',
    description: '@CNAME',
    comments: '@CNAME',
    references: '@NAME',
    status: '@NAME',
  },
})
const Data = {
  comments: '',
  description:
    'The constructSQL function in inc/search.class.php in GLPI 9.2.x through 9.3.0 allows SQL Injection, as demonstrated by triggering a crafted LIMIT cla',
  name: 'CVE-2018-13049',
  phase: 'Assigned (20180702)',
  references: 'CONFIRM:https://github.com/glpi-project/glpi/issues/4270',
  status: 'Candidate',
  votes: 'None (candidate not yet proposed)',
}
module.exports = [
  {
    url: '/knowledge/vulnerability/cve',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: `/knowledge/vulnerability/cve/[A-Za-z0-9]`,
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: Data,
      }
    },
  },
]
