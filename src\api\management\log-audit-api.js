import request from '@util/request'

export function downloadLogAuditTableData(obj) {
  return request(
    {
      url: `/auditlog/logs/download`,
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

export function deleteLogAuditData(logIds) {
  return request({
    url: `/auditlog/log/${logIds}`,
    method: 'delete',
  })
}

export function queryLogAuditTableData(obj) {
  return request({
    url: '/auditlog/logs',
    method: 'get',
    params: obj || {},
  })
}

export function queryLogForwardConfig() {
  return request({
    url: '/auditlog/logs/queryForward',
    method: 'get',
  })
}

export function saveLogForwardConfig(obj) {
  return request({
    url: '/auditlog/logs/forward',
    method: 'post',
    data: obj || {},
  })
}
