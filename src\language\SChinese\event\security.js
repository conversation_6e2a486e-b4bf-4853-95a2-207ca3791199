export default {
  security: {
    table: {
      type2Name: '事件名称',
      alarmTypeName: '事件类型',
      alarmCategoryName: '事件类别',
      level: '事件等级',
      count: '聚合数量',
      srcIpv: '源IP',
      srcPort: '源端口',
      dstIpv: '目的IP',
      dstPort: '目的端口',
      aggrStartDate: '聚合开始时间',
      aggrEndDate: '聚合结束时间',
      fromDeviceType: '发生源设备类型',
      deviceTypeName: '日志源设备',
      protocol: '协议',
      advice: '处理意见',
      raw: '日志原文',
      date: '时间',
      fromIpv: '发生源IP',
      alarmDesc: '事件描述',
    },
    dialog: {
      detailTitle: '安全事件详情',
      colTitle: '安全事件自定义列',
    },
    detailColumns: {
      type2Name: '原始事件名称',
      eventName: '事件类型',
      eventCategoryName: '事件类别',
      level: '事件等级',
      srcIp: '源IP',
      dstIp: '目的IP',
      dateTime: '时间',
      raw: '日志原文',
    },
    placeholder: {
      type2Name: '事件名称',
      inputSearch: '事件名称/事件类型/发生源IP/日志源设备',
      startIp: '发生源起始IP',
      endIp: '发生源终止IP',
      srcStartIp: '源起始IP',
      srcEndIp: '源终止IP',
      dstStartIp: '目的起始IP',
      dstEndIp: '目的终止IP',
    },
    header: '安全事件',
    checkBox: {
      type2Name: '事件名称',
      alarmTypeName: '事件类型',
      alarmCategoryName: '事件类别',
      level: '事件等级',
      count: '聚合数量',
      srcIpv: '源IP',
      srcPort: '源端口',
      dstIpv: '目的IP',
      dstPort: '目的端口',
      aggrStartDate: '聚合开始时间',
      aggrEndDate: '聚合结束时间',
      deviceTypeName: '日志源设备',
      fromIpv: '发生源IP',
      protocol: '协议',
    },
    panel: {
      original: '原始日志查询',
      detail: '详情信息',
    },
  },
}
