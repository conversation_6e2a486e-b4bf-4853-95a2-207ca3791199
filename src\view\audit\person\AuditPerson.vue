<!--
 * @Description: 审计人员
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('audit.person.accountName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQueryEvent('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="inputQueryEvent('e')">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAdd">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'query'" @click="resetTable">
            {{ $t('button.reset.default') }}
          </el-button>
        </section>
      </section>
    </header>
    <main class="treeBox">
      <div class="treeBox-warp">
        <div class="treeBox-warp-title">
          <span class="audit-tree-title">
            {{ $t('audit.person.groupList') }}
          </span>
          <el-button size="mini" style="margin-left: 10px" icon="el-icon-plus" @click="() => addGroup()"></el-button>
        </div>
        <el-tree v-show="group" :empty-text="$t('audit.person.emptyText')" :data="group" node-key="value" @node-click="handleNodeClick">
          <span
            slot-scope="{ node, data }"
            class="custom-tree-node"
            style="min-width: 150px"
            @mouseenter="mouseenter(node, data)"
            @mouseleave="mouseleave(node, data)"
          >
            <span>
              {{ node.label }}
            </span>
            <span v-show="node.level == 1" style="margin-left: 16px">
              <el-button v-show="data.show" type="text" size="mini" @click="() => updateGroup(node, data)">
                <i class="el-icon-edit-outline"></i>
              </el-button>
              <el-button v-show="data.show && group.length > 1" type="text" size="mini" @click="() => removeGroup(node, data)">
                <i class="el-icon-delete"></i>
              </el-button>
            </span>
          </span>
        </el-tree>
      </div>
      <main class="table-body">
        <header class="table-body-header">
          <h2 class="table-body-title">
            {{ $t('audit.person.person') }}
          </h2>
        </header>
        <main class="table-body-main">
          <el-table
            ref="Table"
            v-loading="data.loading"
            :data="data.table"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
            height="100%"
            @current-change="TableRowChange"
            @selection-change="TableSelectsChange"
          >
            <el-table-column prop="accountName" :label="$t('audit.person.accountName')" show-overflow-tooltip></el-table-column>
            <el-table-column prop="typeName" :label="$t('audit.person.type')" show-overflow-tooltip></el-table-column>
            <el-table-column prop="level" :label="$t('audit.person.level')" width="250" show-overflow-tooltip>
              <template slot-scope="scope">
                <level-tag :level="scope.row.level ? scope.row.level.split(',') : ''"></level-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" :label="$t('audit.person.remark')" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" width="160">
              <template slot-scope="scope">
                <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                  {{ $t('button.update') }}
                </el-button>
                <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                  {{ $t('button.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </main>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加人员弹窗-->
    <Au-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form"
      :width="'35%'"
      @on-submit="clickSubmitAdd"
    ></Au-dialog>
    <!--编辑人员弹窗-->
    <Au-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form="dialog.form"
      :width="'35%'"
      @on-submit="clickSubmitUpdate"
    ></Au-dialog>
    <!--添加审计组-->
    <Au-dialog
      is-group
      :visible.sync="dialog.visible.addG"
      :title="dialog.title.addG"
      :form="dialog.group"
      :width="'35%'"
      @on-submit="clickSubmitAddG"
    ></Au-dialog>
    <!--编辑审计组-->
    <Au-dialog
      is-group
      :visible.sync="dialog.visible.updateG"
      :title="dialog.title.updateG"
      :form="dialog.group"
      :width="'35%'"
      @on-submit="clickSubmitUpdateG"
    ></Au-dialog>
  </div>
</template>
<script>
import AuDialog from './PersonAU'
import { prompt } from '@util/prompt'
import LevelTag from '@comp/LevelTag'
import { validateSpace } from '@util/validate'
import { addData, deleteData, queryTableData, addGroups, deleteGroups, updateGroups, queryGroups, queryGroup, queryType } from '@api/audit/person-api'
import { debounce } from '@util/effect'

export default {
  name: 'AuditPerson',
  components: {
    AuDialog,
    LevelTag,
  },
  data() {
    const validatorSpace = (rule, value, callback) => {
      if (!validateSpace(value)) {
        callback(new Error(this.$t('validate.choose')))
      } else {
        callback()
      }
    }
    return {
      clickCode: [],
      group: [],
      queryInput: {
        fuzzyField: '',
        userId: '',
        groupId: '',
      }, // 搜索框内容
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
        visible: true,
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.add', [this.$t('audit.person.person')]),
          update: this.$t('dialog.title.update', [this.$t('audit.person.person')]),
          addG: this.$t('dialog.title.add', [this.$t('audit.person.group')]),
          updateG: this.$t('dialog.title.update', [this.$t('audit.person.group')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false,
          update: false,
          addG: false,
          updateG: false,
        },
        form: {
          groupList: [],
          typeList: [],
          eventLevel: [
            {
              label: this.$t('level.serious'),
              value: '0',
            },
            {
              label: this.$t('level.high'),
              value: '1',
            },
            {
              label: this.$t('level.middle'),
              value: '2',
            },
            {
              label: this.$t('level.low'),
              value: '3',
            },
            {
              label: this.$t('level.general'),
              value: '4',
            },
          ],
          model: {
            // 弹出表单绑定值
            typeId: '',
            remark: '',
            accountName: '',
            level: [],
            groupId: '',
            update: false,
          },
          info: {
            // 弹出表单信息
            typeId: {
              key: 'typeId',
              label: this.$t('audit.person.type'),
              value: '',
            },
            remark: {
              key: 'remark',
              label: this.$t('audit.person.remark'),
              value: '',
            },
            accountName: {
              key: 'accountName',
              label: this.$t('audit.person.accountName'),
              value: '',
            },
            level: {
              key: 'level',
              label: this.$t('audit.person.level'),
            },
            groupId: {
              key: 'groupId',
              label: this.$t('audit.person.group'),
              value: '',
            },
          },
          rules: {
            // 弹出表单的校验规则
            accountName: [
              {
                validator: validatorSpace,
                required: true,
                message: this.$t('validate.none'),
                trigger: 'blur',
              },
            ],
            groupId: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            level: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
          },
        },
        group: {
          model: {
            groupName: '',
            remark: '',
            id: '',
          },
          info: {
            // 弹出表单信息
            groupName: {
              key: 'groupName',
              label: this.$t('audit.person.groupName'),
              value: '',
            },
            remark: {
              key: 'remark',
              label: this.$t('audit.person.remark'),
              value: '',
            },
          },
          rules: {
            // 弹出表单的校验规则
            groupName: [
              {
                required: true,
                validator: validatorSpace,
                message: this.$t('validate.none'),
                trigger: 'blur',
              },
            ],
          },
        },
      },
      queryDebounce: null,
    }
  },
  mounted() {
    // 查询列表
    this.getTableData()
    this.getGroups()
    this.initDebounce()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          inputVal: this.queryInput.fuzzyField || '',
          id: this.queryInput.userId || '',
          groupId: this.queryInput.groupId || '',
        }
        this.getTableData(params)
        this.getGroups()
      }, 500)
    },
    // 查询列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 查询审计组树形
    getGroups() {
      queryGroups().then((res) => {
        this.group = res
      })
    },
    // 查询审计组下拉框
    getGroup() {
      queryGroup().then((res) => {
        this.dialog.form.groupList = res
      })
    },
    // 查询审计事件类型下拉框
    getType() {
      queryType().then((res) => {
        this.dialog.form.typeList = res
      })
    },
    // 添加审计人员
    add(obj) {
      const param = {
        ...obj,
        typeId: obj.typeId.toString(),
        level: obj.level.toString(),
      }
      addData(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.resetTable()
              this.getGroups()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 添加审计组
    addG(obj) {
      addGroups(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.resetTable()
              this.getGroups()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 删除审计人员
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteData(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.inputQueryEvent()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 编辑审计组
    updateG(obj) {
      updateGroups(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getGroups()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeatGroup',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 清空表单数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        typeId: '',
        groupId: '',
        accountName: '',
        level: [],
        remark: '',
        update: false,
      }
    },
    // 点击添加操作并弹出对话框操作
    clickAdd() {
      this.clearDialogFormModel()
      this.getGroup()
      this.getType()
      this.dialog.form.model.update = false
      this.dialog.visible.add = true
    },
    // 提交添加表单操作
    clickSubmitAdd(formModel) {
      this.add(formModel)
    },
    // 提交添加审计组操作
    clickSubmitAddG(formModel) {
      this.addG(formModel)
    },
    // 点击删除操作
    clickDelete(row) {
      this.delete(row.id)
    },
    // 点击修改并弹出对话框操作
    clickUpdate(row) {
      this.getGroup()
      this.getType()
      this.TableRowChange(row)
      this.clearDialogFormModel()
      this.dialog.form.model = this.pagination.currentRow
      this.dialog.form.model.typeId = this.pagination.currentRow.typeId ? this.pagination.currentRow.typeId.toString().split(',') : []
      this.dialog.form.model.level = this.pagination.currentRow.level ? this.pagination.currentRow.level.toString().split(',') : []
      this.dialog.form.model.update = true
      this.dialog.visible.update = true
    },
    // 提交修改审计人员
    clickSubmitUpdate(formModel) {
      if (formModel === 'pass') {
        this.inputQueryEvent()
      }
    },
    // 提交修改审计组
    clickSubmitUpdateG(formModel) {
      this.updateG(formModel)
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.inputQueryEvent('e')
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQueryEvent()
    },
    // 列表多选改变
    TableSelectsChange(select) {
      this.data.selected = select
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      const newRow = Object.assign({}, row)
      this.pagination.currentRow = newRow
    },
    // 输入框搜索方法
    inputQueryEvent(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    // 添加审计组
    addGroup() {
      this.dialog.group.model = {
        groupName: '',
        remark: '',
      }
      this.dialog.visible.addG = true
    },
    // 编辑审计组
    updateGroup(data) {
      this.dialog.group.model = {
        groupName: data.data.label,
        remark: data.data.remark,
        id: data.data.value,
      }
      this.dialog.visible.updateG = true
    },
    // 删除审计组
    removeGroup(data) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteGroups(data.data.value).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.getGroups()
                this.pagination.pageNum = 1
                const param = {
                  pageSize: this.pagination.pageSize,
                  pageNum: this.pagination.pageNum,
                }
                this.getTableData(param)
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 点击树形 查询审计人员
    handleNodeClick(data) {
      if (data.level === '1') {
        this.handleGroupClick(data)
      } else {
        this.pagination.pageNum = 1
        const params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          id: data.value,
        }
        this.queryInput.userId = data.value
        this.getTableData(params)
      }
    },
    // 点击树形 查询审计组
    handleGroupClick(data) {
      this.pagination.pageNum = 1
      const params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        groupId: data.value,
      }
      this.queryInput.groupId = data.value
      this.getTableData(params)
    },
    // 树节点鼠标移入
    mouseenter(node, data) {
      this.$set(data, 'show', true)
    },
    // 树节点鼠标移出
    mouseleave(node, data) {
      this.$set(data, 'show', false)
    },
    // 重置表格
    resetTable() {
      this.queryInput = {
        fuzzyField: '',
        userId: '',
        groupId: '',
      }
      this.pagination.pageNum = 1
      this.queryDebounce()
    },
  },
}
</script>
<style lang="scss" scoped>
.audit-tree-title {
  @include theme('color', table-body-title-color);
}

.el-button--text {
  padding: 2px;
}

.treeBox {
  display: flex;
  height: 100%;
  &-warp {
    width: 300px;
    @include theme('background-color', treeBox-warp-bg);
    &-title {
      padding: 10px;
      margin-right: 5px;
      @include theme('background-color', treeBox-warp-title-bg);
    }
    .custom-tree-node {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
