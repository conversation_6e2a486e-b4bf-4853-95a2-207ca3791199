<template>
  <div class="tab-context-wrapper">
    <el-form ref="form" :model="form.model" :rules="form.rule" label-width="180px">
      <el-col :span="12" :offset="1">
        <el-form-item :label="$t('management.system.label.flowGather')" prop="zeekStart">
          <el-switch v-model="form.model.zeekStart"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.flowGather') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="1">
        <el-form-item :label="$t('management.system.label.flowGatherNetworkCard')" prop="zeekNetworkCard">
          <el-select v-model="form.model.zeekNetworkCard" clearable filterable>
            <el-option v-for="item in networkCardOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-has="'upload'" @click="clickSaveSystemConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button v-has="'query'" @click="clickResetSystemConfig">
        {{ $t('button.reset.default') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { queryNetWork } from '@api/management/network-api'
import { getZeekConfigData, saveZeekConfigData } from '@api/management/system-api'
export default {
  data() {
    return {
      networkCardOptions: [],
      form: {
        model: {
          zeekStart: false,
          zeekNetworkCard: '',
        },
        rule: {
          zeekNetworkCard: [],
        },
      },
    }
  },
  watch: {
    'form.model.zeekStart': {
      handler(newVal) {
        if (newVal) {
          // 开启镜像流量审计时，网卡必填
          this.form.rule.zeekNetworkCard = [{ required: true, message: '请选择镜像流量绑定网卡' }]
        } else {
          // 关闭镜像流量审计时，网卡不必填
          this.form.rule.zeekNetworkCard = []
          // 清除表单验证状态
          if (this.$refs.form) {
            this.$refs.form.clearValidate('zeekNetworkCard')
          }
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.getOptions()
    this.init()
  },
  methods: {
    getOptions() {
      queryNetWork().then((res) => {
        this.networkCardOptions = res.networkConfig.map((item) => {
          return { label: item.name, value: item.id }
        })
      })
    },
    init() {
      getZeekConfigData().then((res) => {
        this.form.model.zeekStart = res.zeekStart
        this.form.model.zeekNetworkCard = res.zeekNetworkCard
      })
    },
    clickSaveSystemConfig() {
      // 如果未开启镜像流量审计，直接保存
      if (!this.form.model.zeekStart) {
        // 确保清除验证状态
        this.$refs.form.clearValidate('zeekNetworkCard')
        saveZeekConfigData(this.form.model).then((res) => {
          if (res === 2) {
            this.$message.error('license 中未开启镜像流量，无法编辑。')
          } else {
            this.$message.success('保存成功')
          }
        })
        return
      }

      // 开启镜像流量审计时，验证表单
      this.$refs.form.validate((valid) => {
        if (valid) {
          saveZeekConfigData(this.form.model).then((res) => {
            if (res === 2) {
              this.$message.error('license 中未开启镜像流量，无法编辑。')
            } else {
              this.$message.success('保存成功')
            }
          })
        }
      })
    },
    clickResetSystemConfig() {
      this.init()
    },
  },
}
</script>
<style lang="scss" scoped>
.tab-context-wrapper {
  height: 100%;
}
.form-validate-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 5px;
}
.tab-footer-button {
  margin-right: 48px;
  text-align: right;
}
</style>
