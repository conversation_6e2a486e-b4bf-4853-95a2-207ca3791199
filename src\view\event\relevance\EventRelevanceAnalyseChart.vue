<template>
  <section class="chart-wrap">
    <section class="chart-wrap-content">
      <fishbone-chart id="fishboneChartDom" :option="series"></fishbone-chart>
    </section>
  </section>
</template>

<script>
import FishboneChart from '@comp/ChartFactory/dashboard/FishboneChart.vue'
export default {
  name: 'EventRelevanceAnalyseChart',
  components: {
    FishboneChart,
  },
  props: {
    fishboneData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      series: {
        data: [],
        links: [],
      },
      width: '100%',
      height: '100%',
    }
  },
  watch: {
    fishboneData: {
      handler(data) {
        this.option = this.handleFishboneData(data)
      },
      deep: true,
    },
  },
  methods: {
    handleFishboneData(data) {
      this.series.data = []
      this.series.links = []
      this.width = String(document.getElementById('fishboneChartDom').clientWidth)
      this.height = String(document.getElementById('fishboneChartDom').clientHeight)
      const xPos = 60
      const yPos = this.height / 2
      const interval = (this.width - xPos * 2) / data.hAxis.length
      data.hAxis.forEach((item, index) => {
        this.series.data.push({
          id: item,
          name: item,
          x: xPos + index * interval,
          y: yPos,
          type: 'time',
          symbolSize: 10,
          label: {
            show: true,
            fontSize: 11,
            rotate: 53,
            position: 'bottom',
          },
        })
        // 时间点创造关联关系形成坐标轴
        if (index > 0 && index < data.hAxis.length) {
          this.series.links.push({ source: data.hAxis[index - 1], target: data.hAxis[index] })
        }
      })
      let pointIndex = 0
      let inner = 0
      let even = 0
      data.scatters.forEach((item, index) => {
        if (item.name.length > 10) {
          item.name = item.name.slice(0, 10) + '...'
        }
        data.hAxis.forEach((time, i) => {
          if (time === item.time) {
            pointIndex = i
          }
        })
        if (index > 0 && item.time === data.scatters[index - 1].time) {
          even++
          if (even % 2 === 0) {
            inner += interval / Math.ceil(item.pointNum / 2)
          }
        } else {
          even = 0
          inner = 0
        }
        this.series.data.push({
          id: item.id,
          name: item.count + ',' + item.name,
          sourceIp: item.sourceIp,
          level: item.level,
          levelName: item.levelName,
          items: item.items,
          x: xPos + (pointIndex + 1) * interval + inner,
          y: index % 2 === 0 ? yPos - 150 : yPos + 150,
          type: 'scatter',
        })
      })
      data.links.forEach((item, index) => {
        data.scatters.map((scatter) => {
          if (item.target === scatter.id) {
            item.name = scatter.count + ',' + scatter.name
          }
        })
        this.series.links.push({ source: item.source, target: item.target, name: item.name })
      })
    },
  },
}
</script>

<style scoped lang="scss">
.chart-wrap {
  height: 100%;
  &-content {
    width: 100%;
    height: 100%;
  }
}
</style>
