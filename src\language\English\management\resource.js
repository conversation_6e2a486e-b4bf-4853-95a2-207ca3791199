export default {
  resource: {
    infoItem: {
      resourceToken: 'Resource Identifier',
      resourceName: 'Resource Name',
      resourceStatus: 'Resource Status',
      resourceStatusText: 'Resource Status',
      authority: 'Authority',
      authorityText: 'Authority',
      resourceDescription: 'Resource Description',
      actionName: 'Function Name',
      actionToken: 'Function Identifier',
      actionStatus: 'Function Status',
      actionDescription: 'Function Description',
    },
    authority: {
      system: 'System Administrator',
      running: 'Operations Administrator',
      audit: 'System Auditor',
    },
    codeList: {
      resourceStatus: {
        show: 'Show',
        hide: 'Hide',
      },
      actionStatus: {
        show: 'Show',
        hide: 'Hide',
      },
    },
    header: {
      dialogTitle: 'Resource Management',
      operation: 'Operation',
      placeholder: 'Please select',
      innerDialogTitle: 'Function',
      tipInfo: {
        resourceTokenRepeat: 'Resource identifier already exists!',
        actionTokenRepeat: 'Function identifier already exists!',
      },
    },
  },
}
