import request from '@util/request'

// 增加
export function addData(obj) {
  return request({
    url: '/strategy/audit/strategy',
    method: 'post',
    data: obj || {},
  })
}

// 删除
export function deleteData(ids) {
  return request({
    url: `/strategy/audit/strategy/${ids}`,
    method: 'delete',
  })
}

// 修改
export function updateData(obj) {
  return request({
    url: `/strategy/audit/strategy`,
    method: 'put',
    data: obj || {},
  })
}

// 修改状态
export function changeState(obj) {
  return request({
    url: `/strategy/audit/strategy/state`,
    method: 'put',
    data: obj || {},
  })
}

// 查询列表
export function queryTableData(obj) {
  return request({
    url: '/strategy/audit/strategies',
    method: 'get',
    params: obj || {},
  })
}

// 上移下移
export function upAndDown(obj) {
  return request({
    url: '/strategy/audit/strategy/move',
    method: 'put',
    data: obj || {},
  })
}
// 查询事件类型下拉列表
export function queryEventTypeList(obj) {
  return request({
    url: '/strategy/audit/combo/event-types',
    method: 'get',
    params: obj || {},
  })
}
// 查询审计事件类型下拉列表
export function queryOutputEventTypeList(obj) {
  return request({
    url: '/strategy/audit/combo/audit-types',
    method: 'get',
    params: obj || {},
  })
}

// 查询转发外系统下拉列表
export function queryIsForwardList(obj) {
  return request({
    url: '/strategy/audit/combo/forward-strategies',
    method: 'get',
    params: obj || {},
  })
}

// 查询详情
export function queryDetails(obj) {
  return request({
    url: '/strategy/audit/strategies/details',
    method: 'get',
    params: obj || {},
  })
}
