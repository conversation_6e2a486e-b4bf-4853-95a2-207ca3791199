import request from '@util/request'

// 查询区域列表
export function queryAreaTable(obj) {
  return request({
    url: '/domainmanagement/domains',
    method: 'get',
    params: obj || {},
  })
}

// 添加区域
export function addArea(obj) {
  return request({
    url: '/domainmanagement/addDomain',
    method: 'post',
    data: obj || {},
  })
}

// 修改区域
export function updateArea(obj) {
  return request({
    url: `/domainmanagement/updateDomain`,
    method: 'put',
    data: obj || {},
  })
}

// (批量)删除区域
export function deleteArea(ids) {
  return request({
    url: `/domainmanagement/deleteDomains/${ids}`,
    method: 'delete',
  })
}
