<template>
  <div class="chart-wrapper">
    <el-row :gutter="20">
      <el-col :span="5">
        <el-select v-model="type" class="width-max">
          <el-option value="fashbone" label="鱼骨图"></el-option>
        </el-select>
      </el-col>
      <el-col :span="5">
        <template v-if="isNone">
          <el-select v-model="axis" class="width-max">
            <el-option value="x" label="横向"></el-option>
            <el-option value="y" label="纵向"></el-option>
          </el-select>
        </template>
      </el-col>
      <el-col :span="4" :offset="10" align="right">
        <el-button @click="clickLookData">
          数据结构
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <fishbone-chart :option="data"></fishbone-chart>
    </el-row>
    <el-dialog title="鱼骨图数据结构" :visible.sync="visible">
      <ace-editor v-model="jsonString" auto-complete lang="json" width="100%" height="400" @init="initEditorConfig()"></ace-editor>
    </el-dialog>
  </div>
</template>

<script>
import AceEditor from 'vue2-ace-editor'
import FishboneChart from '@comp/ChartFactory/dashboard/FishboneChart'

export default {
  name: 'Fishbone',
  components: {
    AceEditor,
    FishboneChart,
  },
  data() {
    return {
      visible: false,
      type: 'fishbone',
      axis: 'x',
      data: {
        hAxis: ['09:32:00', '10:13:00', '11:13:00', '12:13:00'],
        scatters: [
          { id: 'p5', name: '利用Web漏洞（*********,********）' },
          { id: 'p6', name: '利用缓冲区溢出漏洞（*********,********）' },
          { id: 'p7', name: '跨站脚本攻击' },
          { id: 'p8', name: 'SQL注入攻击告警' },
        ],
        links: [
          { source: '09:32:00', target: '10:13:00', name: '1-2' },
          { source: '10:13:00', target: '11:13:00', name: '2-5' },
          { source: '11:13:00', target: '12:13:00', name: '3-4' },
          { source: '09:32:00', target: 'p5', name: '1-5' },
          { source: '10:13:00', target: 'p6', name: '1-6' },
          { source: '11:13:00', target: 'p7', name: '2-7' },
          { source: '12:13:00', target: 'p8', name: '2-8' },
        ],
      },
    }
  },
  computed: {
    isNone() {
      return this.type !== 'bar-polar' && this.type !== 'bar-radial'
    },
    option() {
      return {
        type: this.type,
        axis: this.axis,
        data: this.data,
      }
    },
    jsonString() {
      return JSON.stringify(this.data, null, '\t')
    },
  },
  methods: {
    initEditorConfig(mode = 'json', theme = 'tomorrow_night_blue') {
      require(`brace/mode/${mode}`)
      require(`brace/theme/${theme}`)
    },
    clickLookData() {
      this.visible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
}

div.el-row:first-child {
  height: 32px;
}

div.el-row:nth-child(2) {
  height: calc(100% - 32px);
}
</style>
