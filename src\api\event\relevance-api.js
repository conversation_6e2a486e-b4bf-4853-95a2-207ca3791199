import request from '@util/request'

export function queryRelevanceTableData(obj) {
  return request({
    url: '/event/associated/events',
    method: 'get',
    params: obj || {},
  })
}

export function queryEventType(obj) {
  return request({
    url: '/event/associated/combo/event-types',
    method: 'get',
    params: obj || {},
  })
}

export function queryColumnsData(obj) {
  return request({
    url: '/event/associated/columns',
    method: 'get',
    params: obj || {},
  })
}

export function queryOriginalData(obj) {
  return request({
    url: '/event/associated/original/events',
    method: 'get',
    params: obj || {},
  })
}

// 查询关联事件详情
export function queryRelevanceTableDetail(id, time) {
  return request({
    url: `/event/associated/event/${id}/${time}`,
    method: 'get',
  })
}

// 查询鱼骨图数据
export function queryFishboneData(id) {
  return request({
    url: `/event/associated/fishBoard/${id}`,
    method: 'get',
  })
}

export function queryRelevanceTableTotal(obj) {
  return request({
    url: `/event/associated/events/total`,
    method: 'get',
    params: obj || {},
  })
}

export function updateColumnsData(obj) {
  return request({
    url: '/event/associated/columns',
    method: 'put',
    data: obj || {},
  })
}

export function downloadTableData(obj) {
  return request(
    {
      url: '/event/associated/download',
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

// 查询关联事件原始日志总数
export function queryOriginalTotal(obj) {
  return request({
    url: '/event/associated/original/events/total',
    method: 'get',
    params: obj || {},
  })
}
