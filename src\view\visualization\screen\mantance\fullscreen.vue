<template>
  <div style="display: contents">
    <i v-if="!isfull" class="el-icon-full-screen icon pointer left5" @click="togglefull"></i>
    <i v-if="isfull" class="el-icon-close icon pointer left5" @click="togglefull"></i>
  </div>
</template>
<script setup>
import { ref } from 'vue'
const isfull = ref(false)

function togglefull() {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
    isfull.value = true
  } else {
    if (document.exitFullscreen) {
      document.exitFullscreen()
      isfull.value = false
    }
  }
}
</script>
<style scoped>
.left5 {
  position: relative;
  left: 5px;
  color: #66b1ff;
  font-size: 14px;
}
</style>
