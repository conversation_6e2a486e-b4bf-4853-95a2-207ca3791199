const { notEmpty } = require('../util.js')

module.exports = {
  description: 'generate store',
  prompts: [
    {
      type: 'input',
      name: 'name',
      message: 'store name please',
      validate: notEmpty('name'),
    },
    {
      type: 'checkbox',
      name: 'blocks',
      message: 'Blocks:',
      choices: [
        {
          name: 'state',
          value: 'state',
          checked: true,
        },
        {
          name: 'mutations',
          value: 'mutations',
          checked: true,
        },
        {
          name: 'actions',
          value: 'actions',
          checked: true,
        },
      ],
      validate(value) {
        if (!value.includes('state') || !value.includes('mutations')) {
          return 'store require at least state and mutations'
        }
        return true
      },
    },
  ],
  actions(data) {
    const { blocks } = data
    const options = ['state', 'mutations']
    const joinFlag = `,
    `
    if (blocks.length === 3) {
      options.push('actions')
    }

    return [
      {
        type: 'add',
        path: `src/store/module/{{ lowerCase name }}.js`,
        templateFile: 'template/store/store.hbs',
        data: {
          options: options.join(joinFlag),
          state: blocks.includes('state'),
          mutation: blocks.includes('mutations'),
          action: blocks.includes('actions'),
        },
      },
    ]
  },
}
