export default {
  customParse: {
    title: '自定义解析',
    label: {
      patternValue: '字段值',
      patternKey: '多元组',
      patternName: '多元组',
      pattern: '表达式',
      devType: '日志源设备',
      devTypeName: '日志源设备',
      status: '状态',
      message: '日志原文',
      patternInfo: '划词信息',
      createTime: '入库时间',
      propDetail: '字段值的明细：',
      multiGroup: '多元组',
    },
    placeholder: {
      patternValue: '字段值',
      patternKey: '多元组',
      devType: '日志源设备',
      status: '状态',
      fuzzyField: '字段值',
    },
    tip: {
      wordTranslation: '自定义解析规则',
      selectContent: '鼠标选取您选择的内容',
      inputCharValid: '字段中只能使用字母数字，如[a-zA-X0-9_]',
      validKeyword: '请重新划词,划词首尾不允许为空格。',
      repeatKeyword: '不允许重复划词，请重新选择。',
      selDevType: '请先选择设备类型后勾选特征值信息。',
      eventTypeRequired: '请在划词中选择特征值多元组，此信息项必填。',
      neighborNoSelected: '相邻划词不允许选中。',
    },
  },
}
