<!--
 * @Description: 角色管理
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('management.role.infoItem.roleName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQuery('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddRole">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteRole">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.roleName"
                  class="width-max"
                  clearable
                  :placeholder="$t('management.role.placeholder.roleName')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.roleStatus"
                  :placeholder="$t('management.role.placeholder.roleStatus')"
                  clearable
                  class="width-max"
                  @change="inputQuery('e')"
                >
                  <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.roleDescription"
                  class="width-max"
                  clearable
                  :placeholder="$t('management.role.placeholder.roleDescription')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="4" align="right" :offset="5">
                <el-button v-has="'query'" @click="inputQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="seniorQuery"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.role.header.dialogTitle') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="roleTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @row-dblclick="dblclickRoleDisplayDetail"
          @current-change="roleTableRowChange"
          @selection-change="roleTableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="roleName" :label="$t('management.role.infoItem.roleName')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="roleStatusText" :label="$t('management.role.infoItem.roleStatus')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="roleDescription" :label="$t('management.role.infoItem.roleDescription')" show-overflow-tooltip></el-table-column>
          <el-table-column width="240" fixed="right">
            <template slot-scope="scope">
              <el-button v-has="'grant'" class="el-button--blue" @click="clickGrantRole(scope.row)">
                {{ $t('button.grant') }}
              </el-button>
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateRole(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteRole(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="roleTableSizeChange"
        @current-change="roleTableCurrentChange"
      ></el-pagination>
    </footer>
    <au-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :width="'35%'"
      :form="dialog.form.add"
      @on-submit="clickSubmitAddRole"
    ></au-dialog>
    <au-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :width="'35%'"
      :form="dialog.form.update"
      @on-submit="clickSubmitUpdateRole"
    ></au-dialog>
    <au-dialog
      :visible.sync="dialog.visible.detail"
      :title="dialog.title.detail"
      :width="'35%'"
      :actions="false"
      readonly
      :form="dialog.form.detail"
    ></au-dialog>
    <grant-dialog
      :visible.sync="dialog.visible.grant"
      :title="dialog.title.grant"
      :sel="dialog.form.grant.sel"
      :width="'35%'"
      @on-submit="clickSubmitGrant"
    ></grant-dialog>
  </div>
</template>

<script>
import AuDialog from './TheRoleAddUpdateDialog'
import GrantDialog from './TheRoleGrantDialog'
import { prompt } from '@util/prompt'
import { addInfo, delInfo, updInfo, getList, getRoleInfo, grantRole, getGrantedUsers, getAllResourcesList } from '@api/management/role-api'
import { debounce } from '@util/effect'

export default {
  name: 'ManagementRole',
  components: {
    AuDialog,
    GrantDialog,
  },
  data() {
    return {
      data: {
        table: [],
        loading: false,
        selected: [],
        roleIds: [],
      },
      isShow: false,
      allResources: [],
      queryInput: {
        fuzzyField: '',
        roleName: '',
        roleStatus: '',
        roleDescription: '',
      },
      statusList: [
        {
          value: '0',
          label: this.$t('management.role.roleStatus.show'),
        },
        {
          value: '1',
          label: this.$t('management.role.roleStatus.hidden'),
        },
      ],
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
        visible: true,
      },
      dialog: {
        visible: {
          add: false,
          update: false,
          query: false,
          detail: false,
          grant: false,
        },
        form: {
          add: {
            roleName: '',
            roleStatus: '',
            roleDescription: '',
            actions: [],
            resources: [],
            allResources: [],
          },
          update: {
            roleName: '',
            roleStatus: '',
            roleDescription: '',
            actions: [],
            resources: [],
            allResources: [],
          },
          query: {
            model: {
              roleName: '',
              roleStatus: '',
              roleDescription: '',
            },
            info: {
              roleName: {
                key: 'roleName',
                label: this.$t('management.role.infoItem.roleName'),
                value: '',
              },
              roleStatus: {
                key: 'roleStatus',
                label: this.$t('management.role.infoItem.roleStatus'),
                value: '',
              },
              roleDescription: {
                key: 'roleDescription',
                label: this.$t('management.role.infoItem.roleDescription'),
                value: '',
              },
            },
          },
          detail: {
            model: {
              roleName: '',
              roleStatus: '',
              roleDescription: '',
              actions: [],
              resources: [],
            },
            info: {
              roleName: {
                key: 'roleName',
                label: this.$t('management.role.infoItem.roleName'),
                value: '',
              },
              roleStatus: {
                key: 'roleStatus',
                label: this.$t('management.role.infoItem.roleStatus'),
                value: '',
              },
              roleDescription: {
                key: 'roleDescription',
                label: this.$t('management.role.infoItem.roleDescription'),
                value: '',
              },
              grantResources: {
                key: 'grantResources',
                label: this.$t('management.role.infoItem.grantResources'),
                value: '',
              },
            },
          },
          grant: {
            sel: [],
          },
        },
        title: {
          add: this.$t('dialog.title.add', [this.$t('management.role.header.dialogTitle')]),
          update: this.$t('dialog.title.update', [this.$t('management.role.header.dialogTitle')]),
          query: this.$t('dialog.title.query', [this.$t('management.role.header.dialogTitle')]),
          detail: this.$t('dialog.title.detail', [this.$t('management.role.header.dialogTitle')]),
          grant: this.$t('dialog.title.grant', [this.$t('management.role.header.dialogTitle')]),
        },
        query: {
          filter: [],
        },
      },
      queryDebounce: null,
    }
  },
  mounted() {
    this.getRoleTableData()
    this.initDebounce()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const params = {
          ...this.queryInput,
          pageNum: this.pagination.pageNum,
          pageSize: this.pagination.pageSize,
        }
        this.getRoleTableData(params)
      }, 500)
    },
    // 查询列表
    getRoleTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      getList(params).then((res) => {
        if (res) {
          this.data.table = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 添加角色
    addRole(form) {
      addInfo(form).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.resetQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'management.role.tipsError',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 删除角色
    deleteRole(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        delInfo(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.inputQuery()
              }
            )
          } else if (res === 3) {
            prompt({
              i18nCode: 'tip.delete.use',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 修改角色
    updateRole(form) {
      updInfo(form).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.inputQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'management.role.tipsError',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 获取角色详情
    getRoleDetail(roleId) {
      getRoleInfo(roleId).then((res) => {
        this.dialog.form.detail = res
      })
    },
    // 更新角色授权
    updateRoleGrantRole(obj) {
      grantRole(obj).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.operate.success',
            i18nParam: [this.$t('button.grant')],
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.operate.error',
            i18nParam: [this.$t('button.grant')],
            type: 'error',
          })
        }
      })
    },
    // 点击添加角色按钮
    clickAddRole() {
      getAllResourcesList().then((res) => {
        this.dialog.form.add.roleId = ''
        this.dialog.form.add.roleName = ''
        this.dialog.form.add.roleStatus = ''
        this.dialog.form.add.roleDescription = ''
        this.dialog.form.add.actions = []
        this.dialog.form.add.resources = []
        this.dialog.form.add.allResources = res
        this.dialog.visible.add = true
      })
    },
    // 点击批量删除按钮
    clickBatchDeleteRole() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.roleId).toString()
        this.deleteRole(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击角色授权按钮
    clickGrantRole(row) {
      getGrantedUsers(row.roleId).then((res) => {
        if (res.length > 0) {
          this.dialog.form.grant.sel = res.split(',')
        } else {
          this.dialog.form.grant.sel = []
        }
        this.data.roleIds = []
        this.data.roleIds.push(row.roleId)
        this.dialog.visible.grant = true
      })
    },
    // 点击修改角色按钮
    async clickUpdateRole(row) {
      let list = []
      await getAllResourcesList().then((res) => {
        list = res.concat()
      })
      await getRoleInfo(row.roleId).then((res) => {
        row.actions = res.actions
        row.resources = res.resources
        this.dialog.form.update = {
          allResources: list,
          ...row,
        }
        this.dialog.visible.update = true
      })
    },
    // 点击删除角色按钮
    clickDeleteRole(row) {
      this.deleteRole(row.roleId)
    },
    // 双击获取详情
    dblclickRoleDisplayDetail(row) {
      this.getRoleDetail(row.roleId)
      this.dialog.visible.detail = true
    },
    // 点击提交 添加角色
    clickSubmitAddRole(obj) {
      const params = {
        roleName: obj.roleName,
        roleStatus: obj.roleStatus,
        roleDescription: obj.roleDescription,
        resources: obj.resources,
        actions: obj.actions,
      }
      this.addRole(params)
    },
    // 点击提交 修改角色
    clickSubmitUpdateRole(obj) {
      const params = {
        roleId: obj.roleId,
        roleName: obj.roleName,
        roleStatus: obj.roleStatus,
        roleDescription: obj.roleDescription,
        resources: obj.resources,
        actions: obj.actions,
      }
      this.updateRole(params)
    },
    // 点击提交 角色授权
    clickSubmitGrant(users) {
      this.updateRoleGrantRole({
        users: users,
        roles: this.data.roleIds,
      })
    },
    // 获取当前行
    roleTableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 改变分页
    roleTableSizeChange(size) {
      this.pagination.pageSize = size
      this.inputQuery('e')
    },
    // 点击跳页
    roleTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQuery()
    },
    // 列表复选框
    roleTableSelectsChange(select) {
      this.data.selected = select
    },
    // 输入框搜索方法
    inputQuery(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    // 清空搜索条件
    clearQuery() {
      this.queryInput = {
        fuzzyField: '',
        roleName: '',
        roleStatus: '',
        roleDescription: '',
      }
      this.pagination.pageNum = 1
    },
    // 重置并查询列表
    resetQuery() {
      this.clearQuery()
      this.queryDebounce()
    },
  },
}
</script>
