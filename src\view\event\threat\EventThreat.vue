<!--
 * @Description: 威胁事件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <div class="router-wrap-table">
    <table-header
      :condition.sync="query"
      @on-change="changeQueryTable"
      @on-strategy-config="clickStrategyConfig"
      @on-download="clickDownload"
    ></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-scroll="table.scroll"
      :table-data="table.data"
      @on-scroll="scrollTable"
      @on-select="clickSelectRows"
      @on-detail="clickDetail"
    ></table-body>
    <table-footer :total-loading="table.totalLoading" :total="table.total" :nomore="table.nomore"></table-footer>
    <detail-dialog
      :visible.sync="dialog.detail.visible"
      :title-name="title"
      :model="dialog.detail.model"
      :actions="false"
      @on-drawer-show="clickDetailDrawer"
    ></detail-dialog>
    <!--抽屉-->
    <drawer :visible.sync="drawer.visible" :loading="drawer.loading" :detail-data="drawer.data"></drawer>
    <strategy-dialog
      :width="'40%'"
      :visible.sync="dialog.strategy.visible"
      :loading="dialog.strategy.loading"
      :strategy-data="dialog.strategy.data"
      @on-submit="doSubmitStrategy"
    ></strategy-dialog>
  </div>
</template>

<script>
import TableHeader from './TheThreatHeader'
import TableBody from './TheThreatBody'
import TableFooter from './TheThreatFooter'
import DetailDialog from './TheDetailDialog'
import Drawer from './TheLogDetailDrawer'
import StrategyDialog from './TheStrategyDialog'
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import { queryTable, queryTableTotal, downloadThreatEvent, queryStrategy, updateStrategy } from '@api/event/threat-api'

export default {
  name: 'EventThreat',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
    Drawer,
    StrategyDialog,
  },
  data() {
    return {
      title: this.$t('event.threat.title'),
      query: {
        high: false,
        fuzzyField: '',
        form: {
          eventLevel: '',
          eventType: '',
          eventTime: '',
        },
        typeList: [],
      },
      table: {
        loading: false,
        scroll: true,
        data: [],
        totalLoading: false,
        total: 0,
        nomore: false,
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      },
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
        strategy: {
          visible: false,
          loading: false,
          data: {},
        },
      },
      drawer: {
        visible: false,
        loading: false,
        data: [],
      },
    }
  },
  mounted() {
    this.getThreatTableData()
  },
  methods: {
    changeQueryTable() {
      this.table.nomore = false
      this.table.data = []
      const param = this.handleQueryParams()
      this.getThreatTableData(param)
    },
    scrollTable() {
      const params = this.handleQueryParams(true)
      this.getThreatTableData(params, false)
    },
    handleQueryParams(scroll = false) {
      let params = {
        pageSize: this.pagination.pageSize,
      }
      if (scroll) {
        const lastRow = this.table.data[this.table.data.length - 1] || null
        if (lastRow) {
          params = Object.assign(params, {
            eventId: lastRow.eventId,
            timestamp: lastRow.eventTime,
          })
        }
      }
      if (this.query.high) {
        params = Object.assign(params, {
          eventLevel: this.query.form.eventLevel,
          eventType: this.query.form.eventType,
          eventTime: this.query.form.eventTime ? `${this.query.form.eventTime[0]},${this.query.form.eventTime[1]}` : '',
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.fuzzyField,
        })
      }
      return params
    },
    getThreatTableData(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      },
      total = true
    ) {
      this.table.scroll = true
      this.table.loading = true
      queryTable(param).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.table.data.push(...res)
          this.table.scroll = true
          if (this.table.data.length > this.pagination.pageSize) {
            this.table.nomore = true
          }
        } else {
          this.table.data.push(...res)
          this.table.scroll = false
        }
        this.table.loading = false
        if (total) {
          this.getThreatEventTotal(param)
        }
      })
    },
    getThreatEventTotal(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.table.totalLoading = true
      queryTableTotal(param).then((res) => {
        this.table.total = res
        this.table.totalLoading = false
      })
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    clickDetailDrawer(row) {
      this.drawer.visible = true
      this.drawer.data = row
    },
    clickSelectRows(select) {
      this.table.selected = select
    },
    clickStrategyConfig() {
      queryStrategy().then((res) => {
        if (!isEmpty(res.forwardId)) {
          res.forwardId = res.forwardId.split(',')
        }
        this.dialog.strategy.visible = true
        this.dialog.strategy.data = res
        this.dialog.strategy.loading = false
      })
    },
    doSubmitStrategy(data) {
      const param = {
        forwardId: data.forwardId.toString(),
        forwardName: data.forwardName,
        id: data.id,
        status: data.status,
      }
      updateStrategy(param).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.config.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.config.error',
            type: 'error',
          })
        }
      })
    },
    clickDownload() {
      const ids = this.table.selected.length !== 0 ? this.table.selected.map((item) => item.eventId).toString() : null
      let params = this.handleQueryParams()
      params = Object.assign(params, {
        downloadId: ids,
      })
      this.downloadEvent(params)
    },
    downloadEvent(params = {}) {
      this.table.loading = true
      downloadThreatEvent(params).then((res) => {
        if (res) {
          this.table.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
