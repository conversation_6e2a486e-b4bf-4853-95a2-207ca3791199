<!--
 * @Description: 日志源类型 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023-02-21
 * @Editor:
 * @EditDate: 2023-02-21
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
      >
        <el-table-column
          v-for="(item, key) in columns"
          :key="key"
          :prop="item"
          :label="$t(`collector.logSource.label.${item}`)"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column fixed="right" width="140">
          <template slot-scope="scope">
            <section v-if="scope.row.isDefault === 0">
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </section>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      columns: ['manufact', 'categoryName', 'typeName'],
    }
  },
  methods: {
    clickSelectRows(sel) {
      this.$emit('on-select', sel)
    },
    clickUpdate(row) {
      this.$emit('on-update', row)
    },
    clickDelete(row) {
      this.$emit('on-delete', row)
    },
  },
}
</script>
