<!--
 * @Description: 登录首页 - 修改用户信息弹窗
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="userDialog" :visible="visible" :title="$t('layout.setting.user.label')" width="60%" @on-close="close" @on-submit="submit">
    <el-form ref="userForm" :model="userForm.model" :rules="userForm.rules" label-width="100px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.username')" prop="username">
            <el-input v-model="userForm.model.username" class="width-small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.nickname')" prop="nickname">
            <el-input v-model="userForm.model.nickname" class="width-small" maxlength="64"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.email')" prop="email">
            <el-input v-model="userForm.model.email" class="width-small" maxlength="32"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.mobilephone')" prop="mobilephone">
            <el-input v-model="userForm.model.mobilephone" class="width-small"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.telephone')" prop="telephone">
            <el-input v-model="userForm.model.telephone" class="width-small"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('layout.setting.user.menu')" prop="menu">
            <el-cascader
              v-model="userForm.model.menu"
              expand-trigger="hover"
              :options="data.menu"
              :props="data.menuRule"
              clearable
              class="width-small"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateEmail, validateCellphone, validateTelephone } from '@util/validate'
import { queryMenuData, queryUserInfoData, updateUserInfoData } from '@api/layout/layout-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    const validatorCellphone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validateCellphone(value)) {
        callback(new Error(this.$t('validate.comm.cellphone')))
      } else {
        callback()
      }
    }
    const validatorTelephone = (rule, value, callback) => {
      if (value.trim() === '') {
        callback()
      } else if (!validateTelephone(value)) {
        callback(new Error(this.$t('validate.comm.telephone')))
      } else {
        callback()
      }
    }

    return {
      userForm: {
        model: {
          username: '',
          nickname: '',
          email: '',
          mobilephone: '',
          telephone: '',
          menu: [],
        },
        rules: {
          username: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          email: [{ validator: validatorEmail, trigger: 'blur' }],
          mobilephone: [{ validator: validatorCellphone, trigger: 'blur' }],
          telephone: [{ validator: validatorTelephone, trigger: 'blur' }],
        },
      },
      data: {
        menuRule: {
          value: 'menuId',
          label: 'menuName',
          children: 'children',
        },
        menu: [],
      },
    }
  },
  mounted() {
    this.preloadData()
  },
  methods: {
    preloadData() {
      this.getMenu()
      this.getUserInfo()
    },
    close() {
      this.$emit('on-display')
      this.$refs['userForm'].resetFields()
      // this.preloadData();
    },
    submit() {
      this.$refs['userForm'].validate((valid) => {
        if (valid) {
          let defaultMenu = this.userForm.model.menu
          if (typeof defaultMenu !== 'string') {
            const menuLength = defaultMenu.length
            defaultMenu = menuLength > 0 ? this.userForm.model.menu[menuLength - 1] : ''
          }
          this.updateUserInfo({
            userFullName: this.userForm.model.username,
            userSortName: this.userForm.model.nickname,
            userMail: this.userForm.model.email,
            userPhone: this.userForm.model.telephone,
            userMobile: this.userForm.model.mobilephone,
            defaultMenu: defaultMenu,
          })
          this.close()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.userDialog.end()
    },
    getMenu() {
      queryMenuData().then((res) => {
        this.data.menu = res
      })
    },
    getUserInfo() {
      queryUserInfoData().then((res) => {
        this.userForm.model.username = res.userFullName ? res.userFullName : ''
        this.userForm.model.nickname = res.userSortName ? res.userSortName : ''
        this.userForm.model.email = res.userMail ? res.userMail : ''
        this.userForm.model.mobilephone = res.userMobile ? res.userMobile : ''
        this.userForm.model.telephone = res.userPhone ? res.userPhone : ''
        this.userForm.model.menu = res.defaultMenu ? res.defaultMenu : ''
      })
    },
    updateUserInfo(obj) {
      updateUserInfoData(obj).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.preloadData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
