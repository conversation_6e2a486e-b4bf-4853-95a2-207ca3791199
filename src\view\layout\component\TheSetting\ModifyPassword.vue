<!--
 * @Description: 登录首页 - 修改密码弹窗
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="passwordDialog"
    :visible="visible"
    :title="$t('layout.setting.password.label')"
    width="40%"
    @on-close="close"
    @on-submit="submit"
  >
    <el-form ref="passwordForm" class="password-form" :model="password.form" :rules="password.rules" label-width="25%">
      <el-form-item :label="$t('layout.setting.password.old')" prop="old">
        <el-input v-model="password.form.old" class="width-mini" show-password @paste.native.capture.prevent="disablePaste()"></el-input>
        <aside slot="error" class="soc-form-error-text">
          {{ password.error.old }}
        </aside>
      </el-form-item>
      <el-form-item :label="$t('layout.setting.password.new')" prop="new">
        <el-input v-model="password.form.new" class="width-mini" show-password @paste.native.capture.prevent="disablePaste()"></el-input>
        <aside slot="error" class="soc-form-error-text">
          {{ password.error.new }}
        </aside>
      </el-form-item>
      <el-form-item :label="$t('layout.setting.password.confirm')" prop="confirm">
        <el-input v-model="password.form.confirm" class="width-mini" show-password @paste.native.capture.prevent="disablePaste()"></el-input>
        <aside slot="error" class="soc-form-error-text">
          {{ password.error.confirm }}
        </aside>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>
<script>
import { JSEncrypt } from 'jsencrypt'
import CustomDialog from '@comp/CustomDialog'
import { validatePassword } from '@util/validate'
import { prompt } from '@util/prompt'
import { updateUserPasswordData } from '@api/layout/layout-api'
import { querySystemConfigData } from '@api/management/system-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatorOldPassword = (rule, value, callback) => {
      if (value === '') {
        callback((this.password.error.old = this.$t('validate.password.old.empty')))
      } else {
        callback()
      }
    }
    const getValidText = () => {
      const complexity = this.complexity
      const passwordConfig = {
        includeNumbers: complexity.includes('number'),
        includeLowercase: complexity.includes('lowercase'),
        includeUppercase: complexity.includes('uppercase'),
        includeSpecial: complexity.includes('special'),
        minLength: this.minLength,
      }
      let txt = '密码需要包含'
      if (passwordConfig.includeNumbers) {
        txt += ' 数字'
      }
      if (passwordConfig.includeLowercase) {
        txt += ' 小写字母'
      }
      if (passwordConfig.includeUppercase) {
        txt += ' 大写字母'
      }
      if (passwordConfig.includeSpecial) {
        txt += ' 特殊字符'
      }
      txt += ` 且长度不少于 ${passwordConfig.minLength} 位`
      return txt
    }
    const createPasswordRegex = () => {
      const complexity = this.complexity
      const passwordConfig = {
        includeNumbers: complexity.includes('number'),
        includeLowercase: complexity.includes('lowercase'),
        includeUppercase: complexity.includes('uppercase'),
        includeSpecial: complexity.includes('special'),
        minLength: this.minLength,
      }
      let regex = '^'
      if (passwordConfig.includeNumbers) {
        regex += '(?=.*\\d)'
      }
      if (passwordConfig.includeLowercase) {
        regex += '(?=.*[a-z])'
      }
      if (passwordConfig.includeUppercase) {
        regex += '(?=.*[A-Z])'
      }
      if (passwordConfig.includeSpecial) {
        regex += '(?=.*[!@#$%^&*(),.?":{}|<>])'
      }
      regex += `.{${passwordConfig.minLength},}$`
      return new RegExp(regex)
    }
    const complexityValidator = (password) => {
      const passwordRegex = createPasswordRegex()
      const isValid = passwordRegex.test(password)
      return isValid
    }
    const validatorNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback((this.password.error.new = this.$t('validate.password.new.empty')))
      } else if (value === this.password.form.old) {
        callback((this.password.error.new = this.$t('validate.password.new.compare')))
      } else if (!complexityValidator(value)) {
        callback((this.password.error.new = getValidText(this.complexity)))
      } else {
        callback()
      }
    }
    const validatorConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback((this.password.error.confirm = this.$t('validate.password.confirm.empty')))
      } else if (value !== this.password.form.new) {
        callback((this.password.error.confirm = this.$t('validate.password.confirm.compare')))
      } else {
        callback()
      }
    }
    return {
      complexity: [],
      minLength: 6,
      password: {
        form: {
          new: '',
          old: '',
          confirm: '',
        },
        rules: {
          old: [{ required: true, validator: validatorOldPassword, trigger: 'blur' }],
          new: [{ required: true, validator: validatorNewPassword, trigger: 'blur' }],
          confirm: [{ required: true, validator: validatorConfirmPassword, trigger: 'blur' }],
        },
        error: {
          new: '',
          old: '',
          confirm: '',
        },
      },
    }
  },
  watch: {
    visible(val) {
      if (val) {
        querySystemConfigData().then((res) => {
          this.complexity = res.passwordComplexity?.split(',') || []
          this.minLength = res.minPasswordLength || 6
        })
      }
    },
  },
  methods: {
    close() {
      this.$refs.passwordForm.resetFields()
      this.$emit('on-display')
    },
    submit() {
      this.$refs.passwordForm.validate((valid) => {
        if (valid) {
          const param = this.handlePasswordParam()
          this.updateUserPassword(param)
          this.close()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.passwordDialog.end()
    },
    handlePasswordParam() {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.$store.getters.publicKey)
      return {
        userId: this.$store.getters.userID,
        newPassword: encryptor.encrypt(this.password.form.new),
        oldPassword: encryptor.encrypt(this.password.form.old),
        confirmPassword: encryptor.encrypt(this.password.form.confirm),
      }
    },
    disablePaste() {
      return null
    },
    updateUserPassword(obj) {
      updateUserPasswordData(obj).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.update.password',
            type: 'success',
          })
          this.$router.replace({
            path: '/login',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
