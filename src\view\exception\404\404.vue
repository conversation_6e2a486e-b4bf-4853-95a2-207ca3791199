<!--
 * @Description: 异常页面 - 路由异常 - 404
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="soc-http404">
    <el-row class="soc-http404-container">
      <el-col :span="12" class="soc-http404-img">
        <img class="soc-http404-img-bg" src="@/asset/image/exception/404/404.png" alt="404" />
        <img class="soc-http404-img-cloud left" src="@/asset/image/exception/404/404-cloud.png" alt="404" />
        <img class="soc-http404-img-cloud mid" src="@/asset/image/exception/404/404-cloud.png" alt="404" />
        <img class="soc-http404-img-cloud right" src="@/asset/image/exception/404/404-cloud.png" alt="404" />
      </el-col>
      <el-col :span="12" class="soc-http404-context">
        <section v-if="department" class="soc-http404-context-department">
          {{ $t('exception.page404.department') }}
        </section>
        <section v-if="info" class="soc-http404-context-info">
          <p>
            <span>{{ $t('exception.page404.copyright.label') }}</span>
            :
            <span>{{ $t('exception.page404.copyright.name') }}</span>
          </p>
          <p>
            <span>{{ $t('exception.page404.author.label') }}</span>
            :
            <a class="link-type">{{ $t('exception.page404.author.name') }}</a>
          </p>
        </section>
        <section class="soc-http404-context-headline">
          {{ $t('exception.page404.instructions') }}
        </section>
        <section class="soc-http404-context-info">
          {{ $t('exception.page404.check') }}
        </section>
        <el-button v-if="backhome" plain class="soc-http404-context-backhome" @click="goRecently()">
          {{ $t('exception.page404.home') }}
        </el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Page404',
  data() {
    return {
      department: false,
      info: false,
      backhome: true,
    }
  },
  methods: {
    goRecently() {
      this.$router.replace({
        path: this.$store.getters.homePath || this.$store.getters.homePath !== '' ? this.$store.getters.homePath : '/layout',
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.soc-http404 {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);

  &-container {
    position: relative;
    width: 1200px;
    padding: 0 50px;
    overflow: hidden;

    .soc-http404-img {
      position: relative;
      width: 600px;
      float: left;
      overflow: hidden;

      &-bg {
        width: 100%;
      }

      &-cloud {
        position: absolute;

        &.left {
          top: 17px;
          left: 220px;
          width: 80px;
          animation: cloudLeft 2s linear 1s forwards;
          opacity: 0;
        }

        &.mid {
          top: 10px;
          left: 420px;
          width: 46px;
          animation: cloudMid 2s linear 1.2s forwards;
          opacity: 0;
        }

        &.right {
          top: 100px;
          left: 500px;
          width: 62px;
          animation: cloudRight 2s linear 1s forwards;
          opacity: 0;
        }

        @keyframes cloudLeft {
          0% {
            top: 17px;
            left: 220px;
            opacity: 0;
          }
          20% {
            top: 33px;
            left: 188px;
            opacity: 1;
          }
          80% {
            top: 81px;
            left: 92px;
            opacity: 1;
          }
          100% {
            top: 97px;
            left: 60px;
            opacity: 0;
          }
        }
        @keyframes cloudMid {
          0% {
            top: 10px;
            left: 420px;
            opacity: 0;
          }
          20% {
            top: 40px;
            left: 360px;
            opacity: 1;
          }
          70% {
            top: 130px;
            left: 180px;
            opacity: 1;
          }
          100% {
            top: 160px;
            left: 120px;
            opacity: 0;
          }
        }
        @keyframes cloudRight {
          0% {
            top: 100px;
            left: 500px;
            opacity: 0;
          }
          20% {
            top: 120px;
            left: 460px;
            opacity: 1;
          }
          80% {
            top: 180px;
            left: 340px;
            opacity: 1;
          }
          100% {
            top: 200px;
            left: 300px;
            opacity: 0;
          }
        }
      }
    }

    .soc-http404-context {
      position: relative;
      float: left;
      width: 300px;
      padding: 30px 0;
      overflow: hidden;

      &-department {
        margin-bottom: 20px;
        line-height: 40px;
        font-size: 32px;
        font-weight: bold;
        opacity: 0;
        animation: slideUp 0.5s forwards;
      }

      &-headline {
        margin-bottom: 10px;
        line-height: 24px;
        font-size: 20px;
        font-weight: bold;
        opacity: 0;
        animation: slideUp 0.5s 0.1s forwards;
      }

      &-info {
        margin-bottom: 30px;
        line-height: 21px;
        font-size: 13px;
        opacity: 0;
        animation: slideUp 0.5s 0.2s forwards;
      }

      &-backhome {
        border-radius: 100px;
        font-size: 14px;
        opacity: 0;
        animation: slideUp 0.5s 0.3s forwards;
        cursor: pointer;
        @include theme('background-color', primary-color);
      }

      @keyframes slideUp {
        0% {
          transform: translateY(60px);
          opacity: 0;
        }
        100% {
          transform: translateY(0);
          opacity: 1;
        }
      }
    }
  }
}
</style>
