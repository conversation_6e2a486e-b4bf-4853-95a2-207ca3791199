<!--
 * @Description: 性能事件 - 底部翻页
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <section class="table-footer">
    <el-pagination
      v-if="filterCondition.visible"
      small
      background
      align="right"
      :current-page="filterCondition.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="filterCondition.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="filterCondition.total"
      @size-change="tableSizeChange"
      @current-change="tablePageChange"
    ></el-pagination>
  </section>
</template>

<script>
export default {
  name: 'TableFooter',
  props: {
    pagination: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.pagination,
    }
  },
  watch: {
    pagination(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:pagination', newCondition)
    },
  },
  methods: {
    tableSizeChange(size) {
      this.$emit('size-change', size)
    },
    tablePageChange(pageNum) {
      this.$emit('page-change', pageNum)
    },
  },
}
</script>

<style scoped></style>
