<!--
 * @Description: 监控器 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-1
 * @Editor:
 * @EditDate: 2021-08-1
-->
<template>
  <section class="table-body">
    <section class="tree-box">
      <div>
        <el-tree :data="monitorTypeOption" :props="defaultProps" @node-click="clickNode"></el-tree>
      </div>
      <div class="table-box">
        <header class="table-body-header">
          <h2 class="table-body-title">
            {{ title }}
          </h2>
        </header>
        <main v-loading="tableLoading" class="table-body-main">
          <el-table
            ref="monitorTable"
            :data="tableData"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
            height="100%"
            fit
            @selection-change="clickSelect"
          >
            <el-table-column type="selection" prop="monitorId"></el-table-column>
            <el-table-column :label="$t('monitor.management.props.health')" width="60">
              <template slot-scope="scope">
                <el-tooltip placement="top" effect="light">
                  <div slot="content" v-html="showHealthTip(scope.row)"></div>
                  <i class="soc-icon-menu-asset-situation" :class="transformHealthIcon(scope.row)"></i>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              v-for="(item, key) in columns"
              :key="key"
              :prop="item"
              :label="$t(`monitor.management.props.${item}`)"
              show-overflow-tooltip
            >
              <template slot-scope="scope">
                <p v-if="item === 'agentStatus'">
                  {{ scope.row.agentStatus === 1 ? $t('monitor.management.status.online') : $t('monitor.management.status.offline') }}
                </p>
                <p v-else>
                  {{ scope.row[item] }}
                </p>
              </template>
            </el-table-column>
            <el-table-column :label="$t('monitor.management.props.stateChange')" width="80">
              <template slot-scope="scope">
                <el-switch
                  v-model="scope.row.monitorEnabled"
                  active-value="1"
                  inactive-value="0"
                  :disabled="scope.row.agentStatus === 0"
                  @change="toggleStatus(scope.row)"
                >
                  {{ scope.row.monitorEnabled }}
                </el-switch>
              </template>
            </el-table-column>
            <el-table-column fixed="right" width="300">
              <template slot-scope="scope">
                <el-button v-has="'query'" class="el-button--blue" :disabled="scope.row.agentStatus === 0" @click="clickView(scope.row)">
                  {{ $t('button.view') }}
                </el-button>
                <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
                  {{ $t('button.detail') }}
                </el-button>
                <el-button v-has="'update'" :disabled="scope.row.agentStatus === 0" class="el-button--blue" @click="clickUpdate(scope.row)">
                  {{ $t('button.update') }}
                </el-button>
                <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                  {{ $t('button.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </main>
      </div>
    </section>
  </section>
</template>

<script>
export default {
  props: {
    title: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    monitorTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      defaultProps: {
        children: 'children',
        label: 'label',
      },
      columns: ['monitorName', 'monitorTypeName', 'edName', 'agentIp', 'agentStatus'],
    }
  },
  methods: {
    doLayout() {
      this.$refs.monitorTable.doLayout()
    },
    clickNode(data) {
      this.$emit('on-node', data)
    },
    clickSelect(sel) {
      this.$emit('on-select', sel)
    },
    clickDelete(row) {
      this.$emit('on-delete', row)
    },
    clickUpdate(row) {
      this.$emit('on-update', row)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    toggleStatus(row) {
      this.$emit('toggle-status', row)
    },
    clickView(row) {
      this.$emit('on-view', row)
    },
    showHealthTip(row) {
      if (row.deviceStatus === 1) {
        return this.$t('monitor.management.props.healthTip.unableConnect')
      } else if (row.noData === 1) {
        return this.$t('monitor.management.props.healthTip.noData')
      } else {
        return this.$t('monitor.management.props.healthTip.normal')
      }
    },
    transformHealthIcon(row) {
      if (row.deviceStatus === 1) {
        return 'monitor-health-grey'
      } else if (row.noData === 1) {
        return 'monitor-health-red'
      } else {
        return 'monitor-health-green'
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.tree-box {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;

  div.table-box {
    width: 100%;
    overflow: auto;
  }

  .el-tree {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}

::v-deep .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.3);
}

.table-body-main {
  .monitor-health-grey {
    font-size: 16px;
    color: #b7b6b6;
  }

  .monitor-health-red {
    font-size: 16px;
    color: #d21712;
  }

  .monitor-health-green {
    font-size: 16px;
    color: #31ab10;
  }
}
</style>
