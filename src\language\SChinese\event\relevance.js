export default {
  relevance: {
    table: {
      eventName: '事件名称',
      policyName: '策略名称',
      eventLevelName: '事件等级',
      createDate: '产生时间',
      eventTypeName: '事件名称',
      eventCategoryName: '事件类型',
      updateDate: '更新时间',
      count: '次数',
      handel: '操作',
      typeName: '事件类型',
      level: '事件等级',
      srcIp: '源IP',
      dstIp: '目的IP',
      dateTime: '时间',
      raw: '日志原文',
      eventDesc: '事件描述',
    },
    time: {
      createDateStart: '产生时间起',
      createDateEnd: '产生时间止',
      updateDateStart: '更新时间起',
      updateDateEnd: '更新结束止',
    },
    dialog: {
      detailTitle: '关联事件详情',
      colTitle: '关联事件自定义列',
    },
    header: '关联事件',
    detailOriginalColumn: {
      type2Name: '原始事件名称',
      eventName: '事件类型',
      eventCategoryName: '事件类别',
      level: '事件等级',
      srcIp: '源IP',
      dstIp: '目的IP',
      dateTime: '时间',
      raw: '日志原文',
    },
    chart: {
      attackCount: '攻击次数',
      srcIp: '源IP',
      dstIp: '目的IP',
      level: '等级',
    },
  },
}
