<!--
 * @Description: 资产发现 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="36%">
      <template>
        <el-tabs v-model="form.activeName" type="card" @tab-click="handleClick">
          <el-tab-pane :label="$t('asset.management.baseInfo')" name="first">
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.assetName')" prop="assetName">
                  <el-input v-model.trim="form.model.assetName" :placeholder="$t('asset.management.placeholder.assetName')" maxlength="16"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.assetTypeName')" prop="values">
                  <el-cascader
                    v-model="form.model.values"
                    :placeholder="$t('asset.management.placeholder.assetArr')"
                    :options="form.treeList"
                    filterable
                    :props="{ expandTrigger: 'hover' }"
                    @change="queryCustom"
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.netWorkName')" prop="netWorkName">
                  <el-select v-model="form.model.netWorkId" :placeholder="$t('asset.management.placeholder.netWorkId')" clearable filterable>
                    <el-option v-for="(item, k) in form.netList" :key="k" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.assetModel')" prop="assetModel">
                  <el-input
                    v-model.trim="form.model.assetModel"
                    :placeholder="$t('asset.management.placeholder.assetModel')"
                    maxlength="64"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.manufactor')" prop="manufactor">
                  <el-input
                    v-model.trim="form.model.manufactor"
                    :placeholder="$t('asset.management.placeholder.manufactor')"
                    maxlength="128"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.osType')" prop="osType">
                  <el-input v-model.trim="form.model.osType" maxlength="64" :placeholder="$t('asset.management.placeholder.osType')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-show="form.addAll">
              <el-col :span="8">
                <el-form-item :label="form.startIP.label" :prop="form.startIP.key">
                  <el-input v-model.trim="form.model.startIP" :placeholder="$t('asset.management.placeholder.startIP')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.endIP.label" :prop="form.endIP.key">
                  <el-input v-model.trim="form.model.endIP" :placeholder="$t('asset.management.placeholder.endIP')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col v-show="form.update || form.add" :span="8">
                <el-form-item :label="$t('asset.management.ipvAddress')" prop="ipvAddress">
                  <el-input v-model.trim="form.model.ipvAddress" :placeholder="$t('asset.management.placeholder.ipvAddress')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.memoryInfo')" prop="memoryInfo">
                  <el-input
                    v-model.trim="form.model.memoryInfo"
                    maxlength="16"
                    :placeholder="$t('asset.management.placeholder.memoryInfo')"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.responsiblePerson')" prop="responsiblePerson">
                  <el-input
                    v-model.trim="form.model.responsiblePerson"
                    maxlength="32"
                    :placeholder="$t('asset.management.placeholder.responsiblePerson')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.contactPhone')" prop="contactPhone">
                  <el-input
                    v-model.trim="form.model.contactPhone"
                    maxlength="16"
                    :placeholder="$t('asset.management.placeholder.contactPhone')"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.email')" prop="email">
                  <el-input v-model.trim="form.model.email" :placeholder="$t('asset.management.placeholder.email')" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.makerContactPhone')" prop="makerContactPhone">
                  <el-input
                    v-model.trim="form.model.makerContactPhone"
                    maxlength="16"
                    :placeholder="$t('asset.management.placeholder.makerContactPhone')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="$t('asset.management.assetCode')" prop="assetCode">
                  <el-input v-model.trim="form.model.assetCode" :placeholder="$t('asset.management.placeholder.assetCode')" maxlength="32"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.domaName.label" :prop="form.info.domaName.key">
                  <el-select v-model="form.model.domaId" :placeholder="$t('asset.management.placeholder.domaName')" clearable filterable>
                    <el-option v-for="item in form.domaList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.securityComponent.label" :prop="form.info.securityComponent.key">
                  <el-input
                    v-model.trim="form.model.securityComponent"
                    maxlength="100"
                    :placeholder="$t('asset.management.placeholder.securityComponent')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.assetValue.label" :prop="form.info.assetValue.key">
                  <el-select v-model="form.model.assetValue">
                    <el-option v-for="item in options.assetValue" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="23">
                <el-form-item :label="$t('asset.management.assetDesc')" prop="assetDesc" label-width="12%">
                  <el-input
                    v-model.trim="form.model.assetDesc"
                    :placeholder="$t('asset.management.placeholder.assetDesc')"
                    type="textarea"
                    :rows="5"
                    class="width-max"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane :label="$t('asset.management.moreInfo')" name="second">
            <div v-if="form.model.customAttr && form.model.customAttr.length > 0" style="min-height: 380px">
              <el-row v-for="(item, key) in form.model.customAttr" :key="key">
                <!--文本-->
                <el-form-item v-if="item.componentType === 1" :label="item.name" :required="item.required === 1" label-width="20%">
                  <el-input v-model.trim="item.value" :maxlength="item.length" show-word-limit></el-input>
                </el-form-item>
                <!--下拉-->
                <el-form-item v-if="item.componentType === 2" :label="item.name" :required="item.required === 1" label-width="20%">
                  <el-select
                    v-if="item.multiple === 1"
                    v-model="item.values"
                    :placeholder="$t('asset.management.placeholder.selectPlace')"
                    :multiple="item.multiple === 1"
                    :collapse-tags="item.multiple === 1"
                  >
                    <el-option v-for="(i, k) in item.dataSource" :key="k" :label="i.label" :value="i.value"></el-option>
                  </el-select>
                  <el-select v-else v-model="item.value" :placeholder="$t('asset.management.placeholder.selectPlace')">
                    <el-option v-for="(i, k) in item.dataSource" :key="k" :label="i.label" :value="i.value"></el-option>
                  </el-select>
                </el-form-item>
                <!--时间-->
                <el-form-item v-if="item.componentType === 3" :label="item.name" :required="item.required === 1" label-width="20%">
                  <el-date-picker
                    v-model="item.value"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetime"
                    :placeholder="$t('asset.management.placeholder.time')"
                  ></el-date-picker>
                </el-form-item>
                <!--文本域-->
                <el-form-item v-if="item.componentType === 4" :label="item.name" :required="item.required === 1" label-width="20%">
                  <el-input v-model.trim="item.value" :maxlength="item.length" show-word-limit type="textarea" :rows="5"></el-input>
                </el-form-item>
                <!--单选-->
                <el-form-item v-if="item.componentType === 5" :label="item.name" :required="item.required === 1" label-width="20%">
                  <el-radio v-for="(i, k) in item.dataSource" :key="k" v-model="item.value" :label="i.value">
                    {{ i.label }}
                  </el-radio>
                </el-form-item>
                <!--多选-->
                <el-form-item v-if="item.componentType === 6" :label="item.name" :required="item.required === 1" label-width="20%">
                  <el-checkbox-group v-model="item.values">
                    <el-checkbox v-for="(i, k) in item.dataSource" :key="k" :label="i.value">
                      {{ i.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-row>
            </div>
            <div v-else style="min-height: 380px; text-align: center; padding-top: 20%">
              {{ $t('asset.management.noneInfo') }}
            </div>
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateIp, validateEmail, validateCellphone } from '@util/validate'
import { getCustomAttr, addAssets, queryAssetDomain } from '@api/asset/management-api'
import { validateName } from '@util/validate'
export default {
  name: 'AudDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '900',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const validateContact = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!validateCellphone(value)) {
        callback(new Error(this.$t('validate.comm.cellphone')))
      } else {
        callback()
      }
    }
    const validateEm = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    const validatorIp = (rule, value, callback) => {
      if (this.form.updateAll) {
        // 编辑所有时不传ip，不做校验
        callback()
      } else {
        if (!value) {
          callback()
        } else if (!validateIp(value)) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      }
    }
    const validatorAddAll = (rule, value, callback) => {
      if (this.form.addAll) {
        // 添加所有时校验
        if (!value) {
          callback(new Error(this.$t('validate.ip.empty')))
        } else if (!validateIp(value)) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    const validatorType = (rule, value, callback) => {
      value = this.form.model.values
      if (!value || !value[0] || !value[1]) {
        callback(new Error(this.$t('asset.management.placeholder.assetArr')))
      } else {
        callback()
      }
    }
    return {
      check1: true,
      check2: true,
      dialogVisible: this.visible,
      options: {
        domainOption: [],
        assetValue: [
          { value: '0.2', label: '0.2' },
          { value: '0.4', label: '0.4' },
          { value: '0.6', label: '0.6' },
          { value: '0.8', label: '0.8' },
          { value: '1', label: '1' },
        ],
      },
      rules: {
        contactPhone: [
          {
            validator: validateContact,
            trigger: 'blur',
          },
        ],
        makerContactPhone: [
          {
            validator: validateContact,
            trigger: 'blur',
          },
        ],
        email: [
          {
            validator: validateEm,
            trigger: 'blur',
          },
        ],
        assetName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
          {
            validator: (rule, value, callback) => {
              if (!validateName(value, 1)) {
                callback(new Error(this.$t('validate.nameInput.rule')))
              } else {
                callback()
              }
            },
            trigger: 'blur',
          },
        ],
        values: [
          {
            required: true,
            validator: validatorType,
            trigger: 'change',
          },
        ],
        ipvAddress: [
          {
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        endIP: [
          {
            required: true,
            validator: validatorAddAll,
            trigger: 'blur',
          },
        ],
        startIP: [
          {
            required: true,
            validator: validatorAddAll,
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    // this.initOptions();
  },
  methods: {
    initOptions() {
      queryAssetDomain().then((res) => {
        this.options.domainOption = res
      })
    },
    // 查询自定义属性
    queryCustom(e) {
      getCustomAttr(e[0], e[1]).then((res) => {
        if (res) {
          this.form.model.customAttr = Object.values(res)
          // 下拉框单选时 初始化数组为字符串
          this.form.model.customAttr = this.form.model.customAttr.map((item) => {
            if (item.componentType === 2 && item.multiple === 2) {
              item.values = item.values ? item.values.toString() : ''
              item.value = item.values ? item.values.toString() : ''
            }
            return item
          })
        }
      })
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 调用批量添加接口
    addAll(obj) {
      const params = Object.assign({}, obj)
      if (params.model.customAttr && params.model.customAttr.length > 0) {
        params.model.customAttr = params.model.customAttr.map((item) => {
          // 下拉框单选时 处理字符串为数组
          if (item.componentType === 2 && item.multiple === 2) {
            item.values = [item.value]
          }
          return item
        })
      }

      const param = {
        assetCode: params.model.assetCode,
        assetDesc: params.model.assetDesc,
        assetModel: params.model.assetModel,
        assetName: params.model.assetName,
        contactPhone: params.model.contactPhone,
        domaId: params.model.domaId,
        email: params.model.email,
        endIP: params.model.endIP,
        ipvAddress: params.model.ipvAddress,
        makerContactPhone: params.model.makerContactPhone,
        manufactor: params.model.manufactor,
        memoryInfo: params.model.memoryInfo,
        netWorkId: params.model.netWorkId,
        osType: params.model.osType,
        responsiblePerson: params.model.responsiblePerson,
        startIP: params.model.startIP,
        assetClass: params.model.values[0],
        assetType: params.model.values[1],
        customAttr: params.model.customAttr,
        securityComponent: params.model.securityComponent,
        assetValue: params.model.assetValue,
      }
      addAssets(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.$emit('on-submit', 'true')
              this.clickCancelDialog()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else if (res === -1) {
          prompt({
            i18nCode: 'tip.add.license',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'tip.add.IpType',
            type: 'error',
          })
        } else if (res === 5) {
          prompt({
            i18nCode: 'tip.add.aroundError',
            type: 'error',
          })
        } else if (res === 6) {
          prompt({
            i18nCode: 'tip.add.ipExist',
            type: 'error',
          })
        } else if (res === 7) {
          prompt({
            i18nCode: 'asset.management.ipArrayInfo',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          const params = Object.assign({}, this.form)
          if (params.model.customAttr && params.model.customAttr.length > 0) {
            // 存在扩展信息 进行校验
            params.model.customAttr.map((item) => {
              // 如果是必填项
              if (item.required === 1) {
                // 如果是文本控件，文本域控件，时间控件,单选控件
                if (item.componentType === 1 || item.componentType === 3 || item.componentType === 4 || item.componentType === 5) {
                  // 验证
                  if (!item.value) {
                    this.check1 = false
                  }
                } else {
                  // 下拉框单选
                  if (item.componentType === 2 && item.multiple === 2) {
                    if (!item.value || item.value.length === 0) {
                      this.check2 = false
                    }
                  } else {
                    // 多选 下拉框多选
                    if (!item.values || item.values.length === 0) {
                      this.check2 = false
                    }
                  }
                }
              }
            })
            if (this.check2 && this.check1) {
              this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
                closeOnClickModal: false,
              }).then(() => {
                if (params.addAll) {
                  this.addAll(params)
                } else {
                  // 给父级调用数据
                  this.$emit('on-submit', params)
                  this.clickCancelDialog()
                }
              })
            } else {
              prompt(
                {
                  i18nCode: this.$t('asset.management.placeholder.info'),
                  type: 'warning',
                  print: true,
                },
                () => {
                  this.check1 = true
                  this.check2 = true
                  return false
                }
              )
            }
          } else {
            // 不存在扩展信息直接提交
            this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
              closeOnClickModal: false,
            }).then(() => {
              if (params.addAll) {
                this.addAll(params)
              } else {
                // 给父级调用数据
                this.$emit('on-submit', params)
                this.clickCancelDialog()
              }
            })
          }
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
        this.$refs.dialogTemplate.end()
      })
    },
    // 切换tabs
    handleClick(tab, event) {},
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-tabs__nav-wrap::after {
  width: 0;
}
::v-deep textarea::-webkit-input-placeholder {
  font-family: Arial;
}
::v-deep .el-form-item__error {
  width: 520px;
}
</style>
