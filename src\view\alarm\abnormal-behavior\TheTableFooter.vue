<!--
 * @Description: 异常行为告警 - 底部翻页
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-02
 * @Editor:
 * @EditDate: 2021-11-02
-->
<template>
  <section class="table-footer">
    <el-pagination
      v-if="filterCondition.visible"
      small
      background
      align="right"
      :current-page="filterCondition.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="filterCondition.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="filterCondition.total"
      @size-change="clickSize"
      @current-change="clickPage"
    ></el-pagination>
  </section>
</template>

<script>
export default {
  props: {
    pagination: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.pagination,
    }
  },
  watch: {
    pagination(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:pagination', newCondition)
    },
  },
  methods: {
    clickSize(size) {
      this.$emit('size-change', size)
    },
    clickPage(pageNum) {
      this.$emit('page-change', pageNum)
    },
  },
}
</script>
