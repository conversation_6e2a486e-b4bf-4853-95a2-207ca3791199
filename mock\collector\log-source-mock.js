const { TableMock, createMockTable } = require('../util')

const tableData = new TableMock({
  total: 20,
  template: {
    typeId: '@ID',
    typeName: '@CNAME',
    manufact: '@CNAME',
    categoryId: '@ID',
    categoryName: '@CNAME',
    desc: '@CPARAGRAPH(1, 3)',
    'isDefault|1': [0, 1],
  },
})

const manufact = createMockTable(
  {
    value: '@id',
    label: '@Name',
  },
  5
)

const category = createMockTable(
  {
    value: '@id',
    label: '@Name',
  },
  5
)

module.exports = [
  // 查询日志源设备列表
  {
    url: '/collector/logsource/logSources',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  // 删除日志源设备
  {
    url: `/collector/logsource/logSource/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'typeId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 修改日志源设备
  {
    url: '/collector/logsource/logSource',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'typeId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 添加日志源设备
  {
    url: '/collector/logsource/logSource',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 查询厂商下拉数据
  {
    url: '/collector/logsource/combo/manufact',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: manufact,
      }
    },
  },
  // 查询设备类别下拉数据
  {
    url: '/collector/logsource/combo/category',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: category,
      }
    },
  },
]
