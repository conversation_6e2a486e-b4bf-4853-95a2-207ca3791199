<!--
 * @Description: 代理服务器
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-16
 * @Editor:
 * @EditDate: 2021-07-16
-->
<template>
  <div class="router-wrap-table">
    <table-header
      :condition.sync="query"
      @on-change="changeQueryTable"
      @on-detect="clickDetectButton"
      @on-config-ip="clickConfigCenterIp"
    ></table-header>
    <table-body
      :table-title="title"
      :table-loading="table.loading"
      :table-data="table.data"
      @on-toggle-status="clickOperationStatus"
      @on-network-config="clickNetworkConfig"
      @on-detail="clickDetailButton"
    ></table-body>
    <table-footer :pagination.sync="pagination" @on-change-size="changeTableSize" @on-current="changeTableCurrent"></table-footer>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :detail-model="dialog.detail.model"></detail-dialog>
    <network-dialog
      :visible.sync="dialog.network.visible"
      :title-name="title"
      :model="dialog.network.model"
      @on-change="changeQueryTable"
    ></network-dialog>
    <center-config-dialog :visible.sync="dialog.centerConfig.visible" :title-name="title"></center-config-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import DetailDialog from './TheDetailDialog'
import NetworkDialog from './TheNetworkDialog'
import CenterConfigDialog from './TheCenterConfigDialog'
import { queryProxyTable, detectSystemHealthy, updateAgentStatus, queryNetworkAvaliable } from '@api/management/proxy-server-api'
import { prompt } from '@util/prompt'

export default {
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
    NetworkDialog,
    CenterConfigDialog,
  },
  data() {
    return {
      title: this.$t('management.proxy.title'),
      query: {
        senior: false,
        fuzzyField: '',
        status: '',
      },
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
        network: {
          visible: false,
          model: {},
          networkValid: 1,
        },
        centerConfig: {
          visible: false,
        },
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {}
      if (this.query.senior) {
        params = Object.assign(params, {
          status: this.query.status,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.fuzzyField,
        })
      }
      return params
    },
    clickDetectButton() {
      this.detectSystemHealthy()
    },
    clickConfigCenterIp() {
      this.dialog.centerConfig.visible = true
    },
    clickOperationStatus(row) {
      this.updateAgentOperationStatus(row.agentId, row.operationState)
    },
    clickNetworkConfig(row) {
      queryNetworkAvaliable(row.agentId).then((res) => {
        if (res === 1) {
          this.dialog.network.visible = true
          this.dialog.network.model = row
        } else {
          this.dialog.network.networkValid = res
          prompt({
            i18nCode: 'tip.agent.info',
            type: 'info',
          })
        }
      })
    },
    clickDetailButton(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    changeTableSize(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    changeTableCurrent(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(params) {
      params = Object.assign({}, params, {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      })
      this.table.loading = true
      this.pagination.visible = false
      queryProxyTable(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
    detectSystemHealthy() {
      detectSystemHealthy().then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.detect.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.detect.error',
            type: 'error',
          })
        }
      })
    },
    updateAgentOperationStatus(id, state) {
      updateAgentStatus(id, state).then((res) => {
        if (res === 1) {
          if (state === 1) {
            prompt(
              {
                i18nCode: 'tip.status.recover.success',
                type: 'success',
              },
              () => {
                this.changeQueryTable('turn-page')
              }
            )
          } else {
            prompt(
              {
                i18nCode: 'tip.status.uninstall.success',
                type: 'success',
              },
              () => {
                this.changeQueryTable('turn-page')
              }
            )
          }
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.status.existEquipment',
            type: 'warning',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.status.existMonitor',
            type: 'warning',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'tip.status.existCollector',
            type: 'warning',
          })
        } else {
          prompt({
            i18nCode: 'tip.status.change.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
