<!--
 * @Description: 关联策略
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="inputVal"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('event.relevanceStrategy.table.alarmName')])"
              @keyup.enter.native="pageQuery('e')"
              @change="pageQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="pageQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickCopyButton">
            {{ $t('button.copy') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAddButton">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'upload'" @click="clickUpload">
            {{ $t('button.import') }}
          </el-button>
          <el-button v-has="'download'" v-debounce="clickDownload">
            {{ $t('button.export.default') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteButton">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="QueryForm.incPolicyName"
                  clearable
                  :placeholder="$t('event.relevanceStrategy.table.alarmName')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select v-model="QueryForm.status" clearable :placeholder="$t('collector.management.placeholder.run')" @change="pageQuery('e')">
                  <el-option v-for="item in option.useStateOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="10">
                <el-date-picker
                  v-model="QueryForm.updateTime"
                  clearable
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :start-placeholder="$t('time.option.startUpdateTime')"
                  :end-placeholder="$t('time.option.endUpdateTime')"
                  @change="pageQuery('e')"
                ></el-date-picker>
              </el-col>
              <el-col align="right" :span="4">
                <el-button v-has="'query'" @click="pageQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQueryForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('event.relevanceStrategy.relevanceStrategy') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="selectsChange"
        >
          <el-table-column type="selection" prop="incPolicyID" :selectable="judgeRowSelected"></el-table-column>
          <el-table-column prop="incPolicyName" :label="$t('event.relevanceStrategy.table.alarmName')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="description" :label="$t('event.relevanceStrategy.table.description')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="isSysDefault" :label="$t('event.relevanceStrategy.table.isSysDefault')">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.isSysDefault === '0'" type="success">
                {{ $t('event.relevanceStrategy.tag.custom') }}
              </el-tag>
              <el-tag v-if="scope.row.isSysDefault === '1'">
                {{ $t('event.relevanceStrategy.tag.default') }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" :label="$t('event.relevanceStrategy.table.updateTime')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="status" :label="$t('event.relevanceStrategy.table.state')">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" @change="toggleStatus(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="180">
            <template slot-scope="scope">
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateButton(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" :disabled="scope.row.isSysDefault !== '0'" class="el-button--red" @click="clickDeleteButton(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
    <add-dialog
      :visible.sync="addDialog.visible"
      :title="addDialog.title"
      :form="addDialog.form"
      :system-option="addDialog.systemOption"
      :key-option="addDialog.keyOption"
      :relation-option="addDialog.relationOption"
      :device-class-option="addDialog.DeviceClassOption"
      :device-type-option="addDialog.DeviceTypeOption"
      :event-class-option="addDialog.EventClassOption"
      :event-type-option="addDialog.EventTypeOption"
      @on-submit="clickSubmitAdd"
    ></add-dialog>
    <update-dialog
      :visible.sync="updateDialog.visible"
      :title="updateDialog.title"
      :form="updateDialog.form"
      :system-option="updateDialog.systemOption"
      :key-option="updateDialog.keyOption"
      :fil-data="updateDialog.mergFields"
      :re-data="updateDialog.incPolicyContent"
      :filter-id="updateDialog.filterId"
      :ru-children-id="updateDialog.ruChildrenId"
      :ru-parent-id="updateDialog.ruParentId"
      :relation-option="updateDialog.relationOption"
      :device-class-option="updateDialog.DeviceClassOption"
      :device-type-option="updateDialog.DeviceTypeOption"
      :event-class-option="updateDialog.EventClassOption"
      :event-type-option="updateDialog.EventTypeOption"
      :is-sys-default="updateDialog.form.model.isSysDefault"
      @on-submit="clickSubmitUpdate"
    ></update-dialog>
    <upload-dialog
      :visible.sync="dialog.upload.visible"
      :title="title"
      :form="dialog.upload"
      :width="'35%'"
      @on-submit="clickSubmitUpload"
    ></upload-dialog>
    <copy-dialog :visible.sync="dialog.copy.visible" :title="title" @on-submit="clickSubmitCopy"></copy-dialog>
  </div>
</template>

<script>
import { prompt } from '@util/prompt'
import { debounce } from '@/util/effect'
import AddDialog from './EventRelevanceStrategyAddDialog'
import UpdateDialog from './EventRelevanceStrategyUpdateDialog'
import UploadDialog from './TheUploadDialog'
import CopyDialog from './TheCopyDialog'
import {
  queryRelevanceStrategyDetail,
  getRelevanceTableData,
  getRelationType,
  getKeys,
  getSystems,
  getDeviceClass,
  getDeviceType,
  getEventClass,
  getEventType,
  updateRelevanceData,
  addRelevanceData,
  deleteRelevance,
  updateStrategyStatus,
  uploadRelevanceStrategy,
  exportRelevanceStrategy,
  copyRelevanceStrategy,
} from '@api/event/relevance-strategy-api'
import { validateName } from '@util/validate'

export default {
  name: 'EventRelevanceStrategy',
  components: {
    AddDialog,
    UpdateDialog,
    UploadDialog,
    CopyDialog,
  },
  data() {
    return {
      title: this.$t('event.relevanceStrategy.relevanceStrategy'),
      inputVal: '',
      isShow: false,
      data: {
        loading: false,
        table: [],
        selected: [],
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
      },
      pagination: {
        visible: true,
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
      },
      addDialog: {
        visible: false,
        title: this.$t('event.relevanceStrategy.add.dialogTitle'),
        form: {
          model: {
            incPolicyName: '',
            description: '',
            incSubCategoryID: '',
            externalSystem: '',
            eventDesc: '',
            eventLevel: '',
          },
          info: {
            incPolicyName: {
              key: 'incPolicyName',
              label: this.$t('event.relevanceStrategy.title.incPolicyName'),
            },
            description: {
              key: 'description',
              label: this.$t('event.relevanceStrategy.title.description'),
            },
            incSubCategoryID: {
              key: 'incSubCategoryID',
              label: this.$t('event.relevanceStrategy.title.incSubCategoryID'),
            },
            externalSystem: {
              key: 'externalSystem',
              label: this.$t('event.relevanceStrategy.title.externalSystem'),
            },
            eventLevel: {
              key: 'eventLevel',
              label: this.$t('event.relevanceStrategy.title.eventLevel'),
            },
            eventDesc: {
              key: 'eventDesc',
              label: this.$t('event.relevanceStrategy.title.eventDesc'),
            },
          },
          rules: {
            incPolicyName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
              {
                validator: (rule, value, callback) => {
                  if (!validateName(value, 0)) {
                    callback(new Error(this.$t('validate.nameInput.rule')))
                  } else {
                    callback()
                  }
                },
                trigger: 'blur',
              },
            ],
            incSubCategoryID: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            eventLevel: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
          },
        },
        systemOption: [],
        keyOption: [],
        relationOption: [],
        DeviceClassOption: [],
        DeviceTypeOption: [],
        EventClassOption: [],
        EventTypeOption: [],
      },
      updateDialog: {
        visible: false,
        title: this.$t('event.relevanceStrategy.update.dialogTitle'),
        form: {
          model: {
            incPolicyName: '',
            description: '',
            incSubCategoryID: '',
            externalSystem: '',
            eventLevel: '',
            isSysDefault: '',
            incPolicyID: '',
            eventDesc: '',
            status: '',
          },
          info: {
            incPolicyName: {
              key: 'incPolicyName',
              label: this.$t('event.relevanceStrategy.title.incPolicyName'),
            },
            description: {
              key: 'description',
              label: this.$t('event.relevanceStrategy.title.description'),
            },
            incSubCategoryID: {
              key: 'incSubCategoryID',
              label: this.$t('event.relevanceStrategy.title.incSubCategoryID'),
            },
            externalSystem: {
              key: 'externalSystem',
              label: this.$t('event.relevanceStrategy.title.externalSystem'),
            },
            eventLevel: {
              key: 'eventLevel',
              label: this.$t('event.relevanceStrategy.title.eventLevel'),
            },
            eventDesc: {
              key: 'eventDesc',
              label: this.$t('event.relevanceStrategy.title.eventDesc'),
            },
          },
          rules: {
            incPolicyName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
              {
                validator: (rule, value, callback) => {
                  if (!validateName(value, 0)) {
                    callback(new Error(this.$t('validate.nameInput.rule')))
                  } else {
                    callback()
                  }
                },
                trigger: 'blur',
              },
            ],
            incSubCategoryID: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            eventLevel: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
          },
        },
        systemOption: [],
        keyOption: [],
        incPolicyContent: [],
        mergFields: [],
        relationOption: [],
        filterId: 0,
        ruParentId: 0,
        ruChildrenId: 0,
        DeviceClassOption: [],
        DeviceTypeOption: [],
        EventClassOption: [],
        EventTypeOption: [],
      },
      QueryForm: {
        incPolicyName: '',
        updateTime: '',
        status: '',
      },
      dialog: {
        upload: {
          visible: false,
          header: { 'Content-Type': 'multipart/form-data' },
          files: [], // 上传文件个数
          importRule: '1', // 导入规则：1：覆盖规则；2：忽略规则
          templateType: '', // 导入模板类型（1：xlsm  2:xls)
          rules: {
            files: [
              {
                required: true,
                message: this.$t('validate.choose'),
                trigger: 'change',
              },
            ],
            importRule: [
              {
                required: true,
                message: this.$t('validate.choose'),
                trigger: 'blur',
              },
            ],
          },
        },
        copy: {
          visible: false,
        },
      },
      option: {
        useStateOption: [
          {
            label: this.$t('collector.management.label.useState.on'),
            value: '1',
          },
          {
            label: this.$t('collector.management.label.useState.off'),
            value: '0',
          },
        ], // 使用状态option
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.queryTableData()
      this.initOption()
      this.initDebounce()
    },
    // 加载页面防抖方法
    initDebounce() {
      this.data.debounce.query = debounce(() => {
        let params = {}
        if (this.isShow) {
          this.QueryForm.updateTime = this.QueryForm.updateTime || ['', '']
          params = Object.assign({}, this.QueryForm, {
            updateStartTime: this.QueryForm.updateTime[0],
            updateEndTime: this.QueryForm.updateTime[1],
            updateTime: '',
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
          })
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            inputVal: this.inputVal,
          }
        }
        this.queryTableData(params)
      })
      // 重置查询
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.QueryForm = {
          incPolicyName: '',
          updateTime: '',
          status: '',
        }
        this.pagination.pageNum = 1
        setTimeout(() => {
          this.queryTableData()
        }, 150)
      }, 500)
    },
    // 初始化Option
    initOption() {
      getDeviceClass().then((res) => {
        this.addDialog.DeviceClassOption = res
        this.updateDialog.DeviceClassOption = res
      })
      getDeviceType().then((res) => {
        this.addDialog.DeviceTypeOption = res
        this.updateDialog.DeviceTypeOption = res
      })
      getEventClass().then((res) => {
        this.addDialog.EventClassOption = res
        this.updateDialog.EventClassOption = res
      })
      getEventType().then((res) => {
        this.addDialog.EventTypeOption = res
        this.updateDialog.EventTypeOption = res
      })
      getSystems().then((res) => {
        this.addDialog.systemOption = res
        this.updateDialog.systemOption = res
      })
      getKeys().then((res) => {
        this.addDialog.keyOption = res
        this.updateDialog.keyOption = res
      })
      getRelationType().then((res) => {
        this.addDialog.relationOption = res
        this.updateDialog.relationOption = res
      })
    },
    judgeRowSelected(row, index) {
      if (row.isSysDefault === '1') {
        return false
      } else {
        return true
      }
    },
    // 删除api
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteRelevance(ids).then((res) => {
          if (res === 3) {
            prompt({
              i18nCode: 'event.relevanceStrategy.errorDelete',
              type: 'error',
            })
          }
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.inputVal = ''
                this.QueryForm = {
                  incPolicyName: '',
                  updateTime: '',
                  status: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 修改api
    update(obj) {
      updateRelevanceData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.pageQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeatName',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 添加api
    add(obj) {
      addRelevanceData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.inputVal = ''
              this.QueryForm = {
                incPolicyName: '',
                updateTime: '',
                status: '',
              }
              this.pagination.pageNum = 1
              this.queryTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 查询方法
    pageQuery(flag) {
      if (flag) this.pagination.pageNum = 1
      this.data.debounce.query()
    },
    // 加载页面数据
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.data.loading = true
      this.pagination.visible = false
      getRelevanceTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 单个删除
    clickDeleteButton(row) {
      this.delete(row.incPolicyID)
    },
    // 批量删除
    clickBatchDeleteButton() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.incPolicyID).toString()
        this.delete(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickCopyButton() {
      if (this.data.selected.length === 1) {
        this.dialog.copy.visible = true
      } else {
        prompt({
          i18nCode: 'tip.select.one',
          type: 'warning',
          print: true,
        })
      }
    },
    clickSubmitCopy(formModel) {
      const policyId = this.data.selected[0].incPolicyID
      const params = {
        incPolicyID: policyId,
        incPolicyName: formModel.policyName,
      }
      this.copyStrategy(params)
    },
    // 点击添加按钮，重置添加信息
    clickAddButton() {
      this.addDialog.visible = true
      this.addDialog.form.model = {
        incPolicyName: '',
        description: '',
        externalSystem: '',
        eventLevel: '',
        eventDesc: '',
        incSubCategoryID: '',
      }
    },
    // 提交添加数据
    clickSubmitAdd(formModel, ruData, filterData) {
      let filter = []
      if (filterData.length !== 0) {
        filter = filterData
      }
      const newData = ruData.filter((obj, index) => {
        return index % 2 === 0
      })
      const params = Object.assign({}, formModel, {
        mergFields: JSON.stringify(ruData),
        incPolicyContent: JSON.stringify({
          filter: filter.length === 1 ? filter[0] : filter,
          states: newData,
        }),
        isSysDefault: '0',
        externalSystem: formModel.externalSystem.length !== 0 ? formModel.externalSystem.toString() : '',
      })
      this.add(params)
    },
    // 点击修改按钮
    async clickUpdateButton(row) {
      let incPolicyContent, mergFields
      this.updateDialog.ruParentId = 0
      this.updateDialog.ruChildrenId = 0
      this.updateDialog.incPolicyContent = []
      this.updateDialog.form.model = {}
      this.updateDialog.mergFields = []
      this.updateDialog.filterId = 0
      await queryRelevanceStrategyDetail({ incPolicyID: row.incPolicyID }).then((res) => {
        incPolicyContent = this.isJSON(res.incPolicyContent) ? JSON.parse(res.incPolicyContent) : null
        mergFields = this.isJSON(res.mergFields) ? JSON.parse(res.mergFields) : null
        this.updateDialog.form.model = res
        this.updateDialog.form.model.externalSystem = res.externalSystemList
      })
      if (incPolicyContent !== null && incPolicyContent.hasOwnProperty('filter')) {
        this.updateDialog.filterId = incPolicyContent.filter.treeId
        if (Array.isArray(incPolicyContent.filter)) this.updateDialog.mergFields = incPolicyContent.filter
        else this.updateDialog.mergFields = [incPolicyContent.filter]
      }
      if (incPolicyContent !== null && incPolicyContent.hasOwnProperty('states') && incPolicyContent.states.length !== 0) {
        this.updateDialog.ruParentId = mergFields[0].parentId
        this.updateDialog.ruChildrenId = mergFields[0].childrenId
        this.updateDialog.incPolicyContent = mergFields
      }
      this.$nextTick(() => {
        this.updateDialog.visible = true
      })
    },
    isJSON(str) {
      try {
        const obj = JSON.parse(str)
        return !!obj && typeof obj === 'object'
      } catch (e) {
        return false
      }
    },
    // 提交修改信息
    clickSubmitUpdate(formModel, ruData, filterData) {
      let filter = []
      if (filterData.length !== 0) {
        filter = filterData
      }
      const newData = ruData.filter((obj, index) => {
        return index % 2 === 0
      })
      const params = Object.assign({}, formModel, {
        mergFields: JSON.stringify(ruData),
        incPolicyContent: JSON.stringify({
          filter: filter.length === 1 ? filter[0] : filter,
          states: newData,
        }),
        externalSystem: formModel.externalSystem.length !== 0 ? formModel.externalSystem.toString() : '',
      })
      this.update(params)
    },
    // 点击向上按钮
    clickUpButton() {
      this.isShow = !this.isShow
      this.resetQueryForm()
      this.initDebounce()
    },
    // 点击查询按钮，打开精准查询
    clickQueryButton() {
      this.inputVal = ''
      this.isShow = !this.isShow
      this.resetQueryForm()
      this.initDebounce()
    },
    // 分页
    tableSizeChange(size) {
      this.pagination.pageNum = 1
      this.pagination.pageSize = size
      this.pageQuery()
    },
    // 分页
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.pageQuery()
    },
    // 切换策略使用状态
    toggleStatus({ incPolicyID, status }) {
      updateStrategyStatus(incPolicyID, status).then((res) => {
        if (res) {
          if (status === '1') {
            prompt(
              {
                i18nCode: 'tip.enable.success',
                type: 'success',
              },
              () => {
                this.pageQuery()
              }
            )
          } else {
            prompt(
              {
                i18nCode: 'tip.disable.success',
                type: 'success',
              },
              () => {
                this.pageQuery()
              }
            )
          }
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 重置查询
    resetQueryForm() {
      this.data.debounce.resetQueryDebounce()
    },
    // 勾选数据
    selectsChange(select) {
      this.data.selected = select
    },
    clickUpload() {
      this.dialog.upload.files = []
      this.dialog.upload.importRule = '1'
      this.dialog.upload.templateType = ''
      this.dialog.upload.visible = true
    },
    clickSubmitUpload(formData) {
      uploadRelevanceStrategy(formData)
        .then((res) => {
          if (res > 0) {
            prompt({
              i18nCode: this.$t('event.relevanceStrategy.upload.successUpload', [res]),
              type: 'success',
            })
            this.pagination.pageNum = 1
            this.queryTableData()
          } else {
            prompt({
              i18nCode: 'tip.import.error',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
    clickDownload() {
      this.downloadRelevanceStrategy()
    },
    // 调用导出接口
    downloadRelevanceStrategy() {
      this.data.loading = true
      let params = {}
      const arr = []
      this.data.selected.map((item) => arr.push(item.incPolicyID))
      if (this.isShow) {
        this.QueryForm.updateTime = this.QueryForm.updateTime || ['', '']
        params = Object.assign({}, this.QueryForm, {
          incPolicyIDs: arr.toString(),
          updateStartTime: this.QueryForm.updateTime[0],
          updateEndTime: this.QueryForm.updateTime[1],
          updateTime: '',
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
        })
      } else {
        params = {
          incPolicyIDs: arr.toString(),
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          inputVal: this.inputVal,
        }
      }
      exportRelevanceStrategy(params).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    copyStrategy(obj) {
      copyRelevanceStrategy(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.copy.success',
              type: 'success',
            },
            () => {
              this.inputVal = ''
              this.QueryForm = {
                incPolicyName: '',
                updateTime: '',
                status: '',
              }
              this.pagination.pageNum = 1
              this.queryTableData()
            }
          )
        } else if (res === 0) {
          prompt({
            i18nCode: 'tip.copy.builtIn',
            type: 'error',
          })
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.copy.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.copy.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.row-top {
  div:nth-child(3) {
    display: flex;
    justify-content: flex-end;
  }
}
</style>
