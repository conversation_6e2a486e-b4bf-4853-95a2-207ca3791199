<template>
  <div class="tab-context-wrapper">
    <section class="form-container">
      <el-form ref="alarmForm" :model="form.model" :rules="form.rule" label-width="180px">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('management.system.alarm.form.title')"></el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item :prop="email">
              <el-checkbox v-model="form.model.email" :label="$t('management.system.alarm.form.email')"></el-checkbox>
            </el-form-item>
          </el-col>
          <el-col v-if="form.model.email" :span="7">
            <el-form-item :label="$t('management.system.alarm.form.recipient')" prop="emailAddress">
              <el-input v-model="form.model.emailAddress" :placeholder="$t('management.system.alarm.form.emailAddress')" class="width-max"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="voiceLightElect">
              <el-checkbox v-model="form.model.voice" :label="$t('management.system.alarm.form.voice')"></el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="5">
            <el-form-item prop="snmpTrap">
              <el-checkbox v-model="form.model.snmpTrap" label="snmp trap"></el-checkbox>
            </el-form-item>
          </el-col>
          <el-col v-if="form.model.snmpTrap" :span="7">
            <el-form-item :label="$t('management.system.alarm.form.recipient')" prop="snmpRecipient">
              <el-select
                v-model="form.model.snmpRecipient"
                clearable
                :placeholder="$t('management.system.alarm.form.recipientAddress')"
                class="width-max"
              >
                <el-option v-for="(item, index) in addressList" :key="index" :value="item"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="message">
              <el-checkbox :label="$t('management.system.alarm.form.message')"></el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="12" :offset="7">
            <el-form-item>
              <el-button v-has="'update'" @click="clickSaveAlarmNotice">
                {{ $t('button.save') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
  </div>
</template>

<script>
import { validateEmail } from '@util/validate'
import { prompt } from '@util/prompt'

export default {
  name: 'AlarmNotice',
  props: {},
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    return {
      form: {
        model: {
          email: false,
          emailAddress: '',
          voice: false,
          snmpTrap: false,
          snmpRecipient: '',
          message: false,
        },
        rule: {
          emailAddress: [
            {
              required: true,
              validator: validatorEmail,
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  methods: {
    clickSaveAlarmNotice() {
      this.$refs.alarmForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', this.form.model)
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.tab-context-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .form-container {
    position: relative;

    .tab-footer-button {
      position: absolute;
    }
  }
}
</style>
