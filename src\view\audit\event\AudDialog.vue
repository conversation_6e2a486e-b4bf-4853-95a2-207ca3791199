<!--
 * @Description: 审计事件 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" :loading="loading" @on-close="clickCancelDialog">
    <el-form ref="formTemplate" label-width="36%">
      <template>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick" @tab-remove="removeTab">
          <el-tab-pane :label="$t('audit.event.event')" name="first">
            <el-row>
              <el-col v-for="(item, index) in form.info" :key="index" :span="8">
                <el-form-item :label="item.label" :prop="item.key">
                  <span v-if="item.key === 'level'">
                    <level-tag :level="form.audit.level"></level-tag>
                  </span>
                  <span v-else>{{ form.audit[item.key] }}</span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane :label="$t('audit.event.eventFrom')" name="second">
            <el-table
              v-if="form.audit.sourceEventType == 0"
              v-el-table-scroll="scrollTable"
              v-loading="table.loadingEvent"
              infinite-scroll-disabled="disableScroll"
              :data="form.safe"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="375"
              fit
              @row-dblclick="handleRowDetail"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column prop="type2Name" :label="$t('audit.event.safe.type2Name')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="alarmTypeName" :label="$t('audit.event.safe.safeEventName')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="alarmCategoryName" :label="$t('audit.event.safe.eventTypeName')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="level" :label="$t('audit.event.safe.eventLevelName')" show-overflow-tooltip>
                <template slot-scope="scope">
                  <level-tag :level="scope.row.level"></level-tag>
                </template>
              </el-table-column>
              <el-table-column prop="count" :label="$t('audit.event.safe.count')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="aggrStartDate" :label="$t('audit.event.safe.aggrStartDate')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="deviceTypeName" :label="$t('audit.event.safe.fromDeviceTypeName')" show-overflow-tooltip></el-table-column>
            </el-table>
            <el-table
              v-if="form.audit.sourceEventType == 1"
              v-loading="table.loadingEvent"
              v-el-table-scroll="scrollTable"
              infinite-scroll-disabled="disableScroll"
              :data="form.relevance"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              fit
              height="375"
              @row-dblclick="handleRowDetail"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column prop="eventTypeName" :label="$t('audit.event.link.eventName')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="policyName" :label="$t('audit.event.link.policyName')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="level" :label="$t('audit.event.link.eventLevelName')" show-overflow-tooltip>
                <template slot-scope="scope">
                  <level-tag :level="scope.row.level"></level-tag>
                </template>
              </el-table-column>
              <el-table-column prop="createDate" :label="$t('audit.event.link.createDate')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="updateDate" :label="$t('audit.event.link.updateDate')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="count" :label="$t('audit.event.link.count')" show-overflow-tooltip></el-table-column>
            </el-table>
            <el-table
              v-if="form.audit.sourceEventType == 2"
              v-loading="table.loadingEvent"
              v-el-table-scroll="scrollTable"
              infinite-scroll-disabled="disableScroll"
              :data="form.threat"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              fit
              height="375"
              @row-dblclick="handleRowDetail"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column prop="eventTypeName" :label="$t('audit.event.threat.eventType')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="eventLevel" :label="$t('audit.event.threat.eventLevel')" show-overflow-tooltip>
                <template slot-scope="scope">
                  <level-tag :level="scope.row.eventLevel"></level-tag>
                </template>
              </el-table-column>
              <el-table-column prop="eventDesc" :label="$t('audit.event.threat.eventDesc')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="receiveTime" :label="$t('audit.event.threat.receiveTime')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="eventTime" :label="$t('audit.event.threat.eventTime')" show-overflow-tooltip></el-table-column>
            </el-table>
            <section v-if="form.audit.sourceEventType != 0 && form.audit.sourceEventType != 1 && form.audit.sourceEventType != 2" class="none">
              <div class="text">
                {{ $t('audit.event.none') }}
              </div>
            </section>
            <section class="table-footer infinite-scroll">
              <section class="infinite-scroll-nomore">
                <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
                <i v-show="data.totalLoading" class="el-icon-loading"></i>
              </section>
              <section class="infinite-scroll-total">
                <b>{{ $t('event.original.total') + ':' }}</b>
                <span>{{ data.total }}</span>
              </section>
            </section>
          </el-tab-pane>
          <el-tab-pane v-if="form.showLog" :label="$t('audit.event.log')" name="third" closable>
            <section class="router-wrap-table">
              <section class="table-body">
                <el-table
                  ref="table"
                  v-loading="table.loadingLog"
                  :data="form.logList"
                  class="flag"
                  element-loading-background="rgba(0, 0, 0, 0.3)"
                  size="mini"
                  highlight-current-row
                  tooltip-effect="light"
                  height="375"
                >
                  <el-table-column width="50" type="index"></el-table-column>
                  <el-table-column prop="type2Name" :label="$t('audit.event.logList.type2Name')" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="eventName" :label="$t('audit.event.logList.eventName')" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="eventCategoryName" :label="$t('audit.event.logList.eventCategoryName')"></el-table-column>
                  <el-table-column prop="level" :label="$t('audit.event.logList.level')" show-overflow-tooltip>
                    <template slot-scope="scope">
                      <level-tag :level="scope.row.level"></level-tag>
                    </template>
                  </el-table-column>
                  <el-table-column prop="sourceIp" :label="$t('audit.event.logList.srcIp')" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="targetIp" :label="$t('audit.event.logList.dstIp')" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="time" :label="$t('audit.event.logList.dateTime')" show-overflow-tooltip></el-table-column>
                  <el-table-column fixed="right" width="80">
                    <template slot-scope="scope">
                      <el-button v-has="'query'" class="el-button--blue" @click="clickOriginalTableDetail(scope.row)">
                        {{ $t('button.detail') }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </section>
              <section class="table-footer infinite-scroll">
                <section class="infinite-scroll-nomore">
                  <span v-show="data.noLog">{{ $t('validate.data.nomore') }}</span>
                  <i v-show="data.loadingLog" class="el-icon-loading"></i>
                </section>
                <section class="infinite-scroll-total">
                  <b>{{ $t('event.original.total') + ':' }}</b>
                  <span>{{ data.totalLog }}</span>
                </section>
              </section>
            </section>
          </el-tab-pane>
        </el-tabs>
      </template>
    </el-form>
    <template>
      <detail-drawer :visible.sync="dialog.visible.detail" :detail-data="data.table.detail"></detail-drawer>
    </template>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import LevelTag from '@comp/LevelTag'
import DetailDrawer from './TheLogDetailDrawer'
import {
  queryData,
  queryRelevance,
  queryThreat,
  queryLog,
  queryTotalSafe,
  queryTotalRelevance,
  queryTotalThreat,
  queryTotalLog,
} from '@api/audit/event-api'
import elTableScroll from '@/directive/el-table-scroll'
export default {
  name: 'AudDialog',
  directives: {
    elTableScroll,
  },
  components: {
    CustomDialog,
    LevelTag,
    DetailDrawer,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '1200',
    },
    // dialog底部功能是否存在
    actions: {
      type: Boolean,
      default: true,
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      activeName: 'first',
      dialogVisible: this.visible,
      disabled: false,
      table: {
        loadingLog: false,
        loadingEvent: false,
      },
      tableHeight: 0,
      timer: {},
      count: 0,
      data: {
        table: {
          detail: {},
        },
        total: 0,
        nomore: false,
        noLog: false,
        totalLoading: false,
        loadingLog: false,
        totalLog: 0,
      },
      dialog: {
        visible: {
          detail: false,
        },
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
    disableScroll() {
      return this.table.loadingEvent
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 点击tab
    handleClick(tab) {
      const revertThird = () => {
        this.data.nomore = false
        this.form.safe = []
        this.form.relevance = []
        this.form.threat = []
        this.form.showLog = false
      }
      if (tab.name === 'second') {
        revertThird()
        this.count++
        this.getData()
        this.getTotal()
      }
    },
    // 首次查询事件朔源
    getData() {
      this.table.loadingEvent = true
      const param = {
        id: this.form.audit.id,
        timestamp: this.form.audit.createTime,
        pageSize: 20,
      }
      if (String(this.form.audit.sourceEventType) === '0') {
        queryData(param).then((res) => {
          this.form.safe = res
          this.table.loadingEvent = false
        })
      } else if (String(this.form.audit.sourceEventType) === '1') {
        queryRelevance(param).then((res) => {
          this.form.relevance = res
          this.table.loadingEvent = false
        })
      } else if (String(this.form.audit.sourceEventType) === '2') {
        queryThreat(param).then((res) => {
          this.form.threat = res
          this.table.loadingEvent = false
        })
      } else {
        this.form.safe = []
        this.form.relevance = []
        this.form.threat = []
        this.table.loadingEvent = false
      }
    },
    // 查询事件朔源总数
    getTotal(params = { id: this.form.audit.id, timestamp: this.form.audit.createTime }) {
      if (String(this.form.audit.sourceEventType) === '0') {
        // 安全事件
        this.data.totalLoading = true
        queryTotalSafe(params).then((res) => {
          this.data.total = res
          this.data.totalLoading = false
        })
      } else if (String(this.form.audit.sourceEventType) === '1') {
        // 关联事件
        this.data.totalLoading = true
        queryTotalRelevance(params).then((res) => {
          this.data.total = res
          this.data.totalLoading = false
        })
      } else {
        // 威胁事件
        this.data.totalLoading = true
        queryTotalThreat(params).then((res) => {
          this.data.total = res
          this.data.totalLoading = false
        })
      }
    },
    // 朔源列表滚动
    scrollTable(params = {}) {
      if (!this.data.nomore && this.count > 0) {
        if (String(this.form.audit.sourceEventType) === '0') {
          if (this.form.safe.length > 19) {
            const lastData = this.form.safe[this.form.safe.length - 1]
            const { eventId, aggrStartDate } = lastData
            params = {
              id: this.form.audit.id,
              timestamp: aggrStartDate,
              pageSize: 20,
              sourceId: eventId,
            }
            this.table.loadingEvent = true
            queryData(params).then((res) => {
              if (res.length < 20) {
                this.data.nomore = true
              } else {
                this.data.nomore = false
              }
              this.form.safe.push(...res)
              this.table.loadingEvent = false
            })
          }
        } else if (String(this.form.audit.sourceEventType) === '1') {
          if (this.form.relevance.length > 19) {
            const lastData = this.form.relevance[this.form.relevance.length - 1]
            const { eventId, createDate } = lastData
            params = {
              id: this.form.audit.id,
              timestamp: createDate,
              pageSize: 20,
              sourceId: eventId,
            }
            this.table.loadingEvent = true
            queryRelevance(params).then((res) => {
              if (res.length < 20) {
                this.data.nomore = true
              } else {
                this.data.nomore = false
              }
              this.form.relevance.push(...res)
              this.table.loadingEvent = false
            })
          }
        } else {
          if (this.form.threat.length > 19) {
            const lastData = this.form.threat[this.form.threat.length - 1]
            const { eventId, receiveTime } = lastData
            params = {
              id: this.form.audit.id,
              timestamp: receiveTime,
              pageSize: 20,
              sourceId: eventId,
            }
            this.table.loadingEvent = true
            queryThreat(params).then((res) => {
              if (res.length < 20) {
                this.data.nomore = true
              } else {
                this.data.nomore = false
              }
              this.form.threat.push(...res)
              this.table.loadingEvent = false
            })
          }
        }
      }
    },
    // 查询原始日志
    handleRowDetail(row) {
      this.activeName = 'third'
      this.form.showLog = true
      this.form.pageNum = 1
      let params = {}
      if (String(this.form.audit.sourceEventType) === '0') {
        params = {
          eventId: row.eventId,
          sourceEventType: '0',
          aggrEndDate: row.aggrEndDate,
          aggrStartDate: row.aggrStartDate,
          pageSize: 20,
        }
      } else if (String(this.form.audit.sourceEventType) === '1') {
        params = {
          eventId: row.eventId,
          sourceEventType: '1',
          createDate: row.createDate,
          updateDate: row.updateDate,
          pageSize: 20,
          pageNum: this.form.pageNum,
        }
      } else {
        params = {
          eventId: row.originalId,
          sourceEventType: '2',
          // receiveTime: row.receiveTime,
          updateDate: row.receiveTime,
          pageSize: 20,
        }
      }
      this.table.loadingLog = true
      // 查询原始日志
      queryLog(params).then((res) => {
        this.form.logList = res
        this.$nextTick(() => {
          this.$refs.table.bodyWrapper.scrollTop = 0
          this.initScroll(row)
        })
        this.table.loadingLog = false
      })
      this.data.loadingLog = true
      // 查询原始日志总数
      queryTotalLog(params).then((res) => {
        this.data.loadingLog = false
        this.data.totalLog = res
      })
    },
    // 关闭原始日志
    removeTab() {
      this.form.showLog = false
      this.activeName = 'second'
    },
    // 原始日志滚动事件
    initScroll(row) {
      let count = 0
      this.form.pageNum = 1
      const _that = this
      const body = document.querySelector('.flag > .el-table__body-wrapper')
      const tableBody = document.querySelector('.flag > .el-table__body-wrapper > .el-table__body')
      this.timer = setTimeout(() => {
        _that.tableHeight = tableBody.clientHeight
      }, 500)
      function callback() {
        if (body.scrollTop >= _that.tableHeight * count + 200) {
          if (_that.form.logList.length > 0 && !_that.table.loadingLog) {
            const lastRow = _that.form.logList[_that.form.logList.length - 1]
            _that.form.pageNum++
            let params = {}
            if (String(_that.form.audit.sourceEventType) === '0') {
              params = {
                eventId: row.eventId,
                aggrEndDate: row.aggrEndDate,
                aggrStartDate: row.aggrStartDate,
                pageSize: 20,
                originalId: lastRow.id,
                timestamp: lastRow.timestamp,
                sourceEventType: '0',
              }
            } else if (String(_that.form.audit.sourceEventType) === '1') {
              params = {
                eventId: row.eventId,
                createDate: row.createDate,
                updateDate: row.updateDate,
                pageSize: 20,
                originalId: lastRow.id,
                timestamp: lastRow.timestamp,
                pageNum: _that.form.pageNum,
                sourceEventType: '1',
              }
            } else {
              params = {
                eventId: row.eventId,
                receiveTime: row.receiveTime,
                pageSize: 20,
                originalId: lastRow.id,
                timestamp: lastRow.timestamp,
                pageNum: _that.form.pageNum,
                sourceEventType: '2',
              }
            }
            _that.table.loadingLog = true
            queryLog(params).then((res) => {
              _that.form.logList.push(...res)
              _that.table.loadingLog = false
              if (res.length >= 20) {
                _that.data.noLog = false
              } else {
                _that.data.noLog = true
              }
            })
          }
          count++
        }
      }
      body.addEventListener('scroll', callback)
    },
    // 点击查看日志详情
    clickOriginalTableDetail(row) {
      this.data.table.detail = row
      this.dialog.visible.detail = true
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) {
          this.$refs.formTemplate.resetFields()
        }
      })
      this.count = 0
      this.data.table.detail = {}
      this.data.nomore = false
      this.data.noLog = false
      this.data.totalLoading = false
      this.data.total = 0
      this.data.totalLog = 0
      this.data.loadingLog = false
      this.dialog.visible.detail = false
      this.form.pageNum = 1
      this.form.pagination.pageNum = 1
      this.activeName = 'first'
      this.form.showLog = false
      clearTimeout(this.timer)
      this.$refs.dialogTemplate.end()
      this.table.loadingLog = false
      this.table.loadingEvent = false
      this.dialogVisible = false
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
.none {
  height: 92%;
  width: 100%;
  display: table;
  .text {
    display: table-cell;
    text-align: center;
    vertical-align: middle;
  }
}
::v-deep .el-dialog {
  .el-dialog__body {
    height: 500px;
  }
}
::v-deep .el-popover,
.el-popper {
  > p {
    overflow-y: auto;
  }
}
.el-tabs__content {
  .el-tab-pane {
    height: 100%;
  }
}
::v-deep .el-table {
  height: 358px;
  .el-table__body-wrapper {
    height: 322px;
    overflow-y: auto;
  }
}
::v-deep .router-wrap-table {
  position: relative;

  ::v-deep .table-footer {
    position: absolute;
    right: 10px;
    bottom: 0;
  }
}
.el-tab-pane .table-footer {
  margin-top: 8px;
}
::v-deep .el-dialog__body {
  padding: 30px 20px 0 20px;
}
</style>
