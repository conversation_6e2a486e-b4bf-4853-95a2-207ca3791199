<!--
 * @Description: 登录首页 - 顶部功能
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <header ref="header">
    <section class="header-title">
      <!-- <img v-if="!systemSetting.systemLogo" src="@/asset/image/header/header-logo.png" height="90%" /> -->
      <div class="system-logo-container">
        <img :src="systemSetting.systemLogo" height="90%" />
        <span class="system-name">{{ systemSetting.systemName }}</span>
      </div>
    </section>
    <section class="header-shortcut">
      <section v-if="option.type && option.type.length > 0" class="header-shortcut-search">
        <el-input v-model="search.fuzzyField" size="mini" class="header-shortcut-search-input" @keydown.enter.native.prevent="clickFastSearch">
          <el-select slot="prepend" v-model="search.type" class="header-shortcut-search-select">
            <el-option v-for="(item, index) in option.type" :key="index" :label="$t(item.label)" :value="item.url"></el-option>
          </el-select>
          <el-button slot="append" icon="el-icon-search" class="header-shortcut-button" @click="clickFastSearch"></el-button>
        </el-input>
      </section>
      <section
        v-if="(alarmAmount && alarmAmount > 0) || (systemAlarmAmount && systemAlarmAmount > 0)"
        class="header-shortcut-container"
        @mouseenter="animate.alarm = true"
        @mouseleave="animate.alarm = false"
      >
        <i class="icon-font-size soc-icon-alarm"></i>
        <transition name="el-zoom-in-top" mode="out-in">
          <ul v-show="animate.alarm" class="bubble">
            <li @click="clickJumpAlarmTab('alarm')">
              <el-badge :value="alarmAmount" :hidden="alarmAmount <= 0">{{ $t('layout.setting.auditAlarmTitle') }}</el-badge>
            </li>
            <li @click="clickJumpAlarmTab('sysAlarm')">
              <el-badge :value="systemAlarmAmount" :hidden="systemAlarmAmount <= 0">{{ $t('layout.setting.systemAlarmTitle') }}</el-badge>
            </li>
          </ul>
        </transition>
      </section>
      <!--<i
                v-if="alarmAmount && alarmAmount > 0"
                :title="$t('layout.setting.alarm',[alarmAmount])"
                class="icon-font-size soc-icon-alarm"
                @click="clickGetAlarmAmount">
            </i>-->
      <i class="icon-font-size soc-icon-username"></i>
      <section class="header-shortcut-container" @mouseenter="animate.setting = true" @mouseleave="animate.setting = false">
        <a class="header-shortcut-username">{{ username }}</a>
        <transition name="el-zoom-in-top" mode="out-in">
          <ul v-show="animate.setting" class="bubble">
            <li v-for="(val, index) in data.setting" :key="index" @click="clickSettingTab(val)">
              <i :class="[val['icon'], { 'header-config-important': val.important }]"></i>
              <span :class="{ 'header-config-important': val.important }">{{ val.label }}</span>
            </li>
          </ul>
        </transition>
      </section>
    </section>
    <section class="shortcut-component">
      <!--用户信息-->
      <modify-user :visible="visible.userInfo" @on-display="displayUserInfoDialog"></modify-user>
      <!--修改密码-->
      <modify-password :visible="visible.password" @on-display="displayPasswordDialog"></modify-password>
      <!--命令行小工具-->
      <cmd-tool :visible.sync="visible.cmd" @on-display="displayCmdDialog"></cmd-tool>
    </section>
    <i ref="shrinkButton" class="soc-icon-scroller-top-all" @click="clickShrinkHeaderNav"></i>
    <i ref="stretchButton" class="soc-icon-scroller-down" @click="clickStretchHeaderNav"></i>
  </header>
</template>

<script>
import cookie from 'js-cookie'
import ModifyUser from '../TheSetting/ModifyUser.vue'
import ModifyPassword from '../TheSetting/ModifyPassword.vue'
import CmdTool from '../TheSetting/CmdTool.vue'
import { querySystemToolsData, queryQuickSearchData, getSystemNameAndLogo } from '@api/layout/layout-api'
export default {
  components: {
    ModifyUser,
    ModifyPassword,
    CmdTool,
  },
  props: {
    username: {
      type: String,
      default: 'admin',
    },
    systemName: {
      type: String,
      default: '',
    },
    alarmAmount: {
      type: [Number, Boolean],
      default: 0,
    },
    systemAlarmAmount: {
      type: [Number, Boolean],
      default: 0,
    },
  },
  data() {
    return {
      animate: {
        setting: false,
        alarm: false,
      },
      visible: {
        userInfo: false,
        password: false,
        cmd: false,
        theme: false,
      },
      option: {
        type: [],
      },
      search: {
        type: '',
        fuzzyField: '',
      },
      data: {
        setting: [],
        tools: [],
      },
      systemSetting: {
        systemLogo: '',
        systemName: '',
      },
    }
  },
  mounted() {
    this.preloadData()
  },
  methods: {
    async preloadData() {
      await this.getSystemTools()
      this.getQuickSearch()
      this.setSettingTools()
      this.handleDisplayTools()
      this.getLogo()
    },
    getLogo() {
      getSystemNameAndLogo().then((res) => {
        if (res) {
          this.systemSetting.systemLogo = res.systemLogo
          this.systemSetting.systemName = res.systemName
        }
      })
    },
    setSettingTools() {
      this.data.setting = [
        {
          label: this.$t('layout.setting.user.label'),
          icon: 'soc-icon-username',
          important: false,
          fn: this.clickTabUserInfo,
        },
        {
          label: this.$t('layout.setting.password.label'),
          icon: 'soc-icon-password',
          important: false,
          fn: this.clickTabUpdatePassword,
        },
        {
          label: this.$t('layout.setting.cmd.label'),
          icon: 'soc-icon-menu-operations',
          important: false,
          fn: this.clickTabCmdTool,
        },
        {
          label: this.$t('layout.setting.logout'),
          icon: 'soc-icon-submit',
          important: true,
          fn: this.logout,
        },
      ]
    },
    handleDisplayTools() {
      const self = this
      this.data.tools.forEach((item) => {
        const [joinLocation, joinObj] = [
          this.data.setting.length - 1,
          {
            label: item.menuName,
            icon: item.menuIcon,
            important: false,
            fn() {
              self.openWindowDisplay(item.menuLocation)
            },
          },
        ]
        this.data.setting.splice(joinLocation, 0, joinObj)
      })
    },
    clickFastSearch() {
      this.$router.push({
        path: this.search.type,
        query: {
          fuzzyField: this.search.fuzzyField,
        },
      })
      this.$emit('on-search', this.search.type)
    },
    clickJumpAlarmTab(tab) {
      if (tab === 'alarm') {
        this.$router.push({
          path: '/alarm/table',
        })
      } else if (tab === 'sysAlarm') {
        this.$router.push({
          path: '/alarm/system',
        })
      }
    },
    clickSettingTab(item) {
      this.animate.setting = false
      item.fn ? item.fn() : null
    },
    displayUserInfoDialog(visible) {
      this.visible.userInfo = visible
    },
    displayPasswordDialog(visible) {
      this.visible.password = visible
    },
    displayCmdDialog(visible) {
      this.visible.cmd = visible
    },
    clickShrinkHeaderNav() {
      this.$refs.header
        .css({
          visibility: 'hidden',
        })
        .animate(
          {
            height: '0',
          },
          400
        )
      this.$refs.shrinkButton.hide()
      this.$refs.stretchButton
        .css({
          visibility: 'initial',
        })
        .show()
      this.$emit('shrink-top-nav')
    },
    clickStretchHeaderNav() {
      this.$refs.header
        .css({
          visibility: '',
        })
        .animate(
          {
            height: '50',
          },
          400
        )
      this.$refs.shrinkButton.show()
      this.$refs.stretchButton.hide()
      this.$emit('stretch-top-nav')
    },
    clickTabUserInfo() {
      this.visible.userInfo = true
    },
    clickTabUpdatePassword() {
      this.visible.password = true
    },
    clickTabCmdTool() {
      this.visible.cmd = true
    },
    clickTabSwitchTheme() {
      const theme = this.$store.getters.theme === 'light' ? 'dark' : 'light'
      this.$store.dispatch('system/switchTheme', theme)
    },
    logout() {
      const removeTip = (className) => {
        Array.from(document.querySelectorAll(className)).forEach((dom) => {
          dom.remove()
        })
      }
      this.$store.dispatch('user/logout').then(() => {
        removeTip('.el-message')
        removeTip('.el-notification')
      })
    },
    openWindowDisplay(url) {
      const routeData = this.$router.resolve({
        path: url,
      })
      this.$store.dispatch('user/updatePath', url)
      cookie.set('store', JSON.stringify(this.$store.state))
      window.open(routeData.href, '_blank')
    },
    async getSystemTools() {
      await querySystemToolsData().then((res) => {
        this.data.tools = res.concat([
          {
            menuId: '165201474575747372',
            parentId: '165371148559712256',
            menuName: this.$t('layout.setting.op'),
            menuLocation: '/visualization/op',
            menuStatus: '2',
            menuIcon: 'soc-icon-menu-overall-situation',
            menuOrder: '4',
            menuDescription: '',
            children: null,
            resourceId: '177744189029613568',
            resourceToken: 'SOC-APP-VISUALIZATION-SECURITY-ATTACK',
            actions: null,
          },
        ])
      })
    },
    getQuickSearch() {
      queryQuickSearchData().then((res) => {
        this.option.type = res
        this.search.type = res[0] && res[0]['url'] ? res[0]['url'] : ''
      })
    },
  },
}
</script>

<style lang="scss" scoped>
header {
  position: relative;
  display: flex;
  height: 50px;
  color: $WT;
  border-bottom: 2px solid;
  box-sizing: border-box;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.65);
  @include theme('background-color', header-bg-color);
  @include theme('border-bottom-color', header-border);

  .system-logo-container {
    position: absolute;
    height: 100%;
    display: flex;
    align-items: center;
    .system-name {
      font-size: 18px;
      font-weight: bold;
      margin-left: 10px;
    }
  }
  .header-title {
    display: flex;
    align-items: center;
    width: 212px;
    padding-left: 12px;
    height: 100%;

    i.soc-icon-logo {
      padding: 10px 20px;
      font-size: 30px;
    }

    h1 {
      font-size: 20px;
      white-space: nowrap;
      cursor: default;
    }

    &:hover i.soc-icon-logo {
      animation: rubberBand 1s;
    }

    &:hover h1 {
      animation: flipInX 2s;
    }
  }

  .header-shortcut {
    position: relative;
    display: flex;
    margin-right: 80px;
    align-items: center;

    &-search {
      border-radius: 4px;

      .el-button {
        border: none;
        background-color: transparent;
      }

      &-input {
        & > ::v-deep .el-input__inner {
          @include theme('background-color', header-search-bg-color);
        }

        ::v-deep .el-input-group__prepend,
        ::v-deep .el-input-group__append {
          @include theme('background-color', header-search-bg-color);
        }

        ::v-deep i {
          @include theme('color', header-search-color);
        }

        ::v-deep .el-input--suffix {
          @include theme('color', header-search-color);
        }

        ::v-deep .el-input__inner {
          border-left: 0;
          border-right: 0;
          @include theme('color', header-search-color);
        }

        .header-shortcut-button {
          &:hover,
          &:focus {
            background-color: transparent !important;
            transform: scale(1.2);
            border-color: transparent;
            transition: 0.2s all;
          }
        }
      }

      &-select {
        width: 80px;
      }
    }

    &-eps {
      margin-right: 20px;

      &-number {
        margin-right: 10px;
        font-size: 24px;
      }

      &-letter {
        font-family: -webkit-pictograph, cursive;
      }
    }

    & i.soc-icon-alarm {
      padding: 6px 10px 0 30px;
      font-size: 28px;
      cursor: pointer;
      animation: flash 2s infinite;
    }

    & > i.soc-icon-username {
      padding: 12px 20px;
      font-size: 28px;
    }

    &-container {
      position: relative;
      cursor: pointer;

      .header-shortcut-username {
        white-space: nowrap;
      }
    }

    ul {
      position: absolute;
      left: 50%;
      top: 32px;
      margin-left: -45px;
      z-index: 2001;
      min-width: 110px;
      border: 1px solid;
      box-sizing: border-box;
      border-radius: 4px;
      @include theme('color', font-color);
      @include theme('background-color', tool-bg-color);
      @include theme('border-color', header-drop-color);

      li {
        display: flex;
        height: 30px;
        padding: 0 5px;
        align-items: center;
        text-align: center;
        line-height: 30px;

        i {
          width: 20px;
          font-size: 16px;
        }

        span {
          white-space: nowrap;
        }

        .header-config-important {
          @include theme('color', error-text-color);
        }

        &:hover {
          @include theme('color', tool-color);
          @include theme('background-color', tool-bg-hover-color);
        }
      }
    }
  }

  .shortcut-component {
    position: absolute;
  }

  i.soc-icon-scroller-top-all {
    position: absolute;
    right: 10px;
    font-size: 20px;
    cursor: pointer;

    &:hover {
      animation: flash 1s;
    }
  }

  i.soc-icon-scroller-down {
    position: absolute;
    top: 0;
    left: 50%;
    display: none;
    width: 60px;
    text-align: center;
    border-bottom-left-radius: 10px;
    border-bottom-right-radius: 10px;
    transform: translateX(-50%);
    opacity: 0.3;
    @include theme('background-color', header-bg-color);
    cursor: pointer;
    z-index: 9999;

    &:hover {
      opacity: 1;
    }
  }
}

.bubble {
  &:before,
  &:after {
    position: absolute;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -8px;
    border-width: 8px;
    border-style: solid;
    content: ' ';
  }

  &:before {
    top: -17px;
    border-color: transparent;
    @include theme('border-bottom-color', border-color);
  }

  &:after {
    top: -16px;
    border-color: transparent;
    @include theme('border-bottom-color', tool-bg-color);
  }
}
</style>
