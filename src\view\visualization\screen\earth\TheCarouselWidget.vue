<!--
 * @Description: 大屏展示 - 底部轮播组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="carousel-container">
    <el-carousel v-if="times > 0" trigger="click" height="130px" direction="vertical" indicator-position="none" :interval="interval">
      <el-carousel-item v-for="item in times" :key="item">
        <widget-item v-if="sourceData[(item - 1) * itemLength]" :widget-item-data="sourceData[(item - 1) * itemLength]"></widget-item>
        <widget-item v-if="sourceData[(item - 1) * itemLength + 1]" :widget-item-data="sourceData[(item - 1) * itemLength + 1]"></widget-item>
        <widget-item v-if="sourceData[(item - 1) * itemLength + 2]" :widget-item-data="sourceData[(item - 1) * itemLength + 2]"></widget-item>
      </el-carousel-item>
    </el-carousel>
    <section v-else class="no-data">
      {{ $t('tip.data.empty') }}
    </section>
  </div>
</template>

<script>
import WidgetItem from './TheCarouselWidgetItem'

export default {
  components: {
    WidgetItem,
  },
  props: {
    sourceData: {
      required: true,
      type: Array,
    },
    interval: {
      required: false,
      type: Number,
      default: 5000,
    },
  },
  data() {
    return {
      itemLength: 3,
    }
  },
  computed: {
    times() {
      if (this.sourceData.length > 0) {
        return Math.ceil(this.sourceData.length / this.itemLength)
      } else {
        return 0
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.carousel-container {
  width: 1920px;
  height: 130px;

  .no-data {
    width: 100%;
    height: 130px;
    color: $WT;
    font-size: 20px;
    line-height: 130px;
    text-align: center;
  }
}
</style>
