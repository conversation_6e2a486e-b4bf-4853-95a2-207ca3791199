<!--
 * @Description: 登录 - 完善密码
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="passwordFormDialog"
    :visible="selfVisible"
    :title="$t('layout.setting.password.label')"
    width="40%"
    @on-close="close"
    @on-submit="submit"
  >
    <el-form ref="passwordForm" class="password-form" :model="password.form" :rules="password.rules" label-width="25%">
      <el-form-item v-if="hasOldPassword" :label="$t('layout.setting.password.old')" prop="old">
        <el-input v-model="password.form.old" class="width-mini" show-password @paste.native.capture.prevent="disablePaste()"></el-input>
        <aside slot="error" class="soc-form-error-text">
          {{ password.error.old }}
        </aside>
      </el-form-item>
      <el-form-item :label="$t('layout.setting.password.new')" prop="new">
        <el-input v-model="password.form.new" class="width-mini" show-password @paste.native.capture.prevent></el-input>
        <aside slot="error" class="soc-form-error-text">
          {{ password.error.new }}
        </aside>
      </el-form-item>
      <el-form-item :label="$t('layout.setting.password.confirm')" prop="confirm">
        <el-input v-model="password.form.confirm" show-password class="width-mini" @paste.native.capture.prevent></el-input>
        <aside slot="error" class="soc-form-error-text">
          {{ password.error.confirm }}
        </aside>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import { JSEncrypt } from 'jsencrypt'
import CustomDialog from '@comp/CustomDialog'
import { validatePassword } from '@util/validate'
import { prompt } from '@util/prompt'
import { resetPasswordData } from '@api/login/login-api'
import { querySystemConfigData } from '@api/management/system-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    hasOldPassword: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const validatorOldPassword = (rule, value, callback) => {
      if (value === '') {
        callback((this.password.error.old = this.$t('validate.password.old.empty')))
      } else {
        callback()
      }
    }
    const getValidText = () => {
      const complexity = this.complexity
      const passwordConfig = {
        includeNumbers: complexity.includes('number'),
        includeLowercase: complexity.includes('lowercase'),
        includeUppercase: complexity.includes('uppercase'),
        includeSpecial: complexity.includes('special'),
        minLength: this.minLength,
      }
      let txt = '密码需要包含'
      if (passwordConfig.includeNumbers) {
        txt += ' 数字'
      }
      if (passwordConfig.includeLowercase) {
        txt += ' 小写字母'
      }
      if (passwordConfig.includeUppercase) {
        txt += ' 大写字母'
      }
      if (passwordConfig.includeSpecial) {
        txt += ' 特殊字符'
      }
      txt += ` 且长度不少于 ${passwordConfig.minLength} 位`
      return txt
    }
    const createPasswordRegex = () => {
      const complexity = this.complexity
      const passwordConfig = {
        includeNumbers: complexity.includes('number'),
        includeLowercase: complexity.includes('lowercase'),
        includeUppercase: complexity.includes('uppercase'),
        includeSpecial: complexity.includes('special'),
        minLength: this.minLength,
      }
      let regex = '^'
      if (passwordConfig.includeNumbers) {
        regex += '(?=.*\\d)'
      }
      if (passwordConfig.includeLowercase) {
        regex += '(?=.*[a-z])'
      }
      if (passwordConfig.includeUppercase) {
        regex += '(?=.*[A-Z])'
      }
      if (passwordConfig.includeSpecial) {
        regex += '(?=.*[!@#$%^&*(),.?":{}|<>])'
      }
      regex += `.{${passwordConfig.minLength},}$`
      return new RegExp(regex)
    }
    const complexityValidator = (password) => {
      const passwordRegex = createPasswordRegex()
      const isValid = passwordRegex.test(password)
      return isValid
    }
    const validatorNewPassword = (rule, value, callback) => {
      if (value === '') {
        callback((this.password.error.new = this.$t('validate.password.new.empty')))
      } else if (value === this.password.form.old) {
        callback((this.password.error.new = this.$t('validate.password.new.compare')))
      } else if (!complexityValidator(value)) {
        callback((this.password.error.new = getValidText(this.complexity)))
      } else {
        callback()
      }
    }
    const validatorConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback((this.password.error.confirm = this.$t('validate.password.confirm.empty')))
      } else if (value !== this.password.form.new) {
        callback((this.password.error.confirm = this.$t('validate.password.confirm.compare')))
      } else {
        callback()
      }
    }
    return {
      complexity: [],
      selfVisible: this.visible,
      password: {
        form: {
          old: '',
          new: '',
          confirm: '',
        },
        rules: {
          old: [{ required: true, validator: validatorOldPassword, trigger: 'blur' }],
          new: [{ required: true, validator: validatorNewPassword, trigger: 'blur' }],
          confirm: [{ required: true, validator: validatorConfirmPassword, trigger: 'blur' }],
        },
        error: {
          old: '',
          new: '',
          confirm: '',
        },
      },
    }
  },
  watch: {
    visible(vis) {
      this.selfVisible = vis
      if (vis) {
        this.getComplexity()
      }
    },
    selfVisible(vis) {
      this.$emit('update:visible', vis)
    },
  },
  methods: {
    getComplexity() {
      querySystemConfigData().then((res) => {
        this.complexity = res?.passwordComplexity?.split(',') || []
        this.minLength = res?.minPasswordLength || 6
      })
    },
    close() {
      this.selfVisible = false
      this.$refs['passwordForm'].resetFields()
    },
    submit() {
      this.$refs['passwordForm'].validate((valid) => {
        if (valid) {
          // 设置公钥 加密密码
          const encryptor = new JSEncrypt()
          encryptor.setPublicKey(this.$store.getters.publicKey)
          let password = {
            newPassword: encryptor.encrypt(this.password.form.new),
            confirmPassword: encryptor.encrypt(this.password.form.confirm),
          }
          if (this.hasOldPassword) {
            password = Object.assign(password, {
              oldPassword: encryptor.encrypt(this.password.form.old),
            })
          }

          this.resetPassword(password)
          this.close()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }

        this.$refs.passwordFormDialog.end()
      })
    },
    disablePaste() {
      return null
    },
    resetPassword(obj) {
      resetPasswordData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.$emit('on-submit')
            }
          )
        } else if (res === 7) {
          prompt({
            i18nCode: 'tip.update.passwordComplexError',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
