<!--
 * @Description: 系统管理 - 数据库维护
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="tab-context-wrapper">
    <section class="database-config">
      <h2>{{ $t('management.system.title.databaseConfig') }}</h2>
      <el-form ref="databaseForm" :model="form.model" :rules="form.rule" label-width="180px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('management.system.label.used') + $t('management.system.label.spaceSurpass')" prop="safeguard">
              <el-input-number v-model="form.model.safeguard" controls-position="right" :max="99" :min="50" class="width-half"></el-input-number>
              % {{ $t('management.system.label.safeguard') }}
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item prop="dataRetainTime" :label="$t('management.system.label.dataRetainTime')">
              <el-select v-model="form.model.dataRetainTime" clearable class="width-small">
                <el-option
                  v-for="item in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]"
                  :key="item"
                  :label="item + $t('time.unit.month')"
                  :value="item"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="5" align="right">
            <el-form-item label-width="0">
              <el-button v-has="'upload'" @click="clickSaveDatabaseConfig">
                {{ $t('button.save') }}
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
    <el-divider></el-divider>
    <section class="database-table router-wrap-table">
      <section class="table-header">
        <h2>{{ $t('management.system.title.databaseTable') }}</h2>
      </section>
      <section class="table-body">
        <el-table :data="tableData.slice((pagination.pageNum - 1) * pagination.pageSize, pagination.pageNum * pagination.pageSize)" height="100%">
          <el-table-column width="50"></el-table-column>
          <el-table-column prop="safeguardTime" :label="$t('management.system.label.time')"></el-table-column>
          <el-table-column prop="safeguardDescription" :label="$t('management.system.label.description')"></el-table-column>
          <el-table-column prop="safeguardResult" :label="$t('management.system.label.result')"></el-table-column>
        </el-table>
      </section>
      <section class="table-footer">
        <el-pagination
          small
          background
          align="right"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          @size-change="databaseTableSizeChange"
          @current-change="databaseTableCurrentChange"
        ></el-pagination>
      </section>
    </section>
  </div>
</template>

<script>
import { prompt } from '@util/prompt'

export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
    tableData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      form: {
        model: {
          safeguard: 0,
        },
        rule: {
          safeguard: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model = this.formData
      }
    },
    clickSaveDatabaseConfig() {
      this.$refs.databaseForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', {
              safeguardCycle: this.form.model.safeguard,
              safeguardMonth: this.form.model.dataRetainTime,
            })
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    databaseTableSizeChange(size) {
      this.pagination.pageSize = size
    },
    databaseTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
    },
  },
}
</script>

<style lang="scss" scoped>
.tab-context-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .database-table {
    .table-header {
      justify-content: normal;
      background-color: $CR;
    }
  }
}
</style>
