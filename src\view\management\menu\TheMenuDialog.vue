<!--
 * @Description: 菜单管理 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :action="!readonly"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <template v-if="readonly">
      <el-form ref="menuForm" class="dialog-form" label-width="25%" :rules="menuForm.rules" :model="menuForm.model">
        <el-form-item :label="$t('management.menu.table.name')" prop="menuName">
          {{ menuForm.model.menuName }}
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.location')">
          {{ menuForm.model.menuLocation }}
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.status')" prop="menuStatus">
          {{ menuForm.model.menuStatusText }}
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.icon')">
          {{ menuForm.model.menuIcon }}
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.resource')">
          {{ menuForm.model.resourceName }}
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.description')">
          {{ menuForm.model.menuDescription }}
        </el-form-item>
      </el-form>
    </template>
    <template v-else>
      <el-form ref="menuForm" class="dialog-form" label-width="25%" :rules="menuForm.rules" :model="menuForm.model">
        <el-form-item :label="$t('management.menu.table.parent')" class="dialog-form-list" prop="parentId">
          <el-tree-select
            :props="tree.prop"
            :options="menuParent"
            :value="menuForm.model.parentId"
            :clearable="tree.clearable"
            :accordion="tree.accordion"
            class="width-mini"
            @get-value="getValue"
          ></el-tree-select>
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.name')" prop="menuName">
          <el-input v-model="menuForm.model.menuName" maxlength="16" show-word-limit class="width-mini"></el-input>
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.location')">
          <el-input v-model="menuForm.model.menuLocation" class="width-mini"></el-input>
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.status')" prop="menuStatus">
          <el-select v-model="menuForm.model.menuStatus" clearable :placeholder="$t('management.menu.header.placeholder')" class="width-mini">
            <el-option :label="$t('management.menu.codeList.show')" value="0"></el-option>
            <el-option :label="$t('management.menu.codeList.hide')" value="1"></el-option>
            <el-option :label="$t('management.menu.codeList.toolbar')" value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.icon')">
          <el-input v-model="menuForm.model.menuIcon" class="width-mini"></el-input>
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.resource')">
          <el-select
            v-model="menuForm.model.resourceId"
            clearable
            filterable
            :placeholder="$t('management.menu.header.placeholder')"
            class="width-mini"
          >
            <el-option v-for="item in resourceOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('management.menu.table.description')">
          <el-input v-model="menuForm.model.menuDescription" type="textarea" class="width-mini"></el-input>
        </el-form-item>
      </el-form>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import ElTreeSelect from '@comp/SelectTree/SelectTree'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
    ElTreeSelect,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    menuParent: {
      type: Array,
      default() {
        return []
      },
    },
    menuForm: {
      type: Object,
      default() {
        return {}
      },
    },
    resourceOption: {
      type: Array,
      default() {
        return []
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      tree: {
        clearable: true,
        filterable: true,
        accordion: false,
        prop: {
          value: 'menuId',
          label: 'menuName',
          children: 'children',
        },
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.menuForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.menuForm.model)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })

      this.$refs.dialogTemplate.end()
    },
    getValue(value) {
      this.menuForm.model.parentId = value
    },
  },
}
</script>
