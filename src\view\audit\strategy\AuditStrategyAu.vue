<!--
 * @Description: 审计策略 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :loading="loading"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="40%">
      <template>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="form.info.policyName.label" :prop="form.info.policyName.key">
              <el-input
                v-model.trim="form.model.policyName"
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.policyName')"
                maxlength="255"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="form.info.orderNumber.label" :prop="form.info.orderNumber.key">
              <el-input-number v-model="form.model.orderNumber" :min="1" :max="999999999"></el-input-number>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="form.info.flag.label" :prop="form.info.flag.key" label-width="20%">
              <el-radio-group v-model="form.model.flag" :disabled="form.model.enable === '1'" @change="changeFlag">
                <el-radio :label="0">
                  {{ $t('audit.strategy.safeEvent') }}
                </el-radio>
                <el-radio :label="1">
                  {{ $t('audit.strategy.linkEvent') }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.model.flag === 0">
          <el-col :span="24">
            <el-form-item :label="form.info.eventTypeList.label" :prop="form.info.eventTypeList.key" label-width="20%">
              <el-cascader
                v-model="form.model.eventTypeList"
                :options="form.eventTypeList"
                :props="{ expandTrigger: 'hover', multiple: true }"
                :collapse-tags="form.model.enable !== '1'"
                filterable
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.eventTypeList')"
                class="width"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-else>
          <el-col :span="24">
            <el-form-item :label="form.info.eventTypeList.label" :prop="form.info.eventTypeList.key" label-width="20%">
              <el-select
                v-model="form.model.eventTypeList"
                multiple
                :collapse-tags="form.model.enable !== '1'"
                :disabled="form.model.enable === '1'"
                clearable
                filterable
                :placeholder="$t('audit.strategy.placeholder.eventTypeList')"
                class="width"
              >
                <el-option v-for="item in form.eventTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label-width="20%" :label="form.info.dateType.label" :prop="form.info.dateType.key">
              <el-radio-group v-model="form.model.dateType" :disabled="form.model.enable === '1'" @change="clearTime">
                <el-radio :label="0">
                  {{ $t('audit.strategy.day') }}
                </el-radio>
                <el-radio :label="1">
                  {{ $t('audit.strategy.week') }}
                </el-radio>
                <el-radio :label="2">
                  {{ $t('audit.strategy.dater') }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item v-show="form.model.dateType === 0" :prop="form.info.date.key" label-width="20%">
              <el-time-picker
                v-model="form.model.date"
                :disabled="form.model.enable === '1'"
                is-range
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                range-separator="~"
                :start-placeholder="$t('audit.strategy.startTime')"
                :end-placeholder="$t('audit.strategy.endTime')"
              ></el-time-picker>
            </el-form-item>
            <el-form-item v-show="form.model.dateType === 1" label-width="20%" :prop="form.info.date.key">
              <el-select
                v-model="form.model.weekStart"
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.placeholder')"
                clearable
                class="mini-width"
                @change="setWeekStart"
              >
                <el-option v-for="item in form.week" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
              ~
              <el-select
                v-model="form.model.weekEnd"
                class="mini-width"
                :placeholder="$t('audit.strategy.placeholder.placeholder')"
                :disabled="!form.model.startWeek || form.model.startWeek < 1 || form.model.enable === '1'"
                clearable
              >
                <el-option
                  v-for="item in form.week"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="form.model.startWeek > item.key"
                ></el-option>
              </el-select>
              <el-time-picker
                v-model="form.model.date"
                is-range
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                :disabled="form.model.enable === '1'"
                range-separator="~"
                :start-placeholder="$t('audit.strategy.startTime')"
                :end-placeholder="$t('audit.strategy.endTime')"
                style="margin-top: 10px"
              ></el-time-picker>
            </el-form-item>
            <el-form-item v-show="form.model.dateType === 2" :prop="form.info.date.key" label-width="20%">
              <el-date-picker
                v-model="form.model.date"
                :disabled="form.model.enable === '1'"
                type="datetimerange"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="right"
                unlink-panels
                range-separator="~"
                :start-placeholder="$t('audit.strategy.startDate')"
                :end-placeholder="$t('audit.strategy.endDate')"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="form.info.outputEventName.label" :prop="form.info.outputEventName.key">
              <el-input
                v-model.trim="form.model.outputEventName"
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.outputEventName')"
                maxlength="255"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="form.info.outputEventLevel.label" :prop="form.info.outputEventLevel.key">
              <el-select
                v-model="form.model.outputEventLevel"
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.outputEventLevel')"
                clearable
              >
                <el-option v-for="item in levelList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="form.info.outputEventType.label" :prop="form.info.outputEventType.key">
              <el-select
                v-model="form.model.outputEventType"
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.outputEventType')"
                clearable
                filterable
              >
                <el-option v-for="item in form.outputEventTypeList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="23">
            <el-form-item :label="form.info.outputEventRemark.label" :prop="form.info.outputEventRemark.key" label-width="21%">
              <el-input
                v-model.trim="form.model.outputEventRemark"
                :disabled="form.model.enable === '1'"
                :placeholder="$t('audit.strategy.placeholder.outputEventRemark')"
                type="textarea"
                :rows="5"
                class="width-max"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider></el-divider>
        <el-row>
          <el-col :span="20">
            <el-form-item :label="form.info.forwardSystemId.label" :prop="form.info.forwardSystemId.key" label-width="24%">
              <el-select
                v-model="form.model.forwardSystemId"
                :placeholder="$t('audit.strategy.placeholder.forwardSystemId')"
                clearable
                multiple
                filterable
                collapse-tags
                class="width-max"
              >
                <el-option v-for="item in form.isForwardList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { queryEventTypeList } from '@api/audit/strategy-api'

export default {
  name: 'AuDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '800',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      levelList: [
        {
          label: this.$t('level.serious'),
          value: 0,
        },
        {
          label: this.$t('level.high'),
          value: 1,
        },
        {
          label: this.$t('level.middle'),
          value: 2,
        },
        {
          label: this.$t('level.low'),
          value: 3,
        },
        {
          label: this.$t('level.general'),
          value: 4,
        },
      ],
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    vaLi() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            if (this.form.model.flag === 1 || this.form.model.flag === 2) {
              this.form.model.eventTypeList = this.form.model.eventTypeList.map((item) => {
                const arr = item.split()
                arr.unshift(this.form.eventTypeList[0].type)
                return arr
              })
            }
            const params = Object.assign({}, this.form.model)
            // 给父级调用数据
            this.$emit('on-submit', params, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    // 点击提交表单
    clickSubmitForm() {
      if (this.form.model.dateType === 1) {
        const arr = [
          this.$t('audit.strategy.weekList.mon'),
          this.$t('audit.strategy.weekList.tue'),
          this.$t('audit.strategy.weekList.wed'),
          this.$t('audit.strategy.weekList.thu'),
          this.$t('audit.strategy.weekList.fri'),
          this.$t('audit.strategy.weekList.sat'),
          this.$t('audit.strategy.weekList.sun'),
        ]
        if (arr.indexOf(this.form.model.weekEnd) !== -1 && arr.indexOf(this.form.model.weekStart) !== -1) {
          this.vaLi()
        } else {
          prompt(
            {
              i18nCode: this.$t('audit.strategy.placeholder.time'),
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      } else {
        this.vaLi()
      }
      this.$refs.dialogTemplate.end()
    },
    setWeekStart(e) {
      let start
      this.form.week.map((item) => {
        if (item.value === e) {
          start = item.key
          return start
        }
      })
      this.form.model.startWeek = start
      this.form.model.weekEnd = ''
    },
    // 清空时间段
    clearTime() {
      this.form.model.date = ''
      this.form.model.weekEnd = ''
      this.form.model.weekStart = ''
    },
    getEventTypeList(param) {
      queryEventTypeList(param).then((res) => {
        if (param.type === '1' || param.type === '2') {
          this.form.eventTypeList = res[0].children
        } else {
          this.form.eventTypeList = res
        }
      })
    },
    changeFlag(e) {
      this.form.eventTypeList = []
      this.form.model.eventTypeList = []
      this.getEventTypeList({
        type: String(e),
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 30px 0;

  .el-cascader {
    .el-input {
      input::-webkit-input-placeholder {
        color: $CR;
      }
    }
  }
}

::v-deep .el-divider--horizontal {
  width: 97%;
  height: 1px;
  margin: 15px auto 24px;
}

.width {
  width: 96% !important;
  font-size: inherit;
  font-weight: 400;
}

.mini-width {
  width: 44% !important;
}
</style>
