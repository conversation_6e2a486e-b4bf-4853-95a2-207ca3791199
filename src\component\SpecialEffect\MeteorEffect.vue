<template>
  <section class="effect-meteor">
    <i
      v-for="index in meteorNum"
      :key="index"
      :style="{
        backgroundColor: randomColor(0, 7),
        left: randomPosition(10, 1800),
        top: randomPosition(-20, 600),
        animationDelay: index % 6 === 0 ? '0s' : index * 0.8 + 's',
      }"
      class="meteor"
    ></i>
  </section>
</template>

<script>
import { randomNum } from '@util/random'

export default {
  name: 'MeteorEffect',
  props: {
    meteorNum: {
      type: Number,
      default: 20,
    },
  },
  data() {
    return {
      colors: ['#ff6e6e', '#ffd17b', '#f8ff7b', '#7bff98', '#7be5ff', '#7bbcff', '#ca7bff'],
    }
  },
  computed: {
    randomColor() {
      return (min, max) => {
        return this.colors[randomNum(min, max)]
      }
    },
    randomPosition() {
      return (min, max) => {
        return `${randomNum(min, max)}px`
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.effect-meteor {
  position: absolute;

  .meteor {
    position: absolute;
    z-index: -10;
    display: block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    filter: alpha(opacity=0);
    box-shadow: 0 0 5px 5px rgba(255, 255, 255, 0.3);
    animation: meteor-effect 7s infinite linear;
    transform-origin: 100% 0;
    opacity: 0.1;

    &:after {
      top: 0;
      left: 4px;
      display: block;
      border-style: solid;
      border-width: 0 170px 2px 170px;
      border-color: transparent transparent transparent rgba(255, 255, 255, 0.3);
      box-shadow: 0 0 1px 0 rgba(255, 255, 255, 0.1);
      transform: rotate(-45deg) translate(1px, 3px);
      transform-origin: 0 100%;
      content: '';
    }
  }
}

@keyframes meteor-effect {
  0% {
    opacity: 0.1;
    filter: alpha(opacity=0);
    transform: scale(0) rotate(0deg) translate(0, 0);
  }
  50% {
    opacity: 1;
    filter: alpha(opacity=100);
    transform: scale(1) rotate(0deg) translate(-200px, 200px);
  }
  100% {
    opacity: 0.1;
    filter: alpha(opacity=0);
    transform: scale(2) rotate(0deg) translate(-500px, 500px);
  }
}
</style>
