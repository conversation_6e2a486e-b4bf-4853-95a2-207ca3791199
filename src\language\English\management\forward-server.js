export default {
  forwardServer: {
    id: 'Forward System ID',
    poid: 'Enterprise Node Number',
    coid: 'OID Node Number',
    community: 'Community',
    type: 'Forward Method',
    reIP: 'Other Server IP',
    port: 'Port Number',
    remark: 'Description',
    forwardServer: 'Forward to External System',
    placeholder: {
      queryInput: 'Please enter forward method',
      type: 'Forward Method',
      community: 'Community',
      poid: 'No Enterprise Node Number',
      coid: 'No OID Node Number',
      reIP: 'Other Server IP',
      remark: 'Description',
    },
    delete: {
      running: {
        audit: 'An audit strategy is using this forward policy, please modify before deleting',
        assoc: 'A correlation strategy is using this forward policy, please modify before deleting',
        aggre: 'An aggregation strategy is using this forward policy, please modify before deleting',
      },
    },
    update: {
      repeat: 'Forward policy information already exists',
    },
  },
}
