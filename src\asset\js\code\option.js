import i18n from '@/language'

export const handleStatus = [
  { value: 0, label: i18n.t('code.handleStatus.unhandle') },
  { value: 1, label: i18n.t('code.handleStatus.ignore') },
]

export const anomalyType = [
  { value: 'illegalAction', label: i18n.t('code.anomalyType.illegalAction') },
  { value: 'illegalIntruder', label: i18n.t('code.anomalyType.illegalIntruder') },
]

export const status = [
  { value: '0', label: i18n.t('code.status.off') },
  { value: '1', label: i18n.t('code.status.on') },
]

export const executeStatus = [
  { value: '0', label: i18n.t('code.executeStatus.off') },
  { value: '1', label: i18n.t('code.executeStatus.on') },
]

export const runStatus = [
  { value: 0, label: i18n.t('code.runStatus.abnormal') },
  { value: 1, label: i18n.t('code.runStatus.normal') },
]

export const eventLevel = [
  { value: '0', label: i18n.t('level.serious') },
  { value: '1', label: i18n.t('level.high') },
  { value: '2', label: i18n.t('level.middle') },
  { value: '3', label: i18n.t('level.low') },
  { value: '4', label: i18n.t('level.general') },
]

export const forecastType = [
  { value: 'total', label: i18n.t('code.forecastType.total') },
  { value: 'eventType', label: i18n.t('code.forecastType.eventType') },
  { value: 'srcIp', label: i18n.t('code.forecastType.srcIp') },
  { value: 'dstIp', label: i18n.t('code.forecastType.dstIp') },
  { value: 'fromIp', label: i18n.t('code.forecastType.fromIp') },
]

export const resultStatus = [
  { value: '0', label: i18n.t('code.resultStatus.fail') },
  { value: '1', label: i18n.t('code.resultStatus.success') },
]
