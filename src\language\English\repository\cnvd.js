export default {
  cnvd: {
    header: 'CNVD Vulnerability Database',
    table: {
      cnvdId: 'Vulnerability ID',
      cnvdTitle: 'Vulnerability Name',
      cnvdLevel: 'Vulnerability Level',
      relateThreat: 'Related Threat',
      cnvdProvider: 'Provider',
      releaseDate: 'Release Date',
      handel: 'Operation',
      affectedProducts: 'Affected Products',
      cnvdDesc: 'Description',
      cnvdReference: 'Reference',
      authInfo: 'Authentication Information',
      cnvdResolve: 'Solution',
      rollOutFlag: 'Related Identifier',
      startDate: 'Release Start Date',
      endDate: 'Release End Date',
    },
    dialog: {
      detailTitle: 'CNVD Vulnerability Database Details',
    },
    placeholder: {
      keyword: 'Vulnerability ID/Vulnerability Name',
    },
    fuzzyQuery: 'Vulnerability ID/Vulnerability Name/Description',
  },
}
