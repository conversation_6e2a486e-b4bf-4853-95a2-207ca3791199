<!--
 * @Description: 故障事件 - 处置弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.handle', [titleName])"
    width="35%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formTemplate" :model="model" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item prop="handleSuggest" :label="$t('event.fault.handleSuggest')">
            <el-input v-model="model.handleSuggest" type="textarea" :rows="2" maxlength="2048"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import { prompt } from '@util/prompt'
import CustomDialog from '@comp/CustomDialog/CustomDialog'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.model.handleState = '1'
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
  },
}
</script>
