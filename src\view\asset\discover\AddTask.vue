<!--
 * @Description: 资产发现 - 添加任务弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <!--自定义dialog-->
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <!--主表单-->
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="25%">
      <template>
        <el-form-item :label="form.info.taskName.label" :prop="form.info.taskName.key">
          <el-input
            v-model.trim="form.model.taskName"
            :placeholder="$t('asset.discover.placeholder.taskName')"
            maxlength="16"
            class="width-mini"
          ></el-input>
        </el-form-item>
        <el-form-item :label="form.info.netWorkId.label" :prop="form.info.netWorkId.key">
          <el-select v-model="form.model.netWorkId" :placeholder="$t('asset.discover.placeholder.netWorkId')" clearable filterable class="width-mini">
            <el-option v-for="item in form.netList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { addTasks } from '@api/asset/discover-api'

export default {
  name: 'AddTask',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 关闭当前dialog
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 添加任务交互操作
    addTask(obj) {
      addTasks(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.$emit('on-submit', 'true')
              this.clickCancelDialog()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else if (res === 5) {
          prompt({
            i18nCode: 'tip.add.repeatName',
            type: 'error',
          })
        } else if (res === 6) {
          prompt({
            i18nCode: 'tip.add.repeatTask',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const formModel = Object.assign({}, this.form.model)
            this.addTask(formModel)
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
