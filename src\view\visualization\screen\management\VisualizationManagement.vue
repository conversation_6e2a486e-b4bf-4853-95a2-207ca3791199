<!--
 * @Description: 大屏管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model="data.fuzzyField"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('visualization.management.fuzzyQuery')])"
              prefix-icon="soc-icon-search"
              @change="clickQueryScreenTable"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddScreen">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteScreen">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('visualization.management.title') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="screenTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="screenTableRowChange"
          @selection-change="screenTableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column :label="$t('visualization.management.table.name')" prop="screenName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('visualization.management.table.url')" prop="screenUrl" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('visualization.management.table.description')" prop="screenDescription" show-overflow-tooltip></el-table-column>
          <el-table-column fixed="right" width="210">
            <template slot-scope="scope">
              <el-button v-has="'find'" class="el-button--blue" @click="clickLookScreen(scope.row)">
                {{ $t('button.look') }}
              </el-button>
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateScreen(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteScreen(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="screenTableSizeChange"
        @current-change="screenTableCurrentChange"
      ></el-pagination>
    </footer>
    <au-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form-data="dialog.form.add"
      @on-submit="clickSubmitAddScreen"
    ></au-dialog>
    <au-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form-data="dialog.form.update"
      @on-submit="clickSubmitUpdateScreen"
    ></au-dialog>
  </div>
</template>

<script>
import AuDialog from './TheManagementDialog'
import { prompt } from '@util/prompt'
import { addScreenData, deleteScreenData, updateScreenData, queryScreenTableData } from '@api/visualization/management-api'
import cookie from 'js-cookie'

export default {
  name: 'VisualizationManagement',
  components: {
    AuDialog,
  },
  data() {
    return {
      data: {
        loading: false,
        table: [],
        selected: [],
        fuzzyField: '',
      },
      pagination: {
        visible: true,
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
      dialog: {
        visible: {
          add: false,
          update: false,
        },
        title: {
          add: this.$t('dialog.title.add', [this.$t('visualization.management.title')]),
          update: this.$t('dialog.title.update', [this.$t('visualization.management.title')]),
        },
        form: {
          add: {
            screenName: '',
            screenUrl: '',
            screenDescription: '',
          },
          update: {
            screenName: '',
            screenUrl: '',
            screenDescription: '',
          },
        },
      },
    }
  },
  mounted() {
    this.getScreenTableData()
  },
  methods: {
    addScreen(obj) {
      addScreenData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.getScreenTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    deleteScreen(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteScreenData(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.getScreenTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    updateScreen(obj) {
      updateScreenData(obj).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getScreenTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    getScreenTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryScreenTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.visible = true
        this.data.loading = false
      })
    },
    clickAddScreen() {
      this.dialog.form.add = {
        screenName: '',
        screenUrl: '',
        screenDescription: '',
      }
      this.dialog.visible.add = true
    },
    clickBatchDeleteScreen() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.screenId).toString()
        this.deleteScreen(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickQueryScreenTable() {
      this.pagination.pageNum = 1
      this.getScreenTableData({
        fuzzyField: this.data.fuzzyField,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      })
    },
    clickLookScreen(row) {
      this.openWindowDisplay(row.screenUrl)
    },
    clickUpdateScreen(row) {
      this.dialog.form.update = {
        screenId: row.screenId,
        screenName: row.screenName,
        screenUrl: row.screenUrl,
        screenDescription: row.screenDescription,
      }
      this.dialog.visible.update = true
    },
    clickDeleteScreen(row) {
      this.deleteScreen(row.screenId)
    },
    clickSubmitAddScreen(form) {
      this.addScreen(form)
    },
    clickSubmitUpdateScreen(form) {
      this.updateScreen(form)
    },
    screenTableRowChange(row) {
      this.pagination.currentRow = row
    },
    screenTableSelectsChange(select) {
      this.data.selected = select
    },
    screenTableSizeChange(size) {
      this.pagination.pageSize = size
      this.getScreenTableData()
    },
    screenTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getScreenTableData()
    },
    openWindowDisplay(url) {
      const routeData = this.$router.resolve({
        path: url,
      })
      this.$store.dispatch('user/updatePath', url)
      cookie.set('store', JSON.stringify(this.$store.state))
      window.open(routeData.href, '_blank')
    },
  },
}
</script>
