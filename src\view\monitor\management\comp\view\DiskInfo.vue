<!--
 * @Description: 监控器展示 - 磁盘信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-13
 * @Editor:
 * @EditDate: 2021-08-13
-->
<template>
  <line-chart height="300px" proto :width="'100%'" :line-data="diskOption"></line-chart>
</template>

<script>
import LineChart from '@comp/ChartFactory/common/LineChart.vue'
import { queryMonitorDisk } from '@api/monitor/view-api'

export default {
  components: {
    LineChart,
  },
  props: {
    params: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      diskOption: {},
    }
  },
  computed: {
    condition() {
      return {
        edId: this.params.edId,
        monitorId: this.params.monitorId,
        startTime: this.params.startTime,
        endTime: this.params.endTime,
      }
    },
  },
  watch: {
    params: {
      handler(newValue, oldValue) {
        this.getDiskInfo()
      },
      deep: true,
    },
  },
  mounted() {
    this.getDiskInfo()
  },
  methods: {
    getDiskInfo(params = this.condition) {
      queryMonitorDisk(params).then((res) => {
        this.diskOption = this.getLineOption(res)
      })
    },
    getLineOption(data) {
      const legend = this.chartLegendConfig(data)
      const series = this.chartSeriesConfig(data)
      const option = {
        backgroundColor: 'transparent',
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          subtext: '磁盘使用率',
        },
        tooltip: {
          trigger: 'axis',
        },
        legend: {
          type: 'scroll',
          icon: 'rect',
          itemWidth: 14,
          itemHeight: 5,
          itemGap: 13,
          data: legend,
        },
        xAxis: {
          type: 'category',
          data: data.axis,
        },
        yAxis: {
          type: 'value',
        },
        series: series,
      }
      return option
    },
    chartSeriesConfig(data) {
      const series = []
      const seriesItem = (name, data) => {
        return {
          name: name,
          type: 'line',
          data: data,
        }
      }
      data.data.forEach((item, index) => {
        series.push(seriesItem(item.name, item.value))
      })
      return series
    },
    chartLegendConfig(data) {
      const legend = []
      data.data.forEach((item, index) => {
        legend.push(item.name)
      })
      return legend
    },
  },
}
</script>
