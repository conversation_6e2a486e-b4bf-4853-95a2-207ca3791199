<template>
  <div class="widget">
    <header class="widget-header">
      {{ this.$t('visualization.compliance.title.logStorageSpace') }}
    </header>
    <main class="widget-body">
      <div class="icon-box">
        <i class="el-icon-files"></i>
      </div>
      <div class="detail-box">
        <p class="detail-box-word">
          <span>{{ useSpace }}G</span>
          &nbsp;&nbsp; {{ $t('visualization.compliance.label.diskTotalSpace', [percentage]) }}%
        </p>
        <div class="detail-box-chart">
          <el-progress :percentage="percentage" color="#e74a25" :format="formatProgress" :title="`${percentage}%`"></el-progress>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { queryLogStorageSpace } from '@api/visualization/compliance-api'

export default {
  data() {
    return {
      useSpace: '',
      totalSpace: '',
      percentage: 0,
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    formatProgress(percentage) {
      return ''
    },
    loadData() {
      this.getLogStorageSpace()
    },
    getLogStorageSpace() {
      queryLogStorageSpace().then((res) => {
        if (res) {
          this.useSpace = Number(res.chSize)
          this.percentage = parseFloat(((res.chSize * 100) / res.diskSize).toFixed(2))
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.widget-body {
  .icon-box {
    background-color: #e74a25;
  }
  .detail-box {
    &-word {
      span {
        color: #e74a25;
      }
    }
    &-chart {
      ::v-deep .el-progress-bar {
        padding-right: 5px;
      }
    }
  }
}
</style>
