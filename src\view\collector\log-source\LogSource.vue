<!--
 * @Description: 日志源类型
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023-02-21
 * @Editor:
 * @EditDate: 2023-02-21
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" :options="options" @on-change="changeQueryTable" @on-add="clickAdd"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-select="clickSelectRows"
      @on-update="clickUpdate"
      @on-delete="clickDelete"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <add-dialog
      :visible.sync="dialog.add.visible"
      :title-name="title"
      :model="dialog.add.model"
      :options="options"
      @on-submit="addSubmit"
    ></add-dialog>
    <update-dialog
      :visible.sync="dialog.update.visible"
      :title-name="title"
      :model="dialog.update.model"
      :options="options"
      @on-submit="updSubmit"
    ></update-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import AddDialog from './TheAddDialog'
import UpdateDialog from './TheUpdateDialog'
import { prompt } from '@util/prompt'

import {
  queryLogSourcesList,
  addLogSource,
  deleteLogSource,
  updateLogSource,
  queryManufactCombo,
  queryCategoryCombo,
} from '@api/collector/log-source-api'

export default {
  name: 'DeviceModel',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    AddDialog,
    UpdateDialog,
  },
  data() {
    return {
      title: this.$t('collector.logSource.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '', // 日志源名称
          manufact: '',
          categoryId: '',
          typeName: '',
          // desc: ""
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        manufact: [],
        category: [],
      },
      dialog: {
        add: {
          visible: false,
          model: {},
        },
        update: {
          visible: false,
          model: {
            typeId: '',
            typeName: '',
            categoryId: '',
            categoryName: '',
            manufact: '',
            // desc: ""
          },
        },
      },
    }
  },
  mounted() {
    this.initOptions()
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') {
        this.pagination.pageNum = 1
      }
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          manufact: this.query.form.manufact.toString(),
          categoryId: this.query.form.categoryId,
          typeName: this.query.form.typeName,
          // desc: this.query.form.desc
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickAdd() {
      this.dialog.add.visible = true
      this.dialog.add.model = {
        typeName: '',
        categoryId: '',
        manufact: '',
        // desc: ""
      }
    },
    addSubmit(obj) {
      this.addLogSource(obj)
    },
    clickUpdate(row) {
      this.dialog.update.model = {
        typeId: row.typeId,
        typeName: row.typeName,
        categoryId: row.categoryId,
        manufact: row.manufact,
        // desc: row.desc
      }
      this.dialog.update.visible = true
    },
    updSubmit(obj) {
      this.updateLogSource(obj)
    },
    clickDelete(row) {
      this.deleteLogSource(row.typeId)
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryLogSourcesList(params).then((res) => {
        this.table.data = res.rows
        console.log(res)
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
    addLogSource(obj) {
      addLogSource(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
              this.getManufactCombo()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    updateLogSource(obj) {
      updateLogSource(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
              this.getManufactCombo()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.update.use',
            type: 'warning',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    deleteLogSource(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteLogSource(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.query.form = {
                  fuzzyField: '',
                  typeName: '',
                  manufact: '',
                  categoryId: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.table.data.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else if (res === 3) {
            prompt({
              i18nCode: 'tip.delete.use',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    initOptions() {
      this.getManufactCombo()
      this.getCategoryCombo()
    },
    getManufactCombo() {
      queryManufactCombo().then((res) => {
        this.options.manufact = res
      })
    },
    getCategoryCombo() {
      queryCategoryCombo().then((res) => {
        this.options.category = res
        console.log(this.options)
      })
    },
  },
}
</script>
