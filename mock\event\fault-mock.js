const { TableMock } = require('../util')

const faultTable = new TableMock({
  total: 20,
  template: {
    faultNo: '@ID',
    faultName: '@CNAME',
    'currentStatus|1': [0, 1],
    'faultStatus|1': ['0', '1', '2', '3'],
    'faultClassName|1': ['应用服务性能', '数据库性能', '网络设备性能', '主机性能'],
    edName: '@IP',
    domaName: '@CNAME',
    'faultLevel|1': ['1', '2', '3'],
    enterDate: '@DATETIME',
    updateDate: '@DATETIME',
    faultModule: '@TITLE',
    faultSolution: '@DESC',
    'handleState|1': '0',
    handleSuggest: 'dfgd',
  },
})

const faultClass = [
  { value: '0', label: '应用服务性能' },
  { value: '1', label: '数据库性能' },
  { value: '2', label: '网络设备性能' },
  { value: '3', label: '主机性能' },
]

const faultLevel = [
  { value: '0', label: '严重' },
  { value: '1', label: '高级' },
  { value: '2', label: '中级' },
  { value: '3', label: '低级' },
  { value: '4', label: '一般' },
  { value: '-1', label: '未知' },
]

module.exports = [
  {
    url: '/faulteventmanagement/queryFaultEvents',
    type: 'get',
    response: (option) => {
      faultTable.query(option)
      return {
        code: 200,
        data: faultTable.getMockData(),
      }
    },
  },
  {
    url: '/faulteventmanagement/queryFaultEventDetails',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: faultTable.detail(option, 'faultNo'),
      }
    },
  },
  {
    url: '/faulteventmanagement/combo/faultclass',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: faultClass,
      }
    },
  },
  {
    url: '/faulteventmanagement/combo/faultlevel',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: faultLevel,
      }
    },
  },
]
