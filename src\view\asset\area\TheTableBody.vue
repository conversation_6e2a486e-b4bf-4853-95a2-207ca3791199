<!--
 * @Description: 区域管理 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-06-29
 * @Editor:
 * @EditDate: 2021-06-29
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @selection-change="clickSelectRows"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column prop="domaName" :label="$t('asset.area.prop.domaName')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="domaAbbreviation" :label="$t('asset.area.prop.domaAbbr')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="officeTel" :label="$t('asset.area.prop.officeTel')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="email" :label="$t('asset.area.prop.email')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceNum" :label="$t('asset.area.prop.deviceNum')" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" width="260">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
            <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
              {{ $t('button.update') }}
            </el-button>
            <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
              {{ $t('button.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
  },
  methods: {
    clickSelectRows(select) {
      this.$emit('on-select', select)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    clickUpdate(row) {
      this.$emit('on-update', row)
    },
    clickDelete(row) {
      this.$emit('on-delete', row)
    },
  },
}
</script>
