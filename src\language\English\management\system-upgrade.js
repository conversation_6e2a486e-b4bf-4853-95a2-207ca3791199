export default {
  systemUpgrade: {
    upContent: 'Upgrade Content',
    upOldEdition: 'Version Before Upgrade',
    edition: 'Current Version',
    noEdition: 'No version information',
    fileSize: 'Upload file size cannot exceed 100MB!',
    upNewEdition: 'Version After Upgrade',
    upResult: 'Upgrade Result',
    upTime: 'Upgrade Time',
    bag: 'Upgrade Package Selection',
    upGrade: 'Upgrade',
    backOff: 'Rollback to Previous Version',
    upManagement: 'System Upgrade Management',
    loadingText: 'Upgrading... Some services will restart after successful upgrade... Please wait for three minutes...',
    updatePackageNameError: 'Upload file name is invalid',
    updatePackageCurrentError: 'Please upload an upgrade package compatible with the current version',
    updatePackageSameError: 'Version numbers cannot be the same',
  },
}
