.DS_Store

# Project Files
.husky/
deploy/
node_modules/
dist/
docs/
plugins/
public/
CNAME
LICENSE
**/assets
/src/util/particles.js

# Dependencies version lock file
package-lock.json
pnpm-lock.yaml
yarn.lock

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.sh
*.snap
*.png
*.yml

# Other
.eslintignore
.gitmodules
.gitattributes
.gitignore
.prettierignore
.npmignore
.editorconfig
.stylelintrc
.vcmrc
.npmrc.template
.huskyrc

netlify.toml
Dockerfile
Jenkinsfile

# deps exclude
types/auto-imports.d.ts
types/components.d.ts