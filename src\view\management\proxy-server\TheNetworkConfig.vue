<template>
  <div class="network-wrapper">
    <section class="netcard">
      <div class="netcard-tip">
        <el-alert title="点击网口图标可进行网口测试，测试时间1分钟，" type="warning"></el-alert>
      </div>
      <div class="netcard-wrapper">
        <div v-for="(item, aIndex) in panels" :key="item.id" class="netcard-wrapper-row">
          <section :class="{ 'is-flash': item.id === enps.enpsId && enps.status === 1 }" @click="testNetwork(item, aIndex)">
            <i :class="['netcard-wrapper-row-cursor', 'soc-icon-network-port', item.state === 1 ? 'color' : '']"></i>
          </section>
          <div>{{ item.name }}</div>
        </div>
      </div>
    </section>

    <section class="main">
      <!--循环wlan模块 -->
      <section class="main-wlan">
        <div v-for="(item, index) in jsonData.networkConfig" :id="'id' + index" :key="index">
          <h2 class="main-wlan-title">
            {{ $t('management.network.title.network', [item.name]) }}
          </h2>
          <div class="main-wlan-form">
            <el-form :key="index" :model="item" :rules="rules" label-width="120px">
              <el-row>
                <el-col :span="8">
                  <el-form-item prop="ipAddress" :label="$t(`management.network.form.ipAddress`)">
                    <template>
                      <el-input v-model.trim="item.ipAddress" clearable class="width-small"></el-input>
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item :label="$t(`management.network.form.maskCodeBit`)">
                    <template>
                      <el-select
                        v-model="item.bits"
                        clearable
                        class="width-small"
                        :placeholder="$t('management.network.form.bits')"
                        @change="(val) => bitsChange(val, index)"
                      >
                        <el-option v-for="item in bitsOption" :key="item.value" :label="item.label" :value="item.label"></el-option>
                      </el-select>
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item class="width-small" prop="maskCode" :label="$t(`management.network.form.maskCode`)">
                    {{ item.maskCode }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row>
                <el-col :span="8">
                  <el-form-item prop="dns1" :label="$t(`management.network.form.firstDns`)">
                    <el-input v-model.trim="item.dns1" clearable class="width-small"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item prop="dns2" :label="$t(`management.network.form.secondDns`)">
                    <el-input v-model.trim="item.dns2" clearable class="width-small"></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item prop="gateWay" :label="$t(`management.network.form.gateWay`)">
                    <el-input v-model.trim="item.gateWay" clearable class="width-small"></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </div>
      </section>
      <!--按钮 -->
      <section class="form-button">
        <el-button @click="submitForm">
          {{ $t('button.save') }}
        </el-button>
      </section>
    </section>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import { validateIpv4 } from '@util/validate'
import { prompt } from '@util/prompt'
import { queryNetWork, updateNetworkData, queryNetworkPanel, testNetworkCard } from '@api/management/proxy-server-api'
import { isEmpty } from '@util/common'
export default {
  props: {
    agentId: {
      type: String,
      default: '',
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value === '') {
        callback()
      } else if (!validateIpv4(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }
    return {
      navgatorIndex: 0,
      values: '',
      enps: {
        status: 0, // 1-没运行完在闪
        enpsId: '',
      },
      useFlag: 0, // 最后一个端口的运行状态
      bitsEnum: {
        '1': '*********',
        '2': '*********',
        '3': '*********',
        '4': '240.0.0.0',
        '5': '*********',
        '6': '*********',
        '7': '*********',
        '8': '*********',
        '9': '***********',
        '10': '***********',
        '11': '***********',
        '12': '***********',
        '13': '***********',
        '14': '***********',
        '15': '***********',
        '16': '***********',
        '17': '*************',
        '18': '*************',
        '19': '*************',
        '20': '*************',
        '21': '*************',
        '22': '*************',
        '23': '*************',
        '24': '*************',
        '25': '***************',
        '26': '***************',
        '27': '***************',
        '28': '***************',
        '29': '***************',
        '30': '***************',
        '31': '***************',
        '32': '***************',
      },
      // 32种掩码Option
      bitsOption: [
        { label: '1', value: '*********' },
        { label: '2', value: '*********' },
        { label: '3', value: '*********' },
        { label: '4', value: '240.0.0.0' },
        { label: '5', value: '*********' },
        { label: '6', value: '*********' },
        { label: '7', value: '*********' },
        { label: '8', value: '*********' },
        { label: '9', value: '***********' },
        { label: '10', value: '***********' },
        { label: '11', value: '***********' },
        { label: '12', value: '***********' },
        { label: '13', value: '***********' },
        { label: '14', value: '***********' },
        { label: '15', value: '***********' },
        { label: '16', value: '***********' },
        { label: '17', value: '*************' },
        { label: '18', value: '*************' },
        { label: '19', value: '*************' },
        { label: '20', value: '*************' },
        { label: '21', value: '*************' },
        { label: '22', value: '*************' },
        { label: '23', value: '*************' },
        { label: '24', value: '*************' },
        { label: '25', value: '***************' },
        { label: '26', value: '***************' },
        { label: '27', value: '***************' },
        { label: '28', value: '***************' },
        { label: '29', value: '***************' },
        { label: '30', value: '***************' },
        { label: '31', value: '***************' },
        { label: '32', value: '***************' },
      ],
      // ip校验规则
      rules: {
        ipAddress: [
          {
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        gateWay: [
          {
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        dns1: [
          {
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        dns2: [
          {
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
      },
      panels: [],
      jsonData: {},
      allow: '',
    }
  },
  computed: {
    ...mapState({
      status: (state) => state.websocket.status,
      managementNetwork: (state) => state.websocket.managementNetwork,
    }),

    buildId() {
      return (index) => {
        return '#'.concat(index)
      }
    },
    changeButton: function() {
      let buttonStatus = false
      if (this.enps.status !== 0) {
        buttonStatus = true
      } else {
        buttonStatus = false
      }
      return buttonStatus
    },
  },
  watch: {
    status: {
      handler(value) {
        if (value) {
          this.sendWebsocket(1)
        }
      },
      immediate: true,
    },
    managementNetwork(value) {
      const { enpsId, status } = value.message
      this.enps = { enpsId, status }
    },
  },
  mounted() {
    this.queryNetWorkConfig()
    this.queryPanel()
    this.queryload()
  },
  beforeDestroy() {
    this.sendWebsocket(2)
  },
  methods: {
    queryload() {
      const key = []
      const value = []
      const objValue = this.managementNetwork.message
      if (objValue !== '' || objValue !== null || objValue !== undefined) {
        for (var i in objValue) {
          key.push(i)
          value.push(objValue[i])
        }
        this.enps.enpsId = key[0]
        this.enps.status = value[0]
      }
    },
    sendWebsocket(action) {
      if (this.status) {
        this.$store.dispatch('websocket/send', {
          topic: 'check_network_card_push',
          action,
          message: null,
        })
      }
    },
    submitForm() {
      // 表单校验标识位
      let flag
      // 校验表单数据方法
      const validate = (array) => {
        // flag=0 检验成功  flag=1 校验失败
        let flag = 0
        for (const { ipAddress, maskCode, dns1, dns2, gateWay } of array) {
          const validateArr = [ipAddress, maskCode, dns1, dns2, gateWay]
          validateArr.map((item) => {
            if (!isEmpty(item) && item !== null && item !== '') {
              if (!validateIpv4(item)) {
                prompt(
                  {
                    i18nCode: 'validate.form.warning',
                    type: 'warning',
                    print: true,
                  },
                  () => {
                    flag = 1
                    return false
                  }
                )
              }
            }
          })
        }
        return flag
      }
      // 调用校验方法
      if (this.jsonData.networkConfig.length !== 0) {
        flag = validate(this.jsonData.networkConfig)
      }
      if (this.validateIpRepeat(this.jsonData.networkConfig)) {
        flag = 1
      }
      // flag ===0 说明表单数据ip格式正常,提交表单
      if (flag === 0) {
        this.jsonData = Object.assign(this.jsonData, {
          agentId: this.agentId,
        })
        updateNetworkData(this.jsonData).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.update.reboot',
                type: 'success',
              },
              () => {
                this.queryNetWorkConfig()
                this.$emit('on-save')
              }
            )
          } else if (res === 2) {
            prompt({
              i18nCode: 'management.network.tip.configNone',
              type: 'error',
            })
          } else if (res === 3) {
            prompt({
              i18nCode: 'management.network.tip.manageNone',
              type: 'error',
            })
          } else if (res === 4) {
            prompt({
              i18nCode: 'management.network.tip.false',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.update.error',
              type: 'error',
            })
          }
        })
      }
    },
    validateIpRepeat(arr) {
      const ipArr = []
      arr.map((item) => {
        if (item.ipAddress.isNotEmpty()) {
          ipArr.push(item.ipAddress)
        }
      })
      const nary = ipArr.sort()
      for (var i = 0; i < ipArr.length; i++) {
        if (nary[i] === nary[i + 1]) {
          prompt({
            i18nCode: 'management.network.tip.ipRepeat',
            type: 'error',
          })
          return true
        }
      }
      return false
    },
    // 查询网络配置数据
    queryNetWorkConfig() {
      queryNetWork(this.agentId).then((res) => {
        this.jsonData = res
      })
    },
    // 根据位数生成掩码
    bitsChange(val, index) {
      this.jsonData.networkConfig[index].maskCode = this.bitsEnum[val]
    },
    // 查询LAN端口使用信息
    queryPanel() {
      queryNetworkPanel(this.agentId).then((res) => {
        this.panels = res
      })
    },
    queryAnchor(item, index) {
      this.navgatorIndex = index
      this.$el.querySelector(`#id${index}`).scrollIntoView({
        behavior: 'smooth',
      })
    },
    testNetwork(item, index) {
      const objvalue = this.managementNetwork.message
      if (objvalue === '' || objvalue === null || objvalue === undefined) {
        this.queryAnchor(item, index)
        testNetworkCard(item.id, this.agentId).then((res) => {
          this.enps.enpsId = String(item.id)
          this.enps.status = 1
        })
      } else {
        for (const i in objvalue) {
          if (objvalue[i] !== 1) {
            this.queryAnchor(item, index)
            testNetworkCard(item.id).then((res) => {
              this.enps.enpsId = String(item.id)
              this.enps.status = 1
            })
          }
        }
      }
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__label {
  padding-right: 20px;
  font-size: 14px;
  color: $BK;
}

a {
  color: $BK;
  text-decoration: none;
}

a:hover {
  color: $BK;
  text-decoration: none;
  cursor: pointer;
}

.color {
  color: $GENERAL;
}

.is-flash {
  -webkit-animation: bling 2.5s infinite ease-in-out;
  animation: bling 2.5s infinite ease-in-out;
}

@keyframes bling {
  from,
  50%,
  to {
    opacity: 1;
  }
  5%,
  25%,
  75% {
    opacity: 0;
  }
}

.network-wrapper {
  box-sizing: border-box;
  .netcard {
    &-tip {
      max-width: 482px;
      margin: 10px auto;
    }

    &-wrapper {
      max-width: 462px;
      margin: 10px auto;
      border: 1px solid #f5f6f8;
      box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
      border-radius: 5px;
      box-sizing: border-box;

      &-row {
        display: inline-block;
        height: 77%;
        padding: 12px;

        &-cursor {
          cursor: pointer;
        }

        i {
          margin: 5px 5px;
          font-size: 30px;
        }

        div {
          padding-left: 2px;
          font-size: 8px;
        }
      }
    }
  }

  .main {
    height: 85%;
    &-dns {
      border-top: 2px solid #f5f6f8;

      &-title {
        color: #1873d7;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        font-weight: 600;
      }

      &-content {
        width: 60%;
        margin: 20px auto 30px auto;
      }
    }

    &-wlan {
      height: 80%;
      overflow: auto;
      border-top: 2px solid #f5f6f8;

      &-title {
        color: #1873d7;
        font-weight: 600;
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
      }

      &-form {
        width: 100%;
        margin: 25px auto 20px auto;
      }
    }

    .form-button {
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
