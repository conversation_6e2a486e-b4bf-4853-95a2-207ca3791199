import request from '@util/request'

// 审计员增加操作
export function addData(obj) {
  return request({
    url: '/audituser/user',
    method: 'post',
    data: obj || {},
  })
}

// 人员删除操作
export function deleteData(ids) {
  return request({
    url: `/audituser/user/${ids}`,
    method: 'delete',
  })
}

// 人员更新操作
export function updateData(obj) {
  return request({
    url: `/audituser/user`,
    method: 'put',
    data: obj || {},
  })
}

// 人员查询列表操作
export function queryTableData(obj) {
  return request({
    url: '/audituser/users',
    method: 'get',
    params: obj || {},
  })
}

// 审计组增加操作
export function addGroups(obj) {
  return request({
    url: '/audituser/group',
    method: 'post',
    data: obj || {},
  })
}

// 审计组删除操作
export function deleteGroups(ids) {
  return request({
    url: `/audituser/group/${ids}`,
    method: 'delete',
  })
}

// 审计组更新操作
export function updateGroups(obj) {
  return request({
    url: `/audituser/group`,
    method: 'put',
    data: obj || {},
  })
}

// 审计组查询操作
export function queryGroups() {
  return request({
    url: '/audituser/groups',
    method: 'get',
  })
}

// 查询审计组下拉框
export function queryGroup() {
  return request({
    url: '/audituser/combo/groups',
    method: 'get',
  })
}

// 查询审计事件类型下拉框
export function queryType() {
  return request({
    url: '/audituser/combo/audit-types',
    method: 'get',
  })
}
