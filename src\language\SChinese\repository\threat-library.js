export default {
  threatLibrary: {
    header: '威胁情报库',
    type: {
      ip: '威胁情报IP',
      domain: '威胁情报域名',
      url: '威胁情报网址',
      email: '威胁情报邮箱',
      hash: '威胁情报Hash',
    },
    table: {
      threatItem: '威胁项',
      threatIp: '威胁IP',
      threatType: '威胁类型',
      frequency: '发生次数',
      createTime: '创建时间',
      firstTime: '首次时间',
      firstStartTime: '首次开始时间',
      firstEndTime: '首次结束时间',
      lastTime: '末次时间',
      lastStartTime: '末次开始时间',
      lastEndTime: '末次结束时间',
      record: '记录',
      action: '动作标签',
      objectType: '对象类型',
      historyUrl: '历史网址',
      malware: '恶意软件标签',
      country: '国家',
      province: '省份',
      city: '城市',
      organization: '组织',
      operator: '运营商',
      code: '代码',
      longitude: '经度',
      latitude: '纬度',
      sha1: 'SHA1值',
      sha265: 'SHA256值',
      hash: 'MD5 HASH值',
      hashType: 'Hash类型',
      source: '发生来源',
      threatLevel: '威胁级别',
      activeTime: '激活时间',
      confidence: '置信度',
      subscriptionChannel: '订阅频道',
      attackProtocol: '攻击使用协议',
    },
    tabPanel: {
      info: '信息',
      confidence: '置信度',
    },
    fuzzyQuery: '威胁项/威胁类型',
  },
}
