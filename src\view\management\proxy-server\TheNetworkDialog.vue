<!--
 * @Description: 代理服务器 - 网络配置弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-04-25
 * @Editor:
 * @EditDate: 2022-04-25
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.network', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane :label="$t('management.proxy.network.networkConfig')" name="network">
        <network-config :agent-id="model.agentId" @on-save="clickHandle"></network-config>
      </el-tab-pane>
      <el-tab-pane :label="$t('management.proxy.network.service')" name="service">
        <service-config :agent-id="model.agentId" @on-save="clickHandle"></service-config>
      </el-tab-pane>
    </el-tabs>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import NetworkConfig from './TheNetworkConfig'
import ServiceConfig from './TheServiceConfig'

export default {
  components: {
    CustomDialog,
    NetworkConfig,
    ServiceConfig,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'network',
      data: {
        form: {
          network: {},
          service: {},
        },
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.activeName = 'network'
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickHandle() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
      this.$emit('on-change')
    },
  },
}
</script>

<style lang="scss" scoped>
.el-tabs {
  height: 100%;
  ::v-deep .el-tabs__content {
    height: calc(100% - 60px);
    .el-tab-pane {
      min-height: 300px;
      height: 100%;
      overflow: auto;
    }
  }
}
</style>
