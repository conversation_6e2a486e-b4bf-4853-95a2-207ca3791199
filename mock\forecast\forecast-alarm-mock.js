const { TableMock, getURLLastPath } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    id: '@ID',
    label: '@ID',
    lableName: '@NAME',
    'type|1': ['total', 'srcIp', 'dstIp', 'eventType'],
    updateTime: '@DATETIME',
  },
})

// 事件类型信息项
const labelList = [
  { value: '********', label: '********', type: 'srcIp' },
  { value: '********', label: '********', type: 'srcIp' },
  { value: '********', label: '********', type: 'dstIp' },
  { value: '********', label: '********', type: 'dstIp' },
  { value: '23003', label: '暴力破解/登录', type: 'eventType' },
  { value: '13004', label: '利用FTP漏洞', type: 'eventType' },
  { value: 'total', label: 'total', type: 'total' },
]

const analysisData = {
  lable: '***********',
  lableName: '***********',
  times: ['1970-01-20 05:24', '1970-01-20 06:24', '1970-01-20 07:24', '1970-01-20 08:25', '1970-01-20 09:25', '1970-01-20 10:25', '1970-01-20 11:25'],
  type: 'fromIp',
  updateTime: '2021-10-26 15:32:38',
  anomalys: [
    { key: 'KSigmaModel', name: '经典K-sigma模型', time: '1970-01-20 05:31', actualVal: 141750, expectedVal: 378592 },
    { key: 'KSigmaModel', name: '经典K-sigma模型', time: '1970-01-20 05:36', actualVal: 755151, expectedVal: 379192.5 },
    { key: 'DBScanModel', name: '基于DBScan密度的模型', time: '1970-01-20 05:31', actualVal: 141750, expectedVal: 378592 },
    { key: 'DBScanModel', name: '基于DBScan密度的模型', time: '1970-01-20 05:36', actualVal: 755151, expectedVal: 379192.5 },
    { key: 'ExtremeLowDensityModel', name: '超低密度模型', time: '1970-01-20 05:31', actualVal: 141750, expectedVal: 378592 },
    { key: 'ExtremeLowDensityModel', name: '超低密度模型', time: '1970-01-20 05:36', actualVal: 755151, expectedVal: 379192.5 },
    { key: 'SimpleThresholdModel', name: null, time: '1970-01-20 05:24', actualVal: 58228, expectedVal: 124990.81 },
    { key: 'SimpleThresholdModel', name: null, time: '1970-01-20 05:36', actualVal: 755151, expectedVal: 427797.94 },
  ],
  charts: [
    { key: 'actual', name: '实际值', values: [58228, 379050, 379145, 379146, 379145, 379241, 379145] },
    { key: 'predict', name: '预测值', values: [58228, 37905, 37915, 379150, 37914, 379221, 379142] },
    { key: 'mae', name: '平均绝对误差', values: [58228, 379050, 379145, 37916, 379145, 379241, 379145] },
    { key: 'mapee', name: '平均绝对误差百分比(按预期值)', values: [58228, 379050, 379145, 379186, 379145, 379241, 379145] },
    { key: 'mase', name: '预测缩放误差平均值', values: [58228, 379050, 379145, 379146, 379145, 379241, 379145] },
    { key: 'mape', name: '平均绝对误差百分比', values: [58228, 379050, 379145, 379146, 379145, 379041, 379145] },
    { key: 'smape', name: '对称平均绝对百分比误差', values: [58228, 379050, 379145, 379146, 379145, 376241, 379145] },
  ],
}

const modelData = [
  { value: 'ExtremeLowDensityModel', label: 'modelData' },
  { value: 'KSigmaModel', label: '经典K-sigma模型' },
  { value: 'DBScanModel', label: '基于DBScan密度的模型' },
]

module.exports = [
  {
    url: '/dataanalysis/queryDataAnalysisAnomalyLists',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: `/dataanalysis/findLableByType/[A-Za-z0-9]`,
    type: 'get',
    response: (option) => {
      const labelCombo = labelList.filter((item) => item.type === getURLLastPath(option.url))
      return {
        code: 200,
        data: labelCombo,
      }
    },
  },
  {
    url: '/dataanalysis/queryDataAnalysisByTypeAndLable',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: analysisData,
      }
    },
  },
  {
    url: '/dataanalysis/queryDataAnalysisAnomalyModel',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: modelData,
      }
    },
  },
]
