<template>
  <section class="effect-ripple">
    <section
      v-for="index in rippleNum"
      :key="index"
      :style="{
        left: random(100, 1700, 'px'),
        top: random(100, 800, 'px'),
      }"
      class="ripple-outer"
    >
      <i
        v-for="idx in 4"
        :key="idx"
        :style="{
          animationDelay: 0.2 + (idx - 1) * 0.5 + 's',
          animationDuration: random(1, 3, 's'),
        }"
        class="ripple-inner"
      ></i>
    </section>
  </section>
</template>

<script>
import { randomNum } from '@util/random'

export default {
  name: 'RippleEffect',
  props: {
    rippleNum: {
      type: Number,
      default: 20,
    },
  },
  computed: {
    random() {
      return (min, max, unit = '') => {
        return `${randomNum(min, max)}${unit}`
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.effect-ripple {
  .ripple-outer {
    position: absolute;
    .ripple-inner {
      position: absolute;
      left: 50%;
      top: 50%;
      z-index: -1;
      margin-left: -100px;
      margin-top: -100px;
      box-sizing: border-box;
      width: 200px;
      height: 200px;
      border: 6px solid #5ebab9;
      border-radius: 50%;
      animation: ripple-effect 4s ease-out infinite;
      box-shadow: 1px 1px 100px #5ebab9;
      opacity: 0;
    }
  }
}

@keyframes ripple-effect {
  0% {
    transform: scale(0);
    opacity: 0;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    transform: scale(1);
    opacity: 0;
  }
}
</style>
