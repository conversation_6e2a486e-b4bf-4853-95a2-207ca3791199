const { TableMock } = require('../util')
const tableData = new TableMock({
  total: 1,
  template: {
    screenId: '@ID',
    screenName: '地球大屏',
    screenUrl: '/visualization/earth',
    screenDescription: '安全事件大屏展示，包含攻击趋势，事件类型TOP3，攻击源IP地址TOP10,安全等级等。',
  },
})

module.exports = [
  {
    url: '/visualization/screenmanagement',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/visualization/screenmanagement/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'screenId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/visualization/screenmanagement',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'screenId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/visualization/screenmanagement',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
]
