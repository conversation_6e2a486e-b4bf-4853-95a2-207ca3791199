<template>
  <div class="chart-wrapper">
    <el-row :gutter="20">
      <el-col :span="5">
        <el-select v-model="type" class="width-max">
          <el-option value="pie" label="饼图"></el-option>
          <el-option value="pie-rose" label="玫瑰图"></el-option>
          <el-option value="pie-half" label="半圆图"></el-option>
          <el-option value="pie-3d" label="3D圆图"></el-option>
          <el-option value="ring" label="环形图"></el-option>
          <el-option value="ring-rose" label="玫瑰环图"></el-option>
          <el-option value="ring-half" label="半环图"></el-option>
          <el-option value="ring-3d" label="3D环图"></el-option>
        </el-select>
      </el-col>
      <el-col :span="4" :offset="15" align="right">
        <el-button @click="clickLookData">
          数据结构
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <pie-chart :option="option"></pie-chart>
    </el-row>
    <el-dialog title="饼图数据结构" :visible.sync="visible">
      <ace-editor v-model="jsonString" auto-complete lang="json" width="100%" height="400" @init="initEditorConfig()"></ace-editor>
    </el-dialog>
  </div>
</template>

<script>
import AceEditor from 'vue2-ace-editor'
import PieChart from '@comp/ChartFactory/forecast/PieChart'

export default {
  name: 'ChartPie',
  components: {
    AceEditor,
    PieChart,
  },
  data() {
    return {
      visible: false,
      type: 'pie',
      data: [
        {
          name: 'aa',
          value: 44,
        },
        {
          name: 'bb',
          value: 32,
        },
        {
          name: 'cc',
          value: 37,
        },
        {
          name: 'dd',
          value: 23,
        },
        {
          name: 'ee',
          value: 16,
        },
        {
          name: 'ff',
          value: 76,
        },
        {
          name: 'gg',
          value: 54,
        },
        {
          name: 'hh',
          value: 88,
        },
        {
          name: 'ii',
          value: 3,
        },
        {
          name: 'jj',
          value: 65,
        },
        {
          name: 'kk',
          value: 64,
        },
        {
          name: 'll',
          value: 76,
        },
        {
          name: 'mm',
          value: 40,
        },
        {
          name: 'nn',
          value: 50,
        },
        {
          name: 'oo',
          value: 77,
        },
        {
          name: 'pp',
          value: 30,
        },
        {
          name: 'nn',
          value: 50,
        },
        {
          name: 'qq',
          value: 89,
        },
        {
          name: 'rr',
          value: 32,
        },
        {
          name: 'ss',
          value: 23,
        },
        {
          name: 'tt',
          value: 98,
        },
        {
          name: 'uu',
          value: 63,
        },
        {
          name: 'vv',
          value: 71,
        },
        {
          name: 'ww',
          value: 93,
        },
        {
          name: 'xx',
          value: 81,
        },
        {
          name: 'yy',
          value: 45,
        },
        {
          name: 'zz',
          value: 56,
        },
      ],
    }
  },
  computed: {
    jsonString() {
      return JSON.stringify(this.data, null, '\t')
    },
    option() {
      return {
        type: this.type,
        data: this.data,
      }
    },
  },
  methods: {
    initEditorConfig(mode = 'json', theme = 'tomorrow_night_blue') {
      require(`brace/mode/${mode}`)
      require(`brace/theme/${theme}`)
    },
    clickLookData() {
      this.visible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;

  div.el-row:first-child {
    height: 32px;
  }

  div.el-row:nth-child(2) {
    height: calc(100% - 32px);
  }
}
</style>
