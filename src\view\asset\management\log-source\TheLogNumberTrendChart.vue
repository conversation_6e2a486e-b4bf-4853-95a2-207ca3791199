<!--
 * @Description: 日志数量趋势图表
 * @Version: 3.5
 * @Author:
 * @Date: 2023/03/05
 * @Editor:
 * @EditDate: 2023/03/05
-->
<template>
  <div ref="logNumberTrendChartDom">
    <el-container class="widget" :style="{ height: height + 'px' }">
      <el-header class="widget-title" height="30px">
        <el-col :span="20">
          {{ $t('visualization.compliance.title.logNumberTrend') }}
        </el-col>
        <el-col :span="3" align="right">
          <el-select v-model="cycleValue" size="mini" @change="changeCycle">
            <el-option v-for="item in options.cycle" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-col>
      </el-header>
      <el-main class="widget-main">
        <line-chart ref="lineChartDom" :option="option"></line-chart>
      </el-main>
    </el-container>
  </div>
</template>

<script>
import LineChart from '@comp/ChartFactory/forecast/LineChart'
import { queryLogCollectTrendChart } from '@api/asset/management-api'

export default {
  components: {
    LineChart,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      chartData: [],
      cycleValue: 3,
      options: {
        cycle: [
          { value: 1, label: this.$t('visualization.compliance.cycle.lastDay') },
          { value: 2, label: this.$t('visualization.compliance.cycle.lastWeek') },
          { value: 3, label: this.$t('visualization.compliance.cycle.lastMonth') },
          { value: 4, label: this.$t('visualization.compliance.cycle.lastHalfYear') },
          { value: 5, label: this.$t('visualization.compliance.cycle.lastYear') },
        ],
      },
    }
  },
  computed: {
    option() {
      return {
        type: 'line-stack',
        axis: 'x',
        data: this.chartData,
      }
    },
  },
  methods: {
    loadData() {
      this.getLogCollectTrendChart()
    },
    changeCycle(value) {
      this.cycleValue = value
      this.getLogCollectTrendChart()
    },
    getLogCollectTrendChart(
      params = {
        devId: this.data.devId,
        categoryID: this.data.categoryID,
        typeId: this.data.typeId,
        ip: this.data.ip,
        type: this.cycleValue,
      }
    ) {
      queryLogCollectTrendChart(params).then((res) => {
        this.chartData = res
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.widget {
  width: 100%;
  height: 100%;
  padding: 5px;
  &-title {
    display: flex;
    height: 30px;
    padding: 0;
    justify-content: space-between;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    .more-word {
      font-size: 10px;
      color: #1873d7;
    }
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10px;
  }
}
::v-deep .el-input--small .el-input__inner {
  height: 26px;
  line-height: 26px;
}
::v-deep .el-input__prefix,
::v-deep .el-input__suffix {
  top: 2px;
}
</style>
