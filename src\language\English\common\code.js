export default {
  code: {
    level: {
      l0: 'No level information',
      risk: {
        l1: 'Normal', // 1
        l2: 'Low', // 2
        l3: 'Medium', // 3
        l4: 'High', // 4
        l5: 'Compromised', // 5
      },
      event: {
        l1: 'General', // 4
        l2: 'Low', // 3
        l3: 'Medium', // 2
        l4: 'High', // 1
        l5: 'Severe', // 0
      },
      vul: {
        l1: 'Low', // 1
        l2: 'Medium', // 2
        l3: 'High', // 3
      },
      alarm: {
        l1: 'Very Low', // 1
        l2: 'Low', // 2
        l3: 'Medium', // 3
        l4: 'High', // 4
        l5: 'Very High', // 5
      },
      value: {
        l1: 'Low', // 1
        l2: 'General', // 2
        l3: 'Medium', // 3
        l4: 'High', // 4
        l5: 'Very High', // 5
      },
      dga: {
        l0: 'Low', // 0
        l1: 'High', // 1
      },
    },
    status: {
      on: 'Online',
      off: 'Offline',
    },
    executeStatus: {
      on: 'Enabled',
      off: 'Disabled',
    },
    state: {
      device: {
        offline: 'Offline Status', // 1
        online: 'Online Status', // 2
        abnormal: 'Abnormal Status', // 3
      },
      task: {
        enable: 'Enabled', // 0
        disable: 'Disabled', // 1
      },
      scan: {
        fail: 'Submission Failed', // 2
        running: 'Running', // 3
        completed: 'Completed', // 4
        skipped: 'Skipped', // 5
        stopped: 'Stopped', // 6
        wait: 'Waiting', // 8
        timeout: 'Timeout', // 9
      },
      vul: {
        resolved: 'Resolved', // 0
        unsolved: 'Unresolved', // 1
      },
      asset: {
        notConfig: 'SNMP Not Configured', // 0
        noConfig: 'Not Configured', // 0
        offline: 'Offline', // 1
        normal: 'Normal', // 2
        abnormal: 'Abnormal', // 3
      },
      connect: {
        success: 'Success',
        failed: 'Failed',
      },
      current: {
        normal: 'Normal',
        abnormal: 'Abnormal',
      },
    },
    stage: {
      attack: {
        invasionAsset: 'Invade Asset', // 1
        singleBlowup: 'Single Point Blast', // 2
        hInfiltrate: 'Lateral Infiltration', // 3
        stealData: 'Data Theft', // 4
      },
    },
    asset: {
      state: {
        all: 'All',
        register: 'Registered Asset',
        noRegister: 'Unregistered Asset',
        important: 'Important Asset',
        noImportant: 'Non-important Asset',
        focus: 'Focused Asset',
        noFocus: 'Non-focused Asset',
        fixed: 'Fixed Asset',
        noFixed: 'Non-fixed Asset',
      },
      audit: {
        manual: 'Manual Assessment', // 2
        auto: 'Automatic Assessment', // 1
      },
      source: {
        manually: 'Manual Entry', // 1
        passiveFind: 'Passive Discovery', // 2
        deviceSync: 'Device Sync', // 3
      },
      change: {
        flowDiscern: 'Traffic Identification', // 1
        scanFind: 'Scan Discovery', // 2
        deviceSync: 'Device Sync', // 3
        bulkImport: 'Bulk Import', // 4
        manualEdit: 'Manual Edit', // 5
      },
    },
    trend: {
      up: 'Up', // 1
      down: 'Down', // 2
      same: 'Unchanged', // 3
    },
    cycle: {
      immediate: 'Immediate', // 0
      day: 'Once a Day', // 2
      week: 'Once a Week', // 3
      month: 'Once a Month', // 4
    },
    alarm: {
      type: {
        cpu: 'CPU Exceeded', // 1
        memory: 'Memory Exceeded', // 2
        disk: 'Disk Exceeded', // 3
      },
    },
    ip: {
      type: {
        all: 'All',
        outer: 'External', // 0
        inner: 'Internal', // 1
      },
    },
    area: {
      domestic: 'Domestic', // 2
      foreign: 'Foreign', // 3
    },
    place: {
      china: 'China', // 2
      world: 'World', // 3
    },
    flow: {
      type: {
        inner: 'Internal Traffic', // 1
        outer: 'External Traffic', // 2
      },
    },
    handleStatus: {
      unhandle: 'Unhandled',
      ignore: 'Ignored',
    },
    anomalyType: {
      illegalAction: 'Abnormal Behavior',
      illegalIntruder: 'Illegal Intrusion',
    },
    runStatus: {
      normal: 'Normal',
      abnormal: 'Abnormal',
    },
    forecastType: {
      total: 'Total',
      eventType: 'Event Type',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      fromIp: 'Collector IP',
    },
    resultStatus: {
      success: 'Success',
      fail: 'Fail',
    },
    backupWay: {
      increment: 'Incremental',
      all: 'Full',
    },
  },
}
