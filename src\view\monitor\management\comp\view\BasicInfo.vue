<!--
 * @Description: 监控器展示 - 基本信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-10
 * @Editor:
 * @EditDate: 2021-08-10
-->
<template>
  <el-form :model="basicModel" label-width="120px">
    <el-row>
      <el-col v-for="(item, index) in columnColumns" :key="index" :span="12">
        <el-form-item :prop="item.key" :label="item.label">
          {{ basicModel[item.key] }}
        </el-form-item>
      </el-col>
    </el-row>
    <template v-if="compInfo.indexOf('memory') > -1">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('monitor.management.view.basic.currentMemory')">
            <pre>{{ memoryInfo }}</pre>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <template v-if="compInfo.indexOf('cpu') > -1">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('monitor.management.view.basic.currentCpu')">
            <template v-if="cpuModel.length > 0">
              <el-table :data="cpuModel" highlight-current-row tooltip-effect="light">
                <el-table-column v-for="(item, key) in cpuColumns" :key="key" :prop="item.key" :label="item.label" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <p v-if="item.key === 'cpuId'">CPU{{ scope.row[item.key] }}</p>
                    <p v-else>
                      {{ scope.row[item.key] }}
                    </p>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
    <template v-if="compInfo.indexOf('disk') > -1">
      <el-row>
        <el-col :span="24">
          <el-form-item :label="$t('monitor.management.view.basic.currentDisk')">
            <template v-if="diskModel.length > 0">
              <el-table :data="diskModel" highlight-current-row tooltip-effect="light" width="80%">
                <el-table-column
                  v-for="(item, key) in diskColumns"
                  :key="key"
                  :prop="item.key"
                  :label="item.label"
                  show-overflow-tooltip
                ></el-table-column>
              </el-table>
            </template>
          </el-form-item>
        </el-col>
      </el-row>
    </template>
  </el-form>
</template>

<script>
import { queryCurrentCpu, queryCurrentMemory, queryCurrentDisk } from '@api/monitor/view-api'

export default {
  props: {
    params: {
      required: true,
      type: Object,
    },
    basicModel: {
      required: true,
      type: Object,
    },
    compInfo: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      memoryModel: [],
      cpuModel: [],
      diskModel: [],
      memoryInfo: '',
      columnColumns: [
        { key: 'monitorName', label: this.$t('monitor.management.view.basic.monitorName') },
        { key: 'monitorTypeName', label: this.$t('monitor.management.view.basic.monitorTypeName') },
        { key: 'edName', label: this.$t('monitor.management.view.basic.edName') },
        { key: 'edIp', label: this.$t('monitor.management.view.basic.edIp') },
      ],
      cpuColumns: [
        { key: 'cpuId', label: this.$t('monitor.management.view.cpu.cpuId') },
        { key: 'usage', label: this.$t('monitor.management.view.cpu.usage') },
      ],
      diskColumns: [
        { key: 'diskName', label: this.$t('monitor.management.view.disk.diskName') },
        { key: 'allDisk', label: this.$t('monitor.management.view.disk.allDisk') },
        { key: 'restDisk', label: this.$t('monitor.management.view.disk.restDisk') },
        { key: 'usage', label: this.$t('monitor.management.view.disk.usage') },
      ],
    }
  },
  watch: {
    params: {
      handler() {
        this.getBasicInfo()
      },
      deep: true,
    },
  },
  mounted() {
    this.getBasicInfo()
  },
  methods: {
    getBasicInfo() {
      const params = this.handleParams()
      this.getCurrentCpuInfo(params)
      this.getCurrentMemoryInfo(params)
      this.getCurrentDiskInfo(params)
    },
    handleParams() {
      const params = Object.assign(
        {},
        {
          edId: this.params.edId,
          monitorId: this.params.monitorId,
          startTime: this.params.startTime,
          endTime: this.params.endTime,
        }
      )
      return params
    },
    getCurrentCpuInfo(params) {
      queryCurrentCpu(params).then((res) => {
        this.cpuModel = res
      })
    },
    getCurrentMemoryInfo(params) {
      queryCurrentMemory(params).then((res) => {
        this.renderMemory(res)
      })
    },
    renderMemory(data) {
      this.memoryInfo = ''
      data.forEach((item, index) => {
        if (Number(item.allMemory) >= 0) {
          this.memoryInfo += '内存总量' + item.allMemory + 'G， ' + '剩余内存' + item.restMemory + 'G， 已使用内存' + item.usage + '%。'
        } else {
          this.memoryInfo += '已使用内存' + item.usage + '%。'
        }
      })
    },
    getCurrentDiskInfo(params) {
      queryCurrentDisk(params).then((res) => {
        this.diskModel = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-table {
  width: 88% !important;
}
</style>
