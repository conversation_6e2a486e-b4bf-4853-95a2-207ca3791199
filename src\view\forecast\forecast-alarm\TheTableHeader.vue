<!--
 * @Description: 预测告警- 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-26
 * @Editor:
 * @EditDate: 2021-10-26
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model.trim="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('forecast.forecastAlarm.placeholder.infoItem')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select v-model="filterCondition.form.type" clearable :placeholder="$t('forecast.forecastAlarm.label.type')" @change="changeType">
                <el-option v-for="item in forecastTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.infoItem"
                clearable
                filterable
                :placeholder="$t('forecast.forecastAlarm.label.infoItem')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.infoItem" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <el-date-picker
                v-model="filterCondition.form.occurTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('forecast.forecastAlarm.placeholder.startAccurTime')"
                :end-placeholder="$t('forecast.forecastAlarm.placeholder.endAccurTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.model"
                clearable
                :placeholder="$t('forecast.forecastAlarm.label.model')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.model" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col align="right" :offset="15" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
import { isEmpty } from '@util/common'
import { queryInfoItemCombo, queryModelCombo } from '@api/forecast/forecast-alarm-api'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
    forecastTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      options: {
        infoItem: [],
        model: [],
      },
    }
  },
  watch: {
    condition(nVal) {
      this.filterCondition = nVal
    },
    filterCondition(nVal) {
      this.$emit('update:condition', nVal)
    },
  },
  mounted() {
    this.initDebounceQuery()
    this.getModelCombo()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        type: '',
        infoItem: '',
        model: '',
        occurTime: [],
      }
      this.options.infoItem = []
      this.changeQueryCondition()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    async changeType(type) {
      await this.queryInfoItemCombo(type)
      this.filterCondition.form.infoItem = ''
      this.changeQueryCondition()
    },
    queryInfoItemCombo(type) {
      this.options.infoItem = ''
      if (!isEmpty(type)) {
        queryInfoItemCombo(type).then((res) => {
          this.options.infoItem = res
        })
      }
    },
    getModelCombo() {
      queryModelCombo().then((res) => {
        this.options.model = res
      })
    },
  },
}
</script>
