import request from '@util/request'

// 查询自定义解析列表
export function queryCustomParseTable(obj) {
  return request({
    url: '/customPattern/queryList',
    method: 'get',
    params: obj || {},
  })
}

// 查询自定义解析详情
export function queryCustomParseDetail(id) {
  return request({
    url: `/customPattern/detail/${id}`,
    method: 'get',
  })
}

// 添加自定义解析
export function addCustomParse(obj) {
  return request({
    url: '/customPattern/addPattern',
    method: 'post',
    data: obj || {},
  })
}

// 删除自定义解析
export function deleteCustomParse(ids) {
  return request({
    url: `/customPattern/delPattern/${ids}`,
    method: 'delete',
  })
}

// 修改自定义解析状态
export function updateCustomParseStatus(ids, status) {
  return request({
    url: `/customPattern/status/${ids}/${status}`,
    method: 'put',
  })
}

// 生成解析表达式
export function generateParseExpress(obj) {
  return request({
    url: '/customPattern/generate',
    method: 'post',
    data: obj || {},
  })
}

// 查询设备类型下拉
export function queryDevTypeCombo() {
  return request({
    url: '/customPattern/combo/devTypes',
    method: 'get',
  })
}

// 查询多元组下拉
export function queryMultiGroupCombo() {
  return request({
    url: '/customPattern/combo/keys',
    method: 'get',
  })
}

// 查询事件类型下拉
export function queryEventTypeCombo() {
  return request({
    url: '/customPattern/code-alarm/alarm-types',
    method: 'get',
  })
}

// 查询所选关键字对应事件类型
export function queryKeywordEventType(obj) {
  return request({
    url: '/customPattern/queryKeywordEventType',
    method: 'get',
    params: obj || {},
  })
}
