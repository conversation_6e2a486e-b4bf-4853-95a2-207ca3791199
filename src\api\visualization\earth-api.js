import request from '@util/request'

export function querySystemName() {
  return request({
    url: '/visualization/system-name',
    method: 'get',
  })
}

export function queryEPSData() {
  return request({
    url: '/visualization/eps',
    method: 'get',
  })
}

export function queryGlobalData() {
  return request({
    url: '/visualization/global/attack',
    method: 'get',
  })
}

export function queryLineData() {
  return request({
    url: '/visualization/security/trend',
    method: 'get',
  })
}

export function queryPieData() {
  return request({
    url: '/visualization/security/category',
    method: 'get',
  })
}

export function queryBarData() {
  return request({
    url: '/visualization/security/type',
    method: 'get',
  })
}

export function queryRadarData() {
  return request({
    url: '/visualization/security/level',
    method: 'get',
  })
}

export function queryCarouselData() {
  return request({
    url: '/visualization/audit/ranking',
    method: 'get',
  })
}
