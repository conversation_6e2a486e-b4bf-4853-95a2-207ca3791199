<!--
 * @Description: 预测信息 - 入口文件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-27
 * @Editor:
 * @EditDate: 2021-10-27
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" @on-change="changeQueryTable"></table-header>
    <table-body :table-title="title" :table-loading="table.loading" :table-data="table.data" @on-detail="clickDetail"></table-body>
    <table-footer :pagination.sync="pagination" @on-change-size="changeTableSize" @on-current="changeTableCurrent"></table-footer>
    <detail-dialog :visible.sync="detailDialog.visible" :title-name="title" :model="detailDialog.model"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import DetailDialog from './TheDetailDialog'
import { queryForecastInfoTable } from '@api/forecast/forecast-info-api'
export default {
  name: 'ForecastInfo',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('forecast.forecastInfo.title'),
      query: {
        senior: false,
        form: {
          type: '',
          label: '',
        },
      },
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      detailDialog: {
        visible: false,
        model: {},
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable() {
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          type: this.query.form.type,
          lable: this.query.form.label,
        })
      } else {
        params = Object.assign(params, {
          keyword: this.query.form.keyword,
        })
      }
      return params
    },
    clickDetail(row) {
      this.detailDialog.visible = true
      this.detailDialog.model = { type: row.type, lable: row.lable }
    },
    changeTableSize(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    changeTableCurrent(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable()
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryForecastInfoTable(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
  },
}
</script>
