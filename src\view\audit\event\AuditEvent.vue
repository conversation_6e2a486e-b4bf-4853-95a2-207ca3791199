<!--
 * @Description: 审计事件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('audit.event.eventName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQuery()"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="inputQuery">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'download'" v-debounce="clickDownloadTable">
            {{ $t('button.export.default') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.eventName"
                  class="width-max"
                  clearable
                  :placeholder="$t('audit.event.placeholder.eventName')"
                  @change="inputQuery()"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.level"
                  :placeholder="$t('audit.event.placeholder.level')"
                  clearable
                  class="width-max"
                  @change="inputQuery()"
                >
                  <el-option v-for="item in eventLevel" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.auditType"
                  :placeholder="$t('audit.event.placeholder.auditType')"
                  filterable
                  clearable
                  class="width-max"
                  @change="inputQuery()"
                >
                  <el-option v-for="item in auditType" :key="item.label" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.auditUser"
                  :placeholder="$t('audit.event.placeholder.auditUserId')"
                  clearable
                  filterable
                  class="width-max"
                  @change="inputQuery()"
                >
                  <el-option v-for="(item, k) in auditUser" :key="k" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="5">
                <el-select
                  v-model="queryInput.auditStrategy"
                  :placeholder="$t('audit.event.placeholder.policyId')"
                  clearable
                  filterable
                  class="width-max"
                  @change="inputQuery()"
                >
                  <el-option v-for="item in strategyList" :key="item.label" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="10">
                <el-date-picker
                  v-model="queryInput.createTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  class="width-max"
                  format="yyyy-MM-dd HH:mm:ss"
                  type="datetimerange"
                  range-separator="~"
                  :start-placeholder="$t('audit.event.createTimeStart')"
                  :end-placeholder="$t('audit.event.createTimeEnd')"
                  @change="inputQuery()"
                ></el-date-picker>
              </el-col>
              <el-col :span="4" align="right" :offset="5">
                <el-button v-has="'query'" @click="inputQuery()">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button @click="seniorQuery">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('audit.event.event') }}
        </h2>
        <el-button v-has="'query'" @click="clickTh">
          {{ $t('button.th') }}
        </el-button>
      </header>
      <main v-loading="data.loading" class="table-body-main">
        <el-table
          v-if="tableShow"
          ref="auditTable"
          v-el-table-scroll="scrollTable"
          :data="data.table"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          fit
          @selection-change="TableSelectsChange"
        >
          <el-table-column width="50" type="index"></el-table-column>
          <el-table-column type="selection" prop="id"></el-table-column>
          <el-table-column
            v-for="(item, key) in dialog.columns.checked"
            :key="key"
            :prop="item"
            :label="$t(`audit.event.${item}`)"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <level-tag v-if="item === 'level'" :level="scope.row.level"></level-tag>
              <span v-else>
                {{ scope.row[item] }}
              </span>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="80">
            <template slot-scope="scope">
              <el-button v-has="'query'" class="el-button--blue" @click="dblclickDisplayDetail(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
        <i v-show="data.totalLoading" class="el-icon-loading"></i>
      </section>
      <section class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span>{{ data.total }}</span>
      </section>
    </footer>
    <!--自定义列-->
    <ac-dialog
      :visible.sync="dialog.visible.th"
      :title="dialog.title.th"
      :width="'70%'"
      :form="dialog.columns"
      @on-submit="clickSubmitTh"
    ></ac-dialog>
    <!--详情弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.detail"
      :title="dialog.title.detail"
      :actions="false"
      :loading="dialog.detail.dialogLoading"
      :width="'84%'"
      :form="dialog.detail"
    ></table-dialog>
  </div>
</template>
<script>
import TableDialog from './AudDialog'
import elTableScroll from '@/directive/el-table-scroll'
import AcDialog from './ColumnDialog'
import LevelTag from '@comp/LevelTag'
import { prompt } from '@util/prompt'
import {
  queryTableData,
  queryAuditType,
  queryUser,
  queryStrategy,
  queryCustom,
  exportAudit,
  updateCustom,
  queryDetails,
  queryTotal,
} from '@api/audit/event-api'
import { debounce } from '@util/effect'
export default {
  name: 'AuditEvent',
  directives: {
    elTableScroll,
  },
  components: {
    AcDialog,
    TableDialog,
    LevelTag,
  },
  data() {
    return {
      startTime: '',
      endTime: '',
      eventLevel: [
        {
          label: this.$t('level.serious'),
          value: '0',
        },
        {
          label: this.$t('level.high'),
          value: '1',
        },
        {
          label: this.$t('level.middle'),
          value: '2',
        },
        {
          label: this.$t('level.low'),
          value: '3',
        },
        {
          label: this.$t('level.general'),
          value: '4',
        },
      ],
      auditType: [],
      strategyList: [],
      auditUser: [],
      isShow: false,
      tableShow: true,
      queryInput: {
        fuzzyField: '',
        eventName: '',
        level: '',
        auditType: '',
        createTime: ['', ''],
        auditUser: '',
        auditStrategy: '',
      }, // 搜索框内容
      queryType: [],
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
        total: 0,
        nomore: false,
        totalLoading: false,
        scroll: true,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          detail: this.$t('dialog.title.detail', [this.$t('audit.event.event')]),
          th: this.$t('dialog.title.th', [this.$t('audit.event.event')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          th: false,
          detail: false,
        },
        // 自定义列数据
        columns: {
          isIndeterminate: true,
          checkAll: false,
          checked: [], // 显示自定义列
          own: [], // 已选自定义列
          all: [
            {
              value: 'eventName',
              label: this.$t('audit.event.eventName'),
            },
            {
              value: 'level',
              label: this.$t('audit.event.level'),
            },
            {
              value: 'auditTypeName',
              label: this.$t('audit.event.auditTypeName'),
            },
            {
              value: 'auditStrategyName',
              label: this.$t('audit.event.auditStrategyName'),
            },
            {
              value: 'createTime',
              label: this.$t('audit.event.createTime'),
            },
            {
              value: 'updateTime',
              label: this.$t('audit.event.updateTime'),
            },
            {
              value: 'total',
              label: this.$t('audit.event.total'),
            },
          ], // 所有自定义列
        },
        // 查看数据
        detail: {
          showLog: false,
          pageNum: 1,
          sourceEventType: '',
          audit: {},
          safe: [],
          threat: [],
          logList: [],
          pagination: {
            pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
            pageNum: 1, // 列表载入所在的分页
            total: 0, // 一共存在的页数
            currentRow: {}, // 被选中的当前行
            visible: true,
          },
          relevance: [],
          info: {
            eventName: {
              label: this.$t('audit.event.eventName'),
              key: 'eventName',
            },
            level: {
              label: this.$t('audit.event.level'),
              key: 'level',
            },
            auditTypeName: {
              label: this.$t('audit.event.auditTypeName'),
              key: 'auditTypeName',
            },
            auditStrategyName: {
              label: this.$t('audit.event.auditStrategyName'),
              key: 'auditStrategyName',
            },
            createTime: {
              label: this.$t('audit.event.createTime'),
              key: 'createTime',
            },
            updateTime: {
              label: this.$t('audit.event.updateTime'),
              key: 'updateTime',
            },
            total: {
              label: this.$t('audit.event.total'),
              key: 'total',
            },
          },
          dialogLoading: false,
        },
      },
      queryDebounce: null,
    }
  },
  computed: {
    disableScroll() {
      return this.data.scroll
    },
  },
  watch: {
    $route: {
      handler(route) {
        if (Object.keys(this.$route.query).length === 0 || this.$route.query.fuzzyField === '') {
          this.getTableData()
          this.getTotal()
          this.clearQuery()
        } else {
          const params = {
            pageSize: this.pagination.pageSize,
            fuzzyField: route.query.fuzzyField,
          }
          this.data.table = []
          this.data.nomore = false
          this.clearQuery()
          this.isShow = false
          this.queryInput.fuzzyField = route.query.fuzzyField
          this.getTableData(params)
          this.getTotal(params)
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.initLoadData()
  },
  updated() {
    if (this.data.table.length > 0) this.$refs.auditTable.doLayout()
  },
  methods: {
    initLoadData() {
      this.getColumn()
      this.getStrategy()
      this.getUser()
      this.getAuditType()
      this.initDebounce()
    },
    initDebounce() {
      this.queryDebounce = debounce(() => {
        this.data.nomore = false
        this.data.table = []
        this.data.scroll = true
        this.formatTime()
        const { fuzzyField, eventName, level, auditType, auditUser, auditStrategy } = this.queryInput
        const params = {
          pageSize: this.pagination.pageSize,
          fuzzyField,
          eventName,
          level,
          auditType,
          auditUser,
          auditStrategy,
          startDate: this.startTime,
          endDate: this.endTime,
        }
        this.getTableData(params)
        this.getTotal(params)
      }, 500)
    },
    // 查询审计事件列表
    getTableData(params = { pageSize: this.pagination.pageSize }) {
      this.data.loading = true
      this.data.nomore = false
      this.data.scroll = true
      queryTableData(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.push(...res)
          this.data.scroll = true
          if (this.data.table.length > this.pagination.pageSize) {
            this.data.nomore = true
          }
        } else {
          this.data.table.push(...res)
          this.data.scroll = false
        }
        this.data.loading = false
        this.$nextTick(() => {
          if (this.data.table.length > 0) this.$refs.auditTable.doLayout()
        })
      })
    },
    formatTime() {
      this.startTime = ''
      this.endTime = ''
      if (!this.queryInput.createTime) {
        this.startTime = ''
        this.endTime = ''
      } else if (this.queryInput.createTime[0] === 'NaN-NaN-NaN NaN:NaN:NaN') {
        this.startTime = ''
      } else if (this.queryInput.createTime[1] === 'NaN-NaN-NaN NaN:NaN:NaN') {
        this.endTime = ''
      } else {
        this.startTime = this.queryInput.createTime[0]
        this.endTime = this.queryInput.createTime[1]
      }
    },
    // 滚动查询列表
    scrollTable(params = {}) {
      if (!this.data.nomore) {
        const lastData = this.data.table[this.data.table.length - 1]
        const { id, createTime } = lastData
        const { fuzzyField, eventName, level, auditType, auditUser, auditStrategy } = this.queryInput
        this.formatTime()
        params = {
          id,
          timestamp: createTime,
          pageSize: this.pagination.pageSize,
          fuzzyField,
          eventName,
          level,
          auditType,
          auditUser,
          auditStrategy,
          startDate: this.startTime,
          endDate: this.endTime,
        }
        this.getTableData(params)
      }
    },
    // 查询审计事件总数
    getTotal(params = { pageSize: this.pagination.pageSize }) {
      this.data.totalLoading = true
      queryTotal(params).then((res) => {
        this.data.total = res
        this.data.totalLoading = false
      })
    },
    // 查询审计类型
    getAuditType(id) {
      queryAuditType().then((res) => {
        this.auditType = res
      })
    },
    // 查询审计人员
    getUser(id) {
      queryUser().then((res) => {
        this.auditUser = res
      })
    },
    // 查询审计策略
    getStrategy(id) {
      queryStrategy().then((res) => {
        this.strategyList = res
      })
    },
    // 获取已选自定义列
    getColumn() {
      this.tableShow = false
      queryCustom().then((res) => {
        if (res) {
          const allArray = this.dialog.columns.all.map((item) => {
            return item.value
          })
          if (res.length > 0) {
            this.dialog.columns.checked = res
            this.dialog.columns.own = res
          } else {
            this.dialog.columns.checked = allArray
            this.dialog.columns.own = allArray
          }

          if (this.dialog.columns.own.length === allArray.length) {
            this.dialog.columns.checkAll = true
            this.dialog.columns.isIndeterminate = false
          } else {
            this.dialog.columns.checkAll = false
            this.dialog.columns.isIndeterminate = true
          }

          if (this.dialog.columns.own.length > 0 && this.dialog.columns.own.length < allArray.length) {
            this.dialog.columns.isIndeterminate = true
          } else {
            this.dialog.columns.isIndeterminate = false
          }
        } else {
          const allArray = this.dialog.columns.all.map((item) => {
            return item.value
          })
          this.dialog.columns.checked = allArray
          this.dialog.columns.own = allArray
          prompt({
            i18nCode: 'tip.query.error',
            type: 'error',
          })
        }
        setTimeout(() => {
          this.tableShow = true
        }, 100)
      })
    },
    // 调用导出接口
    downloadTable() {
      this.data.loading = true
      this.formatTime()
      const { fuzzyField, eventName, level, auditType, auditUser, auditStrategy } = this.queryInput
      const params = {
        id: this.data.selected.map((item) => item.id).toString() || '',
        fuzzyField,
        eventName,
        level,
        auditType,
        startDate: this.startTime,
        endDate: this.endTime,
        auditUser,
        auditStrategy,
      }
      exportAudit(params).then((res) => {
        if (res) {
          this.data.loading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    // 点击导出列表
    clickDownloadTable() {
      this.downloadTable()
    },
    // 列表多选改变
    TableSelectsChange(select) {
      this.data.selected = select
    },
    // 清空搜索条件
    clearQuery() {
      this.queryInput = {
        fuzzyField: '',
        eventName: '',
        level: '',
        auditType: '',
        createTime: ['', ''],
        auditUser: '',
        auditStrategy: '',
      }
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    // 输入框搜索方法
    inputQuery() {
      this.queryDebounce()
    },
    // 高级查询输入框重置方法
    resetQuery() {
      // 清空搜索条件
      this.clearQuery()
      this.clearDialogFormModel()
      this.queryDebounce()
    },
    // 清空弹窗绑定数据
    clearDialogFormModel() {
      this.dialog.detail.audit = {}
      this.dialog.detail.safe = []
      this.dialog.detail.relevance = []
      this.dialog.detail.threat = []
    },
    // 点击自定义列 对话框弹出
    clickTh() {
      this.getColumn()
      this.dialog.visible.th = true
    },
    // 修改自定义列
    th(obj) {
      const param = obj.own
      updateCustom(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getColumn()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.null',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 提交自定义列
    clickSubmitTh(formModel) {
      this.th(formModel)
    },
    // 查看某一行详情
    async dblclickDisplayDetail(details) {
      this.clearDialogFormModel()
      this.dialog.detail.dialogLoading = true
      await queryDetails(details.id, details.createTime.toString()).then((res) => {
        if (res) {
          this.dialog.detail.audit = res
          this.dialog.detail.dialogLoading = false
        }
      })
      this.dialog.visible.detail = true
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
::v-deep .el-loading-mask {
  background-color: rgba(0, 0, 0, 0.3);
}
</style>
