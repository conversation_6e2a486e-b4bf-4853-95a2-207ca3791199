export default {
  original: {
    name: '原始日志',
    total: '总计',
    label: {
      expression: '表达式名称',
      type2Name: '原始日志名称',
      logName: '事件类型',
      level: '等级',
      srcDevice: '日志源设备',
      dstDevice: '发生源IP',
      srcIP: '源IP',
      dstIP: '目的IP',
      customName: '自定义名称',
    },
    placeholder: {
      srcStartIP: '源起始IP',
      srcEndIP: '源终止IP',
      dstStartIP: '目的起始IP',
      dstEndIP: '目的终止IP',
      fromStartIP: '发生源起始IP',
      fromEndIP: '发生源终止IP',
      logFormat: '日志格式',
      forwardServer: '转发服务器',
      username: '用户',
      targetObject: '目标对象',
      logRecieveTime: '接收时间',
      logTime: '日志时间',
      srcStartMac: "起始源MAC地址",
      srcEndMac: "终止源MAC地址",
      dstStartMac: "起始目的MAC地址",
      dstEndMac: "终止目的MAC地址",
    },
    group: {
      basic: '基本信息',
      source: '源',
      destination: '目的',
      from: '发生源',
      geo: '地理信息',
      other: '其他',
      log: '日志原文',
    },
    basic: {
      type2Name: '原始日志名称',
      eventName: '事件类型',
      eventCategoryName: '事件类别',
      level: '事件等级',
      deviceCategoryName: '设备类别',
      deviceTypeName: '设备类型',
      time: '接收时间',
      logTime: '日志时间',
      code: '特征值',
      username: '用户',
      targetObject: '目标对象',
      eventDesc: '事件描述',
      action: '动作',
      resultName: '事件结果',
    },
    source: {
      sourceIp: '源IP',
      sourceAddress: '源地址',
      sourcePort: '源端口',
      sourceAsset: '源资产',
      sourceMac: '源MAC地址', // 新增
    },
    destination: {
      targetIp: '目的IP',
      targetAddress: '目的地址',
      targetPort: '目的端口',
      targetAsset: '目的资产',
      targetMac: '目的MAC地址', // 新增
    },
    from: {
      fromIp: '发生源IP',
    },
    geo: {
      sourceCountryName: '源国家',
      sourceCountryLongitude: '源国家经度',
      sourceCountryLatitude: '源国家纬度',
      sourceAreaName: '源区域',
      sourceAreaLongitude: '源区域经度',
      sourceAreaLatitude: '源区域纬度',
      targetCountryName: '目的国家',
      targetCountryLongitude: '目的国家经度',
      targetCountryLatitude: '目的国家纬度',
      targetAreaName: '目的区域',
      targetAreaLongitude: '目的区域经度',
      targetAreaLatitude: '目的区域纬度',
    },
    other: {
      protocol: '协议',
      receiveProtocol: '接收协议',
      year: '年',
      month: '月',
      day: '日',
      hour: '时',
      minute: '分',
      second: '秒',
      otherIp: '其他IP',
      otherPort: '其他端口',
    },
    log: {
      raw: '日志原文',
    },
    forward: {
      status: '转发状态',
      logFormat: '日志格式',
      forwardServer: '转发服务器',
    },
    fuzzyQuery: '原始日志名称/日志原文',
    parseRateDesc: '每千条日志解析规则平均成功耗时{0}ms',
  },
}
