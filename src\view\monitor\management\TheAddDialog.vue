<!--
 * @Description: 监控器 - 添加弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-27
 * @Editor:
 * @EditDate: 2021-07-27
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.add', [titleName])"
    width="60%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <template v-if="dialogVisible">
      <basic-comp ref="basicRef" :model="model" @on-change-type="changeMonitorType"></basic-comp>
      <overall-comp ref="allRef" :model="model" :monitor-type="model.monitorType"></overall-comp>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import BasicComp from './comp/config/BasicComp'
import OverallComp from './comp/config/OverallComp'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
    BasicComp,
    OverallComp,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      model: {},
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.initData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initData() {
      this.model = {
        monitorName: '',
        monitorEnabled: '1',
        monitorType: 'NE_CISCO',
        pollDate: '5',
        assetId: '',
        assetIds: '',
        agentId: '',
        cpuUseRate: '80',
        cpuTimes: '3',
        memoryUseRate: '80',
        memoryTimes: '3',
        diskUseRate: '80',
        snmpVersion: '1',
        snmpPort: '161',
        readCommunity: 'public',
        writeCommunity: '',
        authWay: '-1',
        authPwd: '',
        encryptionWay: '-1',
        encryptionPwd: '',
        context: '',
        contextName: '',
        snmpUserName: '',
        secLev: '-1',
      }
    },
    clickCancel() {
      this.$nextTick(() => {
        if (this.$refs.basicRef) this.$refs.basicRef.resetForm()
        if (this.$refs.allRef) this.$refs.allRef.resetForm()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    validateForm() {
      const basicValid = this.$refs.basicRef.validateForm()
      const allValid = this.$refs.allRef.validateForm()
      return basicValid && allValid
    },
    clickSubmit() {
      const valid = this.validateForm()
      if (valid) {
        this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          const model = this.handleModelItems()
          this.$emit('on-submit', model)
          this.clickCancel()
        })
      } else {
        prompt(
          {
            i18nCode: 'validate.form.warning',
            type: 'warning',
            print: true,
          },
          () => {
            return false
          }
        )
      }
      this.$refs.dialogTemplate.end()
    },
    changeMonitorType(value) {
      this.model.monitorType = value
    },
    handleModelItems() {
      const assetId = this.model.assetId
      const assetArr = assetId.filter((item) => item.length === 3)
      this.model.assetIds = assetArr.map((item) => item[2]).toString()
      return this.model
    },
  },
}
</script>
