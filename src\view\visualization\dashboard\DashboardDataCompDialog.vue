<!--
 * @Description: 仪表盘 - 数据组件添加弹窗
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialog" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="form" :model="form.model" :rules="form.rules" label-width="20%">
      <el-form-item :label="$t('visualization.dashboard.dialog.comp.label')" prop="type">
        <el-col :span="12">
          <el-select v-model="form.model.type" class="width-mini" @change="changeDataComp">
            <el-option v-for="item in compType" :key="item.value" :value="item.value" :label="item.label"></el-option>
          </el-select>
        </el-col>
      </el-form-item>
      <el-form-item>
        <el-row :gutter="20">
          <template v-for="(item, index) in data.active.type">
            <el-col v-if="item.visible" :key="index" :span="8" style="height: 300px">
              <div class="chunk-chart-container">
                <section class="chart-canvas">
                  <template v-if="item.type === 'line'">
                    <line-chart :line-data="chart.line"></line-chart>
                  </template>
                  <template v-if="item.type === 'pie'">
                    <pie-chart :pie-data="chart.pie"></pie-chart>
                  </template>
                  <template v-if="item.type === 'bar'">
                    <bar-chart :bar-data="chart.bar"></bar-chart>
                  </template>
                  <template v-if="item.type === 'bar-stack'">
                    <bar-stack-chart :bar-data="chart.barStack"></bar-stack-chart>
                  </template>
                  <section :style="{ 'chart-mask-selected': data.active.chart.id === item.id }" class="chart-mask" @click="clickSelectedChart(item)">
                    <i v-if="data.active.chart.id === item.id" class="el-icon-check"></i>
                  </section>
                </section>
                <h2 class="chart-title">
                  {{ item.title }}
                </h2>
              </div>
            </el-col>
          </template>
        </el-row>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import LineChart from '@comp/ChartFactory/dashboard/LineChart'
import PieChart from '@comp/ChartFactory/dashboard/PieChart'
import BarChart from '@comp/ChartFactory/dashboard/BarChart'
import BarStackChart from '@comp/ChartFactory/dashboard/BarStackChart'
import { prompt } from '@util/prompt'
import { queryChartData } from '@api/visualization/dashboard-api'

export default {
  components: {
    CustomDialog,
    LineChart,
    PieChart,
    BarChart,
    BarStackChart,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    typeData: {
      required: true,
      type: Array,
    },
    chunkData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      data: {
        type: this.typeData,
        active: {
          type: [], // 当前选中大类的小类
          chart: {}, // 当前选中的小类
          chartData: {}, // 当前选中小类的图表的查询数据
        },
      },
      form: {
        model: {
          type: '',
        },
        rules: {
          type: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
      chart: {
        bar: {
          title: '',
          axis: {
            direction: 'horizontal',
            data: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
          },
          legend: ['X'],
          series: [[1, 4, 7, 10, 13, 16, 19, 22]],
        },
        line: {
          title: '',
          axis: {
            direction: 'horizontal',
            data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
          },
          legend: ['A', 'B', 'C'],
          series: [
            [1, 7, 4, 10, 16, 13, 19, 22],
            [2, 11, 8, 5, 14, 13, 20, 17],
            [6, 3, 15, 12, 9, 18, 24, 21],
          ],
        },
        pie: {
          title: '',
          legend: ['A', 'B', 'C', 'D', 'E'],
          series: [20, 15, 25, 10, 30],
        },
        barStack: {
          title: '',
          axis: {
            direction: 'horizontal',
            data: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
          },
          legend: ['X'],
          series: [
            [1, 4, 7, 10, 13, 16, 19, 22],
            [1, 4, 7, 10, 13, 16, 19, 22],
          ],
        },
      },
    }
  },
  computed: {
    compType() {
      const compTypeData = []
      this.data.type.forEach((item) => {
        const visible = []
        item.chart.forEach((chart) => {
          visible.push(chart.visible)
        })
        if (visible.filter((bool) => bool === true).length > 0) {
          compTypeData.push(item)
        }
      })
      return compTypeData
    },
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
    typeData: {
      handler(type) {
        this.data.type = type
      },
      deep: true,
    },
    'data.type': {
      handler(type) {
        this.$emit('update:typeData', type)
      },
      deep: true,
    },
    'form.model.type': {
      handler() {
        this.data.active.chart = {}
      },
      immediate: true,
    },
  },
  methods: {
    clickCancelDialog() {
      this.form.model.type = ''
      this.data.active.type = []
      this.data.active.chartData = {}
      this.$refs.dialog.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.form.validate((valid) => {
        if (valid && Object.keys(this.data.active.chart).length > 0) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.getChartData(this.data.active.chart.id, () => {
              this.handleSwitchTypeChart(this.data.active.chart.id)
              this.$emit(
                'on-submit',
                Object.assign(this.data.active.chartData, {
                  id: this.data.active.chart.id,
                })
              )
              this.clickCancelDialog()
            })
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.lessOne',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialog.end()
    },
    changeDataComp(value) {
      this.data.type.forEach((item) => {
        if (item.value === value) {
          this.data.active.type = item.chart
        }
      })
    },
    clickSelectedChart(item) {
      this.data.active.chart = item
    },
    handleSwitchTypeChart(id) {
      this.data.type.forEach((row) => {
        row.chart.forEach((col) => {
          if (col.id === id) {
            col.visible = false
          }
        })
      })
    },
    getChartData(chartId, fn) {
      queryChartData(chartId).then((res) => {
        this.data.active.chartData = res
        fn()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-row {
  .chunk-chart-container {
    margin-bottom: 20px;

    .chart-canvas {
      position: relative;
      padding: 10px;
      height: 200px;
      border: 1px solid #ccc;
      border-radius: 10px;

      &:hover {
        border-color: $primary-color;
      }

      .chart-mask {
        position: absolute;
        left: 0;
        top: 0;
        z-index: 10;
        width: 100%;
        height: 100%;
        cursor: pointer;

        .el-icon-check {
          position: absolute;
          right: 10px;
          bottom: 10px;
          padding: 5px;
          font-size: 50px;
          color: $primary-color;
          font-weight: 900;
          border-radius: 50%;
        }
      }
    }

    .chart-title {
      text-align: center;
    }
  }
}
</style>
