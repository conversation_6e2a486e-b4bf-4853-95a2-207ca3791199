<!--
 * @Description: 采集器管理 - 基本信息
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-04-18
 * @Editor:
 * @EditDate: 2022-04-18
-->
<template>
  <el-form ref="basicForm" :model="form.model" :rules="rules" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="collectorName" :label="form.info.collectorName.label">
          <el-input v-model.trim="form.model.collectorName" maxlength="50"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="codeWay" :label="form.info.codeWay.label">
          <el-select v-model="form.model.codeWay" filterable clearable :placeholder="$t('collector.management.placeholder.codeWay')">
            <el-option v-for="item in codeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item prop="protId" :label="form.info.protId.label">
          <el-select v-model="form.model.protId" :disabled="sourcePage === 'update'" @change="changeAccessMode">
            <el-option v-for="item in innerTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="agentId" :label="form.info.agentIp.label">
          <el-select v-model="form.model.agentId" filterable clearable :placeholder="$t('collector.management.placeholder.agentIp')">
            <el-option
              v-for="item in agentOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
              :disabled="item.type === '0'"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  props: {
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    innerTypeOption: {
      required: true,
      type: Array,
    },
    agentOption: {
      required: true,
      type: Array,
    },
    sourcePage: {
      type: String,
      default: 'add',
    },
  },
  data() {
    return {
      codeOption: [
        {
          label: '自动',
          value: 'auto',
        },
        {
          label: 'UTF-8',
          value: 'utf8',
        },
        {
          label: 'GBK',
          value: 'gbk',
        },
      ],
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  methods: {
    validateForm() {
      let validate = false
      this.$refs.basicForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    changeAccessMode(val) {
      this.$emit('on-change-accessmode', val)
    },
  },
}
</script>
