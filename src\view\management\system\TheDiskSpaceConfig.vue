<template>
  <div class="tab-context-wrapper">
    <el-form ref="DiskSpaceForm" :model="form.model" :rules="form.rule" label-width="180px">
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.cleanHistory')" prop="cleanHistory">
          <el-input-number v-model="form.model.cleanHistory" controls-position="right" :min="1" :max="999999" class="width-small"></el-input-number>
          <el-select v-model="form.model.cycle" style="margin-left: 10px; width: calc(20% - 10px)">
            <el-option :label="$t('time.unit.month')" value="month"></el-option>
            <el-option :label="$t('time.unit.day')" value="day"></el-option>
          </el-select>
          <section class="form-validate-tip">
            {{ form.model.cycle === 'month' ? $t('management.system.tip.cleanHistoryMonth') : $t('management.system.tip.cleanHistoryDay') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.cleanSpace')" prop="cleanSpace">
          <el-input-number
            v-model="form.model.cleanSpace"
            controls-position="right"
            :max="100"
            :min="1"
            class="width-small"
            style="margin-right: 10px;"
          ></el-input-number>
          <span>%</span>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.cleanSpace') }}
          </section>
        </el-form-item>
      </el-col>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-has="'upload'" @click="clickSaveDiskSpaceConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button v-has="'query'" @click="clickResetDiskSpaceConfig">
        {{ $t('button.reset.default') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { prompt } from '@util/prompt'

export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      form: {
        model: {
          cleanHistory: '',
          cleanSpace: '',
          cycle: 'month',
        },
        rule: {
          cleanHistory: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          cleanSpace: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model = this.formData
      }
    },
    clickSaveDiskSpaceConfig() {
      this.$refs.DiskSpaceForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', {
              diskCleanHistory: this.form.model.cleanHistory,
              diskCleanPercent: this.form.model.cleanSpace,
              diskCleanCycle: this.form.model.cycle,
            })
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetDiskSpaceConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-reset')
      })
    },
  },
}
</script>
