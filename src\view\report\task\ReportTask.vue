<!--
 * @Description: 报表任务
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model="data.fuzzyField"
              prefix-icon="soc-icon-search"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('report.task.label.name')])"
              @change="clickQueryReportTaskTable"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="clickQueryReportTaskTable">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'upload'" @click="clickBatchEnableReportTask">
            {{ $t('button.on') }}
          </el-button>
          <el-button v-has="'upload'" @click="clickBatchDisableReportTask">
            {{ $t('button.off') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAddReportTask">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteReportTask">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('report.task.name') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="reportTaskTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="reportTaskTableRowChange"
          @selection-change="reportTaskTableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column :label="$t('report.task.label.name')" prop="taskName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('report.task.label.type')" prop="taskType" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ scope.row.taskTypeText + scope.row.taskTimeText }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('time.option.executeTime')" prop="taskStartTime" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('report.task.label.status')" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-switch v-model="scope.row.taskStatus" @change="changeTaskStatus(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="260">
            <template slot-scope="scope">
              <el-button v-has="'query'" class="el-button--blue" @click="clickDetailReportTask(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateReportTask(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteReportTask(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="reportTaskTableSizeChange"
        @current-change="reportTaskTableCurrentChange"
      ></el-pagination>
    </footer>

    <task-dialog :visible.sync="dialog.visible.add" :title="dialog.title.add" @on-submit="clickSubmitAddTask"></task-dialog>

    <task-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :recipient="dialog.form.update.taskRecipient"
      @on-submit="clickSubmitUpdateReportTask"
    ></task-dialog>

    <task-dialog :visible.sync="dialog.visible.detail" :title="dialog.title.detail" :form-data="dialog.form.detail"></task-dialog>
  </div>
</template>

<script>
import TaskDialog from './TheTaskDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import {
  addReportTaskData,
  deleteReportTaskData,
  updateReportTaskData,
  updateReportTaskStatusData,
  queryReportTaskTableData,
  queryReportTaskDetailData,
} from '@api/report/task-api'

export default {
  name: 'ReportTask',
  components: {
    TaskDialog,
  },
  data() {
    return {
      data: {
        loading: false,
        debounce: null,
        table: [],
        selected: [],
        fuzzyField: '',
      },
      pagination: {
        visible: true,
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
      dialog: {
        visible: {
          add: false,
          update: false,
          detail: false,
        },
        title: {
          add: this.$t('dialog.title.add', [this.$t('report.task.name')]),
          update: this.$t('dialog.title.update', [this.$t('report.task.name')]),
          detail: this.$t('dialog.title.detail', [this.$t('report.task.name')]),
        },
        form: {
          update: {
            taskId: '',
            taskRecipient: '',
          },
          detail: {
            taskName: '',
            taskInstance: '',
            taskType: '',
            taskStartDate: '',
            taskStartTime: '',
            taskTimeValue: '',
            updateDate: '',
            taskSendType: '',
            taskRecipient: '',
            taskStatus: '',
            taskDescription: '',
          },
        },
      },
    }
  },
  mounted() {
    this.initDebounceQuery()
    this.getReportTaskTableData()
  },
  methods: {
    clickBatchEnableReportTask() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.taskId)
        this.updateReportTaskStatus(ids, 1)
      } else {
        prompt({
          i18nCode: 'tip.enable.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickBatchDisableReportTask() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.taskId)
        this.updateReportTaskStatus(ids, 0)
      } else {
        prompt({
          i18nCode: 'tip.disable.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickAddReportTask() {
      this.dialog.visible.add = true
    },
    clickBatchDeleteReportTask() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.taskId).toString()
        this.deleteReportTask(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    async clickDetailReportTask(row) {
      await this.getReportTaskDetail(row.taskId)
      this.dialog.visible.detail = true
    },
    async clickUpdateReportTask(row) {
      await this.getReportTaskDetail(row.taskId)
      this.dialog.form.update.taskId = row.taskId
      this.dialog.form.update.taskRecipient = this.dialog.form.detail.taskRecipient
      this.dialog.visible.update = true
    },
    clickDeleteReportTask(row) {
      this.deleteReportTask(row.taskId)
    },
    clickSubmitAddTask(form) {
      this.addReportTask({
        taskName: form.taskName,
        taskInstance: form.taskInstance,
        taskType: form.taskType,
        taskStartDate: form.taskStartDate,
        taskStartTime: form.taskStartTime,
        taskTimeValue: form.taskTimeValue,
        taskSendType: form.taskSendType,
        taskRecipient: form.taskRecipient,
        taskDescription: form.taskDescription,
      })
    },
    clickSubmitUpdateReportTask(form) {
      this.updateReportTask({
        taskId: this.dialog.form.update.taskId,
        taskRecipient: form.taskRecipient,
      })
    },
    clickQueryReportTaskTable() {
      this.data.debounce()
    },
    changeTaskStatus(row) {
      this.updateReportTaskStatus([row.taskId], row.taskStatus ? 1 : 0)
    },
    reportTaskTableRowChange(row) {
      this.pagination.currentRow = row
    },
    reportTaskTableSelectsChange(select) {
      this.data.selected = select
    },
    reportTaskTableSizeChange(size) {
      this.pagination.pageSize = size
      this.getReportTaskTableData()
    },
    reportTaskTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getReportTaskTableData()
    },
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        this.pagination.pageNum = 1
        this.getReportTaskTableData()
      }, 500)
    },
    addReportTask(obj) {
      addReportTaskData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.getReportTaskTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    deleteReportTask(ids) {
      this.$confirm(this.$t('report.task.tip.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteReportTaskData(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.getReportTaskTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    updateReportTask(obj) {
      updateReportTaskData(obj).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.getReportTaskTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    updateReportTaskStatus(taskId, enable) {
      updateReportTaskStatusData(taskId, enable).then((res) => {
        if (res) {
          if (enable === 1) {
            prompt(
              {
                i18nCode: 'tip.enable.success',
                type: 'success',
              },
              () => {
                this.getReportTaskTableData()
              }
            )
          }

          if (enable === 0) {
            prompt(
              {
                i18nCode: 'tip.disable.success',
                type: 'success',
              },
              () => {
                this.getReportTaskTableData()
              }
            )
          }
        } else {
          if (enable === 1) {
            prompt({
              i18nCode: 'tip.enable.error',
              type: 'success',
            })
          }

          if (enable === 0) {
            prompt({
              i18nCode: 'tip.disable.error',
              type: 'success',
            })
          }
        }
      })
    },
    getReportTaskTableData(
      params = {
        fuzzyField: this.data.fuzzyField,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryReportTaskTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.visible = true
        this.data.loading = false
      })
    },
    async getReportTaskDetail(taskId) {
      await queryReportTaskDetailData(taskId).then((res) => {
        this.dialog.form.detail.taskName = res.taskName
        this.dialog.form.detail.taskInstance = res.taskInstanceName
        this.dialog.form.detail.taskType = res.taskTypeText + res.taskTimeText
        this.dialog.form.detail.taskStartDate = res.taskStartDate
        this.dialog.form.detail.taskStartTime = res.taskStartTime
        this.dialog.form.detail.taskStartValue = res.taskStartValue
        this.dialog.form.detail.updateDate = res.updateDate
        this.dialog.form.detail.taskSendType = res.taskSendType
        this.dialog.form.detail.taskStatus = res.taskStatus ? this.$t('button.on') : this.$t('button.off')
        this.dialog.form.detail.taskRecipient = res.taskRecipient
        this.dialog.form.detail.taskDescription = res.taskDescription
      })
    },
  },
}
</script>
