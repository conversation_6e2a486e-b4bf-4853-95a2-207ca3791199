const { TableMock } = require('../util')
const tableData = new TableMock({
  total: 200,
  template: {
    cnvdId: '@ID',
    cnvdTitle: '@NAME',
    cnvdLevel: '@CNAME',
    relateThreat: '@NAME',
    cnvdProvider: '@CNAME',
    releaseDate: '@CNAME',
  },
})
const Data = {
  cnvdId: '',
  cnvdTitle:
    'The constructSQL function in inc/search.class.php in GLPI 9.2.x through 9.3.0 allows SQL Injection, as demonstrated by triggering a crafted LIMIT cla',
  cnvdLevel: 'CVE-2018-13049',
  rollOutFlag: 'Assigned (20180702)',
  releaseDate: 'CONFIRM:https://github.com/glpi-project/glpi/issues/4270',
  relateThreat: 'Candidate',
  affectedProducts: 'None (candidate not yet proposed)',
  cnvdDesc: '',
  cnvdReference:
    'The constructSQL function in inc/search.class.php in GLPI 9.2.x through 9.3.0 allows SQL Injection, as demonstrated by triggering a crafted LIMIT cla',
  authInfo: 'CVE-2018-13049',
  cnvdResolve: 'Assigned (20180702)',
  cnvdProvider: 'CONFIRM:https://github.com/glpi-project/glpi/issues/4270',
}
module.exports = [
  {
    url: '/knowledge/vulnerability/cnvd',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: `/knowledge/vulnerability/cnvd/[A-Za-z0-9]`,
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: Data,
      }
    },
  },
]
