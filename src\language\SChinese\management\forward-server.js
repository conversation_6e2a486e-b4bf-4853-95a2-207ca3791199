export default {
  forwardServer: {
    id: '转发系统ID',
    poid: '企业节点号',
    coid: 'OID节点号',
    community: 'Community',
    type: '转发方式',
    reIP: '其他服务器IP',
    port: '端口号',
    remark: '描述',
    forwardServer: '转发外系统',
    placeholder: {
      queryInput: '请输入转发方式',
      type: '转发方式',
      community: 'Community',
      poid: '暂无企业节点号',
      coid: '暂无OID节点号',
      reIP: '其他服务器IP',
      remark: '描述',
    },
    delete: {
      running: {
        audit: '有审计策略正在使用该转发策略，请修改后删除',
        assoc: '有关联策略正在使用该转发策略，请修改后删除',
        aggre: '有聚合策略正在使用该转发策略，请修改后删除',
      },
    },
    update: {
      repeat: '转发策略信息重复',
    },
  },
}
