export default {
  code: {
    level: {
      l0: '暂无等级信息',
      risk: {
        l1: '正常', // 1
        l2: '低危', // 2
        l3: '中危', // 3
        l4: '高危', // 4
        l5: '失陷', // 5
      },
      event: {
        l1: '一般', // 4
        l2: '低级', // 3
        l3: '中级', // 2
        l4: '高级', // 1
        l5: '严重', // 0
      },
      vul: {
        l1: '低级', // 1
        l2: '中级', // 2
        l3: '高级', // 3
      },
      alarm: {
        l1: '很低', // 1
        l2: '低', // 2
        l3: '中', // 3
        l4: '高', // 4
        l5: '很高', // 5
      },
      value: {
        l1: '低', // 1
        l2: '一般', // 2
        l3: '中', // 3
        l4: '高', // 4
        l5: '很高', // 5
      },
      dga: {
        l0: '低级', // 0
        l1: '高级', // 1
      },
    },
    status: {
      on: '在线',
      off: '离线',
    },
    executeStatus: {
      on: '启用',
      off: '停用',
    },
    state: {
      device: {
        offline: '离线状态', // 1
        online: '在线状态', // 2
        abnormal: '异常状态', // 3
      },
      task: {
        enable: '启用', // 0
        disable: '禁用', // 1
      },
      scan: {
        fail: '提交失败', // 2
        running: '正在运行', // 3
        completed: '已完成', // 4
        skipped: '已跳过', // 5
        stopped: '已停止', // 6
        wait: '等待', // 8
        timeout: '超时', // 9
      },
      vul: {
        resolved: '已解决', // 0
        unsolved: '未解决', // 1
      },
      asset: {
        notConfig: '未配置SNMP', // 0
        noConfig: '未配置', // 0
        offline: '离线', // 1
        normal: '正常', // 2
        abnormal: '异常', // 3
      },
      connect: {
        success: '成功',
        failed: '失败',
      },
      current: {
        normal: '正常',
        abnormal: '异常',
      },
    },
    stage: {
      attack: {
        invasionAsset: '入侵资产', // 1
        singleBlowup: '单点爆破', // 2
        hInfiltrate: '横向渗透', // 3
        stealData: '窃取数据', // 4
      },
    },
    asset: {
      state: {
        all: '全部',
        register: '注册资产',
        noRegister: '非注册资产',
        important: '重点资产',
        noImportant: '非重点资产',
        focus: '关注资产',
        noFocus: '非关注资产',
        fixed: '固定资产',
        noFixed: '非固定资产',
      },
      audit: {
        manual: '手动评估', // 2
        auto: '自动评估', // 1
      },
      source: {
        manually: '手动录入', // 1
        passiveFind: '被动发现', // 2
        deviceSync: '设备同步', // 3
      },
      change: {
        flowDiscern: '流量识别', // 1
        scanFind: '扫描发现', // 2
        deviceSync: '设备同步', // 3
        bulkImport: '批量导入', // 4
        manualEdit: '手动编辑', // 5
      },
    },
    trend: {
      up: '上升', // 1
      down: '下降', // 2
      same: '不变', // 3
    },
    cycle: {
      immediate: '立即执行', // 0
      day: '每天一次', // 2
      week: '每周一次', // 3
      month: '每月一次', // 4
    },
    alarm: {
      type: {
        cpu: 'CPU超限', // 1
        memory: '内存超限', // 2
        disk: '磁盘超限', // 3
      },
    },
    ip: {
      type: {
        all: '全部',
        outer: '外网', // 0
        inner: '内网', // 1
      },
    },
    area: {
      domestic: '国内', // 2
      foreign: '国外', // 3
    },
    place: {
      china: '中国', // 2
      world: '世界', // 3
    },
    flow: {
      type: {
        inner: '内网流量', // 1
        outer: '外网流量', // 2
      },
    },
    handleStatus: {
      unhandle: '未处理',
      ignore: '忽略',
    },
    anomalyType: {
      illegalAction: '异常行为',
      illegalIntruder: '非法入侵',
    },
    runStatus: {
      normal: '正常',
      abnormal: '异常',
    },
    forecastType: {
      total: '总体',
      eventType: '事件类型',
      srcIp: '源IP',
      dstIp: '目的IP',
      fromIp: '采集器IP',
    },
    resultStatus: {
      success: '成功',
      fail: '失败',
    },
    backupWay: {
      increment: '增量',
      all: '全量',
    },
  },
}
