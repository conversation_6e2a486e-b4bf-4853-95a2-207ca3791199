<!--
 * @Description: 系统入口
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div id="app" :class="[$store.getters.mode === 'offline' ? 'app-login-bg' : 'app-main-bg']">
    <router-view v-if="isReloadAlive"></router-view>
  </div>
</template>

<script>
import cookie from 'js-cookie'

export default {
  name: 'App',
  provide() {
    return {
      reload: this.reload,
    }
  },
  data() {
    return {
      isReloadAlive: true,
      active: 'login',
    }
  },
  computed: {
    theme() {
      return this.$store.getters.theme
    },
  },
  watch: {
    theme() {
      this.setTheme()
    },
  },
  mounted() {
    document.querySelector('title').innerText = ''
    this.setTheme()
    this.refreshSaveStore()
  },
  methods: {
    setTheme() {
      document.documentElement.setAttribute('data-theme', this.$store.getters.theme)
      require(`@/theme/${this.$store.getters.theme}/element-ui/index.scss`)
    },
    reload() {
      this.isReloadAlive = false
      this.$nextTick(() => {
        this.isReloadAlive = true
      })
    },
    refreshSaveStore() {
      document.querySelector('title').innerText = this.$store.getters.systemName
      window.addEventListener('beforeunload', () => {
        cookie.set('store', JSON.stringify(this.$store.state))
      })
    },
  },
}
</script>

<style lang="scss">
#app {
  position: relative;
  height: 100%;
  overflow: hidden;
  @include theme('color', font-color);
}

.app-login-bg {
  &:after {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -100;
    display: block;
    width: 100%;
    height: 100%;
    @include theme('background-image', login-bg);
    background: {
      repeat: no-repeat;
      attachment: fixed;
      position: center center;
      size: cover;
    }
    content: ' ';
  }
}

.app-main-bg {
  &:after {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    z-index: -100;
    display: block;
    width: 100%;
    height: 100%;
    @include theme('background-color', main-bg-color);
    background: {
      attachment: fixed;
      position: center center;
      size: cover;
    }
    content: ' ';
  }
}

*::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

*::-webkit-scrollbar-thumb {
  border-radius: 5px;
  @include theme('background-color', scrollbar-thumb-color);
  @include theme('box-shadow', scrollbar-thumb-shadow);
}

*::-webkit-scrollbar-track {
  border-radius: 5px;
  @include theme('background-color', scrollbar-track-color);
  @include theme('box-shadow', scrollbar-track-color);
}

*::-webkit-scrollbar-corner {
  display: none;
}
</style>
