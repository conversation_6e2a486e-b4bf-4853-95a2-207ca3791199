<template>
  <div class="router-wrap-table">
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.system.label.desensitizationSetting') }}
        </h2>
        <section style="padding-right: 12px;">
          <el-button @click="clickAdd">
            {{ $t('button.add') }}
          </el-button>
          <el-button @click="clickBatchDelete">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </header>
      <main class="table-body-main">
        <el-table
          ref="Table"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="TableRowChange"
          @selection-change="TableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column prop="desensitizeStr" :label="$t('management.system.label.desensitizeStr')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="desensitizeNewStr" :label="$t('management.system.label.desensitizeNewStr')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="status" :label="$t('management.system.label.desensitizeStatus')" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.status"
                active-color="#13ce66"
                :active-value="1"
                :inactive-value="0"
                @change="toggleDesensitization(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column prop="desc" :label="$t('management.system.label.desensitizeDesc')" show-overflow-tooltip></el-table-column>
          <el-table-column width="160">
            <template slot-scope="scope">
              <el-button class="el-button--blue" @click="clickUpdate(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button class="el-button--red" @click="clickDelete(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <custom-dialog
      ref="dialogTemplate"
      :visible="dialog.visible"
      :title="dialog.title"
      width="700px"
      @on-close="clickCancelDialog"
      @on-submit="clickSubmit"
    >
      <el-form ref="formTemplate" :model="dialog.form.model" :rules="dialog.form.rules" label-width="25%">
        <template>
          <el-form-item :label="$t('management.system.label.desensitizeStr')" prop="desensitizeStr">
            <el-input
              v-model.trim="dialog.form.model.desensitizeStr"
              :placeholder="$t('management.system.tip.desensitizeStr')"
              class="width-mini"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('management.system.label.desensitizeNewStr')" prop="desensitizeNewStr">
            <el-input
              v-model.trim="dialog.form.model.desensitizeNewStr"
              :placeholder="$t('management.system.tip.desensitizeNewStr')"
              class="width-mini"
            ></el-input>
          </el-form-item>
          <el-form-item :label="$t('management.system.label.desensitizeStatus')" prop="status">
            <el-switch v-model="dialog.form.model.status" active-color="#13ce66"></el-switch>
          </el-form-item>
          <el-form-item :label="$t('management.system.label.desensitizeDesc')" prop="desc">
            <el-input v-model.trim="dialog.form.model.desc" :placeholder="$t('management.system.tip.desensitizeDesc')" class="width-mini"></el-input>
          </el-form-item>
        </template>
      </el-form>
    </custom-dialog>
  </div>
</template>
<script>
import { prompt } from '@util/prompt'
import CustomDialog from '@comp/CustomDialog'
import {
  getDesensitizationList,
  addDesensitization,
  updateDesensitization,
  deleteDesensitization,
  toggleDesensitization,
} from '@api/management/system-api'
import { deepClone } from '@util/format'

export default {
  name: 'TheDesensitizationConfig',
  components: { CustomDialog },
  data() {
    const validatorEmpty = (rule, value, callback) => {
      if (!value) {
        callback('字段不能为空')
      } else {
        callback()
      }
    }
    return {
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
      },
      dialog: {
        title: '',
        visible: false,
        form: {
          model: {
            desensitizeStr: '',
            desensitizeNewStr: '',
            status: false,
            desc: '',
          },
          rules: {
            desensitizeStr: [
              {
                required: true,
                validator: validatorEmpty,
                trigger: 'blur',
              },
            ],
            desensitizeNewStr: [
              {
                required: true,
                validator: validatorEmpty,
                trigger: 'blur',
              },
            ],
          },
        },
      },
    }
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    resetQuery() {
      this.pagination.pageNum = 1
      this.pagination.pageSize = this.$store.getters.pageSize
      this.getTableData()
    },
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.data.loading = true
      getDesensitizationList(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
      })
    },
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteDesensitization(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.getTableData()
              }
            )
          } else if (res === 5) {
            prompt({
              i18nCode: 'tip.delete.running',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    clearDialogFormModel() {
      this.dialog.form.model = {
        desensitizeStr: '',
        desensitizeNewStr: '',
        status: false,
        desc: '',
      }
      this.dialog.title = ''
    },
    clickAdd() {
      this.clearDialogFormModel()
      this.dialog.visible = true
      this.dialog.title = this.$t('dialog.title.add', [this.$t('management.system.label.desensitizationSetting')])
    },
    clickDelete(row) {
      this.delete(row.id)
    },
    clickBatchDelete() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.id).toString()
        this.delete(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.getTableData()
    },
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getTableData()
    },
    TableSelectsChange(select) {
      this.data.selected = select
    },
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    clickUpdate(row) {
      this.clearDialogFormModel()
      this.dialog.form.model = deepClone(row)
      this.dialog.form.model.status = this.dialog.form.model.status === 1 ? true : false
      this.dialog.visible = true
      this.dialog.title = '修改日志脱敏配置'
    },
    clickSubmit() {
      if (this.dialog.form.model.desensitizeStr.includes('*')) {
        this.$message.error('敏感字符不能包含*')
        return
      }
      const payload = { ...this.dialog.form.model, status: this.dialog.form.model.status ? 1 : 0 }
      if (payload.id) {
        updateDesensitization(payload).then((res) => {
          if (res === 1) {
            this.$message.success('修改配置成功')
            this.resetQuery()
            this.clickCancelDialog()
          }
          if (res === 2) {
            this.$message.error('该敏感字符已存在')
          }
        })
      } else {
        addDesensitization(payload).then((res) => {
          if (res === 1) {
            this.$message.success('添加配置成功')
            this.resetQuery()
            this.clickCancelDialog()
          }
          if (res === 2) {
            this.$message.error('该敏感字符已存在')
          }
        })
      }
    },
    clickCancelDialog() {
      this.clearDialogFormModel()
      this.dialog.visible = false
    },
    toggleDesensitization(row) {
      toggleDesensitization(row.id, row.status).then((res) => {
        if (res === 1) {
          this.$message.success('修改状态成功')
          this.resetQuery()
        }
      })
    },
  },
}
</script>
