import Vue from 'vue'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'
import Fragment from 'vue-fragment'

import '@style/index.scss'
import router from './router'
import store from './store'
import i18n from './language'
import './directive'
import * as filters from './filter'
import '@util/basic-type-extend.js'
import '@util/permission'
import { initFavicon } from '@util/favicon'

import App from './App'

// 开发mock调试
if (process.env.NODE_ENV === 'development' && process.env.VUE_APP_IS_MOCK === 'true') {
  const { mockXHR } = require('../mock')
  mockXHR()
}

// 设置为 false 阻止 vue 在启动时生成生产提示。
Vue.config.productionTip = false

Vue.use(ElementUI, { size: 'small' })
Vue.use(Fragment.Plugin)

// 注册全局过滤器
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key])
})

// 初始化favicon
initFavicon()

// 创建vue
new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: (h) => h(App),
})
