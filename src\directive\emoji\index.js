import Vue from 'vue'

const findEle = (parent, tagName) => {
  return parent.tagName.toLowerCase() === tagName ? parent : parent.querySelector(tagName)
}

const trigger = (el, tagName) => {
  const event = document.createEvent('HTMLEvents')
  event.initEvent(tagName, true, true)
  el.dispatchEvent(event)
}

Vue.directive('emoji', {
  bind(el, binding, vNode) {
    const regRule = /[^\u4E00-\u9FA5|^\ufe30-\uffa0|\d|a-zA-Z|\r\n\s,.?!:;"'…—&$=@#%^~<>()\-+/*{}[\]]|\s/g
    const $input = findEle(el, 'input') || findEle(el, 'textarea')
    el.$input = $input
    $input.handler = () => {
      const value = $input.value
      $input.value = value.replace(regRule, '')
      trigger($input, 'input')
    }

    $input.addEventListener('keyup', $input.handler)
  },
  unbind(el) {
    el.$input.removeEventListener('click', el.$input.handler)
  },
})
