<!--
 * @Description: 关联策略 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item :label="form.info.incPolicyName.label">
            {{ form.model.incPolicyName }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="20">
          <el-form-item :label="form.info.description.label">
            {{ form.model.description }}
          </el-form-item>
        </el-col>
      </el-row>
      <el-form-item label="条件">
        <section class="section-filter-conditions">
          <div class="section-filter-conditions-content">
            <el-tree
              ref="tree"
              :data="filterData"
              node-key="id"
              default-expand-all
              class="section-filter-conditions-content-tree"
              :props="defaultProps"
              :expand-on-click-node="false"
            >
              <span slot-scope="{ node, data }" class="custom-tree-node">
                <span style="line-height: 30px">{{ node.label }}</span>
              </span>
            </el-tree>
          </div>
        </section>
      </el-form-item>
      <el-form-item label="关联规则">
        <section class="section-filter-rules">
          <div class="section-filter-rules-block">
            <el-tree :data="ruData" node-key="id" :props="defaultProps" default-expand-all :expand-on-click-node="false">
              <div slot-scope="{ node, data }" class="custom-tree-node">
                <span>{{ node.label }}</span>
                <br />
                <span v-show="node.level == 1 && node.data.id % 2 != 0" style="margin-left: 10px">
                  在 {{ node.data.duration }} 秒内，发生 {{ node.data.times }}次
                </span>
                <br />
                <span v-show="node.level == 1 && node.data.id % 2 != 0" style="margin-left: 10px">归并字段：{{ node.data.content }}</span>
                <br />
              </div>
            </el-tree>
          </div>
        </section>
      </el-form-item>
      <el-form-item label="结果">
        <section class="section-result">
          <el-row>
            <el-col :span="12">
              <el-form-item :label="form.info.incSubCategoryID.label">
                {{ enumRelation(form.model.incSubCategoryID) }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="form.info.eventLevel.label">
                {{ enumLevel(form.model.eventLevel) }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="12">
              <el-form-item class="external-system" :label="form.info.externalSystem.label">
                {{ enumSystem(form.model.externalSystemList) }}
              </el-form-item>
            </el-col>
          </el-row>
        </section>
      </el-form-item>
      <el-form-item label="事件文本">
        <section>
          <el-row>
            <el-col :span="23">
              <el-form-item :prop="form.info.eventDesc.key">
                {{ form.model.eventDesc }}
              </el-form-item>
            </el-col>
          </el-row>
        </section>
      </el-form-item>
    </el-form>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '1000',
    },
    form: {
      required: true,
      type: Object,
    },
    actions: {
      type: Boolean,
      default: false,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    filData: {
      type: Array,
      default: () => [],
    },
    reData: {
      type: Array,
      default: () => [],
    },
    systemOption: {
      type: Array,
      default: () => [],
    },
    relationOption: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      ruData: this.reData,
      filterData: this.filData,
      defaultProps: {
        children: 'conditions',
      }, // 修改树默认参数名字
      option: {
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '4',
          },
          {
            label: this.$t('level.high'),
            value: '3',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '1',
          },
          {
            label: this.$t('level.general'),
            value: '0',
          },
        ], // 等级Option
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
    enumLevel() {
      return (level) => {
        for (const { label, value } of this.option.levelOption) {
          if (value === level) return label
        }
      }
    },
    enumSystem() {
      return (system) => {
        let str = ''
        if (system) {
          for (const item of system) {
            for (const { label, value } of this.systemOption) {
              if (item === value) str += label + ' '
            }
          }
        }
        return str
      }
    },
    enumRelation() {
      return (type) => {
        for (const { label, value } of this.relationOption) {
          if (value === type) return label
        }
      }
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
    filData(nVal) {
      this.filterData = nVal
    },
    reData(nVal) {
      this.ruData = nVal
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
html[data-theme='light'] *::-webkit-scrollbar-track {
  background-color: $CR;
}

::v-deep .el-tree-node__content {
  width: 90%;
}

.section-filter-rules {
  ::v-deep .el-tree-node__content {
    width: 90%;
    line-height: normal;
    margin-top: 16px;
  }
}

::v-deep .el-tree__empty-text {
  display: none;
}

.section-filter-conditions {
  overflow-y: scroll;
  margin-bottom: 10px;
  height: 200px;
  width: 800px;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);

  &-title {
    position: relative;

    > span {
      position: absolute;
    }

    span:nth-child(1) {
      top: 0;
      right: 10px;
    }

    span:nth-child(2) {
      top: 0;
      right: 60px;
    }
  }

  &-content {
    width: 100%;
    margin: 0 0 20px 0;

    &-tree {
      .el-tree-node__content {
        &:hover p {
          @include theme('color', font-color);
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding-right: 8px;

          .buttonSpan {
            margin-right: 60px;
          }

          :nth-child(2) {
            display: flex;
            justify-content: space-between;
          }
        }
      }
    }
  }
}

.section-filter-rules {
  overflow-y: scroll;
  width: 800px;
  height: 200px;
  margin-bottom: 10px;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);

  &-block {
    .el-tree {
      margin: 0px 0 20px 0;

      .el-tree-node__content {
        &:hover span {
          @include theme('color', font-color);
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          font-size: 14px;
          padding-right: 8px;
        }
      }
    }
  }
}

.section-result {
  width: 800px;
  height: 140px;
  border: 1px solid #e3e3e3;
  border-radius: 4px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  overflow-x: auto;
  overflow-y: hidden;

  > div {
    margin-top: 20px;
  }

  ::v-deep .el-form-item__content {
    white-space: nowrap;
  }
}

.el-form {
  div:nth-child(3) {
    > ::v-deep .el-form-item__label {
      &::before {
        content: '*';
        color: #f56c6c;
        margin-right: 4px;
      }
    }
  }
}

.external-system {
  ::v-deep .el-select__tags {
    span:nth-child(1) {
      width: 80%;
      white-space: nowrap;

      & + {
        .el-select__input,
        .is-small {
          display: none;
        }
      }
    }
  }
}
</style>
