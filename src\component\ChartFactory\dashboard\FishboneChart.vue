<template>
  <div :id="id" ref="chart" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'
import empty from '../mixin/empty'
import i18n from '@/language'

export default {
  mixins: [resize, empty],
  props: {
    className: {
      type: String,
      default: 'chart-fishbone',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    proto: {
      type: Boolean,
      default: false,
    },
    option: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    option: {
      handler(config) {
        this.configChart(config)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart, this.$store.getters.theme)
    },
    configChart(config = this.option) {
      !config || (Object.keys(config).length > 0 && config.data.length > 0) ? this.drawChart(config) : this.empty()
    },
    drawChart(config) {
      const option = this.proto ? config : this.chartOptionConfig(config)
      this.chart.clear()
      this.chart.setOption(option, true)
    },
    chartOptionConfig(config) {
      const option = {
        backgroundColor: 'transparent',
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        tooltip: {
          enterable: true,
          formatter: function(x) {
            if (x.data.type === 'scatter') {
              let tooltip = "<div style='display:inline-block;max-height:195px;overflow-y:auto;'>"
              for (let i = 0; i < x.data.items.length; i++) {
                tooltip +=
                  "<span style='font-size: 10px;'>" +
                  i18n.t('event.relevance.chart.attackCount') +
                  '：' +
                  x.data.items[i].count +
                  '</br>' +
                  i18n.t('event.relevance.chart.srcIp') +
                  '：' +
                  x.data.items[i].source +
                  '</br>' +
                  i18n.t('event.relevance.chart.dstIp') +
                  '：' +
                  x.data.items[i].target +
                  '</br>' +
                  i18n.t('event.relevance.chart.level') +
                  '：' +
                  x.data.items[i].levelName +
                  '</br></span>'
                if (i < x.data.items.length - 1) {
                  tooltip += '<hr />'
                }
              }
              return tooltip
            } else {
              return ''
            }
          },
        },
        series: [],
      }
      this.chartSeriesConfig(option, config)
      return option
    },
    chartSeriesConfig(option, config) {
      const series = {
        type: 'graph',
        layout: 'none',
        symbolSize: 30,
        roam: true,
        focusNodeAdjacency: true,
        itemStyle: {
          normal: {
            color: function(params) {
              if (params.data.level === 0) {
                return '#f56c6c'
              } else if (params.data.level === 1) {
                return '#e6a23c'
              } else if (params.data.level === 2) {
                return '#bfd228'
              } else if (params.data.level === 3) {
                return '#409eff'
              } else if (params.data.level === 4) {
                return '#67c23a'
              } else {
                return '#37a2da'
              }
            },
          },
        },
        edgeLabel: {
          fontSize: 11,
          show: true,
          formatter: (params) => {
            if (typeof params.data.name !== 'undefined') {
              return params.data.name
            } else {
              return ''
            }
          },
        },
        data: config.data,
        links: config.links,
      }
      option.series = series
      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
