<!--
 * @Description: 表达式查询组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023/4/17
 * @Editor:
 * @EditDate: 2023/4/17
-->
<template>
  <el-autocomplete
    ref="inputDom"
    v-model="input.keyword"
    :fetch-suggestions="handleFieldData"
    :placeholder="placeholder"
    :value-key="valueKey"
    :style="{ width: width }"
    @select="selectSearchKeyword"
    @keyup.native="keydownSearchKeyword"
    @change="changeExpression"
  >
    <i slot="suffix" class="el-input__icon el-icon-search" @click="changeExpression"></i>
  </el-autocomplete>
</template>

<script>
export default {
  props: {
    data: {
      type: Array,
      default() {
        return []
      },
    },
    placeholder: {
      type: String,
      default() {
        return this.$t('expressionAutocomplete.placeholder')
      },
    },
    width: {
      type: String,
      default: '100%',
    },
    valueKey: {
      type: String,
      default: 'label',
    },
  },
  data() {
    return {
      input: {
        status: 'space',
        keyword: '',
        conditions: [],
        allDataset: [],
        correlationDataset: [],
      },
      option: {
        logical: [
          {
            value: '&',
            label: 'AND',
          },
          {
            value: '|',
            label: 'OR',
          },
        ],
        symbol: [
          {
            value: ':',
            label: 'semi',
          },
          {
            value: '@',
            label: 'at',
          },
          {
            value: ',',
            label: 'comma',
          },
        ],
      },
    }
  },
  computed: {
    dataset() {
      return this.data.map((item) => ({
        label: `${item.label}(${item.value})`,
        value: item.value,
        symbols: item.symbols,
        valueRange: item.valueRange.map((item) => ({
          label: `${item.label}(${item.value})`,
          value: item.value,
        })),
      }))
    },
  },
  watch: {
    'input.keyword'(keyword) {
      this.watchKeyword(keyword)
    },
    'input.conditions'(conditions) {
      this.watchConditions(conditions)
    },
    'input.status'(status) {
      this.watchStatus(status)
    },
  },
  mounted() {
    this.setData()
  },
  methods: {
    handleFieldData(inputString, cb) {
      const keyword = this.getKeyword(inputString)
      this.input.correlationDataset = this.findCorrelationDataset(this.input.allDataset, keyword)
      cb(this.input.correlationDataset)
    },
    getKeyword(inputString) {
      let keyword = ''
      if (this.input.status === 'space') {
        keyword = inputString.match(/[\Sa-zA-Z\d]*$/)[0]
      }
      if (this.input.status === 'at') {
        const reg = /@[<>=!a-zA-Z0-9\u4e00-\u9fa5]*$/
        keyword = this.byStatusMatchKeyword(inputString, reg)
      }
      if (this.input.status === 'semi') {
        const reg = /:[a-zA-Z0-9\u4e00-\u9fa5]*$/
        keyword = this.byStatusMatchKeyword(inputString, reg)
      }
      if (this.input.status === 'comma') {
        const reg = /,[a-zA-Z0-9\u4e00-\u9fa5]*$/
        keyword = this.byStatusMatchKeyword(inputString, reg)
      }
      if (this.input.status === 'join') {
        keyword = inputString.match(/[\Sa-zA-Z|&]*$/)[0]
      }
      return keyword
    },
    byStatusMatchKeyword(str, reg) {
      const matchString = str.match(reg) ? str.match(reg)[0] : ''
      return matchString.length > 0 ? matchString.slice(1) : ''
    },
    findCorrelationDataset(arr, keyword) {
      return arr.filter((item) =>
        Object.keys(item).some((key) => {
          if (['label', 'value'].indexOf(key) > -1) {
            return (
              String(item[key])
                .toLowerCase()
                .indexOf(keyword.toLowerCase()) > -1
            )
          }
        })
      )
    },
    selectSearchKeyword(select) {
      this.selectedReplaceLastCondition(select.label)
      this.byStatusSetKeyword(select)
      this.changeExpression()
    },
    selectedReplaceLastCondition(selectLabel) {
      const lastCondition = this.getLastCondition().toLowerCase()
      const selectConditionLabel = selectLabel.toLowerCase()
      if (selectConditionLabel.indexOf(lastCondition) > -1) {
        this.input.conditions.pop()
      }
    },
    byStatusSetKeyword(select) {
      switch (this.input.status) {
        case 'semi':
          this.setKeywordForSemiAndAt(':', select.value)
          return
        case 'at':
          this.setKeywordForSemiAndAt('@', select.label)
          return
        case 'comma':
          this.setKeywordForComma(select.value)
          return
        case 'join':
          this.setKeywordForJoin(select.value)
          return
        default:
          this.setKeywordForSpace(select.value)
          return
      }
    },
    setKeywordForSemiAndAt(symbol, selectSuffix) {
      const conditions = this.input.conditions
      const lastCondition = this.getLastCondition()
      const splitLastCondition = lastCondition.split(symbol)[0]
      if (splitLastCondition !== '') {
        conditions[conditions.length - 1] = `${splitLastCondition}${symbol}`
        this.input.keyword = conditions.join(' ') + selectSuffix
      }
    },
    setKeywordForComma(selectSuffix) {
      const conditions = this.input.conditions
      const splitLastConditions = this.getLastCondition().split(',')
      if (splitLastConditions[0] !== '') {
        let lastConditionStr = ''
        for (let i = 0; i < splitLastConditions.length - 1; i++) {
          lastConditionStr = lastConditionStr + splitLastConditions[i] + ','
        }
        conditions[conditions.length - 1] = lastConditionStr
        this.input.keyword = conditions.join(' ') + selectSuffix
      }
    },
    setKeywordForJoin(selectSuffix) {
      const conditions = this.input.conditions
      this.input.keyword = conditions.join(' ') + ' ' + selectSuffix
    },
    setKeywordForSpace(selectSuffix) {
      const conditions = this.input.conditions
      this.input.keyword = conditions.join(' ').length > 0 ? conditions.join(' ') + ' ' + selectSuffix : conditions.join(' ') + selectSuffix
    },
    keydownSearchKeyword(e) {
      const shiftKey = e.shiftKey || e.metaKey
      const lastLetter = this.input.keyword.charAt(this.input.keyword.length - 1)
      const lastLetterIsSpace = this.detectionLastLetterIsSpace()

      if (shiftKey && e.keyCode === 229) {
        this.handleSymbolCondition(':', lastLetter)
      }

      if (shiftKey && e.keyCode === 50) {
        this.handleSymbolCondition('@', lastLetter)
      }

      if (!shiftKey && e.keyCode === 229) {
        this.handleSymbolCondition(',', lastLetter)
      }

      if (e.keyCode === 32 && lastLetterIsSpace) {
        this.handleSpaceCondition()
      }

      if (e.keyCode === 8 && !lastLetterIsSpace) {
        this.handleBackspaceCondition()
      }
    },
    handleSymbolCondition(symbol, compareSymbol) {
      if (symbol === compareSymbol) {
        const label = this.option.symbol.find((item) => item.value === symbol).label
        this.setAutocompleteStatus(label)
      }
    },
    handleSpaceCondition() {
      const condition = this.input.conditions
      const lastCondition = condition && condition.length > 0 ? condition[condition.length - 1] : ''
      ;['|', '&'].indexOf(lastCondition) > -1 ? this.setAutocompleteStatus('space') : this.setAutocompleteStatus('join')
    },
    handleBackspaceCondition() {
      const lastCondition = this.getLastCondition()
      const lastLetter = this.getLastConditionLetter()

      if (['|', '&'].indexOf(lastCondition) > -1) {
        this.setAutocompleteStatus('join')
      }

      if (lastCondition === '') {
        this.setAutocompleteStatus('space')
      }

      if (lastCondition.length > 0) {
        this.backspaceStringSwitchStatus(lastLetter)
      }
    },
    detectionLastLetterIsSpace() {
      const value = this.$refs.inputDom.value
      const matchValue = value.charAt(value.length - 1).match(/\s$/)
      return matchValue && matchValue.length > 0
    },
    backspaceStringSwitchStatus(letter) {
      const letterCondition = (symbol) => letter.indexOf(symbol) > -1
      const conditionAndStatus = []
      this.option.symbol.forEach((item) => {
        conditionAndStatus.push({
          judge: letterCondition(item.value),
          fn: () => {
            this.setAutocompleteStatus(item.label)
          },
        })
      })
      const findConditionHold = conditionAndStatus.find((item) => item.judge)
      findConditionHold ? findConditionHold.fn() : this.setAutocompleteStatus('space')
    },
    watchKeyword(keyword) {
      const inputString = keyword.match(/([\S]*@[\S]*:[(][^)]*[)])|([\S]*@[\S]*:[\[{][^}|^\]]*[\]}])|[\\|&]|([\S*]+)/g)
      this.input.conditions = inputString || []
    },
    watchConditions(conditions) {
      const lastCondition = this.getLastCondition(conditions)
      const lastLetter = lastCondition.charAt(lastCondition.length - 1)

      if (!conditions || lastCondition === '') {
        this.setAutocompleteStatus('space')
      }
      if (conditions.length % 2 === 0 && (lastCondition === '|' || lastCondition === '&')) {
        this.setAutocompleteStatus('space')
      }
      if (conditions.length % 2 === 1 && lastLetter === ':') {
        this.setAutocompleteStatus('semi')
      }
      if (conditions.length % 2 === 1 && lastLetter === ',') {
        this.setAutocompleteStatus('comma')
      }
      if (conditions.length % 2 === 1 && lastLetter === '@') {
        this.setAutocompleteStatus('at')
      }
    },
    watchStatus(status) {
      const lastCondition = this.getLastCondition()
      const keyword = lastCondition.split('@')[0] || ''
      const dataset = this.dataset.find((item) => item.value === keyword)

      if (status === 'space') {
        this.setData()
      }
      if (status === 'join') {
        this.input.allDataset = this.option.logical
      }
      if ((status === 'semi' || status === 'comma') && dataset) {
        this.input.allDataset = dataset.valueRange || []
      }
      if (status === 'at' && dataset) {
        this.input.allDataset = dataset.symbols || []
      }
    },
    getLastCondition(conditions = this.input.conditions) {
      return conditions && conditions.length > 0 ? conditions[conditions.length - 1] : ''
    },
    getLastConditionLetter() {
      const lastCondition = this.getLastCondition()
      return lastCondition.charAt(lastCondition.length - 1)
    },
    setAutocompleteStatus(status) {
      if (status.length === 1 && [':', '@', ','].indexOf(status) > -1) {
        this.input.status = this.option.symbol.find((item) => item.value === status).label
      }
      if (status.length > 1) {
        this.input.status = status
      }
    },
    changeExpression() {
      this.$emit('on-change', this.input.keyword)
    },
    setData() {
      this.input.allDataset = this.dataset.map((item) => ({
        label: item.label,
        value: item.value,
      }))
    },
  },
}
</script>
