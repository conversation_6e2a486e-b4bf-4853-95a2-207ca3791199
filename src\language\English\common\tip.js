export default {
  tip: {
    sweet: 'Friendly Reminder',
    notice: 'Message Notification',
    total: 'Total',
    without: 'None',
    add: {
      success: 'Added successfully',
      repeat: 'Duplicate addition',
      repeatName: 'Name already exists!',
      repeatNet: 'IP overlaps with existing ones!',
      error: 'Failed to add',
      license: 'Maximum assets reached, cannot add more',
      IpType: 'Start and end IP types are inconsistent!',
      aroundError: 'IP range error, end IP should be greater than start IP',
      innerType: 'This receiving method and collection address already has a collector configured, no need to configure again.',
      maxCount: 'Exceeded maximum collector count',
      innerTypeRepeat: 'Kafka receiving method already exists, cannot be duplicated',
      ipAround: 'IP range cannot exceed 120!',
      errorType: 'Invalid IP range',
      repeatTask: 'A task for discovering this network segment already exists',
      ipExist: 'IP already exists',
      licenseLimit: 'Exceeded license limit for monitor count',
      assetLimit: 'Asset information has reached the threshold, cannot synchronize additional assets.',
      logSourceExist: 'Selected log source does not exist, cannot add.',
      fail: 'Please enter at least one template display property!',
    },
    define: {
      success: 'Confirmed successfully',
      error: 'Confirmation failed',
    },
    delete: {
      success: 'Deleted successfully',
      error: 'Failed to delete',
      prompt: 'Please select content to delete',
      running: 'Currently in use, please disable before deleting',
      use: 'Currently in use, please do not delete',
    },
    update: {
      success: 'Updated successfully',
      error: 'Failed to update',
      repeatDic: 'Dictionary name already exists',
      repeatName: 'Name already exists',
      repeatNet: 'IP overlaps with existing ones',
      repeatIp: 'IP already exists',
      repeatTask: 'Task name already exists',
      repeatUser: 'Username already exists',
      repeatRole: 'Role name already exists',
      repeatMenu: 'Menu name already exists',
      repeatResource: 'Resource identifier already exists',
      repeatAction: 'Function identifier already exists',
      repeatStrategy: 'Strategy name already exists',
      repeatDomain: 'Domain name already exists',
      repeatDomainName: 'Domain name already exists',
      repeatDomainIp: 'Domain IP already exists',
      repeatDomainPort: 'Domain port already exists',
      repeatDomainUrl: 'Domain URL already exists',
      repeatDomainUrlIp: 'Domain URL IP already exists',
      repeatDomainUrlPort: 'Domain URL port already exists',
      repeatDomainUrlPath: 'Domain URL path already exists',
      repeatDomainUrlQuery: 'Domain URL query already exists',
      repeatDomainUrlFragment: 'Domain URL fragment already exists',
      repeatDomainUrlProtocol: 'Domain URL protocol already exists',
      repeatDomainUrlHost: 'Domain URL host already exists',
      repeatDomainUrlHostname: 'Domain URL hostname already exists',
      repeatDomainUrlOrigin: 'Domain URL origin already exists',
      repeatDomainUrlSearch: 'Domain URL search already exists',
      repeatDomainUrlHash: 'Domain URL hash already exists',
      repeatDomainUrlUsername: 'Domain URL username already exists',
      repeatDomainUrlPassword: 'Domain URL password already exists',
      repeatDomainUrlPathname: 'Domain URL pathname already exists',
      repeatDomainUrlSearchParams: 'Domain URL search parameters already exists',
      repeatDomainUrlAnchor: 'Domain URL anchor already exists',
      repeatDomainUrlQuery: 'Domain URL query already exists',
      repeatDomainUrlFragment: 'Domain URL fragment already exists',
    },
    confirm: {
      tip: 'Tip',
      submit: 'Operation successful, do you want to continue?',
      delete: 'Are you sure you want to delete?',
      batchDelete: 'Are you sure you want to batch delete?',
      cancel: 'Are you sure you want to cancel?',
      stop: 'Are you sure you want to stop?',
      start: 'Are you sure you want to start?',
      reset: 'Are you sure you want to reset?',
      logout: 'Are you sure you want to log out?',
      execute: 'Are you sure you want to execute?',
      recovery: 'Are you sure you want to recover?',
      copy: 'Are you sure you want to copy?',
      download: 'Are you sure you want to download?',
      upload: 'Are you sure you want to upload?',
      import: 'Are you sure you want to import?',
      export: 'Are you sure you want to export?',
      save: 'Are you sure you want to save?',
      submit: 'Are you sure you want to submit?',
      cancel: 'Are you sure you want to cancel?',
      close: 'Are you sure you want to close?',
      back: 'Are you sure you want to go back?',
      next: 'Are you sure you want to proceed to the next step?',
      prev: 'Are you sure you want to return to the previous step?',
      finish: 'Are you sure you want to finish?',
    },
    placeholder: {
      query: 'Please enter {0} keyword',
      legalIp: 'Please enter a valid IP',
      select: 'Please select',
    },
    execute: {
      success: 'Executed successfully',
      error: 'Execution failed',
    },
    recovery: {
      success: 'Recovered successfully',
      error: 'Recovery failed',
      process: 'System is in recovery process, please try again later',
      errorfile: 'Invalid file',
      errorversion: 'Version error',
    },
    copy: {
      success: 'Copied successfully',
      error: 'Copy failed',
      repeat: 'Strategy name already exists',
      builtIn: 'Built-in strategy, copying not allowed.',
    },
    detect: {
      success: 'Detection successful',
      error: 'Detection failed',
    },
    agent: {
      info: 'Agent uninstallation has not yet recovered, please try again later.',
    },
    send: {
      success: 'Sent successfully',
      error: 'Failed to send',
      userNotExist: 'User does not exist',
      userEmailNotMatch: 'User reserved email does not match',
      serviceUnavailable: 'Email service unavailable',
      codeHasExpired: 'Verification code has expired',
    },
    expression: {
      repeat: 'Expression already exists',
    },
  },
}
