<template>
  <custom-dialog ref="cmdDialog" :visible="visible" :title="$t('layout.setting.cmd.label')" width="60%" :action="false" @on-close="clickCancelDialog">
    <div v-loading="loading" element-loading-text="加载中..." style="min-height: 200px;">
      <span>命令类型：</span>
      <el-radio-group v-model="cmdType" @change="changeCmdType">
        <el-radio-button label="ping">ping</el-radio-button>
        <el-radio-button label="wmi">wmi</el-radio-button>
        <el-radio-button label="traceroute">traceroute</el-radio-button>
        <el-radio-button label="tcpdump">tcpdump</el-radio-button>
      </el-radio-group>
      <div class="content">
        <el-button type="primary" @click="doCmd" class="commit-btn">执行命令</el-button>
        <el-button type="default" @click="doReset" class="reset-btn">重置</el-button>
        <template v-if="cmdType === 'ping' || cmdType === 'traceroute' || cmdType === 'wmi'">
          <div class="content-item">
            <span class="title">目标地址：</span>
            <el-input v-model="targetAddress" placeholder="请输入目标地址" style="width: 100%;"></el-input>
          </div>
        </template>
        <template v-if="cmdType === 'wmi'">
          <div class="content-item">
            <span class="title">用户名：</span>
            <el-input v-model="wmiUser" placeholder="请输入用户名" style="width: 100%;"></el-input>
          </div>
          <div class="content-item">
            <span class="title">密码：</span>
            <el-input v-model="wmiPass" type="password" placeholder="请输入密码" style="width: 100%;"></el-input>
          </div>
        </template>
        <template v-if="cmdType === 'tcpdump'">
          <div class="content-two-item">
            <div class="content-item">
              <span class="title">生成文件名：</span>
              <el-input v-model="fileName" placeholder="请输入生成文件名" style="width: 80%;"></el-input>
            </div>
            <div class="content-item">
              <span class="title">网口：</span>
              <el-select v-model="networkCard" clearable filterable style="width: 80%;">
                <el-option v-for="item in networkCardOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </div>
          </div>
          <div class="content-two-item">
            <div class="content-item">
              <span class="title">超时时间(秒)：</span>
              <el-input-number
                v-model="timeout"
                type="number"
                placeholder="请输入超时时间"
                style="width: 80%;"
                :min="1"
                :max="60 * 300"
              ></el-input-number>
            </div>
            <div class="content-item">
              <span class="title">最大包数：</span>
              <el-input-number
                v-model="count"
                type="number"
                placeholder="请输入最大包数量"
                style="width: 80%;"
                :min="1"
                :max="10000"
              ></el-input-number>
            </div>
          </div>
          <div class="content-item">
            <span class="title">其他参数：</span>
            <el-input type="textarea" :rows="2" v-model="other" placeholder="请输入其他参数" style="width: 100%;" resize="none"></el-input>
          </div>
        </template>
        <template v-if="cmdType === 'ping' || cmdType === 'traceroute' || cmdType === 'wmi'">
          <div class="content-item">
            <span class="title">执行结果：</span>
            <el-input type="textarea" :rows="8" v-model="result" style="width: 100%;" disabled resize="none"></el-input>
          </div>
        </template>
        <template v-if="cmdType === 'tcpdump'">
          <el-card shadow="never" size="small">
            <div slot="header" class="result-header">
              <span>执行结果</span>
              <i class="el-icon-refresh" @click="getTcpdumpRecords"></i>
            </div>
            <el-table :data="tcpdumpResult" style="width: 100%;" height="170">
              <el-table-column prop="fileName" label="文件名" width="180"></el-table-column>
              <el-table-column prop="other" label="参数" width="180"></el-table-column>
              <el-table-column prop="status" label="是否完成">
                <template slot-scope="scope">
                  <el-tag v-if="scope.row.status === 0" type="info">已完成</el-tag>
                  <el-tag v-else type="success">未完成</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="op" label="操作">
                <template slot-scope="scope">
                  <el-button type="text" @click="doDownload(scope.row)">下载</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </template>
      </div>
    </div>
  </custom-dialog>
</template>
<script>
import CustomDialog from '@comp/CustomDialog'
import { getTcpdumpNetcard, getTcpdumpRecords, execTcpdump, downloadTcpdump, execCommonCmd } from '@api/management/system-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      networkCardOptions: [],
      cmdType: 'ping',
      targetAddress: '',
      wmiUser: '',
      wmiPass: '',
      fileName: '',
      networkCard: '',
      other: '',
      timeout: 60,
      count: 300,
      tcpdumpResult: [],
      result: '',
      loading: false,
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.getNetworkCard()
      }
      this.dialogVisible = val
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    getNetworkCard() {
      getTcpdumpNetcard().then((res) => {
        this.networkCardOptions = res.networkConfig
          .map((item) => {
            return { label: item.name, value: item.id }
          })
          .concat([{ label: 'ANY(任意网卡)', value: 'any' }])
      })
    },
    getTcpdumpRecords() {
      getTcpdumpRecords().then((res) => {
        this.tcpdumpResult = res
      })
    },
    changeCmdType(val) {
      this.cmdType = val
      if (val === 'tcpdump') {
        this.getTcpdumpRecords()
      }
    },
    doCmd() {
      if (this.cmdType === 'ping' || this.cmdType === 'traceroute') {
        if (!this.targetAddress) {
          this.$message.error('请输入目标地址')
          return
        }
        const payload = {
          command: this.cmdType,
          ipAddr: this.targetAddress,
        }
        this.loading = true
        execCommonCmd(payload).then((res) => {
          this.loading = false
          this.result = res
        })
      }
      if (this.cmdType === 'wmi') {
        if (!this.targetAddress) {
          this.$message.error('请输入目标地址')
          return
        }
        if (!this.wmiUser) {
          this.$message.error('请输入用户名')
          return
        }
        if (!this.wmiPass) {
          this.$message.error('请输入密码')
          return
        }
        const payload = {
          command: this.cmdType,
          ipAddr: this.targetAddress,
          username: this.wmiUser,
          password: this.wmiPass,
        }
        this.loading = true
        execCommonCmd(payload).then((res) => {
          this.loading = false
          this.result = res
        })
      }
      if (this.cmdType === 'tcpdump') {
        if (!this.fileName) {
          this.$message.error('请输入生成文件名')
          return
        }
        if (!this.networkCard) {
          this.$message.error('请选择网卡')
          return
        }
        if (this.other.includes('-c')) {
          this.$message.error('tcpdump命令中其他参数中不能包含 "-c"')
          return
        }
        const payload = {
          fileName: this.fileName,
          networkCard: this.networkCard,
          other: this.other,
          timeout: this.timeout,
          count: this.count,
        }
        this.loading = true
        execTcpdump(payload).then((res) => {
          this.loading = false
          if (res.code === 'Failed') {
            this.$message.error('执行失败')
          }
          if (res.code === 'FileExist') {
            this.$message.error('文件已存在')
          }
          if (res.code === 'tcpdumpExist') {
            this.$message.error('抓包进程已存在')
          }
          if (res.code === 'Success') {
            this.$message.success('执行成功')
            this.getTcpdumpRecords()
          }
        })
      }
    },
    doDownload(row) {
      downloadTcpdump(row.fileName).then((res) => {
        const fileName = row.fileName + '.pcap'
        if (window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveBlob(res.data, fileName)
        } else {
          const blob =
            typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
          const link = document.createElement('a')
          link.href = window.URL.createObjectURL(blob)
          link.download = fileName
          link.click()
          window.URL.revokeObjectURL(link.href)
        }
      })
    },
    doReset() {
      if (this.cmdType === 'ping' || this.cmdType === 'traceroute') {
        this.targetAddress = ''
        this.result = ''
      }
      if (this.cmdType === 'wmi') {
        this.wmiUser = ''
        this.wmiPass = ''
        this.result = ''
      }
      if (this.cmdType === 'tcpdump') {
        this.fileName = ''
        this.networkCard = ''
        this.other = ''
        this.timeout = 60
        this.count = 300
        this.tcpdumpResult = []
      }
    },
    clickCancelDialog() {
      this.dialogVisible = false
    },
  },
}
</script>

<style scoped lang="scss">
.content {
  padding: 16px;
  position: relative;
  ::v-deep {
    .el-card__header {
      padding: 8px;
    }
    .el-card__body {
      padding: 0px;
    }
    .el-textarea.is-disabled .el-textarea__inner {
      color: #474747;
    }
  }
  .title {
    display: inline-block;
    width: 120px;
    text-align: right;
  }
  .content-two-item {
    display: flex;
    .content-item {
      flex: 0 0 50%;
    }
  }
  .content-item {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
  }
}
.commit-btn {
  position: absolute;
  right: 16px;
  top: -32px;
  background-color: #116af8 !important;
  color: #fff !important;
  border: 1px solid #116af8 !important;
  &:hover {
    background-color: #0e5ae3 !important;
  }
}
.reset-btn {
  position: absolute;
  right: 108px;
  top: -32px;
}
.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px;
  i {
    cursor: pointer;
    &:hover {
      color: #116af8;
    }
  }
}
</style>
