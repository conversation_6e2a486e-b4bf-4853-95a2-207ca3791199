<!--
 * @Description: 采集器管理 - 添加弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2021-12-14
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :loading="dialogLoading"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form ref="addForm" :model="form.model" :rules="rules" label-width="120px">
      <template>
        <basic-comp
          ref="basicRef"
          :form="form"
          :inner-type-option="innerTypeOption"
          :agent-option="agentOption"
          @on-change-accessmode="changeAccessMode"
        ></basic-comp>
      </template>
      <!--syslog-->
      <template v-if="form.model.protId === '101' || form.model.protId === '102'">
        <syslog-comp
          ref="syslogRef"
          :form="form"
          :device-type-option="options.logSource"
          :filter-option="filterOption"
          @on-add-logsource="clickAddLogSource"
        ></syslog-comp>
      </template>
      <!--JDBC-->
      <template v-if="form.model.protId === '103'">
        <jdbc-comp ref="jdbcRef" :form="form" :filter-option="filterOption"></jdbc-comp>
      </template>
      <!--SSH-->
      <template v-if="form.model.protId === '104'">
        <ssh-comp ref="sshRef" :form="form" :filter-option="filterOption"></ssh-comp>
      </template>
      <!--netflow V5-->
      <template v-if="form.model.protId === '106'">
        <netflow-comp ref="netflowRef" :form="form" :filter-option="filterOption"></netflow-comp>
      </template>
      <!--WMI-->
      <template v-if="form.model.protId === '105'">
        <wmi-comp ref="wmiRef" :form="form" :filter-option="filterOption"></wmi-comp>
      </template>
      <!--ftp/sftp-->
      <template v-if="form.model.protId === '107' || form.model.protId === '108'">
        <ftp-comp
          ref="ftpRef"
          :form="form"
          :device-type-option="options.logSource"
          :filter-option="filterOption"
          @on-add-logsource="clickAddLogSource"
        ></ftp-comp>
      </template>
      <!--netbios-->
      <template v-if="form.model.protId === '109'">
        <netbios-comp ref="netbiosRef" :form="form" :filter-option="filterOption"></netbios-comp>
      </template>
      <!--kafka-->
      <section v-if="form.model.protId === '110'">
        <kafka-comp
          ref="kafkaRef"
          :form="form"
          :device-type-option="options.logSource"
          :filter-option="filterOption"
          @on-add-logsource="clickAddLogSource"
        ></kafka-comp>
      </section>
    </el-form>
    <add-log-source-dialog
      :visible.sync="dialog.add.visible"
      :model="dialog.add.model"
      :options="options"
      @on-submit="addLogSourceSubmit"
    ></add-log-source-dialog>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import BasicComp from './TheBasicComp'
import SyslogComp from './TheSyslogComp'
import JdbcComp from './TheJdbcComp'
import SshComp from './TheSshComp'
import NetflowComp from './TheNetflowComp'
import WmiComp from './TheWmiComp'
import FtpComp from './TheFtpComp'
import NetbiosComp from './TheNetbiosComp'
import KafkaComp from './TheKafkaComp'
import AddLogSourceDialog from './TheAddLogSourceDialog'
import { prompt } from '@util/prompt'
import { queryExistAccessmode, queryDeviceType, addLogSource, queryManufactCombo, queryCategoryCombo } from '@api/collector/collector-management-api'

export default {
  components: {
    CustomDialog,
    BasicComp,
    SyslogComp,
    JdbcComp,
    SshComp,
    NetflowComp,
    WmiComp,
    FtpComp,
    NetbiosComp,
    KafkaComp,
    AddLogSourceDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '800',
    },
    actions: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    deviceTypeOption: {
      required: true,
      type: Array,
    }, // 事件设备Option
    filterOption: {
      required: true,
      type: Array,
    }, // 采集过滤策略Option
    agentOption: {
      required: true,
      type: Array,
    },
    innerTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogLoading: false,
      dialogVisible: this.visible,
      options: {
        codeOption: [
          {
            label: '自动',
            value: 'auto',
          },
          {
            label: 'UTF-8',
            value: 'utf8',
          },
          {
            label: 'GBK',
            value: 'gbk',
          },
        ],
        manufact: [],
        category: [],
        logSource: [],
      },
      leaf: [],
      dialog: {
        add: {
          visible: false,
          model: {},
        },
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.getLogSourceCombo()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.initOptions()
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    async clickSubmitForm() {
      let valid = this.validateForm()
      let msg = 'tip.confirm.submit'
      if (valid) {
        this.dialogLoading = true
        await queryExistAccessmode(this.form.model).then((res) => {
          this.dialogLoading = false
          this.accessFlag = res
          if (this.accessFlag === 1) {
            // 该采集地址已配置其他接入方式
            msg = 'tip.confirm.existAccessmode'
          } else if (this.accessFlag === 2) {
            // 该采集地址已配置资产，是否确认更新资产类型。
            msg = 'tip.confirm.existAsset'
          } else if (this.accessFlag === 4) {
            // 资产信息达到阈值，无法同步添加资产。
            prompt({
              i18nCode: 'tip.add.assetLimit',
              type: 'info',
            })
            valid = false
          }
        })
      }
      if (valid) {
        this.$confirm(this.$t(msg), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          this.$emit('on-submit', this.form.model, this.leaf)
          this.clickCancelDialog()
        })
      } else {
        prompt(
          {
            i18nCode: 'validate.form.warning',
            type: 'warning',
            print: true,
          },
          () => {
            return false
          }
        )
      }
      this.$refs.dialogTemplate.end()
    },
    validateForm() {
      const protId = this.form.model.protId
      let connValid = false
      const basicValid = this.$refs.basicRef.validateForm()
      switch (protId) {
        case '103':
          connValid = this.$refs.jdbcRef.validateForm()
          break
        case '104':
          connValid = this.$refs.sshRef.validateForm()
          break
        case '105':
          connValid = this.$refs.wmiRef.validateForm()
          break
        case '106':
          connValid = this.$refs.netflowRef.validateForm()
          break
        case '107':
        case '108':
          connValid = this.$refs.ftpRef.validateForm()
          this.leaf = this.$refs.ftpRef.$refs.cascader.getCheckedNodes(true)
          break
        case '109':
          connValid = this.$refs.netbiosRef.validateForm()
          break
        case '110':
          connValid = this.$refs.kafkaRef.validateForm()
          this.leaf = this.$refs.kafkaRef.$refs.cascader.getCheckedNodes(true)
          break
        default:
          connValid = this.$refs.syslogRef.validateForm()
          this.leaf = this.$refs.syslogRef.$refs.cascader.getCheckedNodes(true)
          break
      }
      if (protId !== '110') {
        const ip = this.form.model.ip
        if (!ip) {
          this.$message.error('请至少输入一个采集地址')
          return false
        }
      } else {
        if (!this.form.model.kafkaAddress) {
          this.$message.error('请输入Kafka地址')
          return false
        }
      }
      return basicValid && connValid
    },
    changeAccessMode(val) {
      this.$emit('on-change', {
        protId: val,
        collectorName: this.form.model.collectorName,
        codeWay: this.form.model.codeWay,
      })
    },
    clickAddLogSource() {
      this.getManufactCombo()
      this.dialog.add.visible = true
      this.dialog.add.model = {
        manufact: '',
        categoryId: '',
        typeName: '',
        desc: '',
      }
    },
    addLogSourceSubmit(obj) {
      addLogSource(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.getLogSourceCombo()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    initOptions() {
      this.getManufactCombo()
      this.getCategoryCombo()
    },
    getManufactCombo() {
      queryManufactCombo().then((res) => {
        this.options.manufact = res
      })
    },
    getCategoryCombo() {
      queryCategoryCombo().then((res) => {
        this.options.category = res
      })
    },
    getLogSourceCombo() {
      queryDeviceType().then((res) => {
        this.options.logSource = res
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-form-item__error {
  width: 520px;
}
::v-deep .el-dialog__body {
  overflow-x: hidden;
}
</style>
