<!--
 * @Description: 采集器管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.inputVal"
              :placeholder="$t('tip.placeholder.query', [$t('collector.management.table.fuzzyField')])"
              clearable
              @keyup.enter.native="pageQuery('e')"
              @change="pageQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="pageQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <section class="table-header-button">
          <el-button v-has="'upload'" @click="clickLogImportButton">
            {{ $t('button.logImport') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAddButton">
            {{ $t('button.add') }}
          </el-button>
          <el-dropdown placement="bottom" @command="handleDownload">
            <el-button type="default" :loading="downloading">
              插件下载
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="windows">
                Windows插件
              </el-dropdown-item>
              <el-dropdown-item command="linux">
                Linux插件
              </el-dropdown-item>
              <el-dropdown-item command="mysql">
                Mysql插件
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
          <el-dropdown placement="bottom" @command="handleCommand">
            <el-button type="primary">
              {{ $t('button.batchText') }}
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="run">
                {{ $t('button.batch.run') }}
              </el-dropdown-item>
              <el-dropdown-item command="stop">
                {{ $t('button.batch.stop') }}
              </el-dropdown-item>
              <el-dropdown-item command="delete" style="color: #e32a0c">
                {{ $t('button.batch.delete') }}
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.collectorName"
                  clearable
                  :placeholder="$t('collector.management.table.collectorName')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.IP"
                  clearable
                  :placeholder="$t('collector.management.table.ip')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.protId"
                  clearable
                  :placeholder="$t('collector.management.placeholder.innerType')"
                  @change="pageQuery('e')"
                >
                  <el-option v-for="item in option.innerTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.useState"
                  clearable
                  :placeholder="$t('collector.management.placeholder.run')"
                  @change="pageQuery('e')"
                >
                  <el-option v-for="item in option.useStateOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="4" align="right">
                <el-button v-has="'query'" @click="pageQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetSeniorQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体内容-->
    <main class="table-body">
      <!--标题信息-->
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ title }}
        </h2>
      </header>
      <!--列表表格区域-->
      <main class="table-body-main">
        <el-table
          ref="CollectorTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="tableSelectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            v-for="(item, index) in option.tableOption"
            :key="index"
            :prop="item"
            :label="$t(`collector.management.table.${item}`)"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="agentStatus" :label="$t('collector.management.table.agentStatus')">
            <template slot-scope="scope">
              {{ scope.row.agentStatus === 1 ? $t('collector.management.label.agentStatus.on') : $t('collector.management.label.agentStatus.off') }}
            </template>
          </el-table-column>
          <el-table-column prop="runState" :label="$t('collector.management.table.runState')">
            <template slot-scope="scope">
              {{ scope.row.runState === '1' ? '正在运行' : '暂停' }}
            </template>
          </el-table-column>
          <el-table-column prop="useState" :label="$t('collector.management.table.run')">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.useState"
                :disabled="scope.row.agentStatus === 0"
                active-value="1"
                inactive-value="0"
                @change="toggleStatus(scope.row)"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="240">
            <template slot-scope="scope">
              <el-button v-has="'update'" :disabled="scope.row.agentStatus === 0" class="el-button--blue" @click="clickUpdateButton(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteButton(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <!--分页组件-->
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加框-->
    <add-dialog
      :width="'60%'"
      :visible.sync="dialog.addDialog.visibility"
      :title="dialog.addDialog.title"
      :form="dialog.addDialog.form"
      :device-type-option="option.deviceTypeOption"
      :agent-option="option.agentOption"
      :filter-option="option.filterOption"
      :inner-type-option="option.innerTypeOption"
      @on-change="changeAccess"
      @on-submit="clickSubmitAdd"
    ></add-dialog>
    <!--修改框-->
    <!--        <update-dialog
            :width="'60%'"
            :visible.sync="dialog.updateDialog.visibility"
            :title="dialog.updateDialog.title"
            :form="dialog.updateDialog.form"
            :filter-option="option.filterOption"
            :column-option="dialog.updateDialog.colOption"
            :device-type-option="option.deviceTypeOption"
            :agent-option="option.agentOption"
            :inner-type-option="option.innerTypeOption"
            :prot-name="flag.protName"
            @on-submit="clickSubmitUpdate">
        </update-dialog>-->
    <update-dialog
      :width="'60%'"
      :visible.sync="dialog.updateDialog.visibility"
      :title="dialog.updateDialog.title"
      :form="dialog.updateDialog.form"
      :filter-option="option.filterOption"
      :device-type-option="option.deviceTypeOption"
      :agent-option="option.agentOption"
      :inner-type-option="option.innerTypeOption"
      :prot-name="flag.protName"
      @on-submit="clickSubmitUpdate"
    ></update-dialog>
    <log-import-dialog
      ref="logImportRef"
      :visible.sync="dialog.logImportDialog.visible"
      :title="title"
      :form="dialog.logImportDialog.form"
      :filter-option="option.filterOption"
      :device-type-option="option.deviceTypeOption"
      @on-submit="clickLogImportSubmit"
    ></log-import-dialog>
  </div>
</template>

<script>
import AddDialog from './CollectorManagementAddDialog'
import UpdateDialog from './CollectorManagementUpdateDialog'
import LogImportDialog from './TheLogImportDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@/util/effect'
import { validateIp, validatePort, validateMultiIpPort } from '@util/validate'
import {
  queryCollectorTableData,
  queryDeviceType,
  queryAgent,
  queryFilterStrategies,
  queryProtocol,
  queryCollectorTableDetail,
  addCollectorData,
  deleteCollectorData,
  updateCollectorData,
  updateCollectorStatus,
  uploadLogData,
  downloadTool,
} from '@api/collector/collector-management-api'
import { isEmpty } from '@util/common'
import { validateName } from '@util/validate'

export default {
  name: 'CollectorManagement',
  components: {
    AddDialog,
    UpdateDialog,
    LogImportDialog,
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateIp(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }
    const validatorPort = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validatePort(value)) {
        callback(new Error(this.$t('validate.comm.port')))
      } else {
        callback()
      }
    }
    const validatorMultiIpPort = (rule, value, callback) => {
      if (value === '' || isEmpty(value)) {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateMultiIpPort(value)) {
        callback(new Error(this.$t('validate.address.incorrect')))
      } else {
        callback()
      }
    }
    const validatorCollectorName = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (validateIp(value)) {
        callback(new Error(this.$t('collector.management.error.collectorName')))
      } else if (!validateName(value, 1)) {
        callback(new Error(this.$t('validate.nameInput.rule')))
      } else {
        callback()
      }
    }
    return {
      title: this.$t('collector.management.header'),
      downloading: false,
      data: {
        loading: false,
        table: [],
        selected: [],
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
      }, // 列表数据
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      }, // 分页信息
      option: {
        tableOption: ['collectorName', 'ip', 'protName', 'agentIp', 'typeName'],
        filterOption: [], // 采集过滤策略option
        useStateOption: [
          {
            label: this.$t('collector.management.label.useState.on'),
            value: '1',
          },
          {
            label: this.$t('collector.management.label.useState.off'),
            value: '0',
          },
        ], // 使用状态option
        innerTypeOption: [], // 接入方式option
        deviceTypeOption: [], // 设备类型option
        agentOption: [],
      }, // 下拉框option
      show: {
        seniorQueryShow: false, // 高级查询区域显示隐藏
      }, // 显示隐藏
      flag: {
        protName: '', // 回显根据接入方式名称回显
      }, // 标识位
      dialog: {
        addDialog: {
          visibility: false,
          title: this.$t('collector.management.dialog.title.add'),
          form: {
            model: {
              collectorName: '',
              protId: '101',
              ip: '',
              kafkaAddress: '',
              codeWay: '',
              agentId: '',
              logType: '',
              propertyKind: [],
              domain: '',
              describe: '',
              dbType: '',
              dbInst: '',
              port: '',
              userName: '',
              password: '',
              strategy: '',
              isAsset: 1,
            },
            info: {
              collectorName: {
                key: 'collectorName',
                label: this.$t('collector.management.dialog.columns.collectorName'),
              },
              protId: {
                key: 'protId',
                label: this.$t('collector.management.dialog.columns.innerType'),
              },
              strategy: {
                key: 'strategy',
                label: this.$t('collector.management.dialog.columns.strategy'),
              },
              codeWay: {
                key: 'codeWay',
                label: this.$t('collector.management.dialog.columns.codeWay'),
              },
              agentIp: {
                key: 'agentId',
                label: this.$t('collector.management.dialog.columns.agentIp'),
              },
              logType: {
                key: 'logType',
                label: this.$t('collector.management.dialog.columns.logType'),
              },
              ip: {
                key: 'ip',
                label: this.$t('collector.management.dialog.columns.ip'),
              },
              kafkaAddress: {
                key: 'kafkaAddress',
                label: this.$t('collector.management.dialog.columns.kafkaAddress'),
              },
              propertyKind: {
                key: 'propertyKind',
                label: this.$t('collector.management.dialog.columns.propertyKind'),
              },
              describe: {
                key: 'describe',
                label: this.$t('collector.management.dialog.columns.describe'),
              },
              dbType: {
                key: 'dbType',
                label: this.$t('collector.management.dialog.columns.dbType'),
              },
              dbInst: {
                key: 'dbInst',
                label: this.$t('collector.management.dialog.columns.dbInst'),
              },
              domain: {
                key: 'domain',
                label: this.$t('collector.management.dialog.columns.domain'),
              },
              port: {
                key: 'port',
                label: this.$t('collector.management.dialog.columns.port'),
              },
              userName: {
                key: 'userName',
                label: this.$t('collector.management.dialog.columns.userName'),
              },
              password: {
                key: 'password',
                label: this.$t('collector.management.dialog.columns.password'),
              },
              directory: {
                key: 'directory',
                label: this.$t('collector.management.dialog.columns.directory'),
              },
              topic: {
                key: 'topic',
                label: this.$t('collector.management.dialog.columns.topic'),
              },
              isAsset: {
                key: 'isAsset',
                label: this.$t('collector.management.dialog.columns.isAsset'),
              },
            },
            rules: {
              protId: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              collectorName: [
                {
                  required: true,
                  validator: validatorCollectorName,
                  trigger: 'blur',
                },
              ],
              codeWay: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              agentId: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              propertyKind: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              ip: [
                {
                  required: true,
                  validator: validatorIp,
                  trigger: 'blur',
                },
              ],
              kafkaAddress: [
                {
                  required: true,
                  validator: validatorMultiIpPort,
                  trigger: 'blur',
                },
              ],
              dbType: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              userName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              password: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              dbInst: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              port: [
                {
                  required: true,
                  validator: validatorPort,
                  trigger: 'blur',
                },
              ],
              directory: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              topic: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
            },
          },
        }, // 添加dialog信息
        updateDialog: {
          visibility: false,
          title: this.$t('collector.management.dialog.title.update'),
          colOption: [
            [
              [
                { key: 'ip', label: this.$t('collector.management.dialog.columns.ip') },
                { key: 'port', label: this.$t('collector.management.dialog.columns.port') },
                { key: 'userName', label: this.$t('collector.management.dialog.columns.userName') },
                { key: 'password', label: this.$t('collector.management.dialog.columns.password') },
              ],
            ],
            [
              [
                { key: 'ip', label: this.$t('collector.management.dialog.columns.ip') },
                { key: 'port', label: this.$t('collector.management.dialog.columns.port') },
              ],
            ],
            [
              [
                { key: 'ip', label: this.$t('collector.management.dialog.columns.ip') },
                { key: 'domain', label: this.$t('collector.management.dialog.columns.domain') },
                { key: 'userName', label: this.$t('collector.management.dialog.columns.userName') },
                { key: 'password', label: this.$t('collector.management.dialog.columns.password') },
              ],
            ],
            [[{ key: 'ip', label: this.$t('collector.management.dialog.columns.ip') }]],
            [
              [
                { key: 'userName', label: this.$t('collector.management.dialog.columns.userName') },
                { key: 'password', label: this.$t('collector.management.dialog.columns.password') },
              ],
            ],
          ],
          form: {
            model: {
              devId: '',
              collectorName: '',
              IP: '',
              codeWay: '',
              agentId: '',
              logType: '',
              propertyKind: [],
              describe: '',
              password: '',
              userName: '',
              domain: '',
              port: '',
              dbType: '',
              dbInst: '',
              strategy: '',
            },
            info: {
              collectorName: {
                key: 'collectorName',
                label: this.$t('collector.management.dialog.columns.collectorName'),
              },
              protId: {
                key: 'protId',
                label: this.$t('collector.management.dialog.columns.innerType'),
              },
              ip: {
                key: 'ip',
                label: this.$t('collector.management.dialog.columns.ip'),
              },
              kafkaAddress: {
                key: 'kafkaAddress',
                label: this.$t('collector.management.dialog.columns.kafkaAddress'),
              },
              codeWay: {
                key: 'codeWay',
                label: this.$t('collector.management.dialog.columns.codeWay'),
              },
              agentIp: {
                key: 'agentId',
                label: this.$t('collector.management.dialog.columns.agentIp'),
              },
              logType: {
                key: 'logType',
                label: this.$t('collector.management.dialog.columns.logType'),
              },
              propertyKind: {
                key: 'propertyKind',
                label: this.$t('collector.management.dialog.columns.propertyKind'),
              },
              describe: {
                key: 'describe',
                label: this.$t('collector.management.dialog.columns.describe'),
              },
              password: {
                key: 'password',
                label: this.$t('collector.management.dialog.columns.password'),
              },
              userName: {
                key: 'userName',
                label: this.$t('collector.management.dialog.columns.userName'),
              },
              domain: {
                key: 'domain',
                label: this.$t('collector.management.dialog.columns.domain'),
              },
              port: {
                key: 'port',
                label: this.$t('collector.management.dialog.columns.port'),
              },
              dbType: {
                key: 'dbType',
                label: this.$t('collector.management.dialog.columns.dbType'),
              },
              dbInst: {
                key: 'dbInst',
                label: this.$t('collector.management.dialog.columns.dbInst'),
              },
              strategy: {
                key: 'strategy',
                label: this.$t('collector.management.dialog.columns.strategy'),
              },
              directory: {
                key: 'directory',
                label: this.$t('collector.management.dialog.columns.directory'),
              },
              topic: {
                key: 'topic',
                label: this.$t('collector.management.dialog.columns.topic'),
              },
              isAsset: {
                key: 'isAsset',
                label: this.$t('collector.management.dialog.columns.isAsset'),
              },
            },
            rules: {
              collectorName: [
                {
                  required: true,
                  validator: validatorCollectorName,
                  trigger: 'blur',
                },
              ],
              protId: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              codeWay: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              agentId: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              propertyKind: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              ip: [
                {
                  required: true,
                  validator: validatorIp,
                  trigger: 'blur',
                },
              ],
              kafkaAddress: [
                {
                  required: true,
                  validator: validatorMultiIpPort,
                  trigger: 'blur',
                },
              ],
              dbType: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              dbInst: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              port: [
                {
                  required: true,
                  validator: validatorPort,
                  trigger: 'blur',
                },
              ],
              userName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              password: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              directory: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              topic: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
            },
          },
        }, // 修改dialog信息
        logImportDialog: {
          visible: false,
          form: {
            model: {},
            rules: {
              IP: [
                {
                  required: true,
                  validator: validatorIp,
                  trigger: 'blur',
                },
              ],
              codeWay: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              propertyKind: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              files: [
                {
                  required: true,
                  message: this.$t('validate.choose'),
                  trigger: 'change',
                },
              ],
            },
          },
        },
      }, // dialog信息
      query: {
        inputVal: '', // 模糊查询
        seniorQuery: {
          collectorName: '',
          IP: '',
          useState: '',
          protId: '',
        }, // 高级查询信息
      }, // 页面模糊查询和高级查询
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化方法
    init() {
      this.initOption()
      this.initDebounce()
      this.queryTableData()
    },
    // 初始化防抖方法
    initDebounce() {
      this.data.debounce.query = debounce(() => {
        let params = {}
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.seniorQuery, {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
          })
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            inputVal: this.query.inputVal,
          }
        }
        this.queryTableData(params)
      }, 500)
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.pagination.pageNum = 1
        this.query.seniorQuery = {
          collectorName: '',
          IP: '',
          useState: '',
          protId: '',
        }
        setTimeout(() => {
          this.queryTableData()
        }, 150)
      }, 500)
    },
    // 初始化页面Option
    initOption() {
      // 查询接入方式下拉框信息
      queryProtocol().then((res) => {
        this.option.innerTypeOption = res
      })
      // 查询设备分类Option
      queryDeviceType().then((res) => {
        this.option.deviceTypeOption = res
      })
      // 查询采集器策略
      queryFilterStrategies().then((res) => {
        this.option.filterOption = res
      })
      // 查询代理服务器
      queryAgent().then((res) => {
        this.option.agentOption = res
      })
    },
    // 查询采集器列表内容方法
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.data.loading = true
      this.pagination.visible = false
      queryCollectorTableData(params).then((res) => {
        if (res) {
          this.data.table = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 页面查询api
    pageQuery(flag) {
      if (this.validatorIp()) {
        if (flag) this.pagination.pageNum = 1
        this.data.debounce.query()
      }
    },
    validatorIp() {
      const ip = this.query.seniorQuery.IP || ''
      if (ip !== '' && !validateIp(ip)) {
        prompt({
          i18nCode: 'validate.ip.incorrect',
          type: 'error',
        })
        return false
      } else {
        return true
      }
    },
    // 采集器添加API方法
    addCollector(obj) {
      addCollectorData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.add.innerType',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'tip.add.maxCount',
            type: 'error',
          })
        } else if (res === 5) {
          prompt({
            i18nCode: 'tip.add.innerTypeRepeat',
            type: 'error',
          })
        } else if (res === 7) {
          prompt({
            i18nCode: 'tip.add.logSourceExist',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 采集器修改API方法
    updateCollector(obj) {
      updateCollectorData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.pageQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.update.innerType',
            type: 'error',
          })
        } else if (res === 7) {
          prompt({
            i18nCode: 'tip.update.logSourceExist',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 采集器删除API方法
    deleteCollector(id) {
      deleteCollectorData(id).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.delete.success',
              type: 'success',
            },
            () => {
              this.query = {
                inputVal: '',
                seniorQuery: {
                  collectorName: '',
                  IP: '',
                  useState: '',
                  protId: '',
                },
              } // 删除清空查询条件
              const [pageNum, idArray] = [this.pagination.pageNum, id.split(',')]
              if (idArray.length === this.data.table.length) {
                this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
              }
              this.queryTableData()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.delete.error',
            type: 'error',
          })
        }
      })
    },
    // 采集器使用状态修改API方法
    updateStatus(id, state) {
      updateCollectorStatus(id, state).then((res) => {
        if (res) {
          if (state === '1') {
            prompt(
              {
                i18nCode: 'tip.enable.success',
                type: 'success',
              },
              () => {
                this.pageQuery()
              }
            )
          } else {
            prompt(
              {
                i18nCode: 'tip.disable.success',
                type: 'success',
              },
              () => {
                this.pageQuery()
              }
            )
          }
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 点击页面查询按钮
    clickQueryButton() {
      this.query.inputVal = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initDebounce()
      this.resetSeniorQuery()
    },
    // 点击页面删除按钮
    clickDeleteButton(row) {
      const id = row.devId
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.deleteCollector(id)
      })
    },
    // 点击向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initDebounce()
      this.resetSeniorQuery()
    },
    clickLogImportButton() {
      this.dialog.logImportDialog.visible = true
      this.dialog.logImportDialog.form.model = {
        IP: '',
        codeWay: 'auto',
        propertyKind: [],
        strategy: '',
        files: [],
      }
    },
    // 点击添加按钮
    clickAddButton() {
      this.dialog.addDialog.visibility = true
      this.dialog.addDialog.form.model = {
        collectorName: '',
        protId: '101',
        ip: '',
        kafkaAddress: '',
        propertyKind: [],
        codeWay: 'auto',
        logType: '',
        describe: '',
        dbType: '',
        dbInst: '',
        port: '',
        userName: '',
        password: '',
        isAsset: 1,
      }
    },
    // 点击提交添加框
    async clickSubmitAdd(formModel, leaf) {
      if (
        formModel.protId === '101' ||
        formModel.protId === '102' ||
        formModel.protId === '107' ||
        formModel.protId === '108' ||
        formModel.protId === '110'
      ) {
        this.buildTreeData(formModel, leaf)
      }
      await this.addCollector(formModel)
    },
    // 点击修改按钮
    clickUpdateButton(row) {
      queryCollectorTableDetail(row.devId).then((res) => {
        const { typeId, categoryID } = res
        const data = Object.assign({}, res, { propertyKind: [categoryID, typeId] })
        this.dialog.updateDialog.form.model = data
      })
      this.flag.protName = String(row.protName).toLowerCase()
      if (row.protName === 'Kafka') {
        this.dialog.updateDialog.form.rules.password = [
          {
            required: false,
          },
        ]
        this.dialog.updateDialog.form.rules.userName = [
          {
            required: false,
          },
        ]
      } else {
        this.dialog.updateDialog.form.rules.password = [
          {
            required: true,
            message: this.$t('validate.empty'),
          },
        ]
        this.dialog.updateDialog.form.rules.userName = [
          {
            required: true,
            message: this.$t('validate.empty'),
          },
        ]
      }
      this.dialog.updateDialog.visibility = true
    },
    // 点击提交修改框
    clickSubmitUpdate(formModel, leaf) {
      if (
        formModel.protId === '101' ||
        formModel.protId === '102' ||
        formModel.protId === '107' ||
        formModel.protId === '108' ||
        formModel.protId === '110'
      ) {
        this.buildTreeData(formModel, leaf)
      }
      this.updateCollector(formModel)
    },
    // 列表信息选中方法
    tableSelectsChange(select) {
      this.data.selected = select
    },
    // 修改每页size
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.pageQuery()
    },
    // 修改当前页码
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.pageQuery()
    },
    // 切换运行状态
    toggleStatus(row) {
      this.updateStatus(row.devId, row.useState)
    },
    // 页面重置按钮
    resetSeniorQuery() {
      this.data.debounce.resetQueryDebounce()
    },
    // 构建数据结构
    buildTreeData(formModel, leaf) {
      const [option] = leaf
      const [categoryName, typeName] = option.pathLabels
      const [categoryID, typeId] = option.path
      Object.assign(formModel, {
        categoryName,
        categoryID,
        typeName,
        typeId,
      })
    },
    // 批量开启
    batchRun() {
      const arr = []
      const useState = this.data.selected.map((item) => item.useState).toString()
      if (useState.indexOf('1') > -1) {
        prompt({
          i18nCode: 'tip.start.existStart',
          type: 'error',
        })
        return
      }
      this.data.selected.map((item) => arr.push(item.devId))
      if (arr.length === 0) {
        prompt({
          i18nCode: 'collector.management.label.runSuccess',
          type: 'info',
        })
      } else {
        this.updateStatus(arr.toString(), '1')
      }
    },
    // 批量停止
    batchStop() {
      const arr = []
      const useState = this.data.selected.map((item) => item.useState).toString()
      if (useState.indexOf('0') > -1) {
        prompt({
          i18nCode: 'tip.stop.existStop',
          type: 'error',
        })
        return
      }
      this.data.selected.map((item) => arr.push(item.devId))
      if (arr.length === 0) {
        prompt({
          i18nCode: 'collector.management.label.runError',
          type: 'info',
        })
      } else {
        this.updateStatus(arr.toString(), '0')
      }
    },
    // 批量删除
    batchDelete() {
      const arr = []
      this.data.selected.map((item) => arr.push(item.devId))
      if (arr.length === 0) {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'error',
        })
      } else {
        this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          deleteCollectorData(arr.toString()).then((res) => {
            if (res) {
              prompt(
                {
                  i18nCode: 'tip.delete.success',
                  type: 'success',
                },
                () => {
                  this.query.inputVal = ''
                  this.query.seniorQuery = {
                    collectorName: '',
                    IP: '',
                    useState: '',
                    protId: '',
                  }
                  const [pageNum, idArray] = [this.pagination.pageNum, arr]
                  if (idArray.length === this.data.table.length) {
                    this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                  }
                  this.queryTableData()
                }
              )
            } else {
              prompt({
                i18nCode: 'tip.delete.error',
                type: 'error',
              })
            }
          })
        })
      }
    },
    handleDownload(command) {
      let id = 1
      if (command === 'windows') id = 1
      if (command === 'linux') id = 2
      if (command === 'mysql') id = 3
      this.downloading = true
      downloadTool(id).then((res) => {
        if (res) {
          this.downloading = false
          const fileName = res.fileName
          if (window.navigator.msSaveOrOpenBlob) {
            window.navigator.msSaveBlob(res.data, fileName)
          } else {
            const blob =
              typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
            const link = document.createElement('a')
            link.href = window.URL.createObjectURL(blob)
            link.download = fileName
            link.click()
            window.URL.revokeObjectURL(link.href)
          }
        } else {
          prompt({
            i18nCode: 'tip.download.error',
            type: 'error',
          })
        }
      })
    },
    // 批量操作下拉框
    handleCommand(command) {
      switch (command) {
        case 'run':
          this.batchRun()
          break
        case 'stop':
          this.batchStop()
          break
        case 'delete':
          this.batchDelete()
          break
      }
    },
    // 添加dialog的接入方式切换
    changeAccess({ protId, collectorName, codeWay }) {
      this.dialog.addDialog.form.model = {
        collectorName: collectorName,
        protId: protId,
        codeWay: codeWay,
        ip: '',
        kafkaAddress: '',
        propertyKind: [],
        describe: '',
        dbType: '',
        dbInst: '',
        port: '',
        userName: '',
        password: '',
        isAsset: 1,
      }
      if (protId === '110') {
        this.dialog.addDialog.form.rules.password = [
          {
            required: false,
          },
        ]
        this.dialog.addDialog.form.rules.userName = [
          {
            required: false,
          },
        ]
      } else {
        this.dialog.addDialog.form.rules.password = [
          {
            required: true,
            message: this.$t('validate.empty'),
          },
        ]
        this.dialog.addDialog.form.rules.userName = [
          {
            required: true,
            message: this.$t('validate.empty'),
          },
        ]
      }
    },
    clickLogImportSubmit(formData) {
      uploadLogData(formData)
        .then((res) => {
          if (res > 0) {
            prompt({
              i18nCode: this.$t('tip.import.success'),
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.import.error',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
  },
}
</script>
