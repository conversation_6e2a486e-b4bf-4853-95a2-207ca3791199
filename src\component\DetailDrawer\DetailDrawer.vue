<template>
  <el-drawer :with-header="false" :title="title" :visible.sync="dialogVisible" :direction="direction" :size="size" v-bind="$attrs" v-on="$listeners">
    <el-collapse v-model="active" v-loading="loading">
      <el-collapse-item v-for="(detail, itemIndex) in tempData" :key="detail.value" :title="detail.label" :name="itemIndex">
        <el-row v-if="detail.children && detail.children.length > 0" class="detail-row">
          <el-col
            v-for="(item, index) in detail.children"
            :key="index"
            :span="detail.children.length === 1 || item.key === 'eventDesc' ? 30 : 5"
            :offset="1"
            class="detail-col"
          >
            <b class="detail-col-label">{{ item.label }}</b>
            <level-tag v-if="item.key === 'level' || item.key === 'levelName'" :level="item.value"></level-tag>
            <span v-else class="detail-col-value" v-html="highLight(item)"></span>
          </el-col>
        </el-row>
      </el-collapse-item>
    </el-collapse>
  </el-drawer>
</template>

<script>
import LevelTag from '@comp/LevelTag'
import { classify } from '@util/format'
import logColumn from '@asset/js/attr/original-log-column'
function escapeRegExp(str) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&') // $& 表示匹配的字符串
}
export default {
  components: {
    LevelTag,
  },
  inheritAttrs: false,
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      type: String,
      default: '抽屉标题',
    },
    direction: {
      type: String,
      default: 'btt',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    size: {
      type: String,
      default: '70%',
    },
    detailData: {
      type: [Object, Array],
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      active: 0,
    }
  },
  computed: {
    tempData() {
      let data = {}
      if (this.detailData.constructor === Object) {
        logColumn.forEach((item) => {
          Reflect.ownKeys(this.detailData).forEach((key) => {
            if (item.key === key) {
              item.value = this.detailData[key]
            }
          })
        })
        data = classify(logColumn, 'group').map((item) => ({
          label: item.group,
          children: item.children,
        }))
      }

      if (this.detailData.constructor === Array) {
        data = this.detailData
      }
      return data
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      if (!nVal) {
        this.$emit('on-close')
        this.active = 0
      }
    },
  },
  methods: {
    highLight(valueObject) {
      if (valueObject.key !== 'raw') {
        return valueObject.value
      } else {
        const row = this.detailData
        if (row.fuzzyField) {
          const escapedContent = this.escapeHtml(valueObject.value)
          const escapeFuzzy = this.escapeHtml(row.fuzzyField)
          const fieldRegex = new RegExp(escapeFuzzy, 'gi')
          const highlighted = escapedContent.replace(fieldRegex, (match) => {
            return `<span class="highlight-value">${match}</span>`
          })
          return highlighted
        } else {
          const escapedContent = this.escapeHtml(valueObject.value)
          return escapedContent
        }
      }
    },
    escapeHtml(unsafe) {
      return unsafe
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;')
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-drawer {
  padding: 30px 50px;
  overflow: auto;
  outline: none;

  .detail-row {
    .detail-col {
      display: flex;
      min-height: 50px;

      &-label {
        display: inline-block;
        width: 120px;
        max-width: 300px;
        color: #1873d7;
      }

      &-value {
        word-wrap: break-word;
        word-break: break-all;
        display: inline-block;
        width: calc(100% - 120px);
        white-space: pre-wrap;
      }
    }
  }
  .highlight-value {
    background-color: #ffe200;
    padding: 2px 5px;
    border-radius: 3px;
    font-weight: bold;
  }
}
</style>
