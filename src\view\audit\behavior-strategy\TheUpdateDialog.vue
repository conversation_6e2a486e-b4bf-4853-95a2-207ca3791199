<!--
 * @Description: 行为分析策略 - 修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-18
 * @Editor:
 * @EditDate: 2021-10-18
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.update', [titleName])"
    width="60%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formDom" :model="model" :rules="rules" label-width="110px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="systemName" :label="$t('audit.behaviorStrategy.label.systemName')">
            <el-input v-model="model.systemName" maxlength="128"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="systemIp" :label="$t('audit.behaviorStrategy.label.systemIp')">
            <el-input v-model="model.systemIp" maxlength="64"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="abnormalAction" :label="$t('audit.behaviorStrategy.label.abnormalAction')">
            <el-cascader
              v-model="model.abnormalAction"
              :options="options.eventType"
              :props="{ expandTrigger: 'hover', multiple: true }"
              filterable
              clearable
              placeholder=""
              collapse-tags
            ></el-cascader>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="timeRange" :label="$t('audit.behaviorStrategy.label.timeRange')">
            <el-time-picker
              v-model="model.timeRange"
              is-range
              :clearable="false"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              range-separator="~"
              :start-placeholder="$t('time.option.startTime')"
              :end-placeholder="$t('time.option.endTime')"
            ></el-time-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <el-form-item prop="externalSystem" :label="$t('audit.behaviorStrategy.label.externalSystem')">
            <el-select v-model="model.externalSystem" filterable multiple collapse-tags clearable>
              <el-option v-for="item in options.externalSystem" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="externalMail" :label="$t('audit.behaviorStrategy.label.externalMail')">
            <el-input v-model="model.externalMail" maxlength="64"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="legalIp" class="legal-ip-label" :label="$t('audit.behaviorStrategy.label.legalIp')">
            <el-select
              ref="legalIpRef"
              v-model="model.legalIp"
              multiple
              filterable
              allow-create
              default-first-option
              popper-class="el-select-nopop"
              :placeholder="$t('tip.placeholder.legalIp')"
              @focus="onBlur('legalIpRef')"
            >
              <el-option v-for="item in []" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateEmail, validateIp } from '@util/validate'
import { isEmpty } from '@util/common'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value !== '') {
        if (!validateIp(value)) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      } else {
        callback(new Error(this.$t('validate.empty')))
      }
    }
    const validatorLegalIp = (rule, value, callback) => {
      if (value !== '' && value.length > 0) {
        for (let i = 0; i < value.length; i++) {
          if (!validateIp(value[i])) {
            callback(new Error(this.$t('validate.ip.incorrect')))
            return
          }
        }
        callback()
      } else {
        callback(new Error(this.$t('validate.empty')))
      }
    }
    const validatorTimeRange = (rule, value, callback) => {
      if (isEmpty(value) || value.length <= 0) {
        callback(new Error(this.$t('validate.Empty')))
      } else {
        callback()
      }
    }
    const validatorEmail = (rule, value, callback) => {
      if (value !== '' && !validateEmail(value)) {
        callback(new Error(this.$t('validate.email')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: this.visible,
      rules: {
        systemName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        systemIp: [
          {
            required: true,
            trigger: 'blur',
            validator: validatorIp,
          },
        ],
        abnormalAction: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'change',
          },
        ],
        timeRange: [
          {
            required: true,
            trigger: 'blur',
            validator: validatorTimeRange,
          },
        ],
        legalIp: [
          {
            required: true,
            trigger: 'blur',
            validator: validatorLegalIp,
          },
        ],
        externalMail: [
          {
            trigger: 'blur',
            validator: validatorEmail,
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formDom.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
    onBlur(ref) {
      this.$refs[ref].$refs.input.blur = () => {
        const inp = this.$refs[ref].$refs.input.value
        const legalArr = this.model.legalIp
        if (inp) {
          legalArr.indexOf(inp) > -1 ? this.arrayRemove(legalArr, inp) : legalArr.push(inp)
        }
      }
    },
    arrayRemove(arr, val) {
      var index = arr.indexOf(val)
      if (index > -1) {
        arr.splice(index, 1)
      }
      return arr
    },
  },
}
</script>

<style lang="scss" scoped>
.legal-ip-label {
  ::v-deep .el-select {
    width: 96% !important;
  }
  ::v-deep .el-input__suffix {
    display: none;
  }
}
</style>
