export default {
  table: {
    header: '告警列表',
    dialog: {
      colTitle: '告警列表自定义列',
      option: '可选择',
      detailTitle: '告警列表详情',
      reasonTitle: '告警列表确认',
    },
    label: {
      name: '审计名称',
      level: '事件级别',
      auditTypeName: '审计类型',
      alarmStrategyName: '告警策略',
      auditUser: '审计人员',
      state: '告警状态',
      total: '事件总数',
      createTime: '产生时间',
      updateTime: '更新时间',
      reason: '确认原因',
      handel: '操作',
    },
    detail: {
      detailOriginalColumn: {
        type2Name: '原始日志名称',
        eventName: '事件类型',
        eventCategoryName: '事件类别',
        level: '事件等级',
        srcIp: '源IP',
        dstIp: '目的IP',
        dateTime: '时间',
        raw: '日志原文',
      },
      detailSafeColumn: {
        type2Name: '安全事件名称',
        alarmTypeName: '安全事件类型',
        alarmCategoryName: '安全事件类别',
        level: '事件等级',
        count: '聚合数量',
        aggrStartDate: '聚合开始时间',
        deviceTypeName: '日志源设备',
      },
      detailRelevanceColumn: {
        policyName: '策略名称',
        eventTypeName: '关联事件名称',
        level: '事件等级',
        createDate: '产生时间',
        updateDate: '更新时间',
        count: '次数',
      },
      detailThreatColumn: {
        eventTypeName: '威胁事件类型',
        eventLevel: '事件等级',
        eventDesc: '事件描述',
        receiveTime: '接收时间',
        eventTime: '告警时间',
      },
    },
    state: {
      done: '已确认',
      pending: '待处理',
    },
    panel: {
      detail: '详情信息',
      source: '事件溯源',
      original: '原始日志',
    },
  },
}
