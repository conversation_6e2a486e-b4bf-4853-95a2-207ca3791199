<!--
 * @Description: 系统告警
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-24
 * @Editor:
 * @EditDate: 2021-08-24
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" :options="options" @on-change="changeQueryTable" @on-batch-handle="clickBatchHandle"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-select="clickSelectRows"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import { prompt } from '@util/prompt'
import { handleStatus } from '@asset/js/code/option'
import { querySystemAlarmTable, ignoreSystemAlarm } from '@api/alarm/system-api'

export default {
  name: 'SystemAlarm',
  inject: ['sysAlarm'],
  components: {
    TableHeader,
    TableBody,
    TableFooter,
  },
  data() {
    return {
      title: this.$t('alarm.system.header'),
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      query: {
        senior: false,
        form: {
          fuzzyField: '',
        },
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        isIgnore: handleStatus,
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') {
        this.pagination.pageNum = 1
      }
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          alarmName: this.query.form.alarmName,
          isIgnore: this.query.form.isIgnore,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickSelectRows(select) {
      this.table.selected = select
    },
    clickBatchHandle() {
      if (this.table.selected.length > 0) {
        const ids = this.table.selected.map((item) => item.id).toString()
        this.$confirm(this.$t('tip.confirm.batchIgnore'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          this.ignoreSystemAlarm(ids)
        })
      } else {
        prompt({
          i18nCode: 'tip.ignore.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      querySystemAlarmTable(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
    ignoreSystemAlarm(ids) {
      ignoreSystemAlarm(ids).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.ignore.success',
              type: 'success',
            },
            () => {
              this.sysAlarm()
              const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
              if (idArray.length === this.table.data.length) {
                this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
              }
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.ignore.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
