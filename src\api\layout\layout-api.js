import request from '@util/request'
import { updateFaviconFromLogo } from '@util/favicon'

export function queryLogoutData() {
  return request({
    url: '/authentication/logout',
    method: 'get',
  })
}

export function updateUserPasswordData(obj) {
  return request({
    url: '/usermanagement/password',
    method: 'post',
    data: obj || {},
  })
}

export function queryMenuData() {
  return request({
    url: '/menumanagement/menu/navigation',
    method: 'get',
  })
}

export function querySystemData() {
  return request({
    url: '/actuator/home',
    method: 'get',
  })
}

export function querySystemToolsData() {
  return request({
    url: '/menumanagement/menu/tools',
    method: 'get',
  })
}

export function queryUserInfoData() {
  return request({
    url: '/usermanagement/user/extend',
    method: 'get',
  })
}

export function updateUserInfoData(obj) {
  return request({
    url: '/usermanagement/user/extend',
    method: 'post',
    data: obj || {},
  })
}

export function queryAlarmAmountData() {
  return request({
    url: '/event/alarm/total',
    method: 'get',
  })
}

export function querySystemNoticeConfig() {
  return request({
    url: '/systemmanagement/find-system-alarm-notice',
    method: 'get',
  })
}

export function querySystemAlarmAmountData() {
  return request({
    url: '/systemalarm/findNotIgnoreAlarm',
    method: 'get',
  })
}

export function queryLicenseRemainDayData() {
  return request({
    url: '/systemmanagement/license/remain',
    method: 'get',
  })
}

export function queryQuickSearchData() {
  return request({
    url: '/menumanagement/menu/search',
    method: 'get',
  })
}
export function getSystemNameAndLogo() {
  return request({
    url: '/authentication/systemInfo',
    method: 'get',
  }).then((r) => {
    if (r.systemLogo) {
      localStorage.setItem('systemLogo', r.systemLogo)
      updateFaviconFromLogo(r.systemLogo)
    } else {
      localStorage.removeItem('systemLogo')
    }
    return r
  })
}
