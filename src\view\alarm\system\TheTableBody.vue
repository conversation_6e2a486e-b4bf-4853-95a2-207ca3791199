<!--
 * @Description: 系统告警 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-26
 * @Editor:
 * @EditDate: 2021-08-26
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @selection-change="clickSelectRows"
      >
        <el-table-column type="selection" prop="id" :selectable="selectable"></el-table-column>
        <el-table-column v-for="(item, key) in columns" :key="key" :prop="item" :label="$t(`alarm.system.table.${item}`)" show-overflow-tooltip>
          <template slot-scope="scope">
            <p v-if="item === 'isIgnore'">
              {{ columnText(scope.row[item], item) }}
            </p>
            <p v-else>
              {{ scope.row[item] }}
            </p>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      requied: true,
      type: Array,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      columns: ['alarmName', 'alarmRole', 'isIgnore', 'enterDate', 'alarmDesc'],
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  methods: {
    clickSelectRows(sel) {
      this.$emit('on-select', sel)
    },
    selectable(row) {
      return row.isIgnore !== 1
    },
  },
}
</script>
