<!--
 * @Description: 区域管理 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-06-29
 * @Editor:
 * @EditDate: 2021-06-29
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model.trim="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('asset.area.prop.domaName')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'add'" @click="clickAdd">
          {{ $t('button.add') }}
        </el-button>
        <el-button v-has="'delete'" @click="clickBatchDelete">
          {{ $t('button.batch.delete') }}
        </el-button>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model="filterCondition.form.domaName"
                clearable
                :placeholder="$t('asset.area.prop.domaName')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-input
                v-model="filterCondition.form.domaAbbreviation"
                clearable
                :placeholder="$t('asset.area.prop.domaAbbr')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col align="right" :offset="10" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      debounce: null,
    }
  },
  watch: {
    condition(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:condition', newCondition)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        domaName: '',
        domaAbbreviation: '',
      }
      this.changeQueryCondition()
    },
    clickAdd() {
      this.$emit('on-add')
    },
    clickBatchDelete() {
      this.$emit('on-batch-delete')
    },
  },
}
</script>
