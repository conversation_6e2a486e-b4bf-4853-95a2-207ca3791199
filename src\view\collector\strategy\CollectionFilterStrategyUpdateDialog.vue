<!--
 * @Description: 采集器过滤策略 - 修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item prop="policyName" :label="form.info.policyName.label">
            <el-input v-model.trim="form.model.policyName" maxlength="128"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="levelList" :label="form.info.levelList.label">
            <el-select v-model="form.model.levelList" clearable multiple collapse-tags :placeholder="$t('collector.strategy.placeholder.level')">
              <el-option v-for="item in levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="form.info.eventTypeList.label" :prop="form.info.eventTypeList.key">
            <el-cascader
              v-model="form.model.eventTypeList"
              :options="eventTypeOption"
              :props="{ expandTrigger: 'hover', multiple: true }"
              filterable
              collapse-tags
              :placeholder="$t('collector.strategy.placeholder.eventType')"
            ></el-cascader>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="form.info.srcIpv.label" :prop="form.info.srcIpv.key">
            <range-picker
              v-model="form.model.srcIpv"
              type="ip"
              :start-placeholder="$t('collector.strategy.placeholder.srcStartIP')"
              :end-placeholder="$t('collector.strategy.placeholder.srcEndIP')"
            ></range-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item :label="form.info.dstIpv.label" :prop="form.info.dstIpv.key">
            <range-picker
              v-model="form.model.dstIpv"
              type="ip"
              :start-placeholder="$t('event.original.placeholder.dstStartIP')"
              :end-placeholder="$t('event.original.placeholder.dstEndIP')"
            ></range-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="24">
          <el-form-item prop="describe" :label="form.info.describe.label">
            <el-input v-model="form.model.describe" type="textarea" maxlength="2000" :rows="4"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import RangePicker from '@comp/RangePicker'
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import { validateIp, validateIpv4, validateIpv6 } from '@util/validate'

export default {
  components: {
    CustomDialog,
    RangePicker,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    actions: {
      type: Boolean,
      default: true,
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    levelOption: {
      required: true,
      type: Array,
    },
    eventTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        const formValid = this.validateStrategyForm()
        if (valid && formValid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const params = Object.assign({}, this.form.model, {
              srcIpv: this.ipRange(this.form.model.srcIpv),
              dstIpv: this.ipRange(this.form.model.dstIpv),
            })
            this.$emit('on-submit', params)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    ipRange(ipArr) {
      let ip = ''
      const ipRange = ipArr.filter((item) => !isEmpty(item))
      if (ipRange.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    validateStrategyForm() {
      let formValid = false
      const formObj = Object.assign({}, this.form.model)
      const srcIPv = formObj.srcIpv.filter((item) => !isEmpty(item))
      const dstIpv = formObj.dstIpv.filter((item) => !isEmpty(item))
      const eventType = formObj.eventTypeList.filter((item) => !isEmpty(item))
      if (srcIPv.length > 0 && srcIPv.length !== 2) {
        prompt({
          i18nCode: 'validate.ip.rangeExist',
          type: 'warning',
        })
        formValid = false
      } else if (!this.ipValidate(srcIPv)) {
        prompt({
          i18nCode: 'validate.ip.checkRange',
          type: 'warning',
        })
        formValid = false
      } else if (dstIpv.length > 0 && dstIpv.length !== 2) {
        prompt({
          i18nCode: 'validate.ip.rangeExist',
          type: 'warning',
        })
        formValid = false
      } else if (!this.ipValidate(dstIpv)) {
        prompt({
          i18nCode: 'validate.ip.checkRange',
          type: 'warning',
        })
        formValid = false
      } else if (srcIPv.length !== 2 && dstIpv.length !== 2 && eventType.length <= 0 && formObj.levelList.length <= 0) {
        prompt({
          i18nCode: 'validate.filterCondition',
          type: 'warning',
        })
        formValid = false
      } else {
        formValid = true
      }
      return formValid
    },
    ipValidate(ip) {
      const [startIp, endIp] = [ip[0] ? ip[0] : null, ip[1] ? ip[1] : null]
      let validate = null

      if (startIp !== null && endIp === null) {
        validate = validateIp(startIp)
      } else if (startIp === null && endIp !== null) {
        validate = validateIp(endIp)
      } else if (startIp !== null && endIp !== null) {
        validate = (validateIpv4(startIp) && validateIpv4(endIp)) || (validateIpv6(startIp) && validateIpv6(endIp))
      } else {
        validate = true
      }
      return validate
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
  .el-cascader {
    .el-input {
      input::-webkit-input-placeholder {
        color: $CR;
      }
    }
  }
  .range-picker {
    width: 90%;
  }
}
</style>
