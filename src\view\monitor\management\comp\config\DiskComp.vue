<!--
 * @Description: 监控器配置 - 磁盘信息组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-03
 * @Editor:
 * @EditDate: 2021-08-03
-->
<template>
  <section>
    <el-form ref="diskForm" :model="diskModel" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="cpuUseRate" :label="$t('monitor.management.config.disk.diskUseRate')">
            <el-input v-model="diskModel.diskUseRate" maxlength="3" oninput="value=value.replace(/[^0-9]/g,'')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </section>
</template>
<script>
export default {
  props: {
    diskModel: {
      required: true,
      type: Object,
    },
  },
  data() {
    const diskUseRate = (rule, value, callback) => {
      if (value !== null && value !== '' && (value < 1 || value > 100)) {
        callback(new Error(this.$t('validate.monitor.useRate')))
      } else {
        callback()
      }
    }
    return {
      rules: {
        diskUseRate: [
          {
            validator: diskUseRate,
            trigger: 'blur',
          },
        ],
      },
    }
  },
  methods: {
    validateForm() {
      let validate = false
      this.$refs.diskForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    resetForm() {
      this.$refs.diskForm.resetFields()
    },
  },
}
</script>
