import request from '@util/request'

export function queryNetWork(obj) {
  return request(
    {
      url: '/config/network/configuration',
      method: 'get',
      params: obj || {},
    },
    'default',
    '180000'
  )
}

export function updateNetworkData(obj) {
  return request({
    url: '/config/network/configuration',
    method: 'post',
    data: obj || {},
  })
}

export function queryNetworkPanel() {
  return request({
    url: '/config/network/panel',
    method: 'get',
  })
}

export function testNetworkCard(id) {
  return request({
    url: `/config/network/checkCard/${id}`,
    method: 'get',
  })
}
