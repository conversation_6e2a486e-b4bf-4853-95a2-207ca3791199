const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    deviceId: '@ID',
    ip: '@ID',
    collectorName: '@NAME',
    protName: '@CNAME',
    runState: '@word(4)',
    'agentStatus|1': [0, 1],
    'useState|0-1': '1',
  },
})
const innerTypeOption = [
  {
    value: 'zhinan',
    label: '指南',
    children: [
      {
        value: 'yizhi',
        label: '一致',
      },
      {
        value: 'fankui',
        label: '反馈',
      },
      {
        value: 'xiaolv',
        label: '效率',
      },
      {
        value: 'kekong',
        label: '可控',
      },
    ],
  },
]

const filterData = [
  {
    label: 'mysql',
    value: '1',
  },
  {
    label: 'oracle',
    value: '2',
  },
]
const protocolsData = [
  {
    label: 'SNMP Trap',
    value: '101',
  },
  {
    label: 'Syslog',
    value: '102',
  },
]
const agents = [
  { value: '127.0.0.1', label: '127.0.0.1', type: '1' },
  { value: '**********', label: '**********', type: '0' }, // type=1正常，type=0代理下线状态
  { value: '***********', label: '***********', type: '1' },
]

const accessMode = [
  { label: 'Syslog', value: '101' },
  { label: 'SNMP Trap', value: '102' },
  { label: 'JDBC', value: '103' },
  { label: 'SSH', value: '104' },
  { label: 'WMI', value: '105' },
  { label: 'NetflowV5', value: '106' },
  { label: 'Sftp', value: '107' },
  { label: 'Ftp', value: '108' },
  { label: 'Netbios', value: '109' },
  { label: 'Kafka', value: '110' },
]

module.exports = [
  {
    url: '/collector/management/collectors',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: `/collector/management/collector/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/collector/management/combo/source-device-types',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: innerTypeOption,
      }
    },
  },
  {
    url: '/collector/management/collector',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/collector/management/combo/filter-strategies',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: filterData,
      }
    },
  },
  {
    url: '/collector/management/combo/collection-protocols',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: protocolsData,
      }
    },
  },
  {
    url: '/collector/management/collector',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/collector/management/combo/agents',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: agents,
      }
    },
  },
  {
    url: '/collector/management/uploadlog',
    type: 'post',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/collector/management/combo/collection-protocols',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: accessMode,
      }
    },
  },
  {
    url: '/collector/management/collector-checkip',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
]
