import request from '@util/request'

// 查询行为策略列表
export function queryBehaviorStrategyTable(obj) {
  return request({
    url: '/infosystemanomaly/queryConfigs',
    method: 'get',
    params: obj || {},
  })
}

// 查询事件类型下拉列表
export function queryEventTypeCombo(obj) {
  return request({
    url: '/infosystemanomaly/combo/event-types',
    method: 'get',
    params: obj || {},
  })
}

// 添加行为策略
export function addBehaviorStrategy(obj) {
  return request({
    url: '/infosystemanomaly/addConfig',
    method: 'post',
    data: obj || {},
  })
}

// 修改行为策略
export function updateBehaviorStrategy(obj) {
  return request({
    url: '/infosystemanomaly/updateConfig',
    method: 'put',
    data: obj || {},
  })
}

// 删除行为策略
export function deleteBehaviorStrategy(ids) {
  return request({
    url: `/infosystemanomaly/deleteConfigs/${ids}`,
    method: 'delete',
  })
}

// 批量启用行为策略
export function startStrategyStatus(ids) {
  return request({
    url: `/infosystemanomaly/startConfigs/${ids}`,
    method: 'put',
  })
}

// 批量停用行为策略
export function stopStrategyStatus(ids) {
  return request({
    url: `/infosystemanomaly/stopConfigs/${ids}`,
    method: 'put',
  })
}

// 查询转发外系统下拉列表
export function queryExternalSystemCombo() {
  return request({
    url: '/infosystemanomaly/combo/forward-relay-way',
    method: 'get',
  })
}
