<template>
  <section class="effect-char-rain">
    <span
      v-for="index in charNum"
      :key="index"
      :style="{
        left: random(0, 100, '%'),
        fontSize: random(100, 200, '%'),
        animationDelay: random(0, 4, 's'),
        animationDuration: random(3, 5, 's'),
      }"
      class="char-chalk"
    >
      {{ randomChar(1) }}
    </span>
  </section>
</template>

<script>
import { randomNum, randomWord } from '@util/random'

export default {
  name: 'CharRainEffect',
  components: {},
  props: {
    charNum: {
      type: Number,
      default: 50,
    },
  },
  computed: {
    random() {
      return (min, max, unit = '') => {
        return `${randomNum(min, max)}${unit}`
      }
    },
    randomChar() {
      return (min) => {
        return randomWord(false, min)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.effect-char-rain {
  width: 100%;
  height: 100%;

  .char-chalk {
    position: absolute;
    top: -30%;
    z-index: -1;
    font-family: Arial, Helvetica, sans-serif;
    color: #257fe0;
    background-color: transparent;
    animation: char-rain-effect 4s infinite linear;
    filter: blur(1px);
    text-shadow: 1px 1px 20px #257fe0;
  }

  @keyframes char-rain-effect {
    0% {
      top: -10%;
      opacity: 0.6;
    }
    30% {
      opacity: 1;
    }
    100% {
      opacity: 0.9;
      top: 110%;
    }
  }
}
</style>
