<!--
 * @Description: 威胁事件 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <custom-dialog ref="dialogDom" :visible="visible" :title="titleName" width="70%" @on-close="clickCancel">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane :label="$t('event.threat.panel.detail')" name="first">
        <section>
          <el-form :model="model" label-width="120px">
            <el-row>
              <el-col v-for="(itemCol, colIndex) in detailColumns" :key="colIndex" :span="itemCol.key === 'eventDesc' ? 24 : 12">
                <el-form-item :label="itemCol.label">
                  <template>
                    <level-tag v-if="itemCol.key === 'eventLevel'" :level="model[itemCol.key]"></level-tag>
                    <p v-else>
                      {{ model[itemCol.key] }}
                    </p>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </section>
      </el-tab-pane>
      <el-tab-pane :label="$t('event.threat.panel.original')" name="second">
        <section class="router-wrap-table">
          <section class="table-body">
            <el-table
              v-loading="table.loading"
              :data="table.data"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              infinite-scroll-disabled="disableScroll"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="350"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column prop="type2Name" :label="$t('event.threat.detailColumns.type2Name')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="eventName" :label="$t('event.threat.detailColumns.eventName')"></el-table-column>
              <el-table-column prop="eventCategoryName" :label="$t('event.threat.detailColumns.eventCategoryName')"></el-table-column>
              <el-table-column prop="level" :label="$t('event.threat.detailColumns.level')">
                <template slot-scope="scope">
                  <level-tag :level="scope.row.level"></level-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sourceIp" :label="$t('event.threat.detailColumns.srcIp')"></el-table-column>
              <el-table-column prop="targetIp" :label="$t('event.threat.detailColumns.dstIp')"></el-table-column>
              <el-table-column prop="time" width="140" :label="$t('event.threat.detailColumns.dateTime')"></el-table-column>
              <el-table-column width="60">
                <template slot-scope="scope">
                  <el-button v-has="'query'" class="el-button--blue" @click="clickDetailDrawer(scope.row)">
                    {{ $t('button.detail') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </section>
        <footer class="table-footer infinite-scroll">
          <section class="infinite-scroll-nomore">
            <span v-show="table.nomore">{{ $t('validate.data.nomore') }}</span>
            <i v-show="table.totalLoading" class="el-icon-loading"></i>
          </section>
          <section v-show="!table.totalLoading" class="infinite-scroll-total">
            <b>{{ $t('event.original.total') + ':' }}</b>
            <span>{{ table.total }}</span>
          </section>
        </footer>
      </el-tab-pane>
    </el-tabs>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import CustomDialog from '@comp/CustomDialog'
import levelTag from '@comp/LevelTag'
import { debounce } from '@util/effect'
import { queryOriginalLog, queryOriginalTotal } from '@api/event/threat-api'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    CustomDialog,
    levelTag,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
    actions: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'first',
      debounce: null,
      table: {
        loading: false,
        scroll: true,
        data: [],
        nomore: false,
        totalLoading: false,
        total: 0,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      },
      detailColumns: [
        { key: 'eventTypeName', label: this.$t('event.threat.eventType') },
        { key: 'eventLevel', label: this.$t('event.threat.eventLevel') },
        { key: 'receiveTime', label: this.$t('event.threat.receiveTime') },
        { key: 'eventTime', label: this.$t('event.threat.eventTime') },
        { key: 'eventDesc', label: this.$t('event.threat.eventDesc') },
      ],
    }
  },
  computed: {
    rules() {
      return this.validate ? this.model.rules : null
    },
    disableScroll() {
      return this.table.scroll
    },
  },
  watch: {
    visible(nVal) {
      if (nVal) this.initDebounce()
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initDebounce() {
      this.debounce = debounce(() => {
        this.queryOriginalLogData(this.handleParams())
        this.queryOriginalLogTotal(this.handleParams())
      }, 200)
    },
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
      this.activeName = 'first'
      this.table = {
        loading: false,
        scroll: true,
        data: [],
        nomore: false,
        totalLoading: false,
        total: 0,
      }
    },
    clickDetailDrawer(row) {
      this.$emit('on-drawer-show', row)
    },
    handleClick({ name }) {
      if (name === 'second') {
        if (this.table.data.length > 0) return
        this.table.data = []
        this.debounce()
      } else {
        this.table = {
          loading: false,
          scroll: true,
          data: [],
          nomore: false,
          totalLoading: false,
          total: 0,
        }
      }
    },
    handleParams() {
      return Object.assign(
        {},
        {
          originalId: this.model.originalId,
          receiveTime: this.model.receiveTime,
        }
      )
    },
    queryOriginalLogData(params = {}) {
      this.table.scroll = true
      this.table.loading = true
      queryOriginalLog(params.originalId, params.receiveTime).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.table.data.push(...res)
          this.table.scroll = true
          if (this.table.data.length > this.pagination.pageSize) {
            this.table.nomore = true
          }
        } else {
          this.table.data.push(...res)
          this.table.scroll = false
        }
        this.table.loading = false
      })
    },
    queryOriginalLogTotal(params = {}) {
      this.table.totalLoading = true
      queryOriginalTotal(params.originalId, params.receiveTime).then((res) => {
        this.table.totalLoading = false
        this.table.total = res
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}

::v-deep .el-popover,
.el-popper {
  > p {
    overflow: scroll;
  }
}

::v-deep .el-dialog__body {
  min-height: 500px;
}

::v-deep .table-body {
  min-height: 350px;
  max-height: 350px;
}

::v-deep .router-wrap-table {
  position: relative;
  ::v-deep .table-footer {
    position: absolute;
    right: 10px;
    bottom: 0;
  }
}
</style>
