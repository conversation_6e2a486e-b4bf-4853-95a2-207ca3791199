const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    monitorId: '@ID',
    monitorName: '@NAME',
    monitorType: 'NETEYE_FIREWALL',
    'monitorTypeName|1': ['防火墙', 'MySQL', '联想网御入侵检测系统'],
    agentId: '127.0.0.1',
    edName: '@IP',
    edIp: '@IP',
    pollDate: '5',
    'agentStatus|1': [0, 1],
    'monitorEnabled|1': ['0', '1'],
    createUser: '@NAME',
    createDate: '@DATETIME',
    agentIp: '10',
    assetName: '********',
    authPwd: '22',
    'authWay|1': [-1, 1, 2],
    context: '22',
    contextName: 'aa',
    cpuTimes: 3,
    cpuUseRate: 80,
    createPid: 'sysmanager',
    deviceStatus: null,
    diskUseRate: 80,
    edId: '10',
    encryptionPwd: 'sdf',
    'encryptionWay|1': [-1, 1, 2, 3, 4, 5],
    enterDate: '@DATETIME',
    errorDesc: null,
    keyIp: '********',
    memoryTimes: 3,
    memoryUseRate: 80,
    monitorMemo: 11,
    readCommunity: 'aa',
    snmpPort: 7,
    snmpUserName: 1,
    snmpVersion: '1',
    timeout: 1,
    writeCommunity: 'aa',
    edDeviceClass: '1',
    edDeviceType: '109',
    'noData|1': [0, 1, 2],
    'deviceStatus|1': [0, 1],
    'snmpVersionName|1': ['SNMP  V1', 'SNMP V2c', 'SNMP V3'],
  },
})

// 监控器类型
const typeList = [
  {
    children: [
      { value: 'MYSQL', label: 'MySQL', type: '1', children: null },
      { value: 'ORACLE', label: 'Oracle', type: '1', children: null },
      { value: 'DB2', label: 'DB2', type: '1', children: null },
    ],
    label: '数据库',
    type: null,
    value: '1',
  },
  {
    children: [
      { value: 'LENOVOVPN', label: '联想VPN', type: '2', children: null },
      { value: 'NETEYE_FIREWALL', label: '防火墙', type: '2', children: null },
      { value: 'LENOVOIDS', label: '联想网御入侵检测系统', type: '2', children: null },
    ],
    label: '安全设备',
    type: null,
    value: '2',
  },
  {
    children: [
      { value: 'WINDOWS', label: 'Windows', type: '3', children: null },
      { value: 'LINUX', label: 'Linux', type: '3', children: null },
    ],
    label: '操作系统',
    type: null,
    value: '3',
  },
  {
    children: [
      { value: 'NE_CISCO', label: '思科网络设备', type: '4', children: null },
      { value: 'H3C', label: '华三网络设备', type: '4', children: null },
    ],
    label: '网络设备',
    type: null,
    value: '4',
  },
]

const agents = [
  { value: '127.0.0.1', label: '127.0.0.1' },
  { value: '10.9.224.5', label: '10.9.224.5' },
  { value: '10.9.226.12', label: '10.9.226.12' },
]

// 被监控资产下拉框
const assetType = [
  {
    value: '1',
    label: '网络设备',
    type: null,
    children: [
      {
        value: '109',
        label: '流量控制',
        type: '1',
        children: [
          { value: '10', label: '设备1(10.9.224.2)', type: '1', children: null },
          { value: '11', label: '设备2(10.9.224.3)', type: '1', children: null },
        ],
      },
      { value: '108', label: 'UTM', type: '1', children: null },
      { value: '107', label: '路由器', type: '1', children: null },
      { value: '106', label: 'VPN', type: '1', children: null },
      { value: '104', label: '集线器', type: '1', children: null },
      { value: '103', label: '三层交换机', type: '1', children: null },
      { value: '102', label: '核心交换机', type: '1', children: null },
    ],
  },
  {
    value: '2',
    label: '安全设备',
    type: null,
    children: [
      {
        value: '203',
        label: '网络审计系统',
        type: '2',
        children: [
          { value: '12', label: '设备3(10.9.224.2)' },
          { value: '13', label: '设备4(10.9.224.3)' },
        ],
      },
      { value: '202', label: '病毒监测系统', type: '2', children: null },
      { value: '201', label: '入侵检测系统IDS', type: '2', children: null },
    ],
  },
  {
    value: '3',
    label: '服务器',
    type: null,
    children: [
      { value: '303', label: '应用服务器', type: '3', children: null },
      { value: '302', label: '监控设备管理中心', type: '3', children: null },
      { value: '301', label: '监控设备控制台', type: '3', children: null },
      { value: '500', label: '工作站', type: '3', children: null },
      { value: '300', label: '服务器-未知', type: '3', children: null },
    ],
  },
]

const monitorComp = 'cpu,memory,disk,snmp'

module.exports = [
  // 查询监控器类型
  {
    url: '/monitormanagement/combo/types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: typeList,
      }
    },
  },
  // 查询监控器列表
  {
    url: '/monitormanagement/queryMonitors',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  // (批量)删除监控器
  {
    url: `/monitormanagement/deleteMonitors/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'monitorId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  // (批量)停用监控器
  {
    url: `/monitormanagement/stopMonitors/[A-Za-z0-9]`,
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  // (批量)启用监控器
  {
    url: `/monitormanagement/startMonitors/[A-Za-z0-9]`,
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 查询代理服务器下拉框
  {
    url: '/monitormanagement/combo/agents',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: agents,
      }
    },
  },
  // 查询被监控资产类型
  {
    url: `/monitormanagement/monitorAssets`,
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: assetType,
      }
    },
  },
  // 查询监控器组件信息
  {
    url: `/monitormanagement/compInfo/`,
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: monitorComp,
      }
    },
  },
  // 添加监控器
  {
    url: '/monitormanagement/addMonitor',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 'success',
      }
    },
  },
  // 修改监控器
  {
    url: '/monitormanagement/updateMonitor',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'monitorId')
      return {
        code: 200,
        data: 'success',
      }
    },
  },
]
