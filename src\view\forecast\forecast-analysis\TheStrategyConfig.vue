<!--
 * @Description: 策略配置
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-12-27
 * @Editor:
 * @EditDate: 2022-12-27
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.strategyConfig', [titleName])"
    :width="width"
    @on-close="doCancel"
    @on-submit="doSubmit"
  >
    <el-form ref="formTemplate" :model="model" :rules="rules" label-width="120px">
      <section>
        <el-row>
          <el-form-item prop="status" :label="$t('forecast.forecastAnalysis.label.funcEnable')">
            <el-switch v-model="model.status" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="externalSystem" :label="$t('forecast.forecastAnalysis.label.externalSystem')">
            <el-select v-model="model.externalSystem" filterable multiple collapse-tags clearable>
              <el-option v-for="item in option.externalSystem" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="externalMail" :label="$t('forecast.forecastAnalysis.label.externalMail')">
            <el-input v-model="model.externalMail" maxlength="64"></el-input>
          </el-form-item>
        </el-row>
      </section>
    </el-form>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { validateEmail } from '@util/validate'
import { queryExternalSystem } from '@api/forecast/forecast-analysis-api'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    width: {
      type: String,
      default: '800',
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
      default: () => {},
    },
    actions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value !== '' && !validateEmail(value)) {
        callback(new Error(this.$t('validate.email')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: this.visible,
      rules: {
        externalMail: [
          {
            trigger: 'blur',
            validator: validatorEmail,
          },
        ],
      },
      option: {
        externalSystem: [],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.getExternalSystemOption()
  },
  methods: {
    doCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    doSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$emit('on-submit', this.model)
          this.doCancel()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    getExternalSystemOption() {
      queryExternalSystem().then((res) => {
        this.option.externalSystem = res
      })
    },
  },
}
</script>
