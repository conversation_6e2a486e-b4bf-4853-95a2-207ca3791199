<!--
 * @Description: 等级标签
 * @Version: 1.1.0
 * @Author:
 * @Date: 2020/8/30
 * @Editor:
 * @EditDate: 2021/9/3
-->
<template>
  <div class="level-tag-container">
    <template v-if="level.constructor === String || level.constructor === Number">
      <el-tag :class="levelClassColor(level).className" :style="{ cursor }" @click="clickLevelTag(level)">
        {{ levelClassColor(level).innerText }}
      </el-tag>
    </template>
    <template v-else>
      <el-tag v-for="item in level" :key="item" :class="levelClassColor(item).className" :style="{ cursor }" @click="clickLevelTag(item)">
        {{ levelClassColor(item).innerText }}
      </el-tag>
    </template>
  </div>
</template>

<script>
import i18n from '@/language'

export default {
  props: {
    level: {
      required: true,
      type: [String, Array, Number],
    },
    category: {
      type: String,
      default: 'event',
    },
    simple: {
      type: Boolean,
      default: false,
    },
    cursor: {
      type: String,
      default: '',
    },
  },
  computed: {
    levelClassColor() {
      return (value) => {
        return this.handleLevelObj(value)
      }
    },
  },
  methods: {
    clickLevelTag(level) {
      this.$emit('on-click', level)
    },
    handleLevelObj(value) {
      const levelObj = {}

      if (this.category === 'event') {
        this.handleEventLevel(levelObj, value)
      }

      if (this.category === 'risk') {
        this.handleRiskLevel(levelObj, value)
      }

      if (this.category === 'vul') {
        this.handleVulLevel(levelObj, value)
      }

      if (this.category === 'value') {
        this.handleValueLevel(levelObj, value)
      }

      if (this.category === 'alarm') {
        this.handleAlarmLevel(levelObj, value)
      }

      if (this.category === 'dga') {
        this.handleDgaLevel(levelObj, value)
      }

      return levelObj
    },
    handleEventLevel(obj, value) {
      switch (value) {
        case 0:
        case '0':
          obj.className = this.simple ? 'el-tag-simple--red' : 'el-tag--red'
          obj.innerText = i18n.t('code.level.event.l5')
          break
        case 1:
        case '1':
          obj.className = this.simple ? 'el-tag-simple--orange' : 'el-tag--orange'
          obj.innerText = i18n.t('code.level.event.l4')
          break
        case 2:
        case '2':
          obj.className = this.simple ? 'el-tag-simple--yellow' : 'el-tag--yellow'
          obj.innerText = i18n.t('code.level.event.l3')
          break
        case 3:
        case '3':
          obj.className = this.simple ? 'el-tag-simple--blue' : 'el-tag--blue'
          obj.innerText = i18n.t('code.level.event.l2')
          break
        case 4:
        case '4':
          obj.className = this.simple ? 'el-tag-simple--green' : 'el-tag--green'
          obj.innerText = i18n.t('code.level.event.l1')
          break
        default:
          obj.className = 'el-tag--empty'
          obj.innerText = i18n.t('code.level.l0')
          break
      }
    },
    handleRiskLevel(obj, value) {
      switch (value) {
        case 5:
        case '5':
          obj.className = this.simple ? 'el-tag-simple--red' : 'el-tag--red'
          obj.innerText = i18n.t('code.level.risk.l5')
          break
        case 4:
        case '4':
          obj.className = this.simple ? 'el-tag-simple--orange' : 'el-tag--orange'
          obj.innerText = i18n.t('code.level.risk.l4')
          break
        case 3:
        case '3':
          obj.className = this.simple ? 'el-tag-simple--yellow' : 'el-tag--yellow'
          obj.innerText = i18n.t('code.level.risk.l3')
          break
        case 2:
        case '2':
          obj.className = this.simple ? 'el-tag-simple--blue' : 'el-tag--blue'
          obj.innerText = i18n.t('code.level.risk.l2')
          break
        case 1:
        case '1':
          obj.className = this.simple ? 'el-tag-simple--green' : 'el-tag--green'
          obj.innerText = i18n.t('code.level.risk.l1')
          break
        default:
          obj.className = 'el-tag--empty'
          obj.innerText = i18n.t('code.level.l0')
          break
      }
    },
    handleVulLevel(obj, value) {
      switch (value) {
        case 3:
        case '3':
          obj.className = this.simple ? 'el-tag-simple--orange' : 'el-tag--orange'
          obj.innerText = i18n.t('code.level.vul.l3')
          break
        case 2:
        case '2':
          obj.className = this.simple ? 'el-tag-simple--yellow' : 'el-tag--yellow'
          obj.innerText = i18n.t('code.level.vul.l2')
          break
        case 1:
        case '1':
          obj.className = this.simple ? 'el-tag-simple--blue' : 'el-tag--blue'
          obj.innerText = i18n.t('code.level.vul.l1')
          break
        default:
          obj.className = 'el-tag--empty'
          obj.innerText = i18n.t('code.level.l0')
          break
      }
    },
    handleValueLevel(obj, value) {
      switch (value) {
        case 5:
        case '5':
          obj.className = this.simple ? 'el-tag-simple--red' : 'el-tag--red'
          obj.innerText = i18n.t('code.level.value.l5')
          break
        case 4:
        case '4':
          obj.className = this.simple ? 'el-tag-simple--orange' : 'el-tag--orange'
          obj.innerText = i18n.t('code.level.value.l4')
          break
        case 3:
        case '3':
          obj.className = this.simple ? 'el-tag-simple--yellow' : 'el-tag--yellow'
          obj.innerText = i18n.t('code.level.value.l3')
          break
        case 2:
        case '2':
          obj.className = this.simple ? 'el-tag-simple--blue' : 'el-tag--blue'
          obj.innerText = i18n.t('code.level.value.l2')
          break
        case 1:
        case '1':
          obj.className = this.simple ? 'el-tag-simple--green' : 'el-tag--green'
          obj.innerText = i18n.t('code.level.value.l1')
          break
        default:
          obj.className = 'el-tag--empty'
          obj.innerText = i18n.t('code.level.l0')
          break
      }
    },
    handleAlarmLevel(obj, value) {
      switch (value) {
        case 5:
        case '5':
          obj.className = this.simple ? 'el-tag-simple--red' : 'el-tag--red'
          obj.innerText = i18n.t('code.level.alarm.l5')
          break
        case 4:
        case '4':
          obj.className = this.simple ? 'el-tag-simple--orange' : 'el-tag--orange'
          obj.innerText = i18n.t('code.level.alarm.l4')
          break
        case 3:
        case '3':
          obj.className = this.simple ? 'el-tag-simple--yellow' : 'el-tag--yellow'
          obj.innerText = i18n.t('code.level.alarm.l3')
          break
        case 2:
        case '2':
          obj.className = this.simple ? 'el-tag-simple--blue' : 'el-tag--blue'
          obj.innerText = i18n.t('code.level.alarm.l2')
          break
        case 1:
        case '1':
          obj.className = this.simple ? 'el-tag-simple--green' : 'el-tag--green'
          obj.innerText = i18n.t('code.level.alarm.l1')
          break
        default:
          obj.className = 'el-tag--empty'
          obj.innerText = i18n.t('code.level.l0')
          break
      }
    },
    handleDgaLevel(obj, value) {
      switch (value) {
        case 1:
        case '1':
          obj.className = this.simple ? 'el-tag-simple--orange' : 'el-tag--orange'
          obj.innerText = i18n.t('code.level.dga.l1')
          break
        case 0:
        case '0':
          obj.className = this.simple ? 'el-tag-simple--blue' : 'el-tag--blue'
          obj.innerText = i18n.t('code.level.dga.l0')
          break
        default:
          obj.className = 'el-tag--empty'
          obj.innerText = i18n.t('code.level.l0')
          break
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.level-tag-container {
  display: inline-block;

  .el-tag--empty {
    padding: 0;
    background-color: $CR;
    border: none;
    @include theme('color', table-body-color);
  }

  & + & {
    margin-left: 5px;
  }

  span + span {
    margin-left: 5px;
  }

  .el-tag.el-tag--red {
    color: $WT;
    background-color: $SERIOUS;
    border-color: $danger-hover-color;
  }

  .el-tag.el-tag--orange {
    color: $WT;
    background-color: $HIGH;
    border-color: $warning-hover-color;
  }

  .el-tag.el-tag--yellow {
    color: $WT;
    background-color: $MIDDLE;
    border-color: rgba($warning-border-color, 0.5);
  }

  .el-tag.el-tag--green {
    color: $WT;
    background-color: $GENERAL;
    border-color: $success-hover-color;
  }

  .el-tag.el-tag--blue {
    color: $WT;
    background-color: $LOW;
    border-color: $primary-hover-color;
  }

  .el-tag.el-tag-simple--red {
    color: $SERIOUS;
    border: none;
    background: none;
  }

  .el-tag.el-tag-simple--orange {
    color: $HIGH;
    border: none;
    background: none;
  }

  .el-tag.el-tag-simple--yellow {
    color: $MIDDLE;
    border: none;
    background: none;
  }

  .el-tag.el-tag-simple--green {
    color: $GENERAL;
    border: none;
    background: none;
  }

  .el-tag.el-tag-simple--blue {
    color: $LOW;
    border: none;
    background: none;
  }
}
</style>
