<!--
 * @Description: 资源管理 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div>
    <custom-dialog
      ref="dialogTemplate"
      :visible="visible"
      :title="title"
      :readonly="readonly"
      :width="width"
      @on-close="clickCancelDialog"
      @on-submit="clickSubmitForm('resourceForm')"
    >
      <el-form ref="resourceForm" class="dialog-form" label-width="20%" :rules="form.rules" :model="form.model">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('management.resource.infoItem.resourceToken')" class="dialog-form-list" prop="resourceToken">
              <el-input v-model="form.model.resourceToken"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('management.resource.infoItem.resourceName')" prop="resourceName">
              <el-input v-model="form.model.resourceName"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="$t('management.resource.infoItem.resourceStatus')" prop="resourceStatus">
              <el-select v-model="form.model.resourceStatus" :placeholder="$t('management.resource.header.placeholder')">
                <el-option :label="$t('management.resource.codeList.resourceStatus.show')" value="0"></el-option>
                <el-option :label="$t('management.resource.codeList.resourceStatus.hide')" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('management.resource.infoItem.authority')" prop="authorities">
              <el-select v-model="form.model.authorities" multiple collapse-tags :placeholder="$t('management.resource.header.placeholder')">
                <el-option v-for="item in authority" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="23">
            <el-form-item :label="$t('management.resource.infoItem.resourceDescription')" label-width="10%">
              <el-input v-model="form.model.resourceDescription" type="textarea" :rows="2" class="width-max"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <div class="router-wrap-table">
              <section class="table-header">
                <section class="table-header-query"></section>
                <section class="table-header-button">
                  <el-button v-has="'add'" size="mini" @click="handleActionAdd">
                    {{ $t('button.add') }}
                  </el-button>
                </section>
              </section>
              <section class="table-body">
                <el-table :data="form.model.actions" highlight-current-row tooltip-effect="light" height="300">
                  <el-table-column prop="actionName" :label="$t('management.resource.infoItem.actionName')" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="actionToken" :label="$t('management.resource.infoItem.actionToken')" show-overflow-tooltip></el-table-column>
                  <el-table-column
                    prop="actionStatusText"
                    :label="$t('management.resource.infoItem.actionStatus')"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column
                    prop="actionDescription"
                    :label="$t('management.resource.infoItem.actionDescription')"
                    show-overflow-tooltip
                  ></el-table-column>
                  <el-table-column fixed="right" width="200">
                    <template slot-scope="scope">
                      <el-button v-has="'update'" class="el-button--blue" @click="handleActionUpdate(scope.$index, scope.row)">
                        {{ $t('button.update') }}
                      </el-button>
                      <el-button v-has="'delete'" class="el-button--red" @click="handleActionDelete(scope.$index, scope.row)">
                        {{ $t('button.delete') }}
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </section>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template v-if="!actions" slot="action">
        <fragment></fragment>
      </template>
    </custom-dialog>
    <!--  添加、修改功能弹窗  -->
    <el-dialog v-el-dialog-drag :title="actionDialog.title" :close-on-click-modal="false" width="35%" :visible.sync="actionDialog.visible">
      <el-form ref="actionForm" class="dialog-form" label-width="25%" :rules="actionForm.rules" :model="actionForm.data">
        <el-row>
          <el-col>
            <el-form-item :label="$t('management.resource.infoItem.actionName')" prop="actionName">
              <el-input v-model="actionForm.data.actionName" class="width-mini"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item :label="$t('management.resource.infoItem.actionToken')" prop="actionToken">
              <el-select v-model="actionForm.data.actionToken" :placeholder="$t('management.resource.header.placeholder')" class="width-mini">
                <el-option v-for="item in form.actionTokens" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item :label="$t('management.resource.infoItem.actionStatus')" prop="actionStatus">
              <el-select
                ref="actionStatus"
                v-model="actionForm.data.actionStatus"
                :placeholder="$t('management.resource.header.placeholder')"
                class="width-mini"
              >
                <el-option :label="$t('management.resource.codeList.actionStatus.show')" value="0"></el-option>
                <el-option :label="$t('management.resource.codeList.actionStatus.hide')" value="1"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col>
            <el-form-item :label="$t('management.resource.infoItem.actionDescription')">
              <el-input v-model="actionForm.data.actionDescription" type="textarea" :rows="2" class="width-mini"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <footer slot="footer">
        <el-button @click="handleActionCancel('actionForm')">
          {{ $t('button.cancel') }}
        </el-button>
        <el-button v-if="actionDialog.status === 'add'" @click="handleActionAddSubmit('actionForm')">
          {{ $t('button.determine') }}
        </el-button>
        <el-button v-if="actionDialog.status === 'update'" @click="handleActionUpdSubmit('actionForm')">
          {{ $t('button.determine') }}
        </el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import elDialogDrag from '@/directive/el-dialog-drag'

export default {
  directives: {
    elDialogDrag,
  },
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    actions: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      authority: [
        {
          value: 0,
          label: this.$t('management.resource.authority.system'),
        },
        {
          value: 1,
          label: this.$t('management.resource.authority.running'),
        },
        {
          value: 2,
          label: this.$t('management.resource.authority.audit'),
        },
      ],
      // 功能相关
      actionForm: {
        rules: {
          actionName: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          actionToken: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          actionStatus: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
        data: {
          actionId: '',
          actionName: '',
          actionToken: '',
          actionStatus: '',
          actionStatusText: '',
          actionDescription: '',
        },
      },
      // 功能添加修改的对话框
      actionDialog: {
        visible: false, // 控制对话框的显隐
        status: '', // 控制对话框是修改还是添加
        title: '', // 控制对话框的标题
        inputReadonly: false, // 输入框是否禁用
      },
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 提交功能更新表单
    handleActionUpdSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.actionForm.data.actionStatusText = this.$refs.actionStatus.selectedLabel
          this.form.model.actions.forEach((val, i) => {
            if (val.actionId + '' === this.actionForm.data.actionId + '') {
              Object.assign(val, this.actionForm.data)
            }
          })
          this.actionDialog.visible = false
          this.$refs[formName].resetFields()
        } else {
          return false
        }
      })
    },
    // 提交功能添加
    handleActionAddSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          const date = new Date()
          this.actionForm.data.actionId = date.getTime().toString()
          this.actionForm.data.actionStatusText = this.$refs.actionStatus.selectedLabel
          const param = Object.assign({}, this.actionForm.data)
          this.form.model.actions.push(param)
          this.actionDialog.visible = false
          this.$refs[formName].resetFields()
        } else {
          return false
        }
      })
    },
    // 取消功能操作
    handleActionCancel(formName) {
      this.actionDialog.visible = false
      this.$refs[formName].resetFields()
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 功能添加
    handleActionAdd() {
      this.actionDialog = {
        visible: true,
        status: 'add',
        title: this.$t('dialog.title.add', [this.$t('management.resource.header.innerDialogTitle')]),
      }
      this.actionForm.data = {
        actionId: '',
        actionName: '',
        actionToken: '',
        actionStatus: '',
        actionStatusText: '',
        actionDescription: '',
      }
    },
    // 功能修改
    handleActionUpdate(index, row) {
      this.actionDialog = {
        visible: true,
        status: 'update',
        title: this.$t('dialog.title.update', [this.$t('management.resource.header.innerDialogTitle')]),
      }
      this.actionForm.data = Object.assign({}, row)
    },
    // 功能删除
    handleActionDelete(index, row) {
      this.form.model.actions.splice(index, 1)
    },
    // 点击提交表单
    clickSubmitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.handleFormInfo()
            // 给父级调用数据
            this.$emit('on-submit', this.form.model, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.error',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    // 将绑定信息同步到formInfo中，供以后查询组件使用
    handleFormInfo() {
      Object.getOwnPropertyNames(this.form.model).forEach((modelKey) => {
        Object.getOwnPropertyNames(this.form.info).forEach((item) => {
          if (this.form.info[item]['key'] === modelKey) {
            this.form.info[item]['value'] = this.form.model[modelKey]
          }
        })
      })
    },
  },
}
</script>
<style scoped lang="scss">
.router-wrap-table {
  .table-header {
    background-color: $CR;
    border-top: 1px solid;
    border-bottom: 1px solid;
    @include theme('border-color', table-body-title-border-color);
  }

  ::v-deep .el-table--small {
    td,
    th {
      padding: 4px 0;
    }
    .el-table__body-wrapper {
      min-height: 124px;
    }
  }
}
</style>
