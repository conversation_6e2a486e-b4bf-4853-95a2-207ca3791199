import request from '@util/request'

export function addMenuData(obj) {
  return request({
    url: '/menumanagement/menu',
    method: 'post',
    data: obj || {},
  })
}

export function deleteMenuData(menuId) {
  return request({
    url: `/menumanagement/menu/${menuId}`,
    method: 'delete',
  })
}

export function updateMenuData(obj) {
  return request({
    url: '/menumanagement/menu',
    method: 'put',
    data: obj || {},
  })
}

export function queryMenuTableData(obj) {
  return request({
    url: '/menumanagement/menus',
    method: 'get',
    params: obj || {},
  })
}

export function queryMenuDetailData(menuId) {
  return request({
    url: `/menumanagement/menu/${menuId}`,
    method: 'get',
  })
}

export function queryParentMenuData(obj) {
  return request({
    url: `/menumanagement/parent-menus/${obj.level}/${obj.leaf}`,
    method: 'get',
    params: obj || {},
  })
}

export function queryResourceOptionData() {
  return request({
    url: '/menumanagement/resource-combo',
    method: 'get',
  })
}

export function sortMenuData(obj) {
  return request({
    url: '/menumanagement/menu/sort',
    method: 'put',
    data: obj || {},
  })
}
