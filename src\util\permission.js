import cookie from 'js-cookie'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import router from '@/router'
import store from '@/store'

const routerWhitelist = ['/login']

if (cookie.get('store')) {
  store.replaceState(Object.assign({}, store.state, JSON.parse(cookie.get('store'))))
}

NProgress.configure({ showSpinner: false })

/**
 * @func 路由拦截 路由权限的校验
 * @param {function}
 * <AUTHOR> @date 2018/11/01
 */
router.beforeEach(async (to, form, next) => {
  NProgress.start()
  const mode = store.getters.mode
  if (mode === 'online') {
    if (to.path === '/login') {
      next()
      NProgress.done()
    } else {
      next()
      /* 动态路由权限控制
            if (store.getters.routerDone) {
                next();
            } else {
                try {
                    const accessRoutes = await store.dispatch("router/generateRoutes");
                    router.addRoutes(accessRoutes);
                    await store.dispatch("router/generateRoutesDone", true);
                    // 确保路由的完整，replace设置为true导航不会留下历史记录
                    next({...to, replace: true});
                } catch (error) {
                    next(`/login?redirect=${to.path}`);
                    NProgress.done();
                }
            }*/
    }
  } else {
    if (routerWhitelist.indexOf(to.path) !== -1) {
      next()
    } else {
      next(`/login?redirect=${to.path}`)
      NProgress.done()
    }
  }
})

router.afterEach((transition) => {
  NProgress.done()
})
