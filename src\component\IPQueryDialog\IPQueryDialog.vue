<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="form.rules" label-width="40%">
      <el-row>
        <el-col :span="11">
          <el-form-item>
            <el-radio-group v-model="form.model.ipType">
              <el-radio label="IPv4">
                {{ 'IPv4' }}
              </el-radio>
              <el-radio label="IPv6">
                {{ 'IPv6' }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="11">
          <el-form-item :label="form.model.ipType" prop="startIp">
            <el-input v-model.trim="form.model.startIp" class="width-max"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="2" style="text-align: center">
          <el-form-item label-width="0">
            -
          </el-form-item>
        </el-col>

        <el-col :span="11">
          <el-form-item label-width="0" prop="endIp" style="width: 60%;">
            <el-input v-model.trim="form.model.endIp" style="width: 100%;"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateIpv4, validateIpv6 } from '@util/validate'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '40%',
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value === '') {
        callback()
      } else if (this.form.model.ipType === 'IPv4' ? !validateIpv4(value) : !validateIpv6(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }

    return {
      dialogVisible: this.visible,
      form: {
        model: {
          ipType: 'IPv4',
          startIp: '',
          endIp: '',
        },
        rules: {
          startIp: [
            {
              trigger: 'blur',
              validator: validatorIp,
            },
          ],
          endIp: [
            {
              trigger: 'blur',
              validator: validatorIp,
            },
          ],
        },
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancelDialog() {
      this.form.model = {
        ipType: 'IPv4',
        startIp: '',
        endIp: '',
      }
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          let value = ''
          if (this.form.model.startIp !== '' && this.form.model.endIp !== '') {
            value = `${this.form.model.startIp}-${this.form.model.endIp}`
          }

          if (this.form.model.startIp === '' && this.form.model.endIp !== '') {
            value = `${this.form.model.endIp}`
          }

          if (this.form.model.startIp !== '' && this.form.model.endIp === '') {
            value = `${this.form.model.startIp}`
          }

          const param = Object.assign(this.form.model, {
            value,
          })

          this.$emit('on-submit', param)
          this.clickCancelDialog()
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
