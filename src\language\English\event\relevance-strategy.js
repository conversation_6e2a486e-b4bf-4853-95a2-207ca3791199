export default {
  relevanceStrategy: {
    errorDelete: 'Cannot delete initialization data',
    relevanceStrategy: 'Correlation Strategy',
    table: {
      alarmName: 'Strategy Name',
      alarmType: 'Strategy Category',
      description: 'Strategy Description',
      updateTime: 'Update Time',
      isSysDefault: 'System Built-in',
      state: 'Usage Status',
      handle: 'Operation',
    },
    tag: {
      custom: 'Custom',
      default: 'Built-in',
    },
    add: { dialogTitle: 'Add Correlation Strategy' },
    update: { dialogTitle: 'Update Correlation Strategy' },
    button: {
      and: '&& AND',
      or: '|| OR',
      addRules: 'Add Correlation Status',
      config: 'Configure',
    },
    tip: {
      length: 'Can only add up to five statuses',
      column: 'Please set merge fields first',
      one: 'Can only add one condition',
      data: 'Please add AND/OR conditions for each correlation status',
      empty: 'Configuration conditions cannot be empty',
      conditions: 'Please add at least one available correlation condition',
      type: 'Different condition types before and after',
    },
    title: {
      incPolicyName: 'Name',
      description: 'Description',
      incSubCategoryID: 'Event Name',
      externalSystem: 'Forward to External System',
      eventLevel: 'Event Level',
      eventDesc: 'Event Text',
    },
    upload: {
      downLoad: 'Download Template',
      chooseFile: 'Please select file',
      exceed: 'Current limit is 1 file, please delete before uploading',
      remindTip: 'When the imported asset name is the same as the system asset name, the imported data takes precedence',
      talkTip: 'When the imported asset name is the same as the system asset name, the system data takes precedence',
      successUpload: 'Operation completed, successfully imported {0} records!',
    },
  },
}
