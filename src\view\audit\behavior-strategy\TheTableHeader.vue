<!--
 * @Description: 行为分析策略 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-13
 * @Editor:
 * @EditDate: 2021-10-13
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model.trim="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('audit.behaviorStrategy.placeholder.fuzzyField')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'add'" @click="clickAdd">
          {{ $t('button.add') }}
        </el-button>
        <el-dropdown placement="bottom" trigger="click" @command="clickBatchHandle">
          <el-button type="primary">
            {{ $t('button.batchText') }}
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-has="'update'" command="run">
              {{ $t('button.batch.run') }}
            </el-dropdown-item>
            <el-dropdown-item v-has="'update'" command="stop">
              {{ $t('button.batch.stop') }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model.trim="filterCondition.form.systemName"
                clearable
                :placeholder="$t('audit.behaviorStrategy.placeholder.systemName')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-input
                v-model.trim="filterCondition.form.systemIp"
                clearable
                :placeholder="$t('audit.behaviorStrategy.placeholder.systemIp')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.status"
                clearable
                :placeholder="$t('audit.behaviorStrategy.placeholder.status')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-cascader
                v-model="filterCondition.form.abnormalAction"
                :placeholder="$t('audit.behaviorStrategy.placeholder.abnormalAction')"
                :options="options.eventType"
                :props="{ expandTrigger: 'hover' }"
                filterable
                clearable
                @change="changeQueryCondition"
              ></el-cascader>
            </el-col>
            <el-col align="right" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
    }
  },
  watch: {
    condition(nVal) {
      this.filterCondition = nVal
    },
    filterCondition(nVal) {
      this.$emit('update:condition', nVal)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        systemName: '',
        systemIp: '',
        status: '',
        abnormalAction: '',
      }
      this.changeQueryCondition()
    },
    clickAdd() {
      this.$emit('on-add')
    },
    clickBatchHandle(command) {
      this.$emit('on-batch-handle', command)
    },
  },
}
</script>
