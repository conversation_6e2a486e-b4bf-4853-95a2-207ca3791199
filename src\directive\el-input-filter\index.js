import Vue from 'vue'

let validateMethod
const validateLevel = function(level) {
  switch (level) {
    case 0:
      // 可输入英文 中文 数字
      return /[^\u4E00-\u9FA5A-Za-z0-9]+$/g
    case 1:
      // 可输入英文 中文 数字 _(下划线) -(中划线)
      return /[^a-zA-Z0-9_\-\u4e00-\u9fa5]+$/g
    case 2:
      // 可输入英文 中文 数字 _(下划线) -(中划线) .(小数点)
      return /[^a-zA-Z0-9_.\-\u4e00-\u9fa5]+$/g
    case 3:
      // 可输入英文 中文 数字 _(下划线) -(中划线) .(小数点) /(顿号线)
      return /[^a-zA-Z0-9_./\-\u4e00-\u9fa5]+$/g
    case 4:
      // 可输入英文 中文 数字 _(下划线) -(中划线) .(小数点) /(顿号线)
      return /[^a-zA-Z0-9_./\-\s\u4e00-\u9fa5]+$/g
    default:
      // 可输入英文 中文 数字 _(下划线) -(中划线)
      return /[^\u4E00-\u9FA5A-Za-z0-9]+$/g
  }
}
Vue.directive('el-input-filter', {
  inserted: function(el, binding) {
    // input框
    const input = el.firstElementChild
    // 定义校验方法
    validateMethod = function() {
      input.value = input.value.replace(validateLevel(binding.arg), '')
      input.dispatchEvent(new Event('input'))
    }
    // 添加监听
    input.addEventListener('keyup', validateMethod)
  },
  unbind: function(el) {
    const input = el.firstElementChild
    input.removeEventListener('keyup', validateMethod)
  },
})
