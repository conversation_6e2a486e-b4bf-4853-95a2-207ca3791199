<!--
 * @Description: 威胁事件 - 策略配置弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('event.threat.strategy.title')"
    :width="width"
    @on-close="doCancel"
    @on-submit="doSubmit"
  >
    <el-form :model="strategyData" label-width="120px">
      <section>
        <el-row>
          <el-form-item prop="status" :label="$t('event.threat.strategy.strategySwitch')">
            <el-switch v-model="strategyData.status" active-value="1" inactive-value="0"></el-switch>
          </el-form-item>
        </el-row>
        <el-row>
          <el-form-item prop="forwardId" :label="$t('event.threat.strategy.forwardService')">
            <el-select v-model="strategyData.forwardId" filterable multiple collapse-tags clearable>
              <el-option v-for="item in option.serviceOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-row>
      </section>
    </el-form>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { queryService } from '@api/event/threat-api'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    width: {
      type: String,
      default: '800',
    },
    actions: {
      type: Boolean,
      default: true,
    },
    strategyData: {
      required: true,
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      option: {
        serviceOption: [],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.getServiceOption()
  },
  methods: {
    getServiceOption() {
      queryService().then((res) => {
        this.option.serviceOption = res
      })
    },
    doCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    doSubmit() {
      this.$emit('on-submit', this.strategyData)
      this.doCancel()
    },
  },
}
</script>
