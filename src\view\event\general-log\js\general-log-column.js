import i18n from '@/language'

export default [
  {
    label: i18n.t('event.generalLog.basic.deviceName'),
    value: '',
    key: 'deviceName',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.insertTime'),
    value: '',
    key: 'insertTime',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.facilityName'),
    value: '',
    key: 'facilityName',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.severityName'),
    value: '',
    key: 'severityName',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.appName'),
    value: '',
    key: 'appName',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.procId'),
    value: '',
    key: 'procId',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.msgId'),
    value: '',
    key: 'msgId',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.logTimestamp'),
    value: '',
    key: 'logTimestamp',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.hostName'),
    value: '',
    key: 'hostName',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.structuredData'),
    value: '',
    key: 'structuredData',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.logMessage'),
    value: '',
    key: 'logMessage',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.generalLog.basic.message'),
    value: '',
    key: 'message',
    group: i18n.t('event.generalLog.group.basic'),
    check: false,
  },
]
