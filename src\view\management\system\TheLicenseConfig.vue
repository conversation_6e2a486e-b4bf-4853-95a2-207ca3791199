<!--
 * @Description: 系统管理 - License管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div>
    <el-row v-has="'upload'" class="update-wrapper">
      <el-col :span="4" :offset="6" class="context-center">
        {{ $t('management.system.tab.license') }}
      </el-col>
      <el-col :span="8">
        <el-upload
          ref="uploadLicense"
          action="#"
          :headers="upload.header"
          auto-upload
          :show-file-list="false"
          accept=".lic, .license"
          :file-list="upload.files"
          :before-upload="beforeUploadValidate"
          :on-change="onUploadFileChange"
          :http-request="submitUploadFile"
          class="header-button-upload"
        >
          <el-button v-has="'upload'" @click="clickUploadLicense">
            {{ $t('button.upload') }}
          </el-button>
        </el-upload>
      </el-col>
      <!--            <el-col :span="2">
                <el-button v-has="'download'" @click="clickDownload">
                    {{ $t("button.download") }}
                </el-button>
            </el-col>-->
    </el-row>
    <el-row v-for="(item, index) in tableData" :key="index" justify="center">
      <el-col :span="4" :offset="6" class="context-center col-border">
        {{ item.label }}
      </el-col>
      <el-col :span="8" class="context-center col-border">
        {{ item.value }}
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { prompt } from '@util/prompt'

export default {
  props: {
    tableData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      upload: {
        header: {
          'Content-Type': 'multipart/form-data',
        },
        files: [],
      },
    }
  },
  methods: {
    beforeUploadValidate(file) {
      if (this.upload.files.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isLic = suffix === 'lic'
        const isLicense = suffix === 'license'
        if (!isLic && !isLicense) {
          prompt({
            i18nCode: 'validate.upload.license',
            type: 'warning',
          })
        }
        return isLic || isLicense
      }
    },
    onUploadFileChange(file) {
      this.upload.files.push(file)
    },
    submitUploadFile(param) {
      if (param.file && this.upload.files.length > 0) {
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.$emit('on-upload', formData)
      }
    },
    clickUploadLicense() {
      this.upload.files = []
      this.$refs.uploadLicense.submit()
    },
    clickDownload() {
      this.$emit('on-download')
    },
  },
}
</script>

<style lang="scss" scoped>
.update-wrapper {
  height: 60px;
  margin: 20px;
  line-height: 60px;

  .el-col {
    height: 100%;
  }
}

.el-row {
  .col-border {
    height: 60px;
    line-height: 60px;
    border-left: 1px solid;
    border-top: 1px solid;
    @include theme('border-color', border-color);
  }

  .col-border:last-child {
    border-right: 1px solid;
    @include theme('border-color', border-color);
  }
}

.el-row:last-child {
  .col-border {
    border-bottom: 1px solid;
    @include theme('border-color', border-color);
  }
}
</style>
