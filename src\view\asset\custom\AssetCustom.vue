<!--
 * @Description: 自定义资产属性
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('asset.custom.attributeName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQuery('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'query'" @click="resetQuery">
            {{ $t('button.reset.default') }}
          </el-button>
          <el-button v-has="'add'" @click="clickAdd">
            {{ $t('button.add') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <section v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.name"
                  class="width-max"
                  clearable
                  :placeholder="$t('asset.custom.placeholder.attributeName')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.componentType"
                  :placeholder="$t('asset.custom.placeholder.controlType')"
                  clearable
                  class="width-max"
                  @change="inputQuery('e')"
                >
                  <el-option v-for="item in dialog.form.controlList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="4" :offset="10" align="right">
                <el-button v-has="'query'" @click="inputQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="seniorQuery"></el-button>
              </el-col>
            </el-row>
          </section>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <section class="tree-box">
        <section>
          <el-tree :data="dialog.form.typeList" :props="defaultProps" @node-click="handleNodeClick"></el-tree>
        </section>
        <section class="table-box">
          <header class="table-body-header">
            <h2 class="table-body-title">
              {{ $t('asset.custom.custom') }}
            </h2>
          </header>
          <main class="table-body-main">
            <el-table
              ref="Table"
              v-loading="data.loading"
              :data="data.table"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="100%"
              fit
              @current-change="TableRowChange"
            >
              <el-table-column prop="name" :label="$t('asset.custom.attributeName')" show-overflow-tooltip></el-table-column>
              <el-table-column :label="$t('asset.custom.controlType')" show-overflow-tooltip>
                <template slot-scope="scope">
                  <span>
                    {{
                      [
                        $t('asset.custom.control.text'),
                        $t('asset.custom.control.select'),
                        $t('asset.custom.control.timer'),
                        $t('asset.custom.control.textarea'),
                        $t('asset.custom.control.radio'),
                        $t('asset.custom.control.checkBox'),
                      ][scope.row.componentType - 1]
                    }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="assetClassName" :label="$t('asset.custom.assetClassName')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="assetTypeName" :label="$t('asset.custom.assetTypeName')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="remark" :label="$t('asset.custom.remark')" show-overflow-tooltip></el-table-column>
              <el-table-column fixed="right" width="240">
                <template slot-scope="scope">
                  <el-button
                    v-has="'query'"
                    :disabled="scope.row.componentType === 1 || scope.row.componentType === 3 || scope.row.componentType === 4"
                    class="el-button--blue"
                    @click="clickDic(scope.row)"
                  >
                    {{ $t('button.dictionary') }}
                  </el-button>
                  <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                    {{ $t('button.update') }}
                  </el-button>
                  <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
                    {{ $t('button.delete') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </main>
        </section>
      </section>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form"
      :width="'60%'"
      @on-submit="clickSubmitAdd"
    ></table-dialog>
    <!--修改弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form="dialog.form"
      :loading="dialog.form.dialogLoading"
      :width="'60%'"
      @on-submit="clickSubmitUpdate"
    ></table-dialog>
    <!--字典弹窗-->
    <dic-dialog
      :visible.sync="dialog.visible.dictionary"
      :title="dialog.title.dictionary"
      :form="dialog.dicForm"
      :width="'35%'"
      @on-submit="clickSubmitDic"
    ></dic-dialog>
  </div>
</template>
<script>
import TableDialog from './AudDialog'
import DicDialog from './dicDialog'
import { prompt } from '@util/prompt'
import { queryTableData, deleteAssets, updateAsset, queryDic, addAsset, queryType, queryDetails } from '@api/asset/custom-api'
import { debounce } from '@util/effect'

export default {
  name: 'AssetCustom',
  components: {
    TableDialog,
    DicDialog,
  },
  data() {
    return {
      isShow: false,
      defaultProps: {
        // 树形默认值
        children: 'children',
        label: 'label',
      },
      queryInput: {
        fuzzyField: '',
        name: '',
        assetClass: '',
        assetType: '',
        componentType: '',
      }, // 搜索框内容
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
        visible: true,
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.add', [this.$t('asset.custom.custom')]),
          update: this.$t('dialog.title.update', [this.$t('asset.custom.custom')]),
          dictionary: this.$t('dialog.title.config', [this.$t('asset.custom.dictionary')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false,
          update: false,
          dictionary: false,
        },
        dicForm: {
          loading: false,
          info: {
            name: {
              key: 'name',
              label: this.$t('asset.custom.dicName'),
            },
          },
          model: {
            name: '',
            list: [],
            parentId: '',
          },
        },
        form: {
          dialogLoading: false,
          showComponent: true,
          typeList: [], // 资产分类
          controlList: [
            {
              value: 1,
              label: this.$t('asset.custom.control.text'),
            },
            {
              value: 2,
              label: this.$t('asset.custom.control.select'),
            },
            {
              value: 3,
              label: this.$t('asset.custom.control.timer'),
            },
            {
              value: 4,
              label: this.$t('asset.custom.control.textarea'),
            },
            {
              value: 5,
              label: this.$t('asset.custom.control.radio'),
            },
            {
              value: 6,
              label: this.$t('asset.custom.control.checkBox'),
            },
          ], // 控件分类
          reqList: [
            {
              value: 2,
              label: this.$t('asset.custom.validate.no'),
            },
            {
              value: 1,
              label: this.$t('asset.custom.validate.yes'),
            },
          ], // 是否必填
          checkList: [
            {
              value: 2,
              label: this.$t('asset.custom.validate.no'),
            },
            {
              value: 1,
              label: this.$t('asset.custom.validate.yes'),
            },
          ], // 是否多选
          model: {
            // 弹出表单绑定值
            name: '',
            assetClass: '',
            remark: '',
            componentType: '',
            length: '',
            multiple: 2,
            required: '',
            id: '',
          },
          info: {
            // 弹出表单信息
            name: {
              key: 'name',
              label: this.$t('asset.custom.attributeName'),
            },
            assetClass: {
              key: 'assetClass',
              label: this.$t('asset.custom.assetClass'),
            },
            remark: {
              key: 'remark',
              label: this.$t('asset.custom.remark'),
            },
            componentType: {
              key: 'componentType',
              label: this.$t('asset.custom.controlType'),
            },
            length: {
              key: 'length',
              label: this.$t('asset.custom.attributeLength'),
            },
            multiple: {
              key: 'multiple',
              label: this.$t('asset.custom.checkType'),
            },
            required: {
              key: 'required',
              label: this.$t('asset.custom.reqType'),
            },
          },
        },
      },
      queryDebounce: null,
    }
  },
  mounted() {
    this.getTableData()
    this.getTypeList()
    this.initDebounce()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          fuzzyField: this.queryInput.fuzzyField,
          name: this.queryInput.name,
          assetClass: this.queryInput.assetClass || '',
          assetType: this.queryInput.assetType || '',
          componentType: this.queryInput.componentType,
        }
        this.pagination.visible = false
        this.data.loading = true
        this.getTableData(params)
      }, 500)
    },
    // 获取table列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryTableData(params).then((res) => {
        if (res) {
          this.data.table = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 获取资产分类数据
    getTypeList() {
      queryType().then((res) => {
        this.dialog.form.typeList = res
      })
    },
    // 点击添加操作并弹出对话框操作
    clickAdd() {
      this.clearDialogFormModel()
      this.dialog.form.showComponent = true
      this.dialog.visible.add = true
    },
    // 提交添加表单操作
    clickSubmitAdd(formModel) {
      this.add(formModel)
    },
    // 调用添加接口
    add(obj) {
      const params = {
        ...obj,
        assetClass: obj.assetClass[0],
        assetType: obj.assetClass[1],
      }
      addAsset(params).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.resetQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 点击修改并弹出对话框操作
    async clickUpdate(details) {
      this.dialog.form.dialogLoading = true
      await queryDetails(details.id).then((res) => {
        if (res) {
          const row = {
            ...res,
            componentType: details.componentType,
          }
          this.TableRowChange(row)
          this.clearDialogFormModel()
          this.dialog.form.model = row
          this.dialog.form.dialogLoading = false
        }
      })
      this.dialog.form.showComponent = false
      this.dialog.visible.update = true
    },
    // 点击字典按钮
    clickDic(row) {
      this.TableRowChange(row)
      this.dialog.dicForm.model = {
        name: '',
        list: [],
        parentId: '',
      }
      this.dialog.dicForm.loading = true
      queryDic(row.id).then((res) => {
        this.dialog.dicForm.model.parentId = row.id
        this.dialog.dicForm.model.list = res
        this.dialog.dicForm.loading = false
      })
      this.dialog.visible.dictionary = true
    },
    // 提交修改表单操作
    clickSubmitUpdate(formModel) {
      this.update(formModel)
    },
    // 提交字典
    clickSubmitDic(formModel) {
      if (formModel) {
        this.inputQuery()
      }
    },
    // 调用修改接口
    update(obj) {
      const params = {
        ...obj,
        assetClass: obj.assetClass,
        assetType: obj.assetType,
      }
      updateAsset(params).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.inputQuery()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 点击侧边栏树形控件
    handleNodeClick(data) {
      // 清空搜索条件
      this.clearQuery()
      this.pagination.pageNum = 1
      if (data.children) {
        this.queryInput.assetClass = data.value
      } else {
        this.queryInput.assetType = data.value
      }
      this.queryDebounce()
    },
    // 点击删除操作
    clickDelete(row) {
      this.delete(row.id)
    },
    // 调用删除接口
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteAssets(ids).then((res) => {
          // 这里根据自己的业务进行操作
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.inputQuery()
              }
            )
          } else if (res === 3) {
            prompt({
              i18nCode: 'tip.delete.running',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.inputQuery('e')
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQuery()
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 清空弹窗表单绑定数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        name: '',
        assetClass: '',
        remark: '',
        componentType: '',
        length: 10,
        multiple: 2,
        required: '',
        id: '',
      }
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    // 清空搜索条件
    clearQuery() {
      this.queryInput = {
        fuzzyField: '',
        name: '',
        assetClass: '',
        assetType: '',
        componentType: '',
      }
    },
    // 输入框搜索方法
    inputQuery(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    // 高级查询输入框重置方法
    resetQuery() {
      this.pagination.pageNum = 1
      this.clearQuery()
      this.queryDebounce()
    },
  },
}
</script>
<style lang="scss" scoped>
.tree-box {
  display: flex;
  height: 100%;
  width: 100%;
  overflow: hidden;
  section.table-box {
    width: 100%;
    overflow: auto;
  }

  .el-tree {
    overflow-x: hidden;
    overflow-y: scroll;
  }
}
</style>
