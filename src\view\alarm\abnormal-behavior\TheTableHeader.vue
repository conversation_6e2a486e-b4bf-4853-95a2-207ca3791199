<!--
 * @Description: 异常行为告警 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-02
 * @Editor:
 * @EditDate: 2021-11-02
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model.trim="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('alarm.abnormalBehavior.label.infoSystemName')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'update'" @click="clickBatchHandle">
          {{ $t('button.batch.ignore') }}
        </el-button>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model="filterCondition.form.infoSystemIp"
                clearable
                :placeholder="$t('alarm.abnormalBehavior.label.infoSystemIp')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.status"
                clearable
                :placeholder="$t('alarm.abnormalBehavior.label.status')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.anomalyType"
                clearable
                :placeholder="$t('alarm.abnormalBehavior.label.anomalyType')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.anomalyType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-input
                v-model="filterCondition.form.role"
                clearable
                :placeholder="$t('alarm.abnormalBehavior.label.role')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="10">
              <el-date-picker
                v-model="filterCondition.form.occurTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('alarm.abnormalBehavior.label.occurStartTime')"
                :end-placeholder="$t('alarm.abnormalBehavior.label.occurEndTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
            <el-col :span="10">
              <el-date-picker
                v-model="filterCondition.form.updateTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('alarm.abnormalBehavior.label.updateStartTime')"
                :end-placeholder="$t('alarm.abnormalBehavior.label.updateEndTime')"
                @change="changeQueryCondition"
              ></el-date-picker>
            </el-col>
            <el-col align="right" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
import { prompt } from '@util/prompt'
import { validateIp } from '@util/validate'

export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      debounce: null,
    }
  },
  watch: {
    condition(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:condition', newCondition)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      if (this.validatorIp()) {
        this.debounce()
      }
    },
    validatorIp() {
      const infoIp = this.filterCondition.form.infoSystemIp || ''
      if (infoIp !== '' && !validateIp(infoIp)) {
        prompt({
          i18nCode: 'validate.ip.incorrect',
          type: 'error',
        })
        return false
      } else {
        return true
      }
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        infoSystemIp: '',
        role: '',
        status: '',
        anomalyType: '',
        occurTime: [],
        updateTime: [],
      }
      this.changeQueryCondition()
    },
    clickBatchHandle() {
      this.$emit('on-batch-handle')
    },
  },
}
</script>
