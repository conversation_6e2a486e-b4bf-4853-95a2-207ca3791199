<!--
 * @Description: 聚合策略 - 解析规则包导入弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-02-17
 * @Editor:
 * @EditDate: 2022-02-17
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.packageUpload')"
    :width="width"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formTemplate" :model="form" :rules="rules" label-width="27%">
      <template>
        <el-form-item :label="$t('event.polymerizationStrategy.upload.chooseFile')" prop="files">
          <el-upload
            ref="upload"
            v-has="'upload'"
            style="margin-left: 10px"
            class="header-button-upload width-mini"
            action="#"
            :headers="form.header"
            :show-file-list="true"
            :limit="1"
            accept=".jar"
            auto-upload
            :file-list="form.files"
            :on-exceed="handleExceed"
            :on-remove="handleRemove"
            :on-change="onUploadFileChange"
            :http-request="submitUploadFile"
            :before-upload="beforeUploadValidate"
            @click="clickUploadTable"
          >
            <el-input suffix-icon="el-icon-folder"></el-input>
          </el-upload>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      fileName: '',
      file: {},
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 导入文件改变时调用
    onUploadFileChange(file) {
      this.form.files.push(file)
    },
    // 超出限制提示
    handleExceed() {
      const text = this.$t('event.polymerizationStrategy.upload.exceed')
      this.$message.warning(text)
    },
    // 提交导入文件
    submitUploadFile(param) {
      if (param.file && this.form.files.length > 0) {
        this.fileName = this.form.files.map((item) => item.name)
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.file = formData
      }
    },
    handleRemove() {
      this.form.files.splice(0, 1)
    },
    // 导入之前的文件名称校验
    beforeUploadValidate(file) {
      if (this.form.files.length > 0) {
        const fileName = file.name
        if (fileName !== this.form.templateName) {
          prompt({
            i18nCode: 'tip.upload.nameError',
            type: 'warning',
          })
          return false
        }
        return true
      }
    },
    // 点击上传文件
    clickUploadTable() {
      this.form.files = []
      this.$refs.upload.submit()
    },
    // 关闭当前dialog
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            // 给父级调用数据
            this.$emit('on-submit', this.file)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-upload-list {
  width: 80%;
  ::v-deep .el-upload-list__item .el-icon-close {
    top: 25%;
  }
}
</style>
