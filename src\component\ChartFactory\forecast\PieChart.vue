<template>
  <section :id="id" ref="chart" :class="className" :style="{ height: height, width: width }"></section>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import common from '../mixin/common'
import { deepMerge } from '@util/format'
import { getColor } from '@util/effect'

export default {
  mixins: [common],
  props: {
    className: {
      type: String,
      default: 'chart-pie',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    proto: {
      type: Boolean,
      default: false,
    },
    option: {
      type: Object,
      default: null,
    },
  },
  data() {
    return {
      chart: null,
      color: getColor(),
      index: {
        selected: 0,
        hovered: 0,
      },
    }
  },
  watch: {
    option: {
      handler(config) {
        this.configChart(config)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart, this.$store.getters.theme)
    },
    configChart(config = this.option) {
      this.chart.showLoading()
      config && Object.keys(config).length > 0 ? this.drawChart(config) : this.empty()
      this.chart.hideLoading()
    },
    drawChart(config) {
      const option = this.proto ? config : this.chartOptionConfig(config)
      this.chart.setOption(option, true)
    },
    chartOptionConfig(config) {
      let option = {
        color: this.color,
        backgroundColor: 'transparent',
        tooltip: {
          trigger: 'item',
        },
        legend: {
          type: 'scroll',
          bottom: 10,
          left: 'center',
          icon: 'circle',
        },
      }
      option = deepMerge(option, this.chartSeriesConfig(config))
      if (this.option.custom) {
        option = deepMerge(option, this.option.custom)
      }
      return option
    },
    chartSeriesConfig(config) {
      config.type = config.type || 1
      if (config.type === 'pie' || config.type === 1) {
        return this.getPieSeries(config)
      }

      if (config.type === 'pie-rose' || config.type === 2) {
        return this.getPieRoseSeries(config)
      }

      if (config.type === 'pie-half' || config.type === 3) {
        return Object.assign(
          {
            legend: { type: '' },
          },
          this.getPieHalfSeries(config)
        )
      }

      if (config.type === 'pie-3d' || config.type === 4) {
        return this.getPie3DSeries(config)
      }

      if (config.type === 'ring' || config.type === 5) {
        return this.getRingSeries(config)
      }

      if (config.type === 'ring-rose' || config.type === 6) {
        return this.getRingRoseSeries(config)
      }

      if (config.type === 'ring-half' || config.type === 7) {
        return Object.assign(
          {
            legend: { type: '' },
          },
          this.getRingHalfSeries(config)
        )
      }

      if (config.type === 'ring-3d' || config.type === 8) {
        return this.getRing3DSeries(config)
      }

      if (config.type === 'nest-ring' || config.type === 9) {
        return this.getNestRingSeries(config)
      }
    },
    getPieSeries(config) {
      return {
        series: [
          {
            type: 'pie',
            radius: '60%',
            center: ['50%', '40%'],
            label: {
              show: false,
            },
            data: config.data,
          },
        ],
      }
    },
    getPieHalfSeries(config) {
      const halfData = this.getHalfPieData()
      return {
        series: [
          {
            type: 'pie',
            radius: '100%',
            center: ['50%', '70%'],
            startAngle: 180,
            label: {
              show: false,
            },
            data: [...config.data, ...halfData],
          },
        ],
      }
    },
    getPieRoseSeries(config) {
      return {
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            center: ['50%', '40%'],
            radius: '70%',
            label: {
              show: false,
            },
            data: config.data,
          },
        ],
      }
    },
    getPie3DSeries(config) {
      return this.getPie3D(config.data, 0)
    },
    getRingSeries(config) {
      return {
        series: [
          {
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['40%', '60%'],
            label: {
              show: false,
            },
            data: config.data,
          },
        ],
      }
    },
    getRingRoseSeries(config) {
      return {
        series: [
          {
            type: 'pie',
            roseType: 'radius',
            center: ['50%', '40%'],
            radius: ['20%', '70%'],
            label: {
              show: false,
            },
            data: config.data,
          },
        ],
      }
    },
    getRingHalfSeries(config) {
      const halfData = this.getHalfPieData()
      return {
        series: [
          {
            type: 'pie',
            radius: ['70%', '100%'],
            center: ['50%', '70%'],
            startAngle: 180,
            label: {
              show: false,
            },
            data: [...config.data, ...halfData],
          },
        ],
      }
    },
    getRing3DSeries(config) {
      return this.getPie3D(config.data, 0.5)
    },
    getNestRingSeries(config) {
      return {
        series: [
          {
            type: 'pie',
            selectedMode: 'single',
            center: ['50%', '40%'],
            radius: [0, '40%'],
            label: {
              normal: {
                position: 'inner',
              },
            },
            labelLine: {
              normal: {
                show: false,
              },
            },
            data: config.data[0],
          },
          {
            type: 'pie',
            center: ['50%', '40%'],
            radius: ['60%', '80%'],
            data: config.data[1],
          },
        ],
      }
    },
    getHalfPieData() {
      const seriesData = []
      const halfData = {
        value: 0,
        tooltip: {
          show: false,
        },
        label: {
          show: false,
        },
        itemStyle: {
          color: 'transparent',
        },
        cursor: 'default',
      }
      this.option.data.forEach((item) => {
        halfData.value += item.value
      })

      if (halfData.value === 0) {
        for (let i = 0; i < this.option.data.length; i++) {
          seriesData.push(halfData)
        }
      } else {
        seriesData.push(halfData)
      }

      return seriesData
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
    /**
     * @func 生成扇形的曲面参数方程
     * @param {float} startRatio - 当前扇形起始比例，取值区间 [0, endRatio)
     * @param {float} endRatio - 当前扇形结束比例，取值区间 (startRatio, 1]
     * @param {boolean} selected - 是否选中，效果参照二维饼图选中效果（单选）
     * @param {boolean} hovered - 是否放大，效果接近二维饼图高亮（放大）效果
     * @param {float} k - 用于参数方程的一个参数，取值 0~1 之间，通过「内径/外径」的值换算而来
     * @param {number} h - 3D环形图高度，取值为series中的value
     * @return {object} 曲面的参数方程
     */
    getParametricEquation(startRatio, endRatio, selected, hovered, k, h) {
      // 计算
      const midRatio = (startRatio + endRatio) / 2

      const startRadian = startRatio * Math.PI * 2
      const endRadian = endRatio * Math.PI * 2
      const midRadian = midRatio * Math.PI * 2

      // 如果只有一个扇形，则不实现选中效果。
      if (startRatio === 0 && endRatio === 1) {
        selected = false
      }

      // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
      k = typeof k !== 'undefined' ? k : 1 / 3

      // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
      const offsetX = selected ? Math.cos(midRadian) * 0.1 : 0
      const offsetY = selected ? Math.sin(midRadian) * 0.1 : 0

      // 计算高亮效果的放大比例（未高亮，则比例为 1）
      const hoverRate = hovered ? 1.05 : 1

      // 返回曲面参数方程
      return {
        u: {
          min: -Math.PI,
          max: Math.PI * 3,
          step: Math.PI / 32,
        },
        v: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20,
        },
        x(u, v) {
          if (u < startRadian) {
            return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        y(u, v) {
          if (u < startRadian) {
            return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          if (u > endRadian) {
            return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
          }
          return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
        },
        z(u, v) {
          if (u < -Math.PI * 0.5) {
            return Math.sin(u)
          }
          if (u > Math.PI * 2.5) {
            return Math.sin(u) * h * 0.1
          }
          return Math.sin(v) > 0 ? h * 0.1 : -1
        },
      }
    },
    /**
     * @func 生成模拟 3D 饼图的配置项
     * @param {object} pieData - 饼图数据
     * @param {number|float} internalDiameterRatio - 内径/外径的值（默认值 1/2），取值 0~1 之间, 当该值等于 0 时，为普通饼图
     * @return {object}  3D饼图的配置项
     */
    getPie3D(pieData, internalDiameterRatio) {
      const series = []
      let sumValue = 0
      let startValue = 0
      let endValue = 0
      const legendData = []
      const k = typeof internalDiameterRatio !== 'undefined' ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio) : 1 / 3

      // 为每一个饼图数据，生成一个 series-surface 配置
      for (let i = 0; i < pieData.length; i++) {
        sumValue += pieData[i].value

        const seriesItem = {
          name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
          type: 'surface',
          parametric: true,
          wireframe: {
            show: false,
          },
          pieData: pieData[i],
          pieStatus: {
            selected: false,
            hovered: false,
            k: k,
          },
        }

        if (typeof pieData[i].itemStyle !== 'undefined') {
          const itemStyle = {}

          typeof pieData[i].itemStyle.color !== 'undefined' ? (itemStyle.color = pieData[i].itemStyle.color) : null
          typeof pieData[i].itemStyle.opacity !== 'undefined' ? (itemStyle.opacity = pieData[i].itemStyle.opacity) : null

          seriesItem.itemStyle = itemStyle
        }
        series.push(seriesItem)
      }

      // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
      // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
      for (let i = 0; i < series.length; i++) {
        endValue = startValue + series[i].pieData.value

        series[i].pieData.startRatio = startValue / sumValue
        series[i].pieData.endRatio = endValue / sumValue
        series[i].parametricEquation = this.getParametricEquation(series[i].pieData.startRatio, series[i].pieData.endRatio, false, false, k, 10)

        startValue = endValue

        legendData.push(series[i].name)
      }

      // 补充一个透明的圆环，用于支撑高亮功能的近似实现。
      series.push({
        name: 'mouseoutSeries',
        type: 'surface',
        parametric: true,
        wireframe: {
          show: false,
        },
        itemStyle: {
          opacity: 0,
        },
        parametricEquation: {
          u: {
            min: 0,
            max: Math.PI * 2,
            step: Math.PI / 20,
          },
          v: {
            min: 0,
            max: Math.PI,
            step: Math.PI / 20,
          },
          x(u, v) {
            return Math.sin(v) * Math.sin(u) + Math.sin(u)
          },
          y(u, v) {
            return Math.sin(v) * Math.cos(u) + Math.cos(u)
          },
          z(u, v) {
            return Math.cos(v) > 0 ? 0.1 : -0.1
          },
        },
      })

      // 准备待返回的配置项，把准备好的 legendData、series 传入。
      return {
        animation: false,
        legend: {
          data: legendData,
        },
        tooltip: {
          formatter: (params) => {
            if (params.seriesName !== 'mouseoutSeries') {
              return `${params.marker} ${params.seriesName} ${pieData[params.seriesIndex].value}`
            }
          },
        },
        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1,
          max: 1,
        },
        grid3D: {
          show: false,
          boxHeight: 10,
          top: -40,
          viewControl: {
            distance: internalDiameterRatio === 0 ? this.chart.getWidth() : this.chart.getWidth() * 0.6,
            alpha: 40,
            beta: 40,
          },
        },
        series: series,
      }
    },
    pie3DHackEvent(option) {
      // 监听 mouseover，近似实现高亮（放大）效果
      this.chart.on('mouseover', (params) => {
        // 准备重新渲染扇形所需的参数
        let isSelected
        let isHovered
        let startRatio
        let endRatio
        let k

        // 如果触发 mouseover 的扇形当前已高亮，则不做操作
        if (this.index.hovered === params.seriesIndex) {
          return false
          // 否则进行高亮及必要的取消高亮操作
        } else {
          // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
          if (this.index.hovered !== '') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
            isSelected = option.series[this.index.hovered].pieStatus.selected
            isHovered = false
            startRatio = option.series[this.index.hovered].pieData.startRatio
            endRatio = option.series[this.index.hovered].pieData.endRatio
            k = option.series[this.index.hovered].pieStatus.k

            // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
            option.series[this.index.hovered].parametricEquation = this.getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, 10)
            option.series[this.index.hovered].pieStatus.hovered = isHovered

            // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
            this.index.hovered = ''
          }

          // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
          if (params.seriesName !== 'mouseoutSeries') {
            // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
            isSelected = option.series[params.seriesIndex].pieStatus.selected
            isHovered = true
            startRatio = option.series[params.seriesIndex].pieData.startRatio
            endRatio = option.series[params.seriesIndex].pieData.endRatio
            k = option.series[params.seriesIndex].pieStatus.k

            // 对当前点击的扇形，执行高亮操作（对 option 更新）
            option.series[params.seriesIndex].parametricEquation = this.getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, 12)
            option.series[params.seriesIndex].pieStatus.hovered = isHovered

            // 记录上次高亮的扇形对应的系列号 seriesIndex
            this.index.hovered = params.seriesIndex
          }

          // 使用更新后的 option，渲染图表
          this.chart.setOption(option)
        }
      })

      // 修正取消高亮失败的 bug
      this.chart.on('globalout', () => {
        if (this.index.hovered !== '') {
          // 从 option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
          const isSelected = option.series[this.index.hovered].pieStatus.selected
          const isHovered = false
          const k = option.series[this.index.hovered].pieStatus.k
          const startRatio = option.series[this.index.hovered].pieData.startRatio
          const endRatio = option.series[this.index.hovered].pieData.endRatio

          // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
          option.series[this.index.hovered].parametricEquation = this.getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, 10)
          option.series[this.index.hovered].pieStatus.hovered = isHovered

          // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
          this.index.hovered = ''
        }

        // 使用更新后的 option，渲染图表
        this.chart.setOption(option)
      })
    },
    removeEvent() {
      this.chart.off('mouseover')
      this.chart.off('globalout')
    },
  },
}
</script>
