<!--
 * @Description: 系统管理 - 数据备份
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="tab-context-wrapper">
    <section class="form-container">
      <el-form ref="backupForm" :model="form.model" :rules="form.rule" label-width="180px">
        <!--<el-form-item :label="$t('management.system.label.backupWay')" style="margin:0">
                    <el-radio-group v-model="form.model.type" @change="handleBackupWay($event)">
                        <el-radio
                            v-for="item in options.backupWay"
                            :key="item.value"
                            :label="item.value">
                            {{ item.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>-->
        <el-form-item :label="$t('management.system.label.backupCycle')">
          <el-radio-group v-model="form.model.cycle" @change="form.model.timeValue = ''">
            <el-radio label="day">
              {{ $t('time.cycle.day') }}
            </el-radio>
            <el-radio label="week">
              {{ $t('time.cycle.week') }}
            </el-radio>
            <el-radio label="month">
              {{ $t('time.cycle.month') }}
            </el-radio>
          </el-radio-group>
          <el-checkbox
            v-model="form.model.excuteImmediate"
            true-label="immediate"
            false-label=""
            :label="$t('time.cycle.immediate')"
            style="margin-left: 30px;"
          ></el-checkbox>
        </el-form-item>
        <el-row v-if="form.model.excuteImmediate !== 'immediate'">
          <el-col :span="1">
            <el-form-item :label="$t('management.system.label.backupTime')"></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item
              v-if="form.model.cycle !== 'day'"
              :label="form.model.cycle === 'week' ? $t('time.cycle.week') : $t('time.cycle.month')"
              prop="timeValue"
            >
              <el-select v-model="form.model.timeValue" clearable class="width-small">
                <template v-if="form.model.cycle === 'week'">
                  <el-option :value="1" :label="$t('time.week.mon')"></el-option>
                  <el-option :value="2" :label="$t('time.week.tue')"></el-option>
                  <el-option :value="3" :label="$t('time.week.wed')"></el-option>
                  <el-option :value="4" :label="$t('time.week.thu')"></el-option>
                  <el-option :value="5" :label="$t('time.week.fri')"></el-option>
                  <el-option :value="6" :label="$t('time.week.sat')"></el-option>
                  <el-option :value="0" :label="$t('time.week.sun')"></el-option>
                </template>
                <template v-else>
                  <el-option v-for="index in 31" :key="index" :value="index" :label="index + $t('time.unit.day')"></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('time.option.time')" prop="time">
              <el-time-picker v-model="form.model.time" format="HH:mm:ss" value-format="HH:mm:ss" clearable class="width-small"></el-time-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="1">
            <el-form-item :label="$t('management.system.label.ftpBackup')"></el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('management.system.label.ip')" prop="ip">
              <el-input v-model="form.model.ip" class="width-small" maxlength="255"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('management.system.label.account')" prop="account">
              <el-input v-model="form.model.account" class="width-small" maxlength="255"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="8" :offset="1">
            <el-form-item :label="$t('management.system.label.password')" prop="password">
              <el-input v-model="form.model.password" type="password" class="width-small" maxlength="255"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('management.system.label.path')" prop="path">
              <el-input v-model="form.model.path" class="width-small" maxlength="255"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <section class="tab-footer-button">
        <el-button v-has="'update'" @click="clickSaveBackupConfig">
          {{ $t('button.save') }}
        </el-button>
        <el-button v-has="'query'" @click="clickResetBackupConfig">
          {{ $t('button.reset.default') }}
        </el-button>
        <!--                <el-button v-has="'recover'" @click="clickRecover">
                    {{ $t("button.dataRecovery") }}
                </el-button>-->
      </section>
    </section>
    <el-divider></el-divider>
    <section class="table-container router-wrap-table">
      <section class="table-header">
        <h2>{{ $t('management.system.title.dataBackupTable') }}</h2>
      </section>
      <section class="table-body">
        <el-table :data="tableData.slice((pagination.pageNum - 1) * pagination.pageSize, pagination.pageNum * pagination.pageSize)" height="100%">
          <el-table-column width="50"></el-table-column>
          <el-table-column prop="backupTime" :label="$t('management.system.label.time')"></el-table-column>
          <el-table-column prop="backupDescription" :label="$t('management.system.label.description')"></el-table-column>
          <el-table-column prop="backupResult" :label="$t('management.system.label.result')"></el-table-column>
        </el-table>
      </section>
      <section class="table-footer">
        <el-pagination
          small
          background
          align="right"
          :current-page="pagination.pageNum"
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.length"
          @size-change="backupConfigTableSizeChange"
          @current-change="backupConfigTableCurrentChange"
        ></el-pagination>
      </section>
    </section>
  </div>
</template>

<script>
import { JSEncrypt } from 'jsencrypt'
import { prompt } from '@util/prompt'
import { validateIp } from '@util/validate'

export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
    tableData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateIp(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }

    return {
      form: {
        model: {
          type: '1',
          cycle: 'week',
          timeValue: 1,
          time: '',
          ip: '',
          account: '',
          password: '',
          path: '',
        },
        rule: {
          timeValue: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          time: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          ip: [
            {
              required: true,
              validator: validatorIp,
              trigger: 'blur',
            },
          ],
          account: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          password: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          path: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
      options: {
        backupWay: [
          { value: '1', label: this.$t('code.backupWay.increment') },
          { value: '2', label: this.$t('code.backupWay.all') },
        ],
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model = this.formData
      }
    },
    clickSaveBackupConfig() {
      this.$refs.backupForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', this.handleParam())
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetBackupConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-reset')
      })
    },
    handleBackupWay(e) {
      this.$forceUpdate()
    },
    handleParam() {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.$store.getters.publicKey)
      return {
        type: parseInt(this.form.model.type),
        excuteImmediate: this.form.model.excuteImmediate,
        backupCycle: this.form.model.cycle,
        backupTimeValue: this.form.model.timeValue,
        backupTime: this.form.model.time,
        ip: this.form.model.ip,
        account: this.form.model.account,
        password: encryptor.encrypt(this.form.model.password),
        path: this.form.model.path,
      }
    },
    backupConfigTableSizeChange(size) {
      this.pagination.pageSize = size
    },
    backupConfigTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
    },
    clickRecover() {
      this.$emit('on-recover')
    },
  },
}
</script>

<style lang="scss" scoped>
.tab-context-wrapper {
  display: flex;
  flex-direction: column;
  height: 100% !important;

  .form-container {
    position: relative;

    .tab-footer-button {
      position: absolute;
    }
  }

  .table-container {
    .table-header {
      justify-content: normal;
      background-color: $CR;
    }
  }
}
</style>
