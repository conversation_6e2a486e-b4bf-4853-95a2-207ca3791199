const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    policyName: '@NAME',
    eventName: '@CNAME',
    auditType: '@CNAME',
    auditIp: '@CNAME',
    eventLevel: '1',
    accountName: '@word(4)',
    createTime: '@DATETIME',
    updateTime: '@DATETIME',
    'state|0-1': '1',
    total: '22223',
  },
})

const columns = ['policyName', 'createTime', 'accountName', 'eventName', 'eventLevel', 'total', 'state', 'auditType']

const uptColumns = ['eventLevel', 'total', 'state', 'auditType']

const auditUsers = [
  {
    label: '审计人员C',
    type: null,
    value: null,
  },
]

const auditTypes = [
  {
    label: '审计_250',
    type: null,
    value: null,
  },
]

const auditStrategies = [
  {
    label: '审计类型二_低级',
    type: null,
    value: null,
  },
  {
    label: '审计类型二_中级',
    type: null,
    value: null,
  },
]

const safeDetailData = {
  pageNum: 1,
  pageSize: 20,
  pages: 1,
  total: 1,
  row: [
    {
      advice:
        '该报警表示IDS检测到了通过SMTP协议传播的可疑代码的信息。可疑邮件附件可能包括病毒和蠕虫（在传输中同样受到保护），或发送这些附件到其他组织。这个报警会在邮件中包含ＶＢＳ文件时同样会被触发。',
      aggrEndDate: '2020-12-08 13:31:04',
      aggrStartDate: '2020-12-08 13:30:45',
      count: '20',
      dstIP: null,
      dstPort: null,
      eventId: '1607405445334I8ec',
      eventLevelName: '0',
      eventTypeName: '病毒/木马',
      fromDeviceType: '107101',
      fromDeviceTypeName: '绿盟_IDS',
      pageNum: null,
      pageSize: null,
      protocol: 'TCP.HTTP',
      safeEventName: '邮件病毒',
      srcIP: null,
      srcPort: null,
      type: 0,
    },
  ],
}

const associatedData = {
  pageNum: 1,
  pageSize: 20,
  pages: 1,
  total: 1,
  row: [
    {
      advice:
        '该报警表示IDS检测到了通过SMTP协议传播的可疑代码的信息。可疑邮件附件可能包括病毒和蠕虫（在传输中同样受到保护），或发送这些附件到其他组织。这个报警会在邮件中包含ＶＢＳ文件时同样会被触发。',
      aggrEndDate: '2020-12-08 13:31:04',
      aggrStartDate: '2020-12-08 13:30:45',
      count: '20',
      dstIP: null,
      dstPort: null,
      eventId: '1607405445334I8ec',
      eventLevelName: '0',
      eventTypeName: '病毒/木马',
      fromDeviceType: '107101',
      fromDeviceTypeName: '绿盟_IDS',
      pageNum: null,
      pageSize: null,
      protocol: 'TCP.HTTP',
      safeEventName: '邮件病毒',
      srcIP: null,
      srcPort: null,
      type: 0,
    },
  ],
}

const threatData = {
  pageNum: 1,
  pageSize: 20,
  pages: 1,
  total: 1,
  row: [
    {
      eventId: '@ID',
      eventType: '@CNAME',
      'eventLevel|1': ['严重', '高级', '中级', '低级', '一般'],
      eventDesc: '@TITLE',
      eventTime: '@DATETIME',
      receiveTime: '@DATETIME',
    },
  ],
}

const originalLog = [
  {
    dateTime: '2020-12-08 13:30:45',
    dstIp: '*************',
    eventName: '邮件病毒',
    id: '1607405445349I8ev',
    level: '0',
    raw:
      '<5>time:2010-08-13 15:35:32;danger_degree:1;breaking_sighn:0;event:[50257]HTTP;src_addr:**********;src_port:1812;dst_addr:*************;dst_port:80;proto:TCP.HTTP',
    srcIp: '**********',
    timestamp: 1607405445338,
    typeName: '15003',
  },
  {
    dateTime: '2020-12-08 13:30:45',
    dstIp: '*************',
    eventName: '邮件病毒',
    id: '1607405445349I8ev',
    level: '0',
    raw:
      '<5>time:2010-08-13 15:35:32;danger_degree:1;breaking_sighn:0;event:[50257]HTTP;src_addr:**********;src_port:1812;dst_addr:*************;dst_port:80;proto:TCP.HTTP',
    srcIp: '**********',
    timestamp: 1607405445338,
    typeName: '15003',
  },
]

module.exports = [
  // {
  //     url: "/event/alarm/events",
  //     type: "get",
  //     response: option => {
  //         tableData.query(option);
  //         return {
  //             code: 200,
  //             data: tableData.getMockData()
  //         };
  //     }
  // },
  {
    url: '/event/alarm/events/associated',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: associatedData,
      }
    },
  },
  {
    url: 'event/alarm/events/apt',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: threatData,
      }
    },
  },
  {
    url: '/event/alarm/events/security',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: safeDetailData,
      }
    },
  },
  {
    url: '/event/alarm/events/original',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: originalLog,
      }
    },
  },
  {
    url: '/event/alarm/columns',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: columns,
      }
    },
  },
  {
    url: '/event/alarm/state',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/event/alarm/columns',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: uptColumns,
      }
    },
  },
  {
    url: '/event/alarm/combo/audit-users',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: auditUsers,
      }
    },
  },
  {
    url: '/event/alarm/combo/audit-types',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: auditTypes,
      }
    },
  },
  {
    url: '/event/alarm/combo/audit-strategies',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: auditStrategies,
      }
    },
  },
]
