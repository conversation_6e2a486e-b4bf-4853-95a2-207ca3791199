import request from '@util/request'

export function queryOriginalLogColumnData() {
  return request({
    url: '/event/original/columns',
    method: 'get',
  })
}

export function queryOriginalLogTableData(obj) {
  return request({
    url: '/event/original/events',
    method: 'get',
    params: obj || {},
  })
}

export function queryOriginalLogDetailData(id, time) {
  return request({
    url: `/event/original/events/${id}/${time}`,
    method: 'get',
  })
}

export function queryOriginalLogTotalData(obj) {
  return request({
    url: '/event/original/total',
    method: 'get',
    params: obj || {},
  })
}

export function updateOriginalLogColumnData(arr) {
  return request({
    url: '/event/original/columns',
    method: 'put',
    data: arr || [],
  })
}

export function queryAssetTypeData() {
  return request({
    url: '/event/original/combo/asset-types',
    method: 'get',
  })
}

export function queryEventTypeData() {
  return request({
    url: '/event/original/combo/event-categories',
    method: 'get',
  })
}

export function queryEventType() {
  return request({
    url: '/event/original/combo/event-types',
    method: 'get',
  })
}

export function queryLogForward() {
  return request({
    url: '/event/original/queryOrilogForwardPolicyById',
    method: 'get',
  })
}

export function updateLogForward(obj) {
  return request({
    url: '/event/original/updateOrilogForwardPolicy',
    method: 'put',
    data: obj || {},
  })
}

export function queryForwardServerCombo() {
  return request({
    url: '/event/original/combo/forward-strategies',
    method: 'get',
  })
}
export function queryollectorServerCombo() {
  return request({
    url: '/event/original/queryCollectors',
    method: 'get',
  })
}

export function queryRarseRate() {
  return request({
    url: '/event/original/consumingTime',
    method: 'get',
  })
}

export function saveCustomQuery(obj) {
  return request({
    url: '/event/original/orilog-interactive/add',
    method: 'post',
    data: obj || {},
  })
}

export function queryCustomQueryCombo() {
  return request({
    url: '/event/original/orilog-interactive/combo',
    method: 'get',
  })
}

export function queryCustomQueryCondition(obj) {
  return request({
    url: `/event/original/orilog-interactive/query/`,
    method: 'get',
    params: obj || {},
  })
}

export function deleteCustomCondition(id) {
  return request({
    url: `/event/original/orilog-interactive/del/${id}`,
    method: 'delete',
  })
}

export function queryAdvanceCombo() {
  return request({
    url: `/event/original/expressions`,
    method: 'get',
  })
}
