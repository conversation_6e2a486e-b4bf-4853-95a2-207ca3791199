<!--
 * @Description: 关联事件 - 详情弹框 - 原始日志抽屉
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <detail-drawer :visible="dialogVisible" :detail-data="detailData" :loading="loading" @on-close="clickCancelDrawer"></detail-drawer>
</template>

<script>
import DetailDrawer from '@comp/DetailDrawer'

export default {
  components: {
    DetailDrawer,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    detailData: {
      type: [Object, Array],
      default() {
        return {}
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {},
  methods: {
    clickCancelDrawer() {
      this.dialogVisible = false
    },
  },
}
</script>
