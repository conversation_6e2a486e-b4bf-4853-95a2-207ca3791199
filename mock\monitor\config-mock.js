const { TableMock } = require('../util')

// 监控器列表信息
const tableData = new TableMock({
  total: 200,
  template: {
    monitorId: '@ID',
    monitorName: '@NAME',
    ipvAddress: '@EMAIL',
    assetName: '@NAME',
    assetType: '@ZIP',
    responsiblePerson: '@CNAME',
  },
})

// 下载内容
const downloadContext = `
PK<��P[Content_Types].xml�S�n�0����*6�PU�C���\\{�X�%����]8�R�
q�cfgfW�d�q�ZCB|��|�*�*h㻆},^�{Va�^K<4�6�N�XQ�ǆ�9�!P��$��҆�d�c�D�j);��ѝP�g��E�M'O�ʕ����H7L�h���R���G��^�'�{��zސʮB��3�˙��h.�h�W�жF�j娄CQՠ똈���}ιL�U:D�����%އ����,�B���[�	�� ;˱�	�{N��~��X�p�ykOL��kN�V��ܿBZ~����q�� �ar��{O�PKz��q;PK<��P_rels/.rels���j�0�_���8�\`�Q��2�m��4[ILb��ږ���.[K
�($}��v?�I�Q.���uӂ�h���x>=��@��p�H"�~�}�	�n����*"�H�׺؁�����8�Z�^'�#��7m{��O�3���G�u�ܓ�'��y|a�����D�	��l_EYȾ����vql3�ML�eh���*���\\3�Y0���oJ׏�	:��^��}PK��z��IPK<��PdocProps/app.xmlM��
�0D�~EȽ��ADҔ���A? ��6�lB�J?ߜ���0���ͯ�)�@��׍H6���V>��$;�SC
;̢(�ra�g�l�&�e��L!y�%��49��\`_���4G���F��J��Wg
�GS�b����
~�PK�|wؑ�PK<��PdocProps/core.xmlm�]K�0��J�}{�N�m�(Aq�d�]H�m�� �v�{�:+�wI��<���樆��I�Q��F�����~��I�גFcM�!���	�p�Ez�I�hτ�I�e^t���"�c�b��!^]��W�"���0p��I���HNJ)�}s�,�p@�:xȳ~؀N��d!��_�q�q5sq���n���^O_H��f�!(�(\`���F�����z�%MA��2������;/�+�5?	���5��������-�����PK?��K�PK<��Pxl/sharedStrings.xml=�A� ﾂ��.z0Ɣ�\`������,�����q2��o�ԇ���N�E��x5�z>�W���(R�K���^4{�����ŀ�5��y�V����y�m�XV�\\�.�j����
8�PKp��&x�PK<��P
xl/styles.xml�V�n�0��),߳�݆
J2�IE\\�Hܺ��X�Od�#�+p�{��
��vҤ]ӕi�7�}�}�9ǎ}_Ղ�[�
S2��FTf*gr�����)�J_��n8�))��$���zE&+� �LUT�L�� �z�JS�G<��F�"A��i,�b&�A�ZK���ҸP��\\�\`Hcs�n	��\\h�W1�Ӛ�	�:�$��5�l���#��M0O��G���J;c��o������߾x֞�EZ��G��8���G�\`2�wi\\k��3��?�T4�R�Ʊ�=�Ή^��ds:�(��_�ly��+L�.G=⩒>���}k�P:��Ӯ�x�[[sZX�k�,]kU�6SY�trF�J�<���(�v��s"&h?�tq탞Vͧ�]�"�{v�����5�ؿ�< O��4��PmŚ�R��ơ>�U9�
��}�0r�����t�L8��Z���^>J��V�=���}��c#RU|�.�\\�[��1͔��*�R
*-��I5�u;��E�e��f0M5F������v	#Sj&Ws5c-
����4��B\\e+�Gm�w]'9钼8�d��p��B������4&u4���a{.���D�y�^�dOPK���U_�PK<��Pxl/workbook.xml��AO�0�����w�tC����I�!1�{��Fk��	?��S�#'��=~����B�\\��A�D��I��aw�����F>c<�IC��XK�LO�*���E����L#��e?ȵR�ңp#��F�:g�%�OO!� L�R6�nL��4{ea1S��4t8$�6����~��h����Ԕ��s�e���4�M{�kg5��n@����j&,gry�~PK�����]PK<��Pxl/_rels/workbook.xml.rels��Mk�0@���}q���n/c����c+qh"K�迟���@;�$��{��~Γy�"#���i� �#
^�O7�\`D=E?1�b�n��8y�?$�YLE�8H���Z		g/
g����^�6�p��U���r΀%�좃����/�I�\`|�Rˤ��:f����~���mF�v�����:���ׯ�������p9HB�SyݵK~�����PK�;��3PK<��Pxl/worksheets/sheet1.xml��KN�0�����My	%AH�	Qk7�$��S��8��m@��-U,{���73vz0�-��5
���)l�L�����=~�o��u��@Fz�3� v�B��-��v\`褲NK����wdM��p�#�T��i�4�d������(�V��ok�SkoCpZf��C9�@��f���8�r�X	���xa�Pu�T�6�I�¶>~�V�xδ�ǹW%6O��3�V�,����9��$Jꑳ=sѾp|ᢅ-��,���f>��D�V����(�/��/����oO�@MQ� ʊ���J"k��(�E�K��"VML;YÙt�2�M-��|���Ye-�QzYˠ�
��3��/��v��p]��PKk#�%r�PK<��Pz��q;[Content_Types].xmlPK<��P��z��I|_rels/.relsPK<��P�|wؑ��docProps/app.xmlPK<��P?��K�gdocProps/core.xmlPK<��Pp��&x��xl/sharedStrings.xmlPK<��P���U_�
fxl/styles.xmlPK<��P�����]xl/workbook.xmlPK<��P�;��3	xl/_rels/workbook.xml.relsPK<��Pk#�%r�2
xl/worksheets/sheet1.xmlPK		?�
`

// 资产类型下拉框
const typeList = [
  {
    children: [
      { value: '172787409065345024', label: 'bigdatasss', type: '1', children: null },
      { value: '1046', label: '防病毒网关', type: '1', children: null },
      { value: '170532302286225408', label: '二层交换机', type: '1', children: null },
      { value: '1045', label: '光纤交换机', type: '1', children: null },
      { value: '110', label: '身份认证网关', type: '1', children: null },
      { value: '109', label: '流量控制', type: '1', children: null },
      { value: '108', label: 'UTM', type: '1', children: null },
      { value: '107', label: '路由器', type: '1', children: null },
      { value: '106', label: 'VPN', type: '1', children: null },
      { value: '104', label: '集线器', type: '1', children: null },
      { value: '103', label: '三层交换机', type: '1', children: null },
      { value: '102', label: '核心交换机', type: '1', children: null },
    ],
    label: '网络设备',
    type: null,
    value: '1',
  },
  {
    children: [
      { value: '171200221789814784', label: 'w', type: '2', children: null },
      { value: '506', label: '漏洞扫描', type: '2', children: null },
      { value: '170537078168223744', label: 'sss', type: '2', children: null },
      { value: '164144437155332096', label: '阿迪斯', type: '2', children: null },
      { value: '105', label: '防火墙', type: '2', children: null },
      { value: '205', label: '邮件监控系统', type: '2', children: null },
      { value: '204', label: '网站安全检测系统', type: '2', children: null },
      { value: '203', label: '网络审计系统', type: '2', children: null },
      { value: '202', label: '病毒监测系统', type: '2', children: null },
      { value: '201', label: '入侵检测系统IDS', type: '2', children: null },
    ],
    label: '安全设备',
    type: null,
    value: '2',
  },
  {
    children: [
      { value: '170902842591150080', label: 'wef', type: '3', children: null },
      { value: '307', label: 'KVM控制台', type: '3', children: null },
      { value: '306', label: '存储设备', type: '3', children: null },
      { value: '305', label: '数据采集服务器', type: '3', children: null },
      { value: '304', label: '数据库服务器', type: '3', children: null },
      { value: '303', label: '应用服务器', type: '3', children: null },
      { value: '302', label: '监控设备管理中心', type: '3', children: null },
      { value: '301', label: '监控设备控制台', type: '3', children: null },
      { value: '500', label: '工作站', type: '3', children: null },
      { value: '300', label: '服务器-未知', type: '3', children: null },
    ],
    label: '服务器',
    type: null,
    value: '3',
  },
  {
    children: [
      { value: '170902787733848064', label: 'test', type: '4', children: null },
      { value: '403', label: 'LED显示屏幕', type: '4', children: null },
      { value: '402', label: '液晶屏幕', type: '4', children: null },
      { value: '401', label: '显示操作终端', type: '4', children: null },
    ],
    label: '终端',
    type: null,
    value: '4',
  },
  {
    children: [
      { value: '173116333351763968', label: '重构资产扩展属性用', type: '5', children: null },
      { value: '505', label: '复印机', type: '5', children: null },
      { value: '504', label: '碎纸机', type: '5', children: null },
      { value: '503', label: '打印机', type: '5', children: null },
      { value: '502', label: '个人办公终端', type: '5', children: null },
      { value: '501', label: '未知设备', type: '5', children: null },
    ],
    label: '其他设备',
    type: null,
    value: '5',
  },
]

// 网段下拉框
const netSelect = [
  {
    Id: '1',
    domainName: '网段一',
  },
  {
    Id: '2',
    domainName: '网段二',
  },
]

// 资产自定义列
const custom = ['assetName', 'assetType', 'osType']

// 自定义属性
const customAttr = [
  {
    assetClass: '5',
    assetType: '502',
    componentType: 1,
    dataSource: [],
    id: '176372350986485760',
    length: 1,
    multiple: 2,
    name: 'ss',
    remark: '',
    required: 2,
    value: 's',
    values: [],
  },
  {
    assetClass: '5',
    assetType: '502',
    componentType: 1,
    dataSource: [],
    id: '176372350986485760',
    length: 1,
    multiple: 2,
    name: 'ss',
    remark: '',
    required: 2,
    value: 's',
    values: [],
  },
  {
    assetClass: '3',
    assetType: '303',
    componentType: 2,
    dataSource: [
      { value: '175234528099434497', label: 'duo2000', type: null },
      { value: '175234528103628801', label: 'duo2111', type: null },
      { value: '175234528099434498', label: 'duo2222', type: null },
      { value: '175234528107823104', label: 'duo2333', type: null },
      { value: '175234528107823107', label: 'duo2444', type: null },
      { value: '175234528103628800', label: 'duo2555', type: null },
      { value: '175234528099434496', label: 'duo26666', type: null },
      { value: '175234528099434499', label: 'duo2777', type: null },
      { value: '175234528107823105', label: 'duo2888', type: null },
      { value: '175234528107823108', label: 'duo2999', type: null },
      { value: '175234528103628803', label: 'duo2eee', type: null },
      { value: '175234528103628802', label: 'duo2qqq', type: null },
      { value: '175234528107823106', label: 'duo2rrr', type: null },
      { value: '175234528107823109', label: 'duo2www', type: null },
    ],
    id: '175232734564712448',
    length: 10,
    multiple: 1,
    name: 'test下拉多非必',
    remark: '脍',
    required: 2,
    value: null,
    values: ['175234528099434496', '175234528103628800', '175234528107823107'],
  },
  {
    assetClass: '3',
    assetType: '303',
    componentType: 3,
    dataSource: [],
    id: '175233104271638528',
    length: 1,
    multiple: 2,
    name: 'test时间非必',
    remark: '',
    required: 2,
    value: '2020-11-03 00:00:00',
    values: [],
  },
  {
    assetClass: '3',
    assetType: '303',
    componentType: 4,
    dataSource: [],
    id: '175233271045554176',
    length: 10,
    multiple: 2,
    name: '文本域',
    remark: '',
    required: 2,
    value: 'sfdsfsssss',
    values: [],
  },
  {
    assetClass: '3',
    assetType: '303',
    componentType: 5,
    dataSource: [
      { value: '175234832375218176', label: 'dan2111', type: null },
      { value: '175234832371023872', label: 'dan2222', type: null },
      { value: '175234832375218177', label: 'dan2333', type: null },
    ],
    id: '175233505549090816',
    length: 10,
    multiple: 2,
    name: 'test单选非必修改',
    remark: '',
    required: 2,
    value: '175234832375218176',
    values: [],
  },
  {
    assetClass: '1',
    assetType: '170532302286225408',
    componentType: 6,
    dataSource: [
      { value: '175236720025927682', label: 'rrr', type: null },
      { value: '175236720025927680', label: 'ttt', type: null },
      { value: '175236720025927681', label: 'yyy', type: null },
    ],
    id: '175235973955715072',
    length: 10,
    multiple: 1,
    name: '多选验证必填',
    remark: '标杆',
    required: 2,
    value: null,
    values: ['175236720025927680'],
  },
]

module.exports = [
  // 查询资产列表
  {
    url: '/assetmanagement/assets',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  // 查询资产类型下拉框
  {
    url: '/assetmanagement/combo/types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: typeList,
      }
    },
  },
  // 查询网段下拉框
  {
    url: '/assetmanagement/asset-networks',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: netSelect,
      }
    },
  },
  // 查询资产自定义列
  {
    url: '/assetmanagement/columns',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: custom,
      }
    },
  },
  // 修改资产自定义列
  {
    url: '/assetmanagement/columns',
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 添加资产
  {
    url: '/assetmanagement/asset',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  // 批量添加资产
  {
    url: '/assetmanagement/assets',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  // 修改资产
  {
    url: '/assetmanagement/asset',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 批量修改资产
  {
    url: '/assetmanagement/assets',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  // (批量)删除资产
  {
    url: `/assetmanagement/asset/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  // 导出资产信息
  {
    url: '/assetmanagement/download',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: downloadContext,
      }
    },
  },
  // 下载资产导入模板
  {
    url: '/assetmanagement/download/template',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: downloadContext,
      }
    },
  },
  // 导入资产
  {
    url: '/assetmanagement/upload',
    type: 'post',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  // 根据资产类型查询自定义属性
  {
    url: '/assetmanagement/properties/[A-Za-z0-9]/[A-Za-z0-9]',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: customAttr,
      }
    },
  },
]
