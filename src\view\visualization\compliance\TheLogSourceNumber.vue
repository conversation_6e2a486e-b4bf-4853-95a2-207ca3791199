<!--
 * @Description: 日志源资产数
 * @Version: 3.5
 * @Author: 
 * @Date: 2023/03/07
 * @Editor: 
 * @EditDate: 2023/03/07
-->
<template>
  <div class="widget">
    <header class="widget-header">
      {{ $t('visualization.compliance.title.logSourceNumber') }}
    </header>
    <main class="widget-body">
      <div class="icon-box">
        <i class="el-icon-monitor"></i>
      </div>
      <div class="detail-box">
        <p class="detail-box-word">
          <span>{{ logSourceNum }}</span>
          &nbsp;&nbsp; {{ $t('visualization.compliance.label.grantTotal', [percentage]) }}%
        </p>
        <div class="detail-box-chart">
          <el-progress :percentage="percentage" color="#37a2da" :format="formatProgress" :title="`${percentage}%`"></el-progress>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { queryLogSourceNumber } from '@api/visualization/compliance-api'
export default {
  data() {
    return {
      logSourceNum: 0,
      percentage: 0,
    }
  },
  mounted() {
    this.loadData()
  },
  methods: {
    formatProgress(percentage) {
      return ''
    },
    loadData() {
      this.getLogSourceNumber()
    },
    getLogSourceNumber() {
      queryLogSourceNumber().then((res) => {
        if (res) {
          this.logSourceNum = res.rizhiyuan
          this.percentage = parseFloat(((res.rizhiyuan * 100) / res.rizhiyuanMax).toFixed(2))
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.widget-body {
  .icon-box {
    background-color: #37a2da;
  }
  .detail-box {
    &-word {
      span {
        color: #37a2da;
        /*color: #e74a25;*/
      }
    }
    &-chart {
      ::v-deep .el-progress-bar {
        padding-right: 5px;
      }
    }
  }
}
</style>
