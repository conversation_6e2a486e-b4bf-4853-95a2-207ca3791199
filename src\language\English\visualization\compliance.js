export default {
  compliance: {
    title: {
      logSourceNumber: 'Log Source Asset Count',
      logTotalNumber: 'Total Logs Received',
      logStorageSpace: 'Log Storage Space',
      logStorageDuration: 'Log Storage Duration',
      systemHealthy: 'System Health Status',
      logNumberTrend: 'Log Collection Trend',
      logSourceType: 'Log Source Asset Type',
      devLogNumber: 'Log Source Log Count',
      devLogNumberTop: 'Log Source Log Count TOP10',
      devStorageDuration: 'Log Source Storage Duration',
      devStorageDurationTop: 'Log Source Storage Duration TOP10 (Unit: Days)',
      logReceivingStatus: 'Log Source Receiving Status',
      auditAlarmTread: 'Audit Alarm Count Trend',
      securityEventCount: 'Security Event Category Count',
      securityEventCountTop: 'Security Event Category Count Top10',
    },
    label: {
      logSourceName: 'Log Source Name',
      count: 'Count',
      storageDuration: 'Storage Duration (Days)',
      logSourceTotal: 'Total Log Sources {0}',
      grantTotal: 'Percentage of Total Authorized {0}',
      diskTotalSpace: 'Percentage of Total Disk Space {0}',
      storageDurationTotal: 'Total Log Storage Duration {0} Days',
      strip: 'Strip',
    },
    empty: {
      component: 'No Data',
    },
    receiving: {
      logSourceName: 'Log Source Name',
      status: 'Status',
      durationHours: '24h',
      durationAll: 'all',
    },
    cycle: {
      lastDay: 'Last Day',
      lastWeek: 'Last Week',
      lastMonth: 'Last Month',
      lastHalfYear: 'Last 6 Months',
      lastYear: 'Last Year',
    },
  },
}
