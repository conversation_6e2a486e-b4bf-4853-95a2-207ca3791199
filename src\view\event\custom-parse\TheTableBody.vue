<!--
 * @Description: 自定义解析 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-12-31
 * @Editor:
 * @EditDate: 2022-12-31
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @selection-change="clickSelectRows"
      >
        <el-table-column
          v-for="(item, key) in columns"
          :key="key"
          :prop="item"
          :label="$t(`event.customParse.label.${item}`)"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column prop="status" :label="$t('collector.management.table.run')">
          <template slot-scope="scope">
            <el-switch v-model="scope.row.status" active-value="1" inactive-value="0" @change="toggleStatus(scope.row)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="210">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
            <el-button v-has="'delete'" class="el-button--red" @click="clickDelete(scope.row)">
              {{ $t('button.delete') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      columns: ['patternValue', 'patternName', 'devTypeName', 'createTime'],
    }
  },
  methods: {
    clickSelectRows(sel) {
      this.$emit('on-select', sel)
    },
    toggleStatus(row) {
      this.$emit('on-toggle-status', row)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    clickDelete(row) {
      this.$emit('on-delete', row)
    },
  },
}
</script>
