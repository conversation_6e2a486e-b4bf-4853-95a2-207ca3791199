import request from '@util/request'

export function queryFaultEvents(obj) {
  return request({
    url: '/faulteventmanagement/queryFaultEvents',
    method: 'get',
    params: obj || {},
  })
}

export function queryFaultDetails(obj) {
  return request({
    url: '/faulteventmanagement/queryFaultEventDetails',
    method: 'get',
    params: obj || {},
  })
}

export function queryFaultClassCombo() {
  return request({
    url: '/faulteventmanagement/combo/faultclass',
    method: 'get',
  })
}

export function queryFaultLevelCombo() {
  return request({
    url: '/faulteventmanagement/combo/faultlevel',
    method: 'get',
  })
}
