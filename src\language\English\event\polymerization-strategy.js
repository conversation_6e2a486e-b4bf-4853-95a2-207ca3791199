export default {
  polymerizationStrategy: {
    strategy: 'Aggregation Strategy',
    table: {
      alarmName: 'Strategy Name',
      matchCriteriaList: 'Matching Rules',
      alarmTimeout: 'Maximum Time Limit (seconds)',
      countThreshold: 'Maximum Aggregation Count',
      eventFrequency: 'Event Frequency (seconds/entry)',
      handel: 'Operation',
    },
    label: {
      alarmTimeout: 'Maximum Time Limit',
      countThreshold: 'Maximum Aggregation Count',
      alarmStartTimeout: 'Maximum Time Limit (Start)',
      alarmEndTimeout: 'Maximum Time Limit (End)',
      countStartThreshold: 'Maximum Aggregation Count (Start)',
      countEndThreshold: 'Maximum Aggregation Count (End)',
      forwardType: 'Forward Type',
      eventFrequency: 'Event Frequency',
    },
    title: {
      update: 'Update Aggregation Strategy',
    },
    tip: {
      alarmTimeout: 'End time limit must be greater than start time limit',
      countThreshold: 'End aggregation count must be greater than start aggregation count',
    },
    upload: {
      chooseFile: 'Please select file',
      exceed: 'Current limit is 1 file, please delete before uploading',
      successUpload: 'Successfully imported parsing rule package',
    },
  },
}
