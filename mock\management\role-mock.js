const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    roleId: '@ID',
    roleName: '@NAME',
    'roleStatus|1': ['显示', '隐藏'],
    roleDescription: '@DATETIME',
  },
})
const usersCombo = [
  {
    value: '@ID',
    label: '@NAME',
    children: null,
  },
]
const resourcesComboList = [
  {
    value: '322043769894803',
    label: '系统管理',
    children: [
      {
        value: '322068128326769',
        label: '恢复',
        children: null,
      },
      {
        value: '322068128326767',
        label: '查询',
        children: null,
      },
      {
        value: '322068128326768',
        label: '保存',
        children: null,
      },
    ],
  },
  {
    value: '410090144516748',
    label: '日志备份',
    children: [
      {
        value: '410090144700912',
        label: '下载',
        children: null,
      },
      {
        value: '410090144793546',
        label: '查询',
        children: null,
      },
      {
        value: '410090144763040',
        label: '添加',
        children: null,
      },
      {
        value: '410090144783297',
        label: '删除',
        children: null,
      },
      {
        value: '410090144785897',
        label: '修改',
        children: null,
      },
    ],
  },
  {
    value: '391171291783801',
    label: '审计日志',
    children: [
      {
        value: '391181206412392',
        label: '下载',
        children: null,
      },
      {
        value: '391181206412391',
        label: '删除',
        children: null,
      },
      {
        value: '391178906964916',
        label: '查询',
        children: null,
      },
    ],
  },
]
module.exports = [
  {
    url: '/rolemanagement/role',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/rolemanagement/role/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'roleId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/rolemanagement/role',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/rolemanagement/role/[A-Za-z0-9]',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: tableData.detail(option, 'roleId'),
      }
    },
  },
  {
    url: '/rolemanagement/roles',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      const table = tableData.getMockData()
      const data = Object.keys(option.query).length > 0 ? table : table.rows
      return {
        code: 200,
        data,
      }
    },
  },
  {
    url: '/rolemanagement/resources-combo',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: resourcesComboList,
      }
    },
  },
  {
    url: '/rolemanagement/users-combo',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: usersCombo,
      }
    },
  },
  {
    url: '/rolemanagement/role/grant',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/rolemanagement/role/users',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: 'admin',
      }
    },
  },
]
