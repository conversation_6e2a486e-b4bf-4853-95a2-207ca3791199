export default {
  management: {
    asset: 'Asset Management',
    baseInfo: 'Basic Information',
    moreInfo: 'Extended Information',
    noneInfo: 'No Extended Properties',
    logInfo: 'Log Information',
    monitorInfo: 'Monitoring Information',
    faultEvent: 'Fault Event',
    perfEvent: 'Performance Event',
    ipArrayInfo: 'IP range cannot exceed 255',
    th: 'Custom Columns',
    allCheck: 'Select All',
    assetCode: 'Asset Code',
    assetName: 'Asset Name',
    assetType: 'Asset Type',
    assetTypeName: 'Asset Type',
    responsiblePerson: 'Responsible Person',
    startTime: 'Start Time',
    endTime: 'End Time',
    netWorkId: 'Network Segment',
    netWorkName: 'Network Segment',
    assetModel: 'Asset Model',
    manufactor: 'Manufacturer',
    osType: 'Operating System',
    ipvAddress: 'Asset IP',
    startIP: 'Start Asset IP',
    endIP: 'End Asset IP',
    memoryInfo: 'Memory Capacity',
    contactPhone: 'Contact Phone',
    email: 'Email Address',
    makerContactPhone: 'Manufacturer Phone',
    domaName: 'Domain Area',
    securityComponent: 'Security Component Type',
    riskRating: 'Risk Level',
    useStatusText: 'Asset Online Status',
    assetValue: 'Asset Value',
    assetDesc: 'Remarks',
    exceed: 'Current limit is 1 file, please delete before uploading',
    successUpload: 'Operation completed, successfully imported {0} records!',
    isOnlineCheck: 'Whether to perform online detection',
    log: {
      logSourceInfo: 'Log Source Information',
      logTotal: 'Total Logs Received',
      logDuration: 'Log Storage Duration',
      logTrend: 'Log Collection Trend',
      originalLog: 'Original Log',
      generalLog: 'General Log',
      logAccessTime: 'Log Receive Time',
      logLatestTime: 'Latest Log Time',
      type2Name: 'Original Log Name',
      eventName: 'Event Type',
      eventCategoryName: 'Event Category',
      level: 'Event Level',
      deviceCategoryName: 'Device Category',
      deviceTypeName: 'Device Type',
      time: 'Log Receive Time',
      logTime: 'Log Time',
      code: 'Feature Value',
      eventDesc: 'Event Description',
      fromIp: 'Source IP',
    },
    columns: {
      assetName: 'Asset Name',
      assetType: 'Asset Type',
      netWorkId: 'Network Segment',
      assetModel: 'Asset Model',
      manufactor: 'Manufacturer',
      osType: 'Operating System',
      memoryInfo: 'Memory Capacity',
      responsiblePerson: 'Responsible Person',
      contactPhone: 'Contact Phone',
      email: 'Email Address',
      makerContactPhone: 'Manufacturer Phone',
      assetCode: 'Asset Code',
      assetDesc: 'Remarks',
      ipvAddress: 'Asset IP',
      domaName: 'Domain Area',
      securityComponent: 'Security Component Type',
      riskRating: 'Risk Level',
      useStatusText: 'Asset Online Status',
    },
    placeholder: {
      info: 'Please add extended information',
      selectPlace: 'Please select',
      time: 'Select date and time',
      keyword: 'Please enter asset name/asset IP',
      ipvAddress: 'Asset IP',
      manufactor: 'Manufacturer',
      responsiblePerson: 'Responsible Person',
      assetArr: 'Asset Classification',
      assetName: 'Asset Name',
      netWorkId: 'Network Segment',
      assetModel: 'Asset Model',
      osType: 'Operating System',
      memoryInfo: 'Memory Capacity',
      contactPhone: 'Contact Phone',
      email: 'Email Address',
      makerContactPhone: 'Manufacturer Phone',
      assetCode: 'Asset Code',
      domaName: 'Domain Area',
      securityComponent: 'Security Component Type',
      assetDesc: 'Remarks',
      startIP: 'Start Asset IP',
      endIP: 'End Asset IP',
      riskRating: 'Risk Level',
    },
    importRule: {
      new: 'Override Rule',
      old: 'Ignore Rule',
    },
    downLoad: 'Download Template',
    chooseFile: 'Please select file',
    uploadRemind: 'When the imported asset name is the same as the system asset name, the imported data takes precedence',
    uploadTalk: 'When the imported asset name is the same as the system asset name, the system data takes precedence',
    originalLog: {
      title: 'Original Log',
      eventName: 'Event Name',
      level: 'Event Level',
      sourceIp: 'Source IP',
      targetIp: 'Destination IP',
      time: 'Log Receive Time',
    },
  },
}
