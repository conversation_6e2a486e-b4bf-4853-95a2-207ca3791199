<!--
 * @Description: 资产发现 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" :action="false" @on-close="clickCancelDialog">
    <template>
      <el-tabs v-model="form.activeName" type="card" @tab-click="handleClick">
        <el-tab-pane :label="$t('asset.management.baseInfo')" name="first">
          <el-form ref="formTemplate1" :model="form.model" :rules="rules" label-width="36%" disabled>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.assetName.label" :prop="form.info.assetName.key">
                  <el-input v-model.trim="form.model.assetName" :placeholder="$t('asset.management.placeholder.assetName')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.assetTypeName.label" prop="values">
                  <el-cascader
                    v-model="form.model.values"
                    :placeholder="$t('asset.management.placeholder.assetArr')"
                    :options="form.treeList"
                    filterable
                    :props="{ expandTrigger: 'hover' }"
                  ></el-cascader>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.netWorkName.label" :prop="form.info.netWorkName.key">
                  <el-select v-model="form.model.netWorkId" :placeholder="$t('asset.management.placeholder.netWorkId')" clearable filterable>
                    <el-option v-for="item in form.netList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.assetModel.label" :prop="form.info.assetModel.key">
                  <el-input v-model.trim="form.model.assetModel" :placeholder="$t('asset.management.placeholder.assetModel')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.manufactor.label" :prop="form.info.manufactor.key">
                  <el-input v-model.trim="form.model.manufactor" :placeholder="$t('asset.management.placeholder.manufactor')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.osType.label" :prop="form.info.osType.key">
                  <el-input v-model.trim="form.model.osType" :placeholder="$t('asset.management.placeholder.osType')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.ipvAddress.label" :prop="form.info.ipvAddress.key">
                  <el-input v-model.trim="form.model.ipvAddress" :placeholder="$t('asset.management.placeholder.ipvAddress')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.memoryInfo.label" :prop="form.info.memoryInfo.key">
                  <el-input v-model.trim="form.model.memoryInfo" :placeholder="$t('asset.management.placeholder.memoryInfo')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.responsiblePerson.label" :prop="form.info.responsiblePerson.key">
                  <el-input
                    v-model.trim="form.model.responsiblePerson"
                    :placeholder="$t('asset.management.placeholder.responsiblePerson')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.contactPhone.label" :prop="form.info.contactPhone.key">
                  <el-input v-model.trim="form.model.contactPhone" :placeholder="$t('asset.management.placeholder.contactPhone')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.email.label" :prop="form.info.email.key">
                  <el-input v-model.trim="form.model.email" :placeholder="$t('asset.management.placeholder.email')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.makerContactPhone.label" :prop="form.info.makerContactPhone.key">
                  <el-input
                    v-model.trim="form.model.makerContactPhone"
                    :placeholder="$t('asset.management.placeholder.makerContactPhone')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.assetCode.label" :prop="form.info.assetCode.key">
                  <el-input v-model.trim="form.model.assetCode" :placeholder="$t('asset.management.placeholder.assetCode')"></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.domaName.label" :prop="form.info.domaName.key">
                  <el-select v-model="form.model.domaName" :placeholder="$t('asset.management.placeholder.domaName')">
                    <el-option v-for="item in domaOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="form.info.securityComponent.label" :prop="form.info.securityComponent.key">
                  <el-input
                    v-model.trim="form.model.securityComponent"
                    :placeholder="$t('asset.management.placeholder.securityComponent')"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="8">
                <el-form-item :label="form.info.riskRating.label" :prop="form.info.riskRating.key">
                  <el-input v-model.trim="form.model.riskRating" :placeholder="$t('asset.management.placeholder.riskRating')"></el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="23">
                <el-form-item :label="form.info.assetDesc.label" :prop="form.info.assetDesc.key" label-width="12%">
                  <el-input
                    v-model.trim="form.model.assetDesc"
                    :placeholder="$t('asset.management.placeholder.assetDesc')"
                    type="textarea"
                    :rows="5"
                    class="width-max"
                  ></el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="$t('asset.management.moreInfo')" name="second">
          <el-form ref="formTemplate2" :model="form.model" :rules="rules" disabled>
            <div v-if="form.model.customAttr && form.model.customAttr.length > 0" style="min-height: 380px">
              <el-row v-for="(item, key) in form.model.customAttr" :key="key">
                <!--文本-->
                <el-form-item v-if="item.componentType == 1" :label="item.name" :required="item.required == 1" label-width="20%">
                  <el-input v-model.trim="item.value" :maxlength="item.length"></el-input>
                </el-form-item>
                <!--下拉-->
                <el-form-item v-if="item.componentType == 2" :label="item.name" :required="item.required == 1" label-width="20%">
                  <el-select
                    v-model="item.values"
                    :placeholder="$t('asset.management.placeholder.selectPlace')"
                    :multiple="item.multiple == 1"
                    :collapse-tags="item.multiple == 1"
                  >
                    <el-option v-for="i in item.dataSource" :key="i.value" :label="i.label" :value="i.value"></el-option>
                  </el-select>
                </el-form-item>
                <!--时间-->
                <el-form-item v-if="item.componentType == 3" :label="item.name" :required="item.required == 1" label-width="20%">
                  <el-date-picker
                    v-model="item.value"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    type="datetime"
                    :placeholder="$t('asset.management.placeholder.time')"
                  ></el-date-picker>
                </el-form-item>
                <!--文本域-->
                <el-form-item v-if="item.componentType == 4" :label="item.name" :required="item.required == 1" label-width="20%">
                  <el-input v-model.trim="item.value" :maxlength="item.length" type="textarea" :rows="5" class="width-max"></el-input>
                </el-form-item>
                <!--单选-->
                <el-form-item v-if="item.componentType == 5" :label="item.name" :required="item.required == 1" label-width="20%">
                  <el-radio v-for="i in item.dataSource" :key="i.value" v-model="item.value" :label="i.value">
                    {{ i.label }}
                  </el-radio>
                </el-form-item>
                <!--多选-->
                <el-form-item v-if="item.componentType == 6" :label="item.name" :required="item.required == 1" label-width="20%">
                  <el-checkbox-group v-model="item.values">
                    <el-checkbox v-for="(i, k) in item.dataSource" :key="k" :label="i.value">
                      {{ i.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
              </el-row>
            </div>
            <div v-else style="min-height: 380px; text-align: center; padding-top: 20%">
              {{ $t('asset.management.noneInfo') }}
            </div>
          </el-form>
        </el-tab-pane>
        <template v-if="tabPanel.monitorMenuShow">
          <el-tab-pane :label="$t('asset.management.monitorInfo')" name="third">
            <template v-if="form.activeName === 'third'">
              <section v-for="(item, index) in tabPanel.monitorInfo" :key="index">
                <basic-info :params="{ edId: item.edId, monitorId: item.monitorId }" :basic-model="item" :comp-info="item.compInfo"></basic-info>
                <template v-if="item.compInfo.indexOf('cpu') > -1">
                  <cpu-info :params="{ edId: item.edId, monitorId: item.monitorId }"></cpu-info>
                </template>
                <template v-if="item.compInfo.indexOf('memory') > -1">
                  <memory-info :params="{ edId: item.edId, monitorId: item.monitorId }"></memory-info>
                </template>
                <template v-if="item.compInfo.indexOf('disk') > -1">
                  <disk-info :params="{ edId: item.edId, monitorId: item.monitorId }"></disk-info>
                </template>
              </section>
            </template>
          </el-tab-pane>
          <el-tab-pane v-if="tabPanel.perfEventShow" :label="$t('asset.management.perfEvent')" name="fourth">
            <perf-info :params="queryParams"></perf-info>
          </el-tab-pane>
          <el-tab-pane v-if="tabPanel.faultEventShow" :label="$t('asset.management.faultEvent')" name="fifth">
            <fault-info :params="queryParams"></fault-info>
          </el-tab-pane>
          <el-tab-pane v-if="tabPanel.oriLogShow" :label="$t('asset.management.originalLog.title')" name="sixth">
            <original-info :params="queryParams"></original-info>
          </el-tab-pane>
        </template>
      </el-tabs>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import BasicInfo from '@view/monitor/management/comp/view/BasicInfo'
import CpuInfo from '@view/monitor/management/comp/view/CpuInfo'
import MemoryInfo from '@view/monitor/management/comp/view/MemoryInfo'
import DiskInfo from '@view/monitor/management/comp/view/DiskInfo'
import PerfInfo from '@view/monitor/management/comp/view/PerfInfo'
import FaultInfo from '@view/monitor/management/comp/view/FaultInfo'
import OriginalInfo from './OriginalInfo'
import { queryMonitorTab, queryMonitorTabData } from '@api/asset/management-api'

export default {
  name: 'DetailDialog',
  components: {
    BasicInfo,
    CpuInfo,
    MemoryInfo,
    DiskInfo,
    PerfInfo,
    FaultInfo,
    OriginalInfo,
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '900',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      domaOption: [],
      rules: {
        assetName: [
          {
            required: true,
          },
        ],
        values: [
          {
            required: true,
          },
        ],
        assetArr: [
          {
            required: true,
          },
        ],
        endIP: [
          {
            required: true,
          },
        ],
        startIP: [
          {
            required: true,
          },
        ],
      },
      tabPanel: {
        monitorMenuShow: false,
        faultEventShow: false,
        perfEventShow: false,
        oriLogShow: false,
        monitorInfo: [],
      },
      queryParams: {
        monitorId: '',
        edId: '',
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.initLoadData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initLoadData() {
      this.queryParams = Object.assign(
        {},
        {
          assetId: this.form.model.assetId,
          monitorId: this.form.model.monitorId,
        }
      )
      const assetId = this.form.model.assetId || ''
      this.queryMonitorTabShow(assetId)
      this.queryMonitorInfo(assetId)
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate1) this.$refs.formTemplate1.resetFields()
        if (this.$refs.formTemplate2) this.$refs.formTemplate2.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 切换tabs
    handleClick(tab, event) {},
    queryMonitorTabShow(assetId) {
      queryMonitorTab({ assetId: assetId }).then((res) => {
        if (res) {
          this.tabPanel.monitorMenuShow = res.monitor
          this.tabPanel.faultEventShow = res.faultEvent
          this.tabPanel.perfEventShow = res.perfEvent
          this.tabPanel.oriLogShow = res.oriLog
        }
      })
    },
    queryMonitorInfo(assetId) {
      queryMonitorTabData({ edId: assetId }).then((res) => {
        if (res) {
          this.tabPanel.monitorInfo = res
        }
      })
    },
  },
}
</script>

<style scoped lang="scss">
::v-deep .el-tabs__nav-wrap::after {
  width: 0px;
}
::v-deep textarea::-webkit-input-placeholder {
  font-family: Arial;
}
</style>
