<template>
  <div :id="id" ref="globalChart" :class="className" :style="[{ width: width }, { height: height }]"></div>
</template>

<script>
import * as echarts from 'echarts'
import 'echarts-gl'
import resize from '../mixin/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart-earth',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    globalData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    globalData: {
      handler(data) {
        this.initChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.initChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    initChart(data = this.globalData) {
      if (!data || Object.keys(data).length === 0) {
        data = []
      }
      this.chart = echarts.init(this.$refs.globalChart)
      this.drawChart(data)
    },
    drawChart(data) {
      const option = this.chartOptionConfig(data)
      this.chart.setOption(option)
    },
    chartOptionConfig(data) {
      let option = {
        tooltip: {
          show: false,
        },
        legend: {
          show: false,
        },
      }
      const [globe, series] = [this.chartGlobalConfig(), this.chartSeriesConfig(data)]
      option = Object.assign(option, globe, series)
      return option
    },
    chartGlobalConfig() {
      const earthBgImg = require('@asset/image/visualization/earth-surface-bg.png')
      return {
        globe: [
          {
            baseTexture: earthBgImg,
            shading: 'lambert',
            light: {
              main: {
                color: '#041a43',
                intensity: 10,
                shadow: true,
                alpha: 40,
                beta: 15,
              },
              ambient: {
                color: '#fff',
                intensity: 1,
              },
            },
            postEffect: {
              enable: true,
              bloom: {
                enable: true,
                bloomIntensity: 0.1,
              },
            },
            viewControl: {
              autoRotate: true,
              projection: 'perspective',
              alpha: 40,
              beta: 15,
              animation: true,
              center: [0, 0, 0],
              distance: 250,
              autoRotateSpeed: 4,
              rotateMouseButton: 'left',
            },
            globeRadius: 100,
            globeOuterRadius: 105,
          },
        ],
      }
    },
    chartSeriesConfig(data) {
      const seriesOption = { series: [] }
      const line = this.chartSeriesLine3DConfig(data)
      seriesOption.series.push(line)
      return seriesOption
    },
    chartSeriesLine3DConfig(lineData) {
      if (lineData.constructor === Array) {
        return {
          type: 'lines3D',
          coordinateSystem: 'globe',
          blendMode: 'lighter',
          effect: {
            show: true,
            trailWidth: 2,
            trailLength: 0.3,
            trailOpacity: 0.2,
          },
          lineStyle: {
            color: (params) => {
              const value = params.value
              switch (value) {
                case 101:
                case '101':
                  return '#0f0'
                case 102:
                case '102':
                  return '#00f'
                case 103:
                case '103':
                  return '#ff0'
                case 104:
                case '104':
                  return '#ff7f00'
                case 105:
                case '105':
                  return '#f00'
                default:
                  return '#fff'
              }
            },
            width: 0,
            opacity: 0,
          },
          data: lineData,
        }
      }
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
