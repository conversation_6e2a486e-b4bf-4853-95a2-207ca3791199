const getters = {
  routers: (state) => state.router.routers,
  addRouters: (state) => state.router.addRouters,
  theme: (state) => state.system.theme,
  pageSize: (state) => state.system.pageSize,
  token: (state) => state.user.token,
  publicKey: (state) => state.user.publicKey,
  aesKey: (state) => state.user.aesKey,
  systemName: (state) => state.user.systemName,
  mode: (state) => state.user.mode,
  userID: (state) => state.user.userID,
  defaultMenu: (state) => state.user.defaultMenu,
  homePath: (state) => state.user.homePath,
  actions: (state) => state.user.actions,
  status: (state) => state.websocket.status,
  assetDiscover: (state) => state.websocket.assetDiscover,
  managementNetwork: (state) => state.websocket.managementNetwork,
}
export default getters
