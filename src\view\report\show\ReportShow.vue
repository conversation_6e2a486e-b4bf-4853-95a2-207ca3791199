<!--
 * @Description: 报表展示 -入口文件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-04-22
 * @Editor:
 * @EditDate: 2022-04-22
-->
<template>
  <div class="container-wrapper">
    <el-row :gutter="20">
      <el-col :span="12">
        <div class="panel">
          <div class="panel-heading panel-info">
            {{ $t('report.show.asset.reportName') }}
            <div class="panel-heading-action">
              <!-- <a ref="panel-collapse" href="#"><i class="el-icon-minus"></i></a> -->
            </div>
          </div>
          <div class="panel-widget">
            <div class="panel-widget-body">
              <el-form ref="formTemplate" :model="query.asset" :rules="rules" label-width="85px">
                <el-form-item :label="$t('report.show.asset.assetType')" prop="assetType">
                  <el-cascader
                    v-model="query.asset.assetType"
                    :options="options.assetType"
                    clearable
                    collapse-tags
                    :props="{ expandTrigger: 'hover', multiple: true }"
                    size="mini"
                  ></el-cascader>
                </el-form-item>
                <el-form-item :label="$t('report.show.asset.datetime')" prop="assetType">
                  <el-date-picker
                    v-model="query.asset.datetime"
                    type="datetimerange"
                    clearable
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    :start-placeholder="$t('time.option.startDate')"
                    :end-placeholder="$t('time.option.endTime')"
                    size="mini"
                  ></el-date-picker>
                </el-form-item>
              </el-form>
            </div>
            <div class="panel-widget-footer">
              <el-button v-has="'query'" @click="clickViewReport('asset')">
                {{ $t('button.query') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="panel">
          <div class="panel-heading panel-info">
            {{ $t('report.show.comprehensive.reportName') }}
            <div class="panel-heading-action">
              <!-- <a ref="panel-collapse" href="#"><i class="el-icon-minus"></i></a> -->
            </div>
          </div>
          <div class="panel-widget">
            <div class="panel-widget-body">
              <el-form ref="formTemplate" :model="query.comprehensive" :rules="rules" label-width="85px">
                <el-form-item :label="$t('report.show.comprehensive.datetime')" prop="assetType">
                  <el-date-picker
                    v-model="query.comprehensive.datetime"
                    type="datetimerange"
                    clearable
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    :start-placeholder="$t('time.option.startDate')"
                    :end-placeholder="$t('time.option.endTime')"
                    size="mini"
                  ></el-date-picker>
                </el-form-item>
              </el-form>
            </div>
            <div class="panel-widget-footer">
              <el-button v-has="'query'" @click="clickViewReport('comprehensive')">
                {{ $t('button.query') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="20" v-if="false">
      <el-col :span="12">
        <div class="panel">
          <div class="panel-heading panel-info">
            {{ $t('report.show.analysis.report') }}
            <div class="panel-heading-action">
              <!-- <a ref="panel-collapse" href="#"><i class="el-icon-minus"></i></a> -->
            </div>
          </div>
          <div class="panel-widget">
            <div class="panel-widget-body">
              <el-form ref="formTemplate" :model="query.analysis" :rules="rules" label-width="85px">
                <el-form-item :label="$t('report.show.asset.asset')" prop="assetType">
                  <el-select v-model="query.analysis.assetType" clearable>
                    <el-option v-for="item in options.assets" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('report.show.asset.datetime')" prop="datetime">
                  <el-date-picker
                    v-model="query.analysis.datetime"
                    type="datetimerange"
                    clearable
                    value-format="yyyy-MM-dd HH:mm:ss"
                    format="yyyy-MM-dd HH:mm:ss"
                    :start-placeholder="$t('time.option.startDate')"
                    :end-placeholder="$t('time.option.endTime')"
                    size="mini"
                  ></el-date-picker>
                </el-form-item>
              </el-form>
            </div>
            <div class="panel-widget-footer">
              <el-button v-has="'query'" @click="clickViewReport('analysis')">
                {{ $t('button.query') }}
              </el-button>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
    <preview-dialog :visible.sync="dialog.visible" :title="dialog.title" :width="'70%'" :url="dialog.url"></preview-dialog>
  </div>
</template>

<script>
import PreviewDialog from './ThePreviewDialog'
import { queryAssetType, queryAssets, queryReportTemplate } from '@api/report/show-api'
import { parseTime } from '@util/format'

export default {
  name: 'ReportShow',
  components: {
    PreviewDialog,
  },
  data() {
    return {
      query: {
        asset: {
          assetType: [],
          datetime: '',
        },
        comprehensive: {
          datetime: '',
        },
        analysis: {
          assetType: '',
          datetime: '',
        },
      },
      rules: {},
      options: {
        assetType: [],
        assets: [],
      },
      dialog: {
        visible: false,
        title: this.$t('report.show.dialog.title'),
        url: '',
      },
    }
  },
  computed: {
    defaultTimeRange() {
      const date1 = new Date()
      const date2 = new Date(date1)
      date2.setDate(date1.getDate() - 30)
      const [last, now] = [parseTime(date2.getTime(), '{y}-{m}-{d} 00:00:00'), parseTime(date1.getTime(), '{y}-{m}-{d} 23:59:59')]
      return [last, now]
    },
  },
  mounted() {
    this.query.comprehensive.datetime = this.defaultTimeRange
    this.initOptions()
  },
  methods: {
    initOptions() {
      queryAssetType().then((res) => {
        this.options.assetType = res
      })
      // queryAssets().then((res) => {
      //   this.options.assets = res
      // })
    },
    clickViewReport(type) {
      const params = this.handleQueryParams(type)
      if (type === 'analysis' && !this.query.analysis.assetType) return this.$message.error('请选择资产类型')
      this.viewReport(params)
    },
    handleQueryParams(type) {
      let params = {}
      if (type === 'asset') {
        this.query.asset.datetime = this.query.asset.datetime || ['', '']
        let assetType = this.query.asset.assetType
        if (Array.isArray(assetType)) {
          assetType = assetType.map((item) => item[1]).toString()
        }
        params = {
          reportType: type,
          assetType: assetType,
          startTime: this.query.asset.datetime[0],
          endTime: this.query.asset.datetime[1],
        }
      }
      if (type === 'comprehensive') {
        this.query.comprehensive.datetime = this.query.comprehensive.datetime || ['', '']
        params = {
          reportType: type,
          startTime: this.query.comprehensive.datetime[0],
          endTime: this.query.comprehensive.datetime[1],
        }
      }
      if (type === 'analysis') {
        this.query.analysis.datetime = this.query.analysis.datetime || ['', '']
        params = {
          reportType: type,
          assetType: this.query.analysis.assetType,
          startTime: this.query.analysis.datetime[0],
          endTime: this.query.analysis.datetime[1],
        }
      }
      return params
    },
    viewReport(params) {
      queryReportTemplate(params).then((res) => {
        if (res) {
          const address = process.env.NODE_ENV === 'production' ? '' : process.env.VUE_APP_PROXY_TARGET
          this.dialog.url = address + res
          this.dialog.visible = true
        }
      })
    },
  },
}
</script>
<style scoped lang="scss">
.container-wrapper {
  height: 100%;
  padding: 20px 20px 0 20px;
  @include theme('background-color', main-bg-color);
  .el-row {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  .el-col {
    .panel {
      border: 1px solid;
      @include theme('border-color', border-color);
      border-radius: 6px;
      @include theme('background-color', main-body-bg-color);
      &-heading {
        padding: 15px 25px;
        font-weight: 500;
        text-transform: uppercase;
        border-bottom: 1px solid;
        @include theme('border-color', border-color);
        &-action {
          float: right;
          a {
            @include theme('color', font-color);
            opacity: 0.5;
          }
          a:hover {
            opacity: 1;
          }
          a i {
            font-size: 12px;
            font-weight: 400;
          }
        }
      }
      &-widget {
        &-body {
          min-height: 145px;
          padding: 25px 25px 20px 25px;
          ::v-deep.el-form-item__label {
            font-size: 12px;
            /*color: #2b2b2b;*/
            @include theme('color', font-color);
          }
          .el-date-editor--datetimerange.el-input,
          .el-date-editor--datetimerange.el-input__inner {
            width: 70%;
          }
          .el-cascader {
            width: 70%;
          }
        }
        &-footer {
          padding: 0 25px 20px 25px;
        }
      }
    }
  }
}
</style>
