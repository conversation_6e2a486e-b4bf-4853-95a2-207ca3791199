import request from '@util/request'

export function addResource(obj) {
  return request({
    url: '/resourcemanagement/resource',
    method: 'post',
    data: obj || {},
  })
}

export function delResource(obj) {
  return request({
    url: `/resourcemanagement/resource/${obj.id}`,
    method: 'delete',
  })
}

export function updResource(obj) {
  return request({
    url: '/resourcemanagement/resource',
    method: 'put',
    data: obj || {},
  })
}

export function getResourcesList(obj) {
  return request({
    url: '/resourcemanagement/resources',
    method: 'get',
    params: obj || {},
  })
}

export function getResourceDetail(obj) {
  return request({
    url: `/resourcemanagement/resource/${obj.id}`,
    method: 'get',
  })
}

export function getActionToken() {
  return request({
    url: '/resourcemanagement/action-token',
    method: 'get',
  })
}
