<!--
 * @Description: 报表实例
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model="data.fuzzyField"
              prefix-icon="soc-icon-search"
              clearable
              :placeholder="$t('tip.placeholder.query', [$t('report.instance.table.name')])"
              @change="clickQueryReportInstanceTable"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="clickQueryReportInstanceTable">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('report.instance.name') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="reportInstanceTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="reportInstanceTableRowChange"
          @selection-change="reportInstanceTableSelectsChange"
        >
          <el-table-column type="expand" show-overflow-tooltip>
            <template slot-scope="props">
              <ul v-if="props.row.taskInstance.length > 0" class="instance-detail-wrapper">
                <li v-for="(item, index) in props.row.taskInstance" :key="index">
                  <section>{{ item.instanceTime }}</section>
                  <section>
                    <el-button v-has="'query'" class="el-button--wt" @click="clickLookReportInstance(item)">
                      {{ $t('button.look') }}
                    </el-button>
                    <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteReportInstance(props.row, item)">
                      {{ $t('button.delete') }}
                    </el-button>
                  </section>
                </li>
              </ul>
              <p v-else>
                {{ $t('tip.data.empty') }}
              </p>
            </template>
          </el-table-column>
          <el-table-column :label="$t('report.instance.table.name')" prop="taskName" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('report.instance.table.type')" prop="taskType" show-overflow-tooltip></el-table-column>
          <el-table-column :label="$t('report.instance.table.amount')" prop="taskAmount" show-overflow-tooltip></el-table-column>
          <el-table-column width="113" fixed="right">
            <template slot-scope="scope">
              <el-button v-has="'delete'" class="el-button--red" @click="clickClearReportInstance(scope.row)">
                {{ $t('button.clear') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="reportInstanceTableSizeChange"
        @current-change="reportInstanceTableCurrentChange"
      ></el-pagination>
    </footer>

    <preview-dialog :visible.sync="dialog.visible" :title="dialog.title" :width="'70%'" :url="dialog.url"></preview-dialog>
  </div>
</template>

<script>
import PreviewDialog from './TheInstanceDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@util/effect'
import { deleteReportInstanceData, queryReportInstanceTableData } from '@api/report/instance-api'

export default {
  name: 'ReportInstance',
  components: {
    PreviewDialog,
  },
  data() {
    return {
      data: {
        loading: false,
        debounce: null,
        table: [],
        selected: [],
        fuzzyField: '',
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        currentRow: {},
      },
      dialog: {
        visible: false,
        title: this.$t('report.instance.dialog.title'),
        url: '',
      },
    }
  },
  mounted() {
    this.initDebounceQuery()
    this.getReportInstanceTableData()
  },
  methods: {
    clickQueryReportInstanceTable() {
      this.data.debounce()
    },
    clickClearReportInstance(row) {
      this.deleteReportInstance(row.taskId)
    },
    clickLookReportInstance(instance) {
      this.dialog.url =  instance.instanceUrl
      this.dialog.visible = true
    },
    clickDeleteReportInstance(task, instance) {
      this.deleteReportInstance(task.taskId, instance.instanceId)
    },
    reportInstanceTableRowChange(row) {
      this.pagination.currentRow = row
    },
    reportInstanceTableSelectsChange(select) {
      this.data.selected = select
    },
    reportInstanceTableSizeChange(size) {
      this.pagination.pageSize = size
      this.getReportInstanceTableData()
    },
    reportInstanceTableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getReportInstanceTableData()
    },
    initDebounceQuery() {
      this.data.debounce = debounce(() => {
        this.pagination.pageNum = 1
        this.getReportInstanceTableData()
      }, 500)
    },
    deleteReportInstance(taskId, instanceId) {
      if (!instanceId) {
        instanceId = -1
      }
      this.$confirm(instanceId ? this.$t('tip.confirm.delete') : this.$t('tip.confirm.clear'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteReportInstanceData(taskId, instanceId).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.getReportInstanceTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    getReportInstanceTableData(
      params = {
        fuzzyField: this.data.fuzzyField,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryReportInstanceTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.visible = true
        this.data.loading = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.router-wrap-table {
  .table-body {
    ::v-deep ul.instance-detail-wrapper {
      li + li,
      li:first-child {
        border-top: 1px dashed #ccc;
      }

      li:last-child {
        border-bottom: 1px dashed #ccc;
      }

      & > li {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .el-button {
          border: none;

          &:hover,
          &:focus {
            // background-color: $CR;
          }

          &.el-button--red {
            color: $WT;
          }

          &.el-button--wt {
            color: $WT;
          }
        }
      }
    }
  }
}
</style>
