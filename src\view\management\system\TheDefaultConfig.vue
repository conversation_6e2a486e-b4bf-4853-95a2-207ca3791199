<template>
  <div class="tab-context-wrapper">
    <el-form ref="systemForm" :model="form.model" :rules="form.rule" label-width="180px">
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.defaultPassword')" prop="defaultPassword">
          <el-input v-model="form.model.defaultPassword" type="password" class="width-small"></el-input>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.defaultPassword') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.passwordComplexity')" prop="passwordComplexity">
          <el-checkbox-group v-model="form.model.passwordComplexity">
            <el-checkbox label="uppercase">{{ $t('management.system.label.upperCase') }}</el-checkbox>
            <el-checkbox label="lowercase">{{ $t('management.system.label.lowerCase') }}</el-checkbox>
            <el-checkbox label="number">{{ $t('management.system.label.number') }}</el-checkbox>
            <el-checkbox label="special">{{ $t('management.system.label.special') }}</el-checkbox>
          </el-checkbox-group>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.passwordComplexity') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.minPasswordLength')" prop="minPasswordLength">
          <el-input-number v-model="form.model.minPasswordLength" :min="6" :max="50"></el-input-number>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.minPasswordLength') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.captcha')" prop="captcha">
          <el-switch v-model="form.model.captcha"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.captcha') }}
          </section>
        </el-form-item>
      </el-col>

      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.accountLockEnable')" prop="accountLockEnable">
          <el-switch v-model="form.model.accountLockEnable"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.accountLockEnable') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.accountLockTime')" prop="accountLockTime">
          <el-input-number v-model="form.model.accountLockTime" :min="1" :max="999999"></el-input-number>
          <span>{{ $t('time.unit.second') }}</span>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.accountLockTime') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.accountLockCnt')" prop="accountLockCnt">
          <el-input-number v-model="form.model.accountLockCnt" :min="1" :max="999999"></el-input-number>
          <span>{{ $t('time.unit.times') }}</span>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.accountLockCnt') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.accountAutoUnlockEnable')" prop="accountAutoUnlockEnable">
          <el-switch v-model="form.model.accountAutoUnlockEnable"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.accountAutoUnlockEnable') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.accountAutoUnlockTime')" prop="accountAutoUnlockTime">
          <el-input-number v-model="form.model.accountAutoUnlockTime" :min="1" :max="999999"></el-input-number>
          <span>{{ $t('time.unit.minute') }}</span>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.accountAutoUnlockTime') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.systemTimeout')" prop="accessTokenValidTime">
          <el-input-number v-model="form.model.accessTokenValidTime" :min="1" :max="999999"></el-input-number>
          <span>{{ $t('time.unit.minute') }}</span>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.systemTimeoutLockTime') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.accountValidEnable')" prop="accountValidEnable">
          <el-switch v-model="form.model.accountValidEnable"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.accountValidEnable') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.passwordOverdueEnable')" prop="passwordOverdueEnable">
          <el-switch v-model="form.model.passwordOverdueEnable"></el-switch>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.passwordOverdueEnable') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.passwordOverdueTime')" prop="passwordOverdueTime">
          <el-input-number v-model="form.model.passwordOverdueTime" :min="1" :max="999999"></el-input-number>
          <span>{{ $t('time.unit.day') }}</span>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.passwordOverdueTime') }}
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.systemName')" prop="systemName">
          <el-input v-model="form.model.systemName" class="width-small"></el-input>
          <section class="form-validate-tip">
            请输入系统名称
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.systemLogo')" prop="systemLogo">
          <div class="logo-uploader">
            <el-upload class="avatar-uploader" action="#" :on-change="handleChange" :before-upload="beforeUpload" :show-file-list="false">
              <img v-if="form.model.systemLogo" :src="form.model.systemLogo" class="avatar" />
              <i v-else class="el-icon-plus avatar-uploader-icon"></i>
              <i v-if="form.model.systemLogo" class="el-icon-close avatar-uploader-remove-icon" @click="handleRemove"></i>
              <div slot="tip" class="el-upload__tip">只能上传jpg/png文件，且不超过500kb</div>
            </el-upload>
          </div>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.syslogPort')" prop="syslogPort">
          <el-input-number v-model="form.model.syslogPort" :min="1" :max="65535"></el-input-number>
          <section class="form-validate-tip">
            请输入syslog端口
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.webPort')" prop="webPort">
          <el-input-number v-model="form.model.webPort" :min="1" :max="65535"></el-input-number>
          <section class="form-validate-tip">
            请输入web端口
          </section>
        </el-form-item>
      </el-col>
      <el-col :span="12" :offset="5">
        <el-form-item :label="$t('management.system.label.localeLanguage')" prop="localeLanguage">
          <el-select v-model="form.model.localeLanguage" :placeholder="$t('tip.placeholder.select')">
            <el-option :label="$t('management.system.label.chinese')" value="zh-CN" />
            <el-option :label="$t('management.system.label.english')" value="en-US" />
          </el-select>
          <section class="form-validate-tip">
            {{ $t('management.system.tip.localeLanguage') }}
          </section>
        </el-form-item>
      </el-col>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-has="'upload'" @click="clickSaveSystemConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button v-has="'query'" @click="clickResetSystemConfig">
        {{ $t('button.reset.default') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { JSEncrypt } from 'jsencrypt'
import { prompt } from '@util/prompt'
import { querySystemConfigData, resetSystemConfigData, saveSystemConfigData } from '@api/management/system-api'

export default {
  data() {
    const validatorPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.password.empty')))
      } else if (value.length < 8) {
        callback(new Error(this.$t('validate.password.sizemin8')))
      } else {
        callback()
      }
    }
    const validatorComplexity = (rule, value, callback) => {
      if (value.length < 1) {
        callback(new Error('密码复杂度至少包含1种字符'))
      } else {
        callback()
      }
    }

    return {
      form: {
        model: {
          oldPassword: '',
          defaultPassword: '',
          passwordComplexity: [],
          minPasswordLength: 6,
          captcha: true,
          accountLockEnable: true,
          accountLockTime: 0,
          accountLockCnt: 0,
          accountAutoUnlockEnable: true,
          accountAutoUnlockTime: 0,
          accessTokenValidTime: 0,
          accountValidEnable: true,
          passwordOverdueEnable: true,
          passwordOverdueTime: 0,
          systemName: '',
          systemLogo: '',
          syslogPort: '',
          webPort: '',
          localeLanguage: 'zh-CN', // 新增字段，默认中文
        },
        rule: {
          defaultPassword: [
            {
              required: true,
              validator: validatorPassword,
              trigger: 'blur',
            },
          ],
          passwordComplexity: [
            {
              required: true,
              validator: validatorComplexity,
              trigger: 'change',
            },
          ],
          accountLockTime: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          accountLockCnt: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          accountAutoUnlockTime: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          passwordOverdueTime: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          localeLanguage: [{ required: true, message: this.$t('validate.empty'), trigger: 'change' }],
        },
      },
    }
  },
  mounted() {},
  methods: {
    init() {
      this.getSystemConfig()
    },
    getSystemConfig() {
      querySystemConfigData().then((res) => {
        this.form.model.oldPassword = res.defaultPassword
        this.form.model.defaultPassword = res.defaultPassword
        this.form.model.passwordComplexity = res.passwordComplexity?.split(',') || []
        this.form.model.minPasswordLength = res.minPasswordLength || 6
        this.form.model.captcha = res.captchaEnable
        this.form.model.accountLockEnable = res.accountLockEnable
        this.form.model.accountLockTime = res.accountLockTime
        this.form.model.accountLockCnt = res.accountLockCnt
        this.form.model.accountAutoUnlockEnable = res.accountAutoUnlockEnable
        this.form.model.accountAutoUnlockTime = res.accountAutoUnlockTime
        this.form.model.accessTokenValidTime = res.accessTokenValidTime
        this.form.model.accountValidEnable = res.accountValidEnable
        this.form.model.passwordOverdueEnable = res.passwordOverdueEnable
        this.form.model.passwordOverdueTime = res.passwordOverdueTime
        this.form.model.systemName = res.systemName
        this.form.model.systemLogo = res.systemLogo
        this.form.model.syslogPort = res.syslogPort
        this.form.model.webPort = res.webPort
        this.form.model.localeLanguage = res.localeLanguage || 'zh-CN'
      })
    },
    clickSaveSystemConfig() {
      this.$refs.systemForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const encryptor = new JSEncrypt()
            encryptor.setPublicKey(this.$store.getters.publicKey)
            let encryptFlag = 0
            if (this.form.model.defaultPassword === this.form.model.oldPassword) {
              encryptFlag = 1
            }
            this.saveSystemConfig({
              defaultPassword: encryptor.encrypt(this.form.model.defaultPassword),
              passwordComplexity: this.form.model.passwordComplexity.join(','),
              minPasswordLength: this.form.model.minPasswordLength,
              isPasswordEncrypt: encryptFlag,
              captchaEnable: this.form.model.captcha ? 1 : 0,
              accountLockEnable: this.form.model.accountLockEnable ? 1 : 0,
              accountLockTime: this.form.model.accountLockTime,
              accountLockCnt: this.form.model.accountLockCnt,
              accountAutoUnlockEnable: this.form.model.accountAutoUnlockEnable ? 1 : 0,
              accountAutoUnlockTime: this.form.model.accountAutoUnlockTime,
              accessTokenValidTime: this.form.model.accessTokenValidTime,
              accountValidEnable: this.form.model.accountValidEnable ? 1 : 0,
              passwordOverdueEnable: this.form.model.passwordOverdueEnable ? 1 : 0,
              passwordOverdueTime: this.form.model.passwordOverdueTime,
              systemName: this.form.model.systemName,
              systemLogo: this.form.model.systemLogo,
              syslogPort: this.form.model.syslogPort,
              webPort: this.form.model.webPort,
              localeLanguage: this.form.model.localeLanguage,
            })
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetSystemConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.resetSystemConfig()
      })
    },
    saveSystemConfig(obj) {
      saveSystemConfigData(obj).then((res) => {
        if (res) {
          prompt({
            i18nCode: 'tip.save.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.save.error',
            type: 'error',
          })
        }
      })
    },
    resetSystemConfig() {
      resetSystemConfigData().then((res) => {
        if (res) {
          this.getSystemConfig()
          prompt({
            i18nCode: 'tip.reset.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.reset.error',
            type: 'error',
          })
        }
      })
    },
    handleChange(file) {
      const isLt500K = file.size / 1024 / 1024 < 0.5
      if (!isLt500K) {
        this.$message.error('上传图片大小不能超过 500KB!')
      } else {
        this.getImageBase64(file.raw).then((base64) => {
          this.form.model.systemLogo = base64
        })
      }
    },
    beforeUpload(file) {
      const isJPG = file.type === 'image/jpeg'
      const isPNG = file.type === 'image/png'
      const isLt500K = file.size / 1024 / 1024 < 0.5
      if (!isJPG && !isPNG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!')
      }
      return isJPG && isPNG && isLt500K
    },
    handleRemove(e) {
      e.stopPropagation()
      e.preventDefault()
      this.form.model.systemLogo = ''
    },
    getImageBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = (error) => reject(error)
      })
    },
  },
}
</script>
<style lang="scss">
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  font-size: 22px;
  color: #8c939d;
  min-width: 64px;
  height: 64px;
  line-height: 64px;
  text-align: center;
}
.avatar-uploader-remove-icon {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 22px;
  color: #8c939d;
  cursor: pointer;
  &:hover {
    color: #409eff;
  }
}

.avatar {
  width: 64px;
  height: 64px;
  display: block;
}
</style>
