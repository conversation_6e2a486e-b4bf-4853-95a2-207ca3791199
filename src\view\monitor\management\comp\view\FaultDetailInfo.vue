<!--
 * @Description: 监控器展示 - 故障事件详情组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-17
 * @Editor:
 * @EditDate: 2021-08-17
-->
<template>
  <custom-dialog
    ref="dialogTemplateDetail"
    :visible="dialogVisible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <el-tabs v-model="activeName" type="card">
      <el-tab-pane :label="$t('monitor.management.view.fault.basicDetail')" name="first">
        <el-form :model="model" label-width="120px">
          <el-row>
            <el-col v-for="(item, index) in columns" :key="index" :span="item.key === 'faultModule' || item.key === 'faultSolution' ? 24 : 12">
              <el-form-item :prop="item.key" :label="item.label">
                <span v-if="item.key === 'currentStatus'">
                  {{ model[item.key] === 1 ? $t('monitor.management.status.normal') : $t('monitor.management.status.abnormal') }}
                </span>
                <span v-else-if="item.key === 'faultLevel'">
                  <level-tag :level="model[item.key]"></level-tag>
                </span>
                <span v-else>
                  {{ model[item.key] }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <el-tab-pane :label="$t('monitor.management.view.fault.faultDetail')" name="second">
        <main class="table-body-main">
          <el-table
            v-loading="table.loading"
            :data="table.data"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
            height="268"
          >
            <el-table-column
              v-for="(item, index) in tableColoums"
              :key="index"
              :prop="item"
              :label="$t(`monitor.management.view.fault.${item}`)"
              show-overflow-tooltip
            ></el-table-column>
          </el-table>
        </main>
        <footer class="table-footer">
          <el-pagination
            v-if="pagination.visible"
            small
            background
            align="right"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="pagination.pageNum"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="pagination.pageSize"
            :total="pagination.total"
            @size-change="tableSizeChange"
            @current-change="tableCurrentChange"
          ></el-pagination>
        </footer>
      </el-tab-pane>
    </el-tabs>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import LevelTag from '@comp/LevelTag'
import { queryFaultDetail } from '@api/monitor/view-api'

export default {
  components: {
    CustomDialog,
    LevelTag,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
    columns: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'first',
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      tableColoums: ['faultName', 'faultClassName', 'enterDate', 'recoveryDate'],
      detailModel: [],
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.activeName = 'first'
        this.queryTableData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.queryTableData()
    },
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.queryTableData()
    },
    clickCancel() {
      this.$refs.dialogTemplateDetail.end()
      this.dialogVisible = false
    },
    queryTableData(
      params = {
        faultNo: this.model.faultNo,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryFaultDetail(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
  },
}
</script>
