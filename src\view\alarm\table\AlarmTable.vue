<!--
 * @Description: 告警列表
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('alarm.table.label.name')])"
              clearable
              @change="inputQuery('e')"
              @keyup.enter.native="inputQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <section class="table-header-button">
          <el-button v-has="'update'" @click="clickAllConfirm">
            {{ $t('button.allConfirm') }}
          </el-button>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.name"
                  clearable
                  :placeholder="$t('alarm.table.label.name')"
                  @change="submitSeniorQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select v-model="query.seniorQuery.level" clearable :placeholder="$t('alarm.table.label.level')" @change="submitSeniorQuery('e')">
                  <el-option v-for="(item, index) in options.levelOption" :key="index + '-only'" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.auditType"
                  filterable
                  clearable
                  :placeholder="$t('alarm.table.label.auditTypeName')"
                  @change="submitSeniorQuery('e')"
                >
                  <el-option v-for="item in options.AuditTypeOption" :key="item.label" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.auditUser"
                  filterable
                  clearable
                  :placeholder="$t('alarm.table.label.auditUser')"
                  @change="submitSeniorQuery('e')"
                >
                  <el-option v-for="item in options.audiUserOption" :key="item.label" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.alarmStrategy"
                  clearable
                  filterable
                  :placeholder="$t('alarm.table.label.alarmStrategyName')"
                  @change="submitSeniorQuery('e')"
                >
                  <el-option v-for="item in options.AlarmStrategyOption" :key="item.label" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="5">
                <el-select v-model="query.seniorQuery.state" clearable :placeholder="$t('alarm.table.label.state')" @change="submitSeniorQuery('e')">
                  <el-option v-for="item in options.statusOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col :span="10">
                <el-date-picker
                  v-model="query.seniorQuery.createTime"
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :start-placeholder="$t('time.option.startCreateTime')"
                  :end-placeholder="$t('time.option.endCreateTime')"
                  @change="submitSeniorQuery('e')"
                ></el-date-picker>
              </el-col>
              <el-col align="right" :span="4">
                <el-button v-has="'query'" @click="submitSeniorQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQueryForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体内容-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('alarm.table.header') }}
        </h2>
        <el-button @click="clickCustomizeButton">
          {{ $t('button.th') }}
        </el-button>
      </header>
      <main class="table-body-main">
        <el-table
          v-if="show.tableShow"
          v-loading="data.loading"
          v-el-table-scroll="scrollAlarmEventTable"
          :data="data.table"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
        >
          <el-table-column width="50" type="index"></el-table-column>
          <el-table-column
            v-for="(item, index) in options.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`alarm.table.label.${item}`)"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <level-tag v-if="item === 'level'" :level="scope.row.level"></level-tag>
              <p v-else-if="item === 'state'">
                {{ scope.row[item] === 1 ? $t('alarm.table.state.done') : $t('alarm.table.state.pending') }}
              </p>
              <p v-else>
                {{ scope.row[item] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column width="160">
            <template slot-scope="scope">
              <el-button class="el-button--blue" @click="clickDetailButton(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
              <el-button v-if="scope.row.state === 0" class="el-button--blue" @click="clickDefineButton(scope.row)">
                {{ $t('button.confirm') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <!--分页组件-->
    <footer class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
        <i v-show="data.totalLoading" class="el-icon-loading"></i>
      </section>
      <section v-show="!data.totalLoading" class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span>{{ data.total }}</span>
      </section>
    </footer>
    <!--自定义列-->
    <col-dialog
      :visible.sync="dialog.columnDialog.visible"
      :title="dialog.columnDialog.title"
      :form="dialog.columnDialog.form"
      @on-submit="clickSubmitCustomize"
    ></col-dialog>
    <!--详情-->
    <detail-dialog
      :visible.sync="dialog.detailDialog.visible"
      :title="dialog.detailDialog.title"
      :form="dialog.detailDialog.form"
      :source-event-type="dialog.detailDialog.sourceEventType"
      :audit-loading="dialog.detailDialog.loading.auditLoading"
      :actions="false"
      @drawerShow="clickDetailDrawer"
    ></detail-dialog>
    <!--确认原因-->
    <confirm-dialog
      :visible.sync="dialog.confirmDialog.visible"
      :width="'35%'"
      :title="dialog.confirmDialog.title"
      :form="dialog.confirmDialog.form"
      @on-submit="clickSubmitConfirm"
    ></confirm-dialog>
    <!--抽屉-->
    <drawer :visible.sync="drawer.visible" :loading="drawer.loading" :detail-data="drawer.data"></drawer>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import colDialog from './AlarmTableCustomizeDialog'
import confirmDialog from './AlarmTableConfirmDialog'
import detailDialog from './AlarmTableDetailDialog'
import levelTag from '@comp/LevelTag'
import drawer from './AlarmTableDetailDrawer'
import { debounce } from '@/util/effect'
import { prompt } from '@util/prompt'
import {
  queryAlarmEventData,
  queryAlarmEventTotal,
  queryAlarmEventDetail,
  queryAuditUserOption,
  queryAuditTypeOption,
  queryAlarmStrategyOption,
  queryColumnsData,
  updateColumnsData,
  updateAlaramState,
  updateAlarmConfirmAll,
} from '@api/alarm/table-api'

export default {
  name: 'AlarmTable',
  inject: ['alarm'],
  directives: {
    elTableScroll,
  },
  components: {
    colDialog,
    detailDialog,
    confirmDialog,
    levelTag,
    drawer,
  },
  data() {
    return {
      data: {
        loading: false,
        table: [],
        total: 0,
        nomore: false,
        totalLoading: false,
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
        scroll: true,
      }, // 列表数据
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      }, // 分页信息
      show: {
        tableShow: true, // 列表显示隐藏
        seniorQueryShow: false, // 高级查询显示隐藏
      }, // 显示隐藏
      dialog: {
        columnDialog: {
          visible: false,
          title: this.$t('alarm.table.dialog.colTitle'),
          form: {
            model: {
              checkList: [],
              checkAll: false,
              isIndeterminate: false,
            },
          },
        },
        confirmDialog: {
          visible: false,
          title: this.$t('alarm.table.dialog.reasonTitle'),
          confirmWay: 'row',
          form: {
            model: {
              reason: '',
              state: '',
              id: '',
            },
            info: {
              reason: {
                key: 'reason',
                label: this.$t('alarm.table.label.reason'),
              },
            },
            row: {},
          },
        },
        detailDialog: {
          visible: false,
          title: this.$t('alarm.table.dialog.detailTitle'),
          form: {
            model: {
              id: '',
              name: '',
              level: '',
              auditTypeName: '',
              alarmStrategyName: '',
              state: '',
              createTime: '',
              updateTime: '',
              total: '',
              reason: '',
              auditEventId: '',
            },
            info: {
              name: {
                key: 'name',
                label: this.$t('alarm.table.label.name'),
              },
              level: {
                key: 'level',
                label: this.$t('alarm.table.label.level'),
              },
              auditType: {
                key: 'auditType',
                label: this.$t('alarm.table.label.auditType'),
              },
              alarmStrategy: {
                key: 'alarmStrategy',
                label: this.$t('alarm.table.label.alarmStrategy'),
              },
              total: {
                key: 'total',
                label: this.$t('alarm.table.label.total'),
              },
              state: {
                key: 'state',
                label: this.$t('alarm.table.label.state'),
              },
              createTime: {
                key: 'createTime',
                label: this.$t('alarm.table.label.createTime'),
              },
              reason: {
                key: 'reason',
                label: this.$t('alarm.table.label.reason'),
              },
            },
          },
          sourceEventType: '0', // 标识位 0安全 1关联
          loading: {
            auditLoading: false,
          },
        },
      }, // 弹出框信息
      query: {
        fuzzyField: '',
        seniorQuery: {
          alarmStrategy: '',
          auditUser: '',
          name: '',
          level: '',
          state: '',
          auditType: '',
          createTime: '',
        },
        tempParams: {},
      }, // 查询条件
      options: {
        audiUserOption: [],
        AlarmStrategyOption: [],
        AuditTypeOption: [],
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        statusOption: [
          {
            label: this.$t('alarm.table.state.pending'),
            value: 0,
          },
          {
            label: this.$t('alarm.table.state.done'),
            value: 1,
          },
        ],
        columnOption: ['name', 'level', 'auditTypeName', 'alarmStrategyName', 'total', 'createTime', 'updateTime', 'state'],
      }, // 下拉框option
      drawer: {
        visible: false,
        data: [],
        loading: false,
      },
    }
  },
  computed: {
    disableScroll() {
      return this.data.scroll
    },
  },
  watch: {
    $route: {
      handler(route) {
        const params = {
          pageSize: 20,
          fuzzyField: route.query.fuzzyField,
        }
        this.query.fuzzyField = route.query.fuzzyField
        this.data.table = []
        this.data.nomore = false
        this.initAlarmEventTable(params)
        this.queryAlarmEventTotalData(params)
      },
      immediate: true,
    },
  },
  mounted() {
    this.initLoadData()
  },
  methods: {
    // 页面初始化
    initLoadData() {
      this.queryColumn()
      this.queryAlarmEventTotalData()
      this.initDebounce()
      this.initOption()
      // if (Object.keys(this.$route.query).length === 0 || this.$route.query.fuzzyField === "") {
      //     this.initAlarmEventTable();
      // }
    },
    // 初始化防抖函数
    initDebounce() {
      this.initChangeDebounce()
      this.initStaticDebounce()
    },
    // 初始化变化的防抖方法
    initChangeDebounce() {
      this.data.debounce.query = debounce(() => {
        this.data.nomore = false
        this.data.table = []
        const params = this.handleQueryParam()
        this.initAlarmEventTable(params)
        this.queryAlarmEventTotalData(params)
      }, 500)
    },
    // 初始化固定的防抖方法
    initStaticDebounce() {
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.data.nomore = false
        this.data.table = []
        this.data.scroll = true
        this.query.seniorQuery = {
          name: '',
          level: '',
          auditType: '',
          createTime: '',
          auditUser: '',
          alarmStrategy: '',
          state: '',
        }
        setTimeout(() => {
          this.initAlarmEventTable()
        }, 150)
        this.queryAlarmEventTotalData()
      }, 500)
    },
    // 初始化下拉框Option
    initOption() {
      // 查询审计人员
      queryAuditUserOption().then((res) => {
        this.options.audiUserOption = res
      })
      // 查询审计类型
      queryAuditTypeOption().then((res) => {
        this.options.AuditTypeOption = res
      })
      // 查询告警策略
      queryAlarmStrategyOption().then((res) => {
        this.options.AlarmStrategyOption = res
      })
    },
    // 初始化加载告警列表
    initAlarmEventTable(
      params = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.data.scroll = true
      this.data.loading = true
      queryAlarmEventData(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.push(...res)
          this.data.scroll = true
          if (this.data.table.length > this.pagination.pageSize) {
            this.data.nomore = true
          }
        } else {
          this.data.table.push(...res)
          this.data.scroll = false
        }
        this.data.loading = false
      })
    },
    // 滚动加载告警列表
    scrollAlarmEventTable() {
      const lastRow = this.data.table[this.data.table.length - 1]
      let params = {}
      if (lastRow) {
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.tempParams, {
            pageSize: this.pagination.pageSize,
            id: lastRow.id,
            timestamp: lastRow.createTime,
          })
        } else {
          params = Object.assign(
            {},
            {
              pageSize: this.pagination.pageSize,
              id: lastRow.id,
              timestamp: lastRow.createTime,
              fuzzyField: this.query.fuzzyField,
            }
          )
        }
      }
      this.initAlarmEventTable(params)
    },
    // 查询告警列表总数
    queryAlarmEventTotalData(
      params = {
        fuzzyField: this.query.fuzzyField,
      }
    ) {
      this.data.totalLoading = true
      queryAlarmEventTotal(params).then((res) => {
        this.data.total = res
        this.data.totalLoading = false
      })
    },
    // 模糊查询
    inputQuery() {
      this.data.debounce.query()
    },
    // 查询自定义列
    queryColumn() {
      this.show.tableShow = false
      queryColumnsData().then((res) => {
        if (res.length !== 0) {
          this.options.columnOption = res
        }
        setTimeout(() => {
          this.show.tableShow = true
        }, 100)
      })
    },
    // 点击向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetQueryForm()
      this.initChangeDebounce()
    },
    // 点击查询按钮
    clickQueryButton() {
      this.query.fuzzyField = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetQueryForm()
      this.initChangeDebounce()
    },
    // 点击确认按钮
    clickDefineButton(row) {
      this.dialog.confirmDialog.visible = true
      this.dialog.confirmDialog.confirmWay = 'row'
      this.dialog.confirmDialog.form.model = {
        reason: '',
        state: row.state,
        id: row.id,
      }
      this.dialog.confirmDialog.form.row = row
    },
    clickAllConfirm() {
      this.dialog.confirmDialog.visible = true
      this.dialog.confirmDialog.confirmWay = 'all'
      this.dialog.confirmDialog.form.model = {
        reason: '',
      }
    },
    // 提交全部确认
    clickSubmitAllConfirm(formModel) {
      updateAlarmConfirmAll({ reason: formModel.reason }).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.define.success',
              type: 'success',
            },
            () => {
              this.alarm()
              this.data.table.map((item) => {
                item.state = 1
              })
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'warning',
          })
        } else {
          prompt({
            i18nCode: 'tip.define.error',
            type: 'error',
          })
        }
      })
    },
    // 提交确认信息
    clickSubmitConfirm(formModel, row) {
      if (this.dialog.confirmDialog.confirmWay === 'all') {
        this.clickSubmitAllConfirm(formModel)
      } else {
        updateAlaramState(Object.assign({}, formModel)).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.define.success',
                type: 'success',
              },
              () => {
                this.alarm()
                this.alarmTableWalk(row)
              }
            )
          } else if (res === 2) {
            prompt({
              i18nCode: 'tip.add.repeat',
              type: 'warning',
            })
          } else {
            prompt({
              i18nCode: 'tip.define.error',
              type: 'error',
            })
          }
        })
      }
    },
    // 提交自定义列
    clickSubmitCustomize(formModel) {
      updateColumnsData(formModel.checkList).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.queryColumn()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 点击自定义列按钮
    clickCustomizeButton() {
      this.dialog.columnDialog.visible = true
      queryColumnsData().then((res) => {
        this.dialog.columnDialog.form.model.checkList = res
        if (res.length === 8) {
          this.dialog.columnDialog.form.model.checkAll = true
          this.dialog.columnDialog.form.model.isIndeterminate = false
        } else if (res.length === 0) {
          this.dialog.columnDialog.form.model.checkList = [
            'name',
            'level',
            'auditTypeName',
            'alarmStrategyName',
            'total',
            'createTime',
            'updateTime',
            'state',
          ]
          this.dialog.columnDialog.form.model.checkAll = true
          this.dialog.columnDialog.form.model.isIndeterminate = false
        } else {
          this.dialog.columnDialog.form.model.checkAll = false
          this.dialog.columnDialog.form.model.isIndeterminate = true
        }
      })
    },
    // 点击详情按钮
    clickDetailButton({ id, createTime }) {
      this.dialog.detailDialog.loading.auditLoading = true
      queryAlarmEventDetail(id, createTime).then((res) => {
        // 审计事件内容
        this.dialog.detailDialog.loading.auditLoading = false
        this.dialog.detailDialog.sourceEventType = res.sourceEventType
        this.dialog.detailDialog.form.model = res
      })
      this.dialog.detailDialog.visible = true
    },
    // 点击打开抽屉
    clickDetailDrawer(row) {
      this.drawer = {
        visible: true,
        data: row,
      }
    },
    // 重置查询信息
    resetQueryForm() {
      this.data.debounce.resetQueryDebounce()
    },
    // 提交高级查询
    submitSeniorQuery() {
      this.data.debounce.query()
    },
    // 处理数据
    handleQueryParam() {
      let params = {}
      if (this.show.seniorQueryShow) {
        params = Object.assign({}, this.query.seniorQuery, {
          startDate: this.query.seniorQuery.createTime !== null ? this.query.seniorQuery.createTime[0] : '',
          endDate: this.query.seniorQuery.createTime !== null ? this.query.seniorQuery.createTime[1] : '',
          createTime: '',
          pageSize: this.pagination.pageSize,
        })
        this.query.tempParams = params
      } else {
        params = {
          pageSize: this.pagination.pageSize,
          fuzzyField: this.query.fuzzyField,
        }
      }
      return params
    },
    // 确认数据
    alarmTableWalk(row) {
      this.data.table.find((d) => {
        if (d.id === row.id) d.state = 1
      })
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
</style>
