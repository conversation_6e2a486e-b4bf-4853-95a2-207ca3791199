export default {
  management: {
    title: 'Monitor',
    props: {
      monitorName: 'Monitor Name',
      status: 'Status',
      monitorType: 'Monitor Type',
      monitorTypeName: 'Monitor Type',
      pollDate: 'Polling Time',
      edName: 'Monitored Asset Name',
      agentId: 'Proxy Server',
      agentIp: 'Proxy Server',
      edIp: 'Monitored Asset IP',
      agentStatus: 'Agent Status',
      monitorEnabled: 'Monitor Status',
      createUser: 'Creator',
      createDate: 'Creation Time',
      pollUnit: 'Polling Time',
      stateChange: 'Enable/Disable',
      health: 'Health',
      healthTip: {
        unableConnect: 'Service cannot connect',
        noData: 'No data',
        normal: 'Normal',
      },
    },
    status: {
      on: 'Enable',
      off: 'Disable',
      normal: 'Normal',
      abnormal: 'Abnormal',
      online: 'Online',
      offline: 'Offline',
    },
    placeholder: {
      startTime: 'Start Time',
      endTime: 'End Time',
    },
    config: {
      asset: {
        title: 'Asset Information',
        edName: 'Asset Name',
        edIp: 'IP Address',
        assetType: 'Asset Type',
        domaAbbr: 'Area Abbreviation',
        createDate: 'Creation Time',
      },
      cpu: {
        cpuUseRate: 'CPU Usage Threshold (%)',
        cpuTimes: 'Consecutive Threshold Reached Count',
      },
      memory: {
        memoryUseRate: 'Memory Usage Threshold (%)',
        memoryTimes: 'Consecutive Threshold Reached Count:',
      },
      disk: {
        diskUseRate: 'Disk Usage Threshold (%)',
      },
      snmp: {
        template: 'SNMP Template',
        version: 'Version',
        port: 'Port',
        readCommunity: 'Read COMMUNITY',
        writeCommunity: 'Write COMMUNITY',
        authWay: 'Authentication Method',
        authPwd: 'Authentication Password',
        encryptionWay: 'Encryption Method',
        encryptionPwd: 'Encryption Password',
        context: 'Context ID',
        contextName: 'Context Name',
        snmpUserName: 'Username',
        securityLevel: 'Security Level',
      },
    },
    view: {
      basic: {
        title: 'Basic Information',
        monitorName: 'Monitor Name',
        monitorTypeName: 'Monitor Type',
        edName: 'Monitored Asset Name',
        edIp: 'IP',
        currentMemory: 'Current Memory',
        currentCpu: 'Current CPU',
        currentDisk: 'Current Disk',
      },
      cpu: {
        cpuId: 'CPU',
        usage: 'Usage (%)',
      },
      disk: {
        diskName: 'Disk Volume',
        allDisk: 'Total Disk (G)',
        restDisk: 'Free Disk Space (G)',
        usage: 'Usage (%)',
      },
      perf: {
        title: 'Performance Event',
        perfName: 'Event Name',
        currentStatus: 'Current Status',
        perfStatus: 'Status',
        perfClassName: 'Event Type',
        perfLevel: 'Event Level',
        edName: 'Asset Name',
        domaName: 'Domain Area',
        enterDate: 'Occurrence Time',
        recoveryDate: 'Recovery Time',
        perfModule: 'Event Description',
        perfSolution: 'Solution',
        basicDetail: 'Basic Details',
        perfDetail: 'Historical Performance',
      },
      fault: {
        title: 'Fault Event',
        faultName: 'Event Name',
        currentStatus: 'Current Status',
        faultStatus: 'Status',
        faultClassName: 'Event Type',
        faultLevel: 'Event Level',
        edName: 'Asset Name',
        domaName: 'Domain Area',
        enterDate: 'Occurrence Time',
        recoveryDate: 'Recovery Time',
        faultModule: 'Event Description',
        faultSolution: 'Solution',
        basicDetail: 'Basic Details',
        faultDetail: 'Historical Faults',
      },
    },
  },
}
