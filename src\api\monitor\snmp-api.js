import request from '@util/request'

export function querySnmpTemplate() {
  return request({
    url: '/monitor/config/snmp-template',
    method: 'get',
  })
}

export function querySnmpVersion() {
  return request({
    url: '/monitormanagement/combo/snmpversions',
    method: 'get',
  })
}

export function queryAuthWay() {
  return request({
    url: '/monitormanagement/combo/snmpauthentications',
    method: 'get',
  })
}

export function queryEncryptionWay() {
  return request({
    url: '/monitormanagement/combo/snmpencryptions',
    method: 'get',
  })
}

export function querySecurityLevel() {
  return request({
    url: '/monitormanagement/combo/snmpsecuritylevels',
    method: 'get',
  })
}
