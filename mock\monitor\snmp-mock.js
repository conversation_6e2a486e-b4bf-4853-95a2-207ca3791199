const snmpTemplate = [
  {
    value: '9999',
    label: 'public',
    snmpNo: '9999',
    snmpCommunity: 'public',
    snmpName: null,
    snmpPort: '161',
    snmpStatus: null,
    snmpUserName: '',
    snmpVersion: '2',
    snmpWCommunity: 'public',
  },
]
const snmpVersion = [
  { value: '1', label: 'SNMP  V1' },
  { value: '2', label: 'SNMP V2c' },
  { value: '3', label: 'SNMP V3' },
]
const authWay = [
  { value: '-1', label: '----' },
  { value: '1', label: 'SHA' },
  { value: '2', label: 'MD5' },
]
const encryptionWay = [
  { value: '-1', label: '----' },
  { value: '1', label: '3DES' },
  { value: '2', label: 'AES128' },
  { value: '3', label: 'AES192' },
  { value: '4', label: 'AES256' },
  { value: '5', label: 'DES' },
]
const securityLevel = [
  { value: '-1', label: '----' },
  { value: '1', label: 'NOAUTH_NOPRIV' },
  { value: '2', label: 'AUTH_NOPRIV' },
  { value: '3', label: 'AUTH_PRIV' },
]
module.exports = [
  // 查询snmp模板
  {
    url: '/monitor/config/snmp-template',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: snmpTemplate,
      }
    },
  },
  // 查询snmp版本
  {
    url: '/combo/snmpversions',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: snmpVersion,
      }
    },
  },
  // 查询认证方式
  {
    url: '/combo/snmpauthentications',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: authWay,
      }
    },
  },
  // 查询加密方式
  {
    url: '/combo/snmpencryption',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: encryptionWay,
      }
    },
  },
  // 查询安全级别
  {
    url: '/combo/snmpsecuritylevels',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: securityLevel,
      }
    },
  },
]
