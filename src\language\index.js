import Vue from 'vue'
import VueI18n from 'vue-i18n'
import elementEnLocale from 'element-ui/lib/locale/lang/en'
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'
import zhCn from './SChinese'
import en from './English'

Vue.use(VueI18n)

const messages = {
  'en-US': {
    ...en,
    ...elementEnLocale,
  },
  'zh-CN': {
    ...zhCn,
    ...elementZhLocale,
  },
}
const i18n = new VueI18n({
  locale: 'zh-CN', // navigator.language || navigator.userLanguage || "zh-CN"
  messages,
})

export default i18n
