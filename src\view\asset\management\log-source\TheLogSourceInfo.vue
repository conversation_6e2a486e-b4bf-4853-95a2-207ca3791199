<!--
 * @Description: 日志源信息
 * @Version: 3.5
 * @Author:
 * @Date: 2023/03/09
 * @Editor:
 * @EditDate: 2023/03/09
-->
<template>
  <div class="widget">
    <header class="widget-header">
      <h2 class="widget-header-title">
        {{ $t('asset.management.log.logSourceInfo') }}
      </h2>
    </header>
    <main class="widget-body">
      <div class="widget-content">
        {{ $t('asset.management.log.logAccessTime') }}
        <span>{{ accessTime }}</span>
      </div>
      <div class="widget-content">
        {{ $t('asset.management.log.logLatestTime') }}
        <span>{{ latestTime }}</span>
      </div>
    </main>
  </div>
</template>

<script>
import { isEmpty } from '@util/common'
import { queryLogTimeInfo } from '@api/asset/management-api'

export default {
  props: {
    data: {
      required: true,
      type: Object,
    },
    height: {
      type: [Number, String],
      default: '150',
    },
  },
  data() {
    return {
      accessTime: '---',
      latestTime: '---',
    }
  },
  methods: {
    loadData() {
      this.getLogSourceInfo()
    },
    getLogSourceInfo() {
      this.resetFields()
      const params = {
        devId: this.data.devId,
        categoryID: this.data.categoryID,
        typeId: this.data.typeId,
        ip: this.data.ip,
      }
      queryLogTimeInfo(params).then((res) => {
        if (res) {
          this.accessTime = isEmpty(res.min) ? '---' : res.min
          this.latestTime = isEmpty(res.max) ? '---' : res.max
        }
      })
    },
    resetFields() {
      this.accessTime = '---'
      this.latestTime = '---'
    },
  },
}
</script>

<style lang="scss" scoped>
.widget {
  &-body {
    display: flex;
    justify-content: center;
    padding: 5px !important;
    height: calc(100% - 30px);
    text-align: center;
    .widget-content {
      padding: 16px 15px 0;
      border: 1px solid #e5ebec;
      border-left: 0;
      span {
        display: block;
        padding: 15px;
        font-size: 16px;
        color: #1873d7;
      }
    }
    .widget-content:last-child {
      border-right: 0;
    }
  }
}
</style>
