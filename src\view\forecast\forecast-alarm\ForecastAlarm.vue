<!--
 * @Description: 预测告警 - 入口文件(预测异常事件)
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-27
 * @Editor:
 * @EditDate: 2021-10-27
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" :forecast-type-option="options.forecastType" @on-change="changeQueryTable"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :forecast-type-option="options.forecastType"
      @on-detail="clickDetail"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import DetailDialog from './TheDetailDialog'
import { forecastType } from '@asset/js/code/option'
import { queryForecastAbnormalTable } from '@api/forecast/forecast-alarm-api'

export default {
  name: 'ForecastAlarm',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('forecast.forecastAlarm.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          type: '',
          infoItem: '',
          model: '',
          occurTime: [],
        },
      },
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        forecastType: forecastType,
      },
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        this.query.form.occurTime = this.query.form.occurTime || ['', '']
        params = Object.assign(params, {
          type: this.query.form.type,
          lable: this.query.form.infoItem,
          key: this.query.form.model,
          startTime: this.query.form.occurTime[0],
          endTime: this.query.form.occurTime[1],
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = { type: row.type, lable: row.lable }
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryForecastAbnormalTable(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
  },
}
</script>
