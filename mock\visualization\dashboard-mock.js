const { getURLLastPath } = require('../util')

const dashboardNameData = []

const compTypeData = [
  {
    label: '资产',
    value: 1,
    chart: [
      {
        id: '11',
        title: '资产类型分布TOP5统计',
        type: 'pie',
        visible: true,
      },
      {
        id: '12',
        title: '资产操作系统统计',
        type: 'bar',
        visible: true,
      },
      {
        id: '13',
        title: '资产所在网段分布统计',
        type: 'pie',
        visible: true,
      },
    ],
  },
  {
    label: '事件',
    value: 2,
    chart: [
      {
        id: '21',
        title: '最新安全事件类型分布top5(最近24小时)',
        type: 'pie',
        visible: true,
      },
      {
        id: '22',
        title: '最新安全事件等级分布(最近24小时)',
        type: 'bar',
        visible: true,
      },
      {
        id: '23',
        title: '安全事件发生趋势(30天)',
        type: 'line',
        visible: true,
      },
      {
        id: '24',
        title: '最新关联事件类型分布top5(最近24小时)',
        type: 'pie',
        visible: true,
      },
      {
        id: '25',
        title: '最新关联事件等级分布(最近24小时)',
        type: 'bar',
        visible: true,
      },
      {
        id: '26',
        title: '关联事件发生趋势(30天)',
        type: 'line',
        visible: true,
      },
    ],
  },
  {
    label: '审计',
    value: 3,
    chart: [
      {
        id: '31',
        title: '最新审计事件类型分布top5(最近24小时)',
        type: 'pie',
        visible: true,
      },
      {
        id: '32',
        title: '最新审计事件等级分布(最近24小时)',
        type: 'bar',
        visible: true,
      },
      {
        id: '33',
        title: '审计事件发生趋势(30天)',
        type: 'line',
        visible: true,
      },
    ],
  },
  {
    label: '告警',
    value: 4,
    chart: [
      {
        id: '41',
        title: '最新告警类型分布top5(最近24小时)',
        type: 'pie',
        visible: true,
      },
      {
        id: '42',
        title: '最新告警级别分布(最近24小时)',
        type: 'bar',
        visible: true,
      },
      {
        id: '43',
        title: '告警发生趋势(30天)',
        type: 'pie',
        visible: true,
      },
    ],
  },
]

const chartData = [
  {
    title: '资产操作系统统计',
    type: 'bar',
    option: {
      axis: {
        direction: 'horizontal',
        data: ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H'],
      },
      legend: ['X', 'Y', 'Z'],
      series: [
        [1, 4, 7, 10, 13, 16, 19, 22],
        [2, 5, 8, 11, 14, 17, 20, 23],
        [3, 6, 9, 12, 15, 18, 21, 24],
      ],
    },
  },
  {
    title: '安全事件发生趋势(30天)',
    type: 'line',
    option: {
      axis: {
        direction: 'horizontal',
        data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      },
      legend: ['A', 'B', 'C'],
      series: [
        [1, 7, 4, 10, 16, 13, 19, 22],
        [2, 11, 8, 5, 14, 13, 20, 17],
        [6, 3, 15, 12, 9, 18, 24, 21],
      ],
    },
  },
  {
    title: '资产类型分布TOP5统计',
    type: 'pie',
    option: {
      legend: ['A', 'B', 'C', 'D', 'E'],
      series: [20, 15, 25, 10, 30],
    },
  },
]

module.exports = [
  {
    url: '/visualization/panel/dashboards',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: dashboardNameData,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard',
    type: 'post',
    response: (option) => {
      dashboardNameData.unshift({
        dashboardId: String(new Date().getTime()),
        dashboardName: option.body.dashboardName,
        dashboardData: option.body.dashboardData,
      })
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard/type/[A-Za-z0-9]+',
    type: 'get',
    response: (option) => {
      const chartId = getURLLastPath(option.url)
      let [type, data] = ['', {}]
      compTypeData.forEach((list) => {
        list.chart.forEach((item) => {
          if (item.id === chartId) {
            type = item.type
          }
        })
      })

      chartData.forEach((item) => {
        if (item.type === type) {
          data = item
        }
      })
      return {
        code: 200,
        data: data,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard/chart/[A-Za-z0-9]+',
    type: 'get',
    response: (option) => {
      const chartId = getURLLastPath(option.url)
      let [type, data] = ['', {}]
      compTypeData.forEach((list) => {
        list.chart.forEach((item) => {
          if (item.id === chartId) {
            type = item.type
          }
        })
      })

      chartData.forEach((item) => {
        if (item.type === type) {
          data = item.option
        }
      })
      return {
        code: 200,
        data,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard/type',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: compTypeData,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard/[A-Za-z0-9]',
    type: 'get',
    response: (option) => {
      const id = getURLLastPath(option.url)
      let dashboardData = []
      dashboardNameData.forEach((item) => {
        if (item.dashboardId === id) {
          dashboardData = item.dashboardData
        }
      })
      return {
        code: 200,
        data: dashboardData,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard',
    type: 'put',
    response: (option) => {
      dashboardNameData.forEach((item) => {
        if (item.dashboardId === option.body.dashboardId) {
          item.dashboardName = option.body.dashboardName
          item.dashboardData = option.body.dashboardData
        }
      })
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/visualization/panel/dashboard/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      const id = getURLLastPath(option.url)
      dashboardNameData.forEach((item, index) => {
        if (item.dashboardId === id) {
          dashboardNameData.splice(index, 1)
        }
      })
      return {
        code: 200,
        data: true,
      }
    },
  },
]
