import { mapGetters } from 'vuex'
import * as echarts from 'echarts'
import empty from '../mixin/empty'
import resize from '../mixin/resize'

export default {
  mixins: [empty, resize],
  props: {
    className: {
      type: String,
      default: 'chart-container',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    proto: {
      type: Boolean,
      default: false,
    },
    mouseEvent: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  computed: {
    ...mapGetters(['theme']),
  },
  watch: {
    option: {
      handler(data) {
        this.configChart(data)
      },
      deep: true,
    },
    theme() {
      this.disposeChart()
      this.renderChart()
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.chart.showLoading()
      this.configChart()
      this.chart.hideLoading()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chartDom, this.theme)
    },
    getChart() {
      return this.chart
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
