/**
 * @func 获取浏览器已储存信息
 * @param {string} key - 将要获取存储storage的key
 * @param {string} type - 获取类型 分为session和local
 * @return {string|number|object|array} 取决于储存的是什么类型 返回对应key的value
 * <AUTHOR> @date 2018/10/22
 */
export function getStorage(key, type = 'session') {
  const storageType = type === 'session' ? window.sessionStorage : window.localStorage
  return JSON.parse(storageType.getItem(key))
}

/**
 * @func 储存浏览器信息
 * @param {string} key - 将要存储storage的key
 * @param {string} value - 将要存储storage的value
 * @param {string} type - 获取类型 分为session和local
 * @return {boolean} 是否储存成功
 * <AUTHOR> @date 2018/10/22
 */
export function setStorage(key, value = '', type = 'session') {
  const storageType = type === 'session' ? window.sessionStorage : window.localStorage
  storageType.setItem(key, JSON.stringify(value))
  return getStorage(key, type) === value
}
/**
 * @func 删除浏览器已储存信息
 * @param {string} key - 将要删除storage的key
 * @param {string} type - 获取类型 分为session和local
 * @return {boolean} 是否删除成功
 * <AUTHOR> @date 2018/10/22
 */
export function removeStorage(key, type = 'session') {
  const storageType = type === 'session' ? window.sessionStorage : window.localStorage
  storageType.removeItem(key)
  return getStorage(key, type) === null
}
