<!--
 * @Description: 系统管理 - 邮件服务
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="tab-context-wrapper">
    <el-form ref="emailForm" :model="form.model" :rules="form.rule" label-width="180px">
      <el-row>
        <el-col :span="7" :offset="1">
          <el-form-item :label="$t('management.system.label.sendMailServer')" prop="sendMailServer">
            <el-input v-model="form.model.sendMailServer" maxlength="30"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item :label="$t('management.system.label.port')" prop="sendPort">
            <el-input-number v-model="form.model.sendPort" controls-position="right" :max="65535" :min="0" class="width-max"></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item :label="$t('management.system.label.senderAddress')" prop="senderAddress">
            <el-input v-model="form.model.senderAddress" maxlength="50"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="7" :offset="1">
          <el-form-item :label="$t('management.system.label.serverAuth')">
            <el-switch v-model="form.model.serverAuth" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.model.serverAuth">
        <el-col :span="7" :offset="1">
          <el-form-item :label="$t('management.system.label.user')" prop="username">
            <el-input v-model="form.model.username" maxlength="32"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="7">
          <el-form-item :label="$t('management.system.label.password')">
            <el-input v-model="form.model.password" type="password" maxlength="64"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="form.model.serverAuth">
        <el-col :span="7" :offset="1">
          <el-form-item label="ssl">
            <el-switch v-model="form.model.ssl" :active-value="1" :inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <section class="tab-footer-button">
      <el-button v-has="'test'" :loading="form.model.loading" @click="clickTestEmailServeConfig">
        {{ $t('button.test') }}
      </el-button>
      <el-button v-has="'upload'" @click="clickSaveEmailServeConfig">
        {{ $t('button.save') }}
      </el-button>
      <el-button v-has="'query'" @click="clickResetEmailServeConfig">
        {{ $t('button.reset.default') }}
      </el-button>
    </section>
  </div>
</template>

<script>
import { JSEncrypt } from 'jsencrypt'
import { validateIp, validateDomain, validateEmail } from '@util/validate'
import { prompt } from '@util/prompt'

export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
    optionData: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    const validatorSever = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateIp(value) && !validateDomain(value)) {
        callback(new Error(this.$t('validate.ip.domain')))
      } else {
        callback()
      }
    }
    const validatorEmail = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.empty')))
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    return {
      time: {
        timer: null,
        value: '',
      },
      form: {
        model: {
          sendMailServer: '',
          sendPort: '',
          senderAddress: '',
          receiveMailServer: '',
          receivePort: '',
          serverAuth: true,
          username: '',
          password: '',
          ssl: true,
        },
        rule: {
          sendMailServer: [
            {
              required: true,
              validator: validatorSever,
              trigger: 'blur',
            },
          ],
          sendPort: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          senderAddress: [
            {
              required: true,
              validator: validatorEmail,
              trigger: 'blur',
            },
          ],
          receiveMailServer: [
            {
              required: true,
              validator: validatorSever,
              trigger: 'blur',
            },
          ],
          receivePort: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          username: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
        },
        option: {
          settingCycle: [
            {
              label: this.$t('time.cycle.month'),
              value: 'month',
            },
            {
              label: this.$t('time.cycle.week'),
              value: 'week',
            },
            {
              label: this.$t('time.cycle.day'),
              value: 'day',
            },
          ],
          encryption: [],
        },
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model = this.formData
      }
    },
    clickTestEmailServeConfig() {
      this.$refs.emailForm.validate((valid) => {
        if (valid) {
          this.$emit('on-test', this.handleParams())
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickSaveEmailServeConfig() {
      this.$refs.emailForm.validate((valid) => {
        if (valid) {
          this.$emit('on-save', this.handleParams())
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetEmailServeConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-reset')
      })
    },
    handleParams() {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.$store.getters.publicKey)
      return {
        sendMailServer: this.form.model.sendMailServer,
        sendPort: this.form.model.sendPort,
        senderAddress: this.form.model.senderAddress,
        receiveMailServer: this.form.model.receiveMailServer,
        receivePort: this.form.model.receivePort,
        serverAuth: this.form.model.serverAuth,
        ssl: this.form.model.ssl,
        username: this.form.model.username,
        password: encryptor.encrypt(this.form.model.password),
      }
    },
  },
}
</script>
