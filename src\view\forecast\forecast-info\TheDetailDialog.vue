<!--
 * @Description: 行为分析策略 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-25
 * @Editor:
 * @EditDate: 2021-10-25
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="90%"
    height="90%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-row>
        <forecast-chart :chart-data="analysis.baseline" :title="chartTitle.baseline" :height="300"></forecast-chart>
      </el-row>
      <el-row>
        <el-collapse v-model="activeName" accordion>
          <el-collapse-item title="高级分析" name="1">
            <div v-if="activeName === '1'">
              <forecast-chart :chart-data="analysis.mae" :title="chartTitle.mae"></forecast-chart>
              <forecast-chart :chart-data="analysis.mapee" :title="chartTitle.mapee"></forecast-chart>
              <forecast-chart :chart-data="analysis.mase" :title="chartTitle.mase"></forecast-chart>
              <forecast-chart :chart-data="analysis.mape" :title="chartTitle.mape"></forecast-chart>
              <forecast-chart :chart-data="analysis.smape" :title="chartTitle.smape"></forecast-chart>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-row>
      <el-row>
        <el-table
          v-loading="table.loading"
          :data="table.data"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="300"
        >
          <el-table-column width="50" type="index"></el-table-column>
          <template v-for="(item, key) in columns">
            <el-table-column :key="key" :prop="item" :label="$t(`forecast.forecastInfo.detail.${item}`)" show-overflow-tooltip></el-table-column>
          </template>
        </el-table>
      </el-row>
    </section>
  </custom-dialog>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import CustomDialog from '@comp/CustomDialog'
import ForecastChart from '../forecast-analysis/ForecastChart'
import { queryAnalysisData } from '@api/forecast/forecast-info-api'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    CustomDialog,
    ForecastChart,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: '1',
      table: {
        data: [],
      },
      columns: ['actualVal', 'expectedVal', 'key', 'name', 'time'],
      chartTitle: {
        baseline: this.$t('forecast.forecastAnalysis.chart.baselineChart'),
        mae: this.$t('forecast.forecastAnalysis.chart.maeChart'),
        mapee: this.$t('forecast.forecastAnalysis.chart.mapeeChart'),
        mase: this.$t('forecast.forecastAnalysis.chart.maseChart'),
        mape: this.$t('forecast.forecastAnalysis.chart.mapeChart'),
        smape: this.$t('forecast.forecastAnalysis.chart.smapeChart'),
      },
      analysisData: {},
      analysis: {
        baseline: [],
        mae: [],
        mapee: [],
        mase: [],
        mape: [],
        smape: [],
        table: [],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.initData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    initData() {
      this.activeName = '1'
      this.analysis = {
        baseline: [],
        mae: [],
        mapee: [],
        mase: [],
        mape: [],
        smape: [],
        table: [],
      }
      this.getAnalysisData()
    },
    getAnalysisData() {
      queryAnalysisData(this.model).then((res) => {
        this.analysisData = res
        this.assemblAnalysisData()
      })
    },
    assemblAnalysisData() {
      this.analysis.baseline.push(this.analysisData.times)
      this.analysisData.times.unshift('')
      this.analysisData.charts.map((item) => {
        item.values.unshift(item.name)
        if (item.key === 'actual' || item.key === 'predict') {
          this.analysis.baseline.push(item.values)
        } else {
          for (const key in this.analysis) {
            if (item.key === key) {
              this.analysis[key].push(this.analysisData.times)
              this.analysis[key].push(item.values)
            }
          }
        }
      })
      this.table.data = this.analysisData.anomalys
    },
  },
}
</script>
