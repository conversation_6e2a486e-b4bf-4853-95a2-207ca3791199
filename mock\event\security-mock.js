const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    eventName: '@NAME',
    eventTypeName: '@CNAME',
    fromDeviceTypeName: '@CNAME',
    protocol: '@CNAME',
    count: '1',
    eventLevelName: '@word(4)',
    fromIP: '@word(4)',
    aggrStartDate: '@DATETIME',
    'state|0-1': '1',
  },
})
const data = {
  dateTime: '2020-12-08 13:30:45',
  dstIp: '*************',
  eventName: '邮件病毒',
  id: '1607405445349I8ev',
  level: '0',
  raw:
    '<5>time:2010-08-13 15:35:32;danger_degree:1;breaking_sighn:0;event:[50257]HTTP;src_addr:**********;src_port:1812;dst_addr:*************;dst_port:80;proto:TCP.HTTP',
  srcIp: '**********',
  timestamp: 1607405445338,
  typeName: '15003',
}
const columns = ['eventName', 'eventTypeName', 'aggrStartDate', 'fromDeviceTypeName', 'eventLevelId', 'fromIP']
const typeOption = [
  {
    pageSize: 0,
    pageNum: 0,
    value: '10',
    label: '拒绝服务',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '11',
    label: '扫描探测',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '12',
    label: '认证/授权/访问',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '13',
    label: '利用漏洞',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '14',
    label: '逃避行为',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '15',
    label: '病毒/木马',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '16',
    label: '可疑活动',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '17',
    label: '系统状况/配置',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '18',
    label: '违背策略',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '19',
    label: '关联事件',
  },
  {
    pageSize: 0,
    pageNum: 0,
    value: '20',
    label: '自定义类型',
  },
]
const categoryOption = [
  {
    value: '1',
    label: '网络设备',
    icon: null,
    description: null,
    children: [
      {
        value: '101',
        label: '二层交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '102',
        label: '核心交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '103',
        label: '三层交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '104',
        label: '集线器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '106',
        label: 'VPN',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '107',
        label: '路由器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '108',
        label: 'UTM',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '109',
        label: '流量控制',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '110',
        label: '身份认证网关',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '1045',
        label: '光纤交换机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '1046',
        label: '防病毒网关',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '2',
    label: '安全设备',
    icon: null,
    description: null,
    children: [
      {
        value: '105',
        label: '防火墙',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '201',
        label: '入侵检测系统IDS',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '202',
        label: '病毒监测系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '203',
        label: '网络审计系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '204',
        label: '网站安全检测系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '205',
        label: '邮件监控系统',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '506',
        label: '漏洞扫描',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '3',
    label: '服务器',
    icon: null,
    description: null,
    children: [
      {
        value: '300',
        label: '服务器-未知',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '301',
        label: '监控设备控制台',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '302',
        label: '监控设备管理中心',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '303',
        label: '应用服务器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '304',
        label: '数据库服务器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '305',
        label: '数据采集服务器',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '306',
        label: '存储设备',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '307',
        label: 'KVM控制台',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '500',
        label: '工作站',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '4',
    label: '终端',
    icon: null,
    description: null,
    children: [
      {
        value: '401',
        label: '显示操作终端',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '402',
        label: '液晶屏幕',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '403',
        label: 'LED显示屏幕',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
  {
    value: '5',
    label: '其他设备',
    icon: null,
    description: null,
    children: [
      {
        value: '501',
        label: '未知设备',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '502',
        label: '个人办公终端',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '503',
        label: '打印机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '504',
        label: '碎纸机',
        icon: '',
        description: '',
        children: null,
      },
      {
        value: '505',
        label: '复印机',
        icon: '',
        description: '',
        children: null,
      },
    ],
  },
]
const downloadContext = `
PK<��P[Content_Types].xml�S�n�0����*6�PU�C���\\{�X�%����]8�R�
q�cfgfW�d�q�ZCB|��|�*�*h㻆},^�{Va�^K<4�6�N�XQ�ǆ�9�!P��$��҆�d�c�D�j);��ѝP�g��E�M'O�ʕ����H7L�h���R���G��^�'�{��zސʮB��3�˙��h.�h�W�жF�j娄CQՠ똈���}ιL�U:D�����%އ����,�B���[�	�� ;˱�	�{N��~��X�p�ykOL��kN�V��ܿBZ~����q�� �ar��{O�PKz��q;PK<��P_rels/.rels���j�0�_���8�\`�Q��2�m��4[ILb��ږ���.[K
�($}��v?�I�Q.���uӂ�h���x>=��@��p�H"�~�}�	�n����*"�H�׺؁�����8�Z�^'�#��7m{��O�3���G�u�ܓ�'��y|a�����D�	��l_EYȾ����vql3�ML�eh���*���\\3�Y0���oJ׏�	:��^��}PK��z��IPK<��PdocProps/app.xmlM��
�0D�~EȽ��ADҔ���A? ��6�lB�J?ߜ���0���ͯ�)�@��׍H6���V>��$;�SC
;̢(�ra�g�l�&�e��L!y�%��49��\`_���4G���F��J��Wg
�GS�b����
~�PK�|wؑ�PK<��PdocProps/core.xmlm�]K�0��J�}{�N�m�(Aq�d�]H�m�� �v�{�:+�wI��<���樆��I�Q��F�����~��I�גFcM�!���	�p�Ez�I�hτ�I�e^t���"�c�b��!^]��W�"���0p��I���HNJ)�}s�,�p@�:xȳ~؀N��d!��_�q�q5sq���n���^O_H��f�!(�(\`���F�����z�%MA��2������;/�+�5?	���5��������-�����PK?��K�PK<��Pxl/sharedStrings.xml=�A� ﾂ��.z0Ɣ�\`������,�����q2��o�ԇ���N�E��x5�z>�W���(R�K���^4{�����ŀ�5��y�V����y�m�XV�\\�.�j����
8�PKp��&x�PK<��P
xl/styles.xml�V�n�0��),߳�݆
J2�IE\\�Hܺ��X�Od�#�+p�{��
��vҤ]ӕi�7�}�}�9ǎ}_Ղ�[�
S2��FTf*gr�����)�J_��n8�))��$���zE&+� �LUT�L�� �z�JS�G<��F�"A��i,�b&�A�ZK���ҸP��\\�\`Hcs�n	��\\h�W1�Ӛ�	�:�$��5�l���#��M0O��G���J;c��o������߾x֞�EZ��G��8���G�\`2�wi\\k��3��?�T4�R�Ʊ�=�Ή^��ds:�(��_�ly��+L�.G=⩒>���}k�P:��Ӯ�x�[[sZX�k�,]kU�6SY�trF�J�<���(�v��s"&h?�tq탞Vͧ�]�"�{v�����5�ؿ�< O��4��PmŚ�R��ơ>�U9�
��}�0r�����t�L8��Z���^>J��V�=���}��c#RU|�.�\\�[��1͔��*�R
*-��I5�u;��E�e��f0M5F������v	#Sj&Ws5c-
����4��B\\e+�Gm�w]'9钼8�d��p��B������4&u4���a{.���D�y�^�dOPK���U_�PK<��Pxl/workbook.xml��AO�0�����w�tC����I�!1�{��Fk��	?��S�#'��=~����B�\\��A�D��I��aw�����F>c<�IC��XK�LO�*���E����L#��e?ȵR�ңp#��F�:g�%�OO!� L�R6�nL��4{ea1S��4t8$�6����~��h����Ԕ��s�e���4�M{�kg5��n@����j&,gry�~PK�����]PK<��Pxl/_rels/workbook.xml.rels��Mk�0@���}q���n/c����c+qh"K�迟���@;�$��{��~Γy�"#���i� �#
^�O7�\`D=E?1�b�n��8y�?$�YLE�8H���Z		g/
g����^�6�p��U���r΀%�좃����/�I�\`|�Rˤ��:f����~���mF�v�����:���ׯ�������p9HB�SyݵK~�����PK�;��3PK<��Pxl/worksheets/sheet1.xml��KN�0�����My	%AH�	Qk7�$��S��8��m@��-U,{���73vz0�-��5
���)l�L�����=~�o��u��@Fz�3� v�B��-��v\`褲NK����wdM��p�#�T��i�4�d������(�V��ok�SkoCpZf��C9�@��f���8�r�X	���xa�Pu�T�6�I�¶>~�V�xδ�ǹW%6O��3�V�,����9��$Jꑳ=sѾp|ᢅ-��,���f>��D�V����(�/��/����oO�@MQ� ʊ���J"k��(�E�K��"VML;YÙt�2�M-��|���Ye-�QzYˠ�
��3��/��v��p]��PKk#�%r�PK<��Pz��q;[Content_Types].xmlPK<��P��z��I|_rels/.relsPK<��P�|wؑ��docProps/app.xmlPK<��P?��K�gdocProps/core.xmlPK<��Pp��&x��xl/sharedStrings.xmlPK<��P���U_�
fxl/styles.xmlPK<��P�����]xl/workbook.xmlPK<��P�;��3	xl/_rels/workbook.xml.relsPK<��Pk#�%r�2
xl/worksheets/sheet1.xmlPK		?�
`
module.exports = [
  //查询列表api
  {
    url: '/event/security/events',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  //查询自定义列
  {
    url: '/event/security/columns',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: columns,
      }
    },
  },
  //修改自定义列
  {
    url: '/event/security/columns',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  //查询资产类型
  {
    url: '/event/security/combo/asset-types',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: typeOption,
      }
    },
  },
  //查询事件类别
  {
    url: '/event/security/combo/event-categories',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: categoryOption,
      }
    },
  },
  //下载导出文字
  {
    url: '/event/security/download',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: downloadContext,
      }
    },
  },
  //查询原始日志
  {
    url: '/event/security/original/events',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: data,
      }
    },
  },
]
