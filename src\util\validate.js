/**
 * @func 账号输入框的正则表达式
 * @param {string} account - 需要校验的账号
 * @return {boolean}
 * <AUTHOR> @date 2019/05/12
 */
export function validateAccount(account) {
  const accountRegExp = /^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-]+$/
  return accountRegExp.test(account)
}

/**
 * @func 名称输入框的正则表达式
 * @param {string} name - 需要校验的名称
 * @param {number} level - 需要校验的名称的等级
 * @return {boolean}
 * <AUTHOR> @date 2019/05/12
 */
export function validateName(name, level = 0) {
  let nameRegExp = ''
  switch (level) {
    case 0:
      // 可输入英文 中文 数字 _(下划线) -(中划线) ， 并且_(下划线) -(中划线)不可以在开头和结尾
      nameRegExp = /^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/
      break
    case 1:
      // 可输入英文 中文 数字 _(下划线) -(中划线) .(小数点) ， 并且_(下划线) -(中划线) .(小数点) 不可以在开头和结尾
      nameRegExp = /^(?![_.\-])(?!.*?[_.\-]$)[a-zA-Z0-9_.\-\u4e00-\u9fa5]+$/
      break
    case 2:
      // 可输入英文 中文 数字 _(下划线) -(中划线) .(小数点) /(顿号线) ， 并且_(下划线) -(中划线) .(小数点) /(顿号线)不可以在开头和结尾
      nameRegExp = /^(?![_./\-])(?!.*?[_./\-]$)[a-zA-Z0-9_./\-\u4e00-\u9fa5]+$/
      break
    case 3:
      // 可输入英文 中文 数字 _(下划线) -(中划线) .(小数点) /(顿号线)  (空白符)， 并且_(下划线) -(中划线) .(小数点) /(顿号线) (空白符)不可以在开头和结尾
      nameRegExp = /^(?![_./\-\s])(?!.*?[_./\-\s]$)[a-zA-Z0-9_./\-\s\u4e00-\u9fa5]+$/
      break
    default:
      // 可输入英文 中文 数字 _(下划线) -(中划线) ， 并且_(下划线) -(中划线)不可以在开头和结尾
      nameRegExp = /^(?![_\-])(?!.*?[_\-]$)[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/
      break
  }
  return nameRegExp.test(name)
}

/**
 * @func 验证密码
 * @param {string} password - 需要校验的密码
 * @return {boolean}
 * @note  - 密码包括至少一个大写字母一个小写字母一个数字一个特殊字符
 * <AUTHOR> @date 2019/05/12
 */
export function validatePassword(password) {
  // 6~18个字符，使用字母、数字组合，需以字母开头 /^[a-zA-Z]{1}(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{5,17}$/
  const passwordRegExp = /^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z])(?=.*[_`~!@#$^&*()=|{}':;',\[\].<>/?\-%]).{0,}$/
  return passwordRegExp.test(password)
}

/**
 * @func 验证码输入框的正则表达式
 * @param {string} captcha - 需要校验的验证码
 * @return {boolean}
 * <AUTHOR> @date 2019/05/12
 */
export function validateCaptcha(captcha) {
  const captchaRegExp = /^([a-zA-Z0-9])*$/
  return captchaRegExp.test(captcha)
}

/**
 * @func 验证url路径
 * @param {string} url - 需要校验的url
 * @return {boolean}
 * <AUTHOR> @date 2019/05/13
 */
export function validateUrl(url) {
  const urlRegExp = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/
  return urlRegExp.test(url)
}

/**
 * @func 验证域名
 * @param {string} domain - 需要校验的域名
 * @return {boolean}
 * <AUTHOR> @date 2019/05/13
 */
export function validateDomain(domain) {
  const urlRegExp = /^(?=^.{3,255}$)[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+$/
  return urlRegExp.test(domain)
}

/**
 * @func 验证空位符(空格和回车)
 * @param {string} blank - 需要校验的空位符
 * @return {boolean}
 * <AUTHOR> @date 2019/05/13
 */
export function validateBlank(blank) {
  const blankRegExp = /\s+/
  return blankRegExp.test(blank)
}

/**
 * @func 验证小写字母
 * @param {string} str - 需要校验的str
 * @return {boolean}
 * <AUTHOR> @date 2019/05/14
 */
export function validateLowercase(str) {
  const lcRegExp = /^[a-z]+$/
  return lcRegExp.test(str)
}

/**
 * @func 验证大写字母
 * @param {string} str - 需要校验的str
 * @return {boolean}
 * <AUTHOR> @date 2019/05/14
 */
export function validateUppercase(str) {
  const ucRegExp = /^[A-Z]+$/
  return ucRegExp.test(str)
}

/**
 * @func 验证字母
 * @param {string} str - 需要校验的str
 * @return {boolean}
 * <AUTHOR> @date 2019/05/14
 */
export function validateAlphabets(str) {
  const letterRegExp = /^[a-zA-Z]+$/
  return letterRegExp.test(str)
}

/**
 * @func 验证邮箱
 * @param {string} email - 需要校验的email
 * @return {boolean}
 * <AUTHOR> @date 2019/05/15
 */
export function validateEmail(email) {
  const EmailRegExp = /^([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.\-]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
  return EmailRegExp.test(email)
}

/**
 * @func 验证手机号
 * @param {string} mobile - 需要校验的mobile
 * @return {boolean}
 * @note - 移动：134(1349除外）135 136 137 138 139
 *              147
 *              150 151 152 157 158 159
 *              182 183 184 187 188
 *         联通：130 131 132
 *              145
 *              155 156
 *              185 186
 *         电信：133
 *              153
 *              177
 *              180 181 189
 * <AUTHOR> @date 2019/05/16
 */
export function validateCellphone(mobile) {
  const mobileRegExp = /^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/
  return mobileRegExp.test(mobile)
}

/**
 * @func 验证座机号
 * @param {string} mobiles - 需要校验的mobiles
 * @return {boolean}
 * <AUTHOR> @date 2021/09/14
 */
export function validateMultiCellphone(mobiles) {
  const telRegExp = /^((\+?86)|(\(\+86\)))?(13[012356789][0-9]{8}|15[012356789][0-9]{8}|18[0123456789][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/
  const telArray = mobiles.split(',')
  for (let i = 0; i < telArray.length; i++) {
    if (!telRegExp.test(telArray[i])) {
      return false
    }
  }
  return true
}

/**
 * @func 验证多个手机号码
 * @param {string} tel - 需要校验的tel
 * @return {boolean}
 * <AUTHOR> @date 2019/05/16
 */
export function validateTelephone(tel) {
  const telRegExp = /^([0-9]{3,4}-)?[0-9]{7,8}$/
  return telRegExp.test(tel)
}

/**
 * @func 验证座机号-识别分机格式
 * @param {string} tel - 需要校验的tel
 * @return {boolean}
 * <AUTHOR> @date 2021/07/20
 */
export function validateExtensionTelephone(tel) {
  const telRegExp = /^(\d{2,5}-)?\d{6,9}(-\d{2,4})?$/
  return telRegExp.test(tel)
}

/**
 * @func 验证传真号
 * @param {string} faxNo - 需要校验的tel
 * @return {boolean}
 * <AUTHOR> @date 2021/07/20
 */
export function validateFax(faxNo) {
  const faxRegExp = /[\+? *[1-9]+]?[0-9 ]+/
  return faxRegExp.test(faxNo)
}

/**
 * @func 验证ipv4
 * @param {string} ipv4 - 需要校验的ipv4
 * @return {boolean}
 * <AUTHOR> @date 2019/05/17
 */
export function validateIpv4(ipv4) {
  const ipv4RegExp = /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
  return ipv4RegExp.test(ipv4)
}

/**
 * @func 验证ipv6
 * @param {string} ipv6 - 需要校验的ipv6
 * @return {boolean}
 * <AUTHOR> @date 2019/05/17
 */
export function validateIpv6(ipv6) {
  const ipv6Validate =
    /:/.test(ipv6) && ipv6.match(/:/g).length < 8 && /::/.test(ipv6)
      ? ipv6.match(/::/g).length === 1 && /^::$|^(::)?([\da-f]{1,4}(:|::))*[\da-f]{1,4}(:|::)?$/i.test(ipv6)
      : /^([\da-f]{1,4}:){7}[\da-f]{1,4}$/i.test(ipv6)
  return ipv6Validate
}

/**
 * @func 验证ip
 * @param {string} ip - 需要校验的ip
 * @return {boolean}
 * <AUTHOR> @date 2019/05/17
 */
export function validateIp(ip) {
  return validateIpv4(ip) || validateIpv6(ip)
}

/**
 * @func 验证port
 * @param port
 * @returns {boolean}
 * <AUTHOR> @date 2020/02/12
 */
export function validatePort(port) {
  const portRegExp = /^([0-9]|[1-9][0-9]{0,4})$/
  return portRegExp.test(port)
}

/**
 * @func 验证多IP端口地址
 * @param port
 * @returns {boolean}
 * <AUTHOR> @date 2022/04/13
 */
export function validateMultiIpPort(address) {
  const ipPortRegExp = /^((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}(\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])){1}$/
  const addressArr = address.split(',')
  for (let i = 0; i < addressArr.length; i++) {
    if (!ipPortRegExp.test(addressArr[i])) {
      return false
    }
  }
  return true
}

/**
 * 验证输入字符串不能有空格
 * @param text
 * @returns {boolean}
 */
export function validateSpace(text) {
  const textRegExp = /^[^ ]+$/
  return textRegExp.test(text)
}

/**
 * @func 验证MAC地址（物理地址）
 * @param {string} mac - 需要校验的mac
 * @return {boolean}
 * <AUTHOR> @date 2020/11/12
 */
export function validateMac(mac) {
  const macRegExp = /^[A-Fa-f0-9]{2}(-[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{2}(:[A-Fa-f0-9]{2}){5}$|^[A-Fa-f0-9]{12}$|^[A-Fa-f0-9]{4}(\.[A-Fa-f0-9]{4}){2}$/
  return macRegExp.test(mac)
}

/**
 * @func 验证口令
 * @param {string} pwd - 需要校验的pwd
 * @returns {boolean}
 * <AUTHOR> @date 2021/06/15
 */
export function validatePwd(pwd) {
  const pwdRegExp = /[^\u4E00-\u9FA5]/
  return pwdRegExp.test(pwd)
}
