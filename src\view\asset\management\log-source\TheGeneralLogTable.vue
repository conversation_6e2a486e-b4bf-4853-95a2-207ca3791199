<template>
  <div ref="originalLogTableDom" class="widget router-wrap-table">
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="query.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-cascader
                ref="cascader"
                v-model="query.form.deviceType"
                filterable
                clearable
                :options="options.deviceType"
                :placeholder="$t('event.generalLog.placeholder.deviceType')"
                :props="{ expandTrigger: 'hover' }"
                @change="changeQueryTable"
              ></el-cascader>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="query.form.severity"
                clearable
                :placeholder="$t('event.generalLog.placeholder.severity')"
                @change="changeQueryTable"
              >
                <el-option v-for="item in options.severity" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <el-date-picker
                v-model="query.form.occurTime"
                type="datetimerange"
                clearable
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                :start-placeholder="$t('time.option.startTime')"
                :end-placeholder="$t('time.option.endTime')"
                @change="changeQueryTable"
              ></el-date-picker>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select
                v-model="query.form.facility"
                clearable
                :placeholder="$t('event.generalLog.placeholder.facility')"
                @change="changeQueryTable"
              >
                <el-option v-for="item in options.facility" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="10">
              <range-picker
                v-model="query.form.ipRange"
                type="ip"
                :start-placeholder="$t('event.generalLog.placeholder.fromStartIp')"
                :end-placeholder="$t('event.generalLog.placeholder.fromEndIp')"
                @change="changeQueryTable"
              ></range-picker>
            </el-col>
            <el-col align="right" :offset="5" :span="4">
              <el-button v-has="'query'" @click="changeQueryTable">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
    <section>
      <main class="table-body-main">
        <el-table
          v-loading="table.loading"
          v-el-table-scroll="scrollTable"
          :data="table.data"
          infinite-scroll-disabled="disableScroll"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="255"
        >
          <el-table-column type="index" show-overflow-tooltip></el-table-column>
          <el-table-column v-for="(item, key) in columns" :key="key" :prop="item" :label="$t(`event.generalLog.label.${item}`)" show-overflow-tooltip>
            <template slot-scope="scope">
              <level-tag v-if="item === 'severity'" :level="scope.row[item]"></level-tag>
              <p v-else>
                {{ scope.row[item] }}
              </p>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </section>
    <section class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="table.nomore">{{ $t('validate.data.nomore') }}</span>
      </section>
      <section class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span v-loading="table.totalLoading">{{ table.total }}</span>
      </section>
    </section>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import RangePicker from '@comp/RangePicker'
import LevelTag from '@comp/LevelTag'
import { isEmpty } from '@util/common'
import { queryGeneralLogTable, queryGeneralLogTotal, queryDeviceTypeCombo, queryFacilityCombo, querySeverityCombo } from '@api/asset/management-api'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    LevelTag,
    RangePicker,
  },
  props: {
    height: {
      type: [Number, String],
      default: '300px',
    },
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      table: {
        loading: false,
        scroll: true,
        nomore: false,
        data: [],
        total: 0,
        totalLoading: false,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
      },
      columns: ['deviceName', 'insertTime', 'facilityName', 'severityName', 'fromIp', 'message'],
      query: {
        senior: true,
        form: {
          fuzzyField: '',
          severity: '',
          deviceType: '',
          facility: '',
          occurTime: [],
          ipRange: ['', ''],
        },
      },
      options: {
        severity: [],
        deviceType: [],
        facility: [],
      },
    }
  },
  computed: {
    disableScroll() {
      return this.table.scroll
    },
  },
  mounted() {
    this.initOptions()
  },
  methods: {
    loadData() {
      this.query.form.deviceType = [this.data.categoryID, this.data.typeId]
      this.query.form.ipRange = [this.data.ip]
      this.changeQueryTable()
    },
    resetQuery() {
      this.query.form = {
        fuzzyField: '',
        severity: '',
        deviceType: '',
        facility: '',
        occurTime: [],
        ipRange: ['', ''],
      }
      this.changeQueryTable()
    },
    changeQueryTable() {
      this.table.data = []
      const param = this.handleQueryParams()
      this.getTableData(param)
    },
    scrollTable() {
      const params = this.handleQueryParams(true)
      this.getTableData(params, false)
    },
    handleQueryParams(scroll = false) {
      this.table.nomore = false
      let params = {
        pageSize: this.pagination.pageSize,
      }
      if (scroll) {
        const lastRow = this.table.data[this.table.data.length - 1] || null
        if (lastRow) {
          params = Object.assign(params, {
            id: lastRow.id,
            timestamp: lastRow.timestamp,
          })
        }
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          timeRange: this.timeRange(),
          severity: this.query.form.severity,
          deviceType: this.query.form.deviceType.toString(),
          facility: this.query.form.facility,
          fromIp: this.ipRange(),
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    timeRange() {
      this.query.form.occurTime = this.query.form.occurTime || ['', '']
      const timeRange = this.query.form.occurTime.filter((item) => !isEmpty(item))
      return timeRange.toString()
    },
    ipRange() {
      let ip = ''
      const ipRange = this.query.form.ipRange.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipRange.length > 0) {
        ip = this.query.form.ipRange.join('-')
      }
      return ip
    },
    getTableData(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.form.fuzzyField,
      },
      total = true
    ) {
      this.table.scroll = true
      this.table.loading = true
      queryGeneralLogTable(param).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.table.data.push(...res)
          this.table.scroll = true
          if (this.table.data.length > this.pagination.pageSize) {
            this.table.nomore = true
          }
        } else {
          this.table.data.push(...res)
          this.table.scroll = false
        }
        this.table.loading = false
        if (total) {
          this.getTableTotal(param)
        }
      })
    },
    getTableTotal(
      param = {
        pageSize: this.pagination.pageSize,
        fuzzyField: this.query.form.fuzzyField,
      }
    ) {
      this.table.totalLoading = true
      queryGeneralLogTotal(param).then((res) => {
        this.table.total = res
        this.table.totalLoading = false
      })
    },
    initOptions() {
      querySeverityCombo().then((res) => {
        this.options.severity = res
      })
      queryDeviceTypeCombo().then((res) => {
        this.options.deviceType = res
      })
      queryFacilityCombo().then((res) => {
        this.options.facility = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.widget {
  .table-header-extend {
    margin: 0;
    .el-row {
      padding: 5px 0;
      background-color: transparent;
    }
  }
}
.router-wrap-table .table-body-main {
  width: 100%;
}
</style>
