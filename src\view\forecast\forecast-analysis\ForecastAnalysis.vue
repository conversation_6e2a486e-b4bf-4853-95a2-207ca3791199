<!--
 * @Description: 预测分析 - 入口文件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-11
 * @Editor:
 * @EditDate: 2021-11-11
-->
<template>
  <div class="router-wrap-table">
    <table-header
      :condition.sync="query"
      :forecast-type-option="options.forecastType"
      @on-change="changeQueryTable"
      @on-strategy-config="clickStrategyConfig"
    ></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :forecast-type-option="options.forecastType"
      @on-detail="clickDetail"
    ></table-body>
    <table-footer :pagination.sync="pagination" @change-size="changeTableSize" @change-page="changeTablePage"></table-footer>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model"></detail-dialog>
    <strategy-dialog
      :width="'40%'"
      :visible.sync="dialog.strategy.visible"
      :title-name="title"
      :model="dialog.strategy.model"
      @on-submit="doSubmitStrategy"
    ></strategy-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import DetailDialog from './TheDetailDialog'
import StrategyDialog from './TheStrategyConfig'
import { forecastType } from '@asset/js/code/option'
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import { queryForecastInfoTable, queryStrategyConfig, updateStrategyConfig } from '@api/forecast/forecast-analysis-api'
export default {
  name: 'ForecastInfo',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    DetailDialog,
    StrategyDialog,
  },
  data() {
    return {
      title: this.$t('forecast.forecastAnalysis.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          type: '',
          infoItem: '',
        },
      },
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        forecastType: forecastType,
      },
      dialog: {
        detail: {
          visible: false,
          model: {},
        },
        strategy: {
          visible: false,
          model: {},
        },
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          type: this.query.form.type,
          lable: this.query.form.infoItem,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = { type: row.type, lable: row.lable }
    },
    clickStrategyConfig() {
      queryStrategyConfig().then((res) => {
        this.dialog.strategy.model = {
          status: res.status,
          externalSystem: isEmpty(res.externalSystem) ? '' : res.externalSystem.split(','),
          externalMail: isEmpty(res.externalMail) ? '' : res.externalMail,
        }
        this.dialog.strategy.visible = true
      })
    },
    doSubmitStrategy(obj) {
      const param = {
        status: obj.status,
        externalSystem: obj.externalSystem.toString(),
        externalMail: obj.externalMail,
      }
      updateStrategyConfig(param).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.config.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.config.error',
            type: 'error',
          })
        }
      })
    },
    changeTableSize(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    changeTablePage(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryForecastInfoTable(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
  },
}
</script>
