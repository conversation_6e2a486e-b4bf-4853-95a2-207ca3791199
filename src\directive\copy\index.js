import Vue from 'vue'

Vue.directive('copy', {
  bind(el, { value }) {
    el.$value = value
    el.handler = () => {
      if (!el.$value) {
        console.warn('没有需要复制的内容')
      }
      const textarea = document.createElement('textarea')
      // 将textarea设置为readonly目的是为了防止iOS系统自动唤起键盘
      textarea.readOnly = 'readonly'
      // 将textarea移出可见范围
      textarea.style.position = 'absolute'
      textarea.style.left = '-99999px'
      textarea.value = el.$value
      document.body.appendChild(textarea)
      textarea.select()
      const result = document.execCommand('Copy')
      if (result) {
        console.info('复制成功')
      }
      document.body.removeChild(textarea)
    }
    el.addEventListener('click', el.handler)
  },
  componentUpdated(el, { value }) {
    el.$value = value
  },
  unbind(el) {
    el.removeEventListener('click', el.handler)
  },
})
