import request from '@util/request'

export function queryPerformance(obj) {
  return request({
    url: '/hegui/management/queryXitongzhuangkuang',
    method: 'get',
    params: obj || {},
  })
}
export function queryLogsTop10(obj) {
  return request({
    url: '/hegui/management/queryRizhishebeishuliangTop10',
    method: 'get',
    params: obj || {},
  })
}
export function queryStorageTop10(obj) {
  return request({
    url: '/hegui/management/queryRizhishebeishichangTop10',
    method: 'get',
    params: obj || {},
  })
}
export function querySourceType(obj) {
  return request({
    url: '/hegui/management/queryFashengyuan',
    method: 'get',
    params: obj || {},
  })
}
export function queryGatherTrend(obj) {
  return request({
    url: '/hegui/management/rizhijieshouqushi?type=1',
    method: 'get',
    params: obj || {},
  })
}
