<!--
 * @Description: 威胁情报库 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :title="$t('dialog.title.detail', [titleName])"
    :visible="visible"
    :width="'60%'"
    :action="false"
    @on-close="clickCancelDialog"
  >
    <section>
      <el-form label-width="40%">
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.threatItem')">
            {{ detail.threatItem ? detail.threatItem : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.threatType')">
            {{ detail.category ? detail.category : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.threatLevel')">
            <template>
              <level-tag :level="detail.threatLevel"></level-tag>
            </template>
          </el-form-item>
        </el-col>
        <template v-if="detail.category === 'ip'">
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.country')">
              {{ detail.country ? detail.country : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.province')">
              {{ detail.province ? detail.province : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.city')">
              {{ detail.city ? detail.city : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.organization')">
              {{ detail.organization ? detail.organization : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.operator')">
              {{ detail.operator ? detail.operator : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.code')">
              {{ detail.code ? detail.code : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.longitude')">
              {{ detail.longitude ? detail.longitude : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.latitude')">
              {{ detail.latitude ? detail.latitude : $t('tip.without') }}
            </el-form-item>
          </el-col>
        </template>

        <template v-if="detail.category === 'hash'">
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.hashType')">
              {{ detail.hashType ? detail.hashType : $t('tip.without') }}
            </el-form-item>
          </el-col>
        </template>

        <template v-if="detail.category === 'url'">
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.objectType')">
              {{ detail.objectType ? detail.objectType : $t('tip.without') }}
            </el-form-item>
          </el-col>
        </template>

        <template v-if="detail.category === 'email'">
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.record')">
              {{ detail.record ? detail.record : $t('tip.without') }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="$t('repository.threatLibrary.table.objectType')">
              {{ detail.objectType ? detail.objectType : $t('tip.without') }}
            </el-form-item>
          </el-col>
        </template>

        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.frequency')">
            {{ detail.total ? detail.total : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.firstTime')">
            {{ detail.firstTime ? detail.firstTime : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.lastTime')">
            {{ detail.lastTime ? detail.lastTime : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.action')">
            {{ detail.actions ? detail.actions : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.malware')">
            {{ detail.malwares ? detail.malwares : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.attackProtocol')">
            {{ detail.attackInProtocol ? detail.attackInProtocol : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.activeTime')">
            {{ detail.activeTime ? detail.activeTime : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.confidence')">
            {{ detail.confidence ? detail.confidence : $t('tip.without') }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item :label="$t('repository.threatLibrary.table.subscriptionChannel')">
            {{ detail.channel ? detail.channel : $t('tip.without') }}
          </el-form-item>
        </el-col>
      </el-form>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import LevelTag from '@comp/LevelTag'
export default {
  components: {
    CustomDialog,
    LevelTag,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    titleName: {
      type: String,
      default: '',
    },
    detail: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'info',
      category: '',
    }
  },
  watch: {
    visible(newVisible) {
      this.dialogVisible = newVisible
    },
    dialogVisible(newVisible) {
      this.$emit('update:visible', newVisible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-dialog {
  &__body {
    height: 500px;
    .custom-dialog-body,
    .el-tabs {
      height: 100%;

      .el-tabs__content {
        height: calc(100% - 60px);

        .el-tab-pane {
          height: 100%;
        }
      }
    }
  }
}
</style>
