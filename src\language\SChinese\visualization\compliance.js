export default {
  compliance: {
    title: {
      logSourceNumber: '日志源资产数',
      logTotalNumber: '日志接收总数',
      logStorageSpace: '日志存储空间',
      logStorageDuration: '日志存储时长',
      systemHealthy: '系统运行状况',
      logNumberTrend: '日志采集趋势',
      logSourceType: '日志源资产类型',
      devLogNumber: '日志源日志数量',
      devLogNumberTop: '日志源日志数量TOP10',
      devStorageDuration: '日志源存储时长',
      devStorageDurationTop: '日志源存储时长TOP10（单位：天）',
      logReceivingStatus: '日志源接收状态',
      auditAlarmTread: '审计告警数量趋势',
      securityEventCount: '安全事件类别数量',
      securityEventCountTop: '安全事件类别数量Top10',
    },
    label: {
      logSourceName: '日志源名称',
      count: '数量',
      storageDuration: '存储时长（天）',
      logSourceTotal: '日志源总数{0}',
      grantTotal: '占授权总数{0}',
      diskTotalSpace: '占磁盘总空间{0}',
      storageDurationTotal: '共存储日志{0}天',
      strip: '条',
    },
    empty: {
      component: '暂无数据',
    },
    receiving: {
      logSourceName: '日志源名称',
      status: '状态',
      durationHours: '24h',
      durationAll: 'all',
    },
    cycle: {
      lastDay: '最近一天',
      lastWeek: '最近一周',
      lastMonth: '最近一月',
      lastHalfYear: '最近半年',
      lastYear: '最近一年',
    },
  },
}
