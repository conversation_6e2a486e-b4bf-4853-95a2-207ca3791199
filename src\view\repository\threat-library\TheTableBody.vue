<!--
 * @Description: 威胁情报库 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="loading"
        v-el-table-scroll="scrollTable"
        :data="data"
        infinite-scroll-disabled="disableScroll"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        fit
        height="100%"
      >
        <el-table-column width="50" type="index"></el-table-column>
        <el-table-column prop="threatItem" :label="$t('repository.threatLibrary.table.threatItem')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="category" :label="$t('repository.threatLibrary.table.threatType')" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('repository.threatLibrary.table.frequency')" prop="total" show-overflow-tooltip></el-table-column>
        <el-table-column :label="$t('repository.threatLibrary.table.threatLevel')" show-overflow-tooltip>
          <template slot-scope="scope">
            <level-tag :level="scope.row.threatLevel"></level-tag>
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip :label="$t('repository.threatLibrary.table.firstTime')" prop="firstTime"></el-table-column>
        <el-table-column show-overflow-tooltip :label="$t('repository.threatLibrary.table.lastTime')" prop="lastTime"></el-table-column>
        <el-table-column width="120" fixed="right">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
import LevelTag from '@comp/LevelTag'
import elTableScroll from '@/directive/el-table-scroll'
export default {
  components: {
    LevelTag,
  },
  directives: {
    elTableScroll,
  },
  props: {
    titleName: {
      required: true,
      type: String,
    },
    data: {
      required: true,
      type: Array,
    },
    loading: {
      required: true,
      type: Boolean,
    },
    scroll: {
      required: true,
      type: Boolean,
    },
  },
  computed: {
    disableScroll() {
      return this.scroll
    },
  },
  methods: {
    scrollTable() {
      this.$emit('on-scroll')
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
  },
}
</script>

<style scoped></style>
