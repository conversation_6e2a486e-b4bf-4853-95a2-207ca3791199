<!--
 * @Description: 关联事件 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" :loading="loading" @on-close="clickCancelDialog">
    <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
      <el-tab-pane label="详情信息" name="first">
        <section>
          <el-form ref="formTemplate" :model="form.model" label-width="120px">
            <el-row>
              <el-col v-for="(item, index) in formCol" :key="index" :span="12">
                <el-form-item :prop="item.key" :label="item.label">
                  <template>
                    <level-tag v-if="item.key === 'level'" :level="form.model[item.key]"></level-tag>
                    <p v-else>
                      {{ form.model[item.key] }}
                    </p>
                  </template>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </section>
      </el-tab-pane>
      <el-tab-pane label="原始日志查询" name="second">
        <section class="router-wrap-table">
          <section class="table-body">
            <el-table
              v-loading="data.loading"
              v-el-table-scroll="scrollOriginalTable"
              infinite-scroll-disabled="disableScroll"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              :data="data.table"
              highlight-current-row
              tooltip-effect="light"
              fit
              height="100%"
            >
              <el-table-column width="50" type="index"></el-table-column>
              <el-table-column prop="type2Name" :label="$t('event.relevance.detailOriginalColumn.type2Name')" show-overflow-tooltip></el-table-column>
              <el-table-column prop="eventName" :label="$t('event.relevance.detailOriginalColumn.eventName')"></el-table-column>
              <el-table-column prop="eventCategoryName" :label="$t('event.relevance.detailOriginalColumn.eventCategoryName')"></el-table-column>
              <el-table-column prop="level" :label="$t('event.relevance.detailOriginalColumn.level')">
                <template slot-scope="scope">
                  <level-tag :level="scope.row.level"></level-tag>
                </template>
              </el-table-column>
              <el-table-column prop="sourceIp" :label="$t('event.relevance.detailOriginalColumn.srcIp')"></el-table-column>
              <el-table-column prop="targetIp" :label="$t('event.relevance.detailOriginalColumn.dstIp')"></el-table-column>
              <el-table-column prop="time" width="140" :label="$t('event.relevance.detailOriginalColumn.dateTime')"></el-table-column>
              <el-table-column width="60">
                <template slot-scope="scope">
                  <el-button class="el-button--blue" @click="clickDetailDrawer(scope.row)">
                    {{ $t('button.detail') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </section>
        </section>
        <footer class="table-footer infinite-scroll">
          <section class="infinite-scroll-nomore">
            <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
            <i v-show="data.totalLoading" class="el-icon-loading"></i>
          </section>
          <section v-show="!data.totalLoading" class="infinite-scroll-total">
            <b>{{ $t('event.original.total') + ':' }}</b>
            <span>{{ data.total }}</span>
          </section>
        </footer>
      </el-tab-pane>
      <el-tab-pane label="事件分析" name="third">
        <section v-if="activeName === 'third'" class="fishbone-chart">
          <event-analyse-chart :fishbone-data="fishboneData"></event-analyse-chart>
        </section>
      </el-tab-pane>
    </el-tabs>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import CustomDialog from '@comp/CustomDialog'
import levelTag from '@comp/LevelTag'
import eventAnalyseChart from './EventRelevanceAnalyseChart'
import { debounce } from '@/util/effect'
import { queryOriginalData, queryOriginalTotal, queryFishboneData } from '@api/event/relevance-api'

export default {
  components: {
    CustomDialog,
    levelTag,
    eventAnalyseChart,
  },
  directives: {
    elTableScroll,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    form: {
      required: true,
      type: Object,
    },
    width: {
      type: String,
      default: '1000',
    },
    formCol: {
      type: Array,
      required: true,
    },
    actions: {
      type: Boolean,
      default: false,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      activeName: 'first',
      data: {
        loading: false,
        pageNum: 2,
        table: [],
        scroll: true,
        nomore: false,
        totalLoading: false,
        total: 0,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
      }, // 分页信息
      debounce: {
        click: null,
        chart: null,
      },
      fishboneData: {},
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
    disableScroll() {
      return this.data.scroll
    },
  },
  watch: {
    visible(nVal) {
      if (nVal) this.initDebounce()
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initDebounce() {
      this.debounce.click = debounce(() => {
        this.queryOriginalLogData(this.handleParams())
        this.queryOriginalLogTotal(this.handleParams())
      }, 200)
      this.debounce.chart = debounce(() => {
        this.queryRelevanceFishboneData()
      }, 200)
    },
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
      this.activeName = 'first'
      this.data = {
        loading: false,
        pageNum: 2,
        table: [],
        scroll: true,
        nomore: false,
        totalLoading: false,
        total: 0,
      }
    },
    clickDetailDrawer(row) {
      this.$emit('drawerShow', row)
    },
    scrollOriginalTable() {
      const lastRow = this.data.table[this.data.table.length - 1]
      let params = {}
      if (lastRow) {
        params = {
          eventId: this.form.model.eventId,
          createDate: this.form.model.createDate,
          updateDate: this.form.model.updateDate,
          pageSize: this.pagination.pageSize,
          pageNum: this.data.pageNum++,
          originalId: lastRow.id,
          timestamp: lastRow.timestamp,
        }
      }
      this.queryOriginalLogData(params)
    },
    handleClick({ name }) {
      if (name === 'second') {
        if (this.data.table.length > 0) return
        this.data.table = []
        this.debounce.click()
      } else if (name === 'third') {
        this.debounce.chart()
      } else {
        this.data = {
          loading: false,
          pageNum: 2,
          table: [],
          scroll: true,
          nomore: false,
          totalLoading: false,
          total: 0,
        }
      }
    },
    queryOriginalLogData(params = {}) {
      this.data.scroll = true
      this.data.loading = true
      queryOriginalData(params).then((res) => {
        if (res.length < this.pagination.pageSize) {
          this.data.table.push(...res)
          this.data.scroll = true
          if (this.data.table.length > this.pagination.pageSize) {
            this.data.nomore = true
          }
        } else {
          this.data.table.push(...res)
          this.data.scroll = false
        }
        this.data.loading = false
      })
    },
    queryOriginalLogTotal(params = {}) {
      this.data.totalLoading = true
      queryOriginalTotal(params).then((res) => {
        this.data.totalLoading = false
        this.data.total = res
      })
    },
    handleParams() {
      const { eventId, createDate, updateDate } = this.form.model
      return Object.assign(
        {},
        {
          eventId,
          createDate,
          updateDate,
          pageSize: this.pagination.pageSize,
          pageNum: 1,
        }
      )
    },
    queryRelevanceFishboneData() {
      queryFishboneData(this.form.model.eventId).then((res) => {
        this.fishboneData = res
      })
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
::v-deep .el-popover,
.el-popper {
  > p {
    overflow: scroll;
  }
}

::v-deep .el-dialog__body {
  min-height: 500px;
}

::v-deep .table-body {
  min-height: 350px;
  max-height: 350px;
}

::v-deep .router-wrap-table {
  position: relative;

  ::v-deep .table-footer {
    position: absolute;
    right: 10px;
    bottom: 0;
  }
}
.fishbone-chart {
  display: flex;
  flex-direction: column;
  width: calc(100% - 5px);
  height: 380px;
  overflow-y: auto;
  overflow-x: hidden;
  ::-webkit-scrollbar {
    width: 5px;
  }
}
</style>
