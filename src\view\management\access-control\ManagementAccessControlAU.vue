<!--
 * @Description: 访问控制 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="25%">
      <template>
        <el-form-item :label="form.info.ipType.label" :prop="form.info.ipType.key">
          <el-radio-group v-model="form.model.ipType" @change="changeIP">
            <el-radio :label="4">
              IPv4
            </el-radio>
            <el-radio :label="6">
              IPv6
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="form.info.startIpv.label" :prop="form.info.startIpv.key">
          <el-input
            v-model.trim="form.model.startIpv"
            :placeholder="$t('asset.networkManagement.placeholder.Ipv')"
            class="width-mini"
            maxlength="39"
          ></el-input>
        </el-form-item>
        <el-form-item :label="form.info.endIpv.label" :prop="form.info.endIpv.key">
          <el-input
            v-model.trim="form.model.endIpv"
            :placeholder="$t('asset.networkManagement.placeholder.Ipv')"
            class="width-mini"
            maxlength="39"
          ></el-input>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { addData } from '@api/management/access-control-api'
import { validateIpv4, validateIpv6 } from '@util/validate'
export default {
  name: 'AuDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const validatorRadio = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('validate.choose')))
      } else {
        callback()
      }
    }
    const validatorIp = (rule, value, callback) => {
      if (this.form.model.ipType === 4) {
        if (!value) {
          callback(new Error(this.$t('validate.ip.empty')))
        } else if (!validateIpv4(value)) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      } else {
        if (!value) {
          callback(new Error(this.$t('validate.ip.empty')))
        } else if (!validateIpv6(value)) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      }
    }
    return {
      dialogVisible: this.visible,
      rules: {
        // 弹出表单的校验规则
        startIpv: [
          {
            required: true,
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        endIpv: [
          {
            required: true,
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        ipType: [
          {
            required: true,
            validator: validatorRadio,
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 关闭当前dialog
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.dialogVisible = false
    },
    // 添加
    add(obj) {
      addData({
        startIpv: obj.startIpv,
        endIpv: obj.endIpv,
      }).then((res) => {
        if (res === 1) {
          prompt({
            i18nCode: 'tip.add.success',
            type: 'success',
          })
          this.$emit('on-submit', 'true')
          this.clickCancelDialog()
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeatNet',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.add.errorType',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    changeIP() {
      this.form.model.startIpv = ''
      this.form.model.endIpv = ''
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const formModel = Object.assign({}, this.form.model)
            this.add(formModel)
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
