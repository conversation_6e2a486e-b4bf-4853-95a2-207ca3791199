export default {
  generalLog: {
    title: 'General Log',
    total: 'Total',
    label: {
      deviceName: 'Device Type',
      insertTime: 'Receive Time',
      facilityName: 'Module',
      severity: 'Level',
      severityName: 'Level',
      fromIp: 'Source IP',
      appName: 'Application Name',
      procId: 'PROCID',
      msgId: 'MSGID',
      logTimestamp: 'Log Time',
      hostName: 'Host Name',
      structuredData: 'Structured Data',
      message: 'Log Original Text',
      logMessage: 'Log Content',
    },
    placeholder: {
      message: 'Log Original Text',
      severity: 'Level',
      deviceType: 'Log Source Type',
      facility: 'Log Module',
      fromIp: 'Source IP',
      fromStartIp: 'Source Start IP',
      fromEndIp: 'Source End IP',
      receiveDate: 'Receive Date',
    },
    group: {
      basic: 'General Log Details',
    },
    basic: {
      deviceName: 'Device Type',
      insertTime: 'Receive Time',
      facilityName: 'Module',
      severityName: 'Level',
      fromIp: 'Source IP',
      appName: 'Application Name',
      procId: 'PROCID',
      msgId: 'MSGID',
      logTimestamp: 'Log Time',
      hostName: 'Host Name',
      structuredData: 'Structured Data',
      message: 'Log Original Text',
      logMessage: 'Log Content',
    },
    parseRateDesc: 'Average failure time for parsing rules per thousand logs: {0}ms',
  },
}
