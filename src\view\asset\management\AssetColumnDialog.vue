<!--
 * @Description: 资产发现 - 自定义列弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form" label-width="25%">
      <template>
        <el-checkbox v-model="form.checkAll" :indeterminate="form.isIndeterminate" @change="handleCheckAllChange">
          {{ $t('asset.management.allCheck') }}
        </el-checkbox>
        <el-checkbox-group v-model="form.own" size="medium" @change="handleCheckedChange">
          <el-checkbox v-for="(item, index) in form.all" :key="index" :label="item.value">
            {{ item.label }}
          </el-checkbox>
        </el-checkbox-group>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  name: 'AssetColumn',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    handleCheckAllChange(val) {
      const all = this.form.all.map((item) => {
        return item.value
      })
      this.form.own = val ? all : []
      const checkedCount = this.form.own.length
      this.form.isIndeterminate = checkedCount > 0 && checkedCount < this.form.all.length
    },
    handleCheckedChange(value) {
      const checkedCount = value.length
      this.form.checkAll = checkedCount === this.form.all.length
      this.form.isIndeterminate = checkedCount > 0 && checkedCount < this.form.all.length
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      if (this.form.own && this.form.own.length > 0) {
        this.$refs.formTemplate.validate((valid) => {
          if (valid) {
            this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
              closeOnClickModal: false,
            }).then(() => {
              // 给父级调用数据
              this.$emit('on-submit', this.form, this.form.own)
              this.clickCancelDialog()
            })
          } else {
            prompt(
              {
                i18nCode: 'validate.form.warning',
                type: 'warning',
                print: true,
              },
              () => {
                return false
              }
            )
          }
        })
      } else {
        prompt(
          {
            i18nCode: 'validate.form.lessOne',
            type: 'warning',
            print: true,
          },
          () => {
            return false
          }
        )
      }
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep .el-checkbox {
  margin-bottom: 20px;
  width: 200px;
}
</style>
