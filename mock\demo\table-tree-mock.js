const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    id: '@ID',
    account: '@NAME',
    name: '@CNAME',
    tel: /^([0-9]{3,4}-)?[0-9]{7,8}$/,
    phone: /^((\+?86)|(\(\+86\)))?(13[*********][0-9]{8}|15[*********][0-9]{8}|18[**********][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,
    email: '@EMAIL',
    time: '@DATETIME',
    'children|1-5': [
      {
        id: '@ID',
        account: '@NAME',
        name: '@CNAME',
        tel: /^([0-9]{3,4}-)?[0-9]{7,8}$/,
        phone: /^((\+?86)|(\(\+86\)))?(13[*********][0-9]{8}|15[*********][0-9]{8}|18[**********][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,
        email: '@EMAIL',
        time: '@DATETIME',
        'children|0-3': [
          {
            id: '@ID',
            account: '@NAME',
            name: '@CNAME',
            tel: /^([0-9]{3,4}-)?[0-9]{7,8}$/,
            phone: /^((\+?86)|(\(\+86\)))?(13[*********][0-9]{8}|15[*********][0-9]{8}|18[**********][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,
            email: '@EMAIL',
            children: [],
          },
        ],
      },
    ],
  },
})

const downloadInfo = `
PK<��P[Content_Types].xml�S�n�0����*6�PU�C���\\{�X�%����]8�R�
q�cfgfW�d�q�ZCB|��|�*�*h㻆},^�{Va�^K<4�6�N�XQ�ǆ�9�!P��$��҆�d�c�D�j);��ѝP�g��E�M'O�ʕ����H7L�h���R���G��^�'�{��zސʮB��3�˙��h.�h�W�жF�j娄CQՠ똈���}ιL�U:D�����%އ����,�B���[�	�� ;˱�	�{N��~��X�p�ykOL��kN�V��ܿBZ~����q�� �ar��{O�PKz��q;PK<��P_rels/.rels���j�0�_���8�\`�Q��2�m��4[ILb��ږ���.[K
�($}��v?�I�Q.���uӂ�h���x>=��@��p�H"�~�}�	�n����*"�H�׺؁�����8�Z�^'�#��7m{��O�3���G�u�ܓ�'��y|a�����D�	��l_EYȾ����vql3�ML�eh���*���\\3�Y0���oJ׏�	:��^��}PK��z��IPK<��PdocProps/app.xmlM��
�0D�~EȽ��ADҔ���A? ��6�lB�J?ߜ���0���ͯ�)�@��׍H6���V>��$;�SC
;̢(�ra�g�l�&�e��L!y�%��49��\`_���4G���F��J��Wg
�GS�b����
~�PK�|wؑ�PK<��PdocProps/core.xmlm�]K�0��J�}{�N�m�(Aq�d�]H�m�� �v�{�:+�wI��<���樆��I�Q��F�����~��I�גFcM�!���	�p�Ez�I�hτ�I�e^t���"�c�b��!^]��W�"���0p��I���HNJ)�}s�,�p@�:xȳ~؀N��d!��_�q�q5sq���n���^O_H��f�!(�(\`���F�����z�%MA��2������;/�+�5?	���5��������-�����PK?��K�PK<��Pxl/sharedStrings.xml=�A� ﾂ��.z0Ɣ�\`������,�����q2��o�ԇ���N�E��x5�z>�W���(R�K���^4{�����ŀ�5��y�V����y�m�XV�\\�.�j����
8�PKp��&x�PK<��P
xl/styles.xml�V�n�0��),߳�݆
J2�IE\\�Hܺ��X�Od�#�+p�{��
��vҤ]ӕi�7�}�}�9ǎ}_Ղ�[�
S2��FTf*gr�����)�J_��n8�))��$���zE&+� �LUT�L�� �z�JS�G<��F�"A��i,�b&�A�ZK���ҸP��\\�\`Hcs�n	��\\h�W1�Ӛ�	�:�$��5�l���#��M0O��G���J;c��o������߾x֞�EZ��G��8���G�\`2�wi\\k��3��?�T4�R�Ʊ�=�Ή^��ds:�(��_�ly��+L�.G=⩒>���}k�P:��Ӯ�x�[[sZX�k�,]kU�6SY�trF�J�<���(�v��s"&h?�tq탞Vͧ�]�"�{v�����5�ؿ�< O��4��PmŚ�R��ơ>�U9�
��}�0r�����t�L8��Z���^>J��V�=���}��c#RU|�.�\\�[��1͔��*�R
*-��I5�u;��E�e��f0M5F������v	#Sj&Ws5c-
����4��B\\e+�Gm�w]'9钼8�d��p��B������4&u4���a{.���D�y�^�dOPK���U_�PK<��Pxl/workbook.xml��AO�0�����w�tC����I�!1�{��Fk��	?��S�#'��=~����B�\\��A�D��I��aw�����F>c<�IC��XK�LO�*���E����L#��e?ȵR�ңp#��F�:g�%�OO!� L�R6�nL��4{ea1S��4t8$�6����~��h����Ԕ��s�e���4�M{�kg5��n@����j&,gry�~PK�����]PK<��Pxl/_rels/workbook.xml.rels��Mk�0@���}q���n/c����c+qh"K�迟���@;�$��{��~Γy�"#���i� �#
^�O7�\`D=E?1�b�n��8y�?$�YLE�8H���Z		g/
g����^�6�p��U���r΀%�좃����/�I�\`|�Rˤ��:f����~���mF�v�����:���ׯ�������p9HB�SyݵK~�����PK�;��3PK<��Pxl/worksheets/sheet1.xml��KN�0�����My	%AH�	Qk7�$��S��8��m@��-U,{���73vz0�-��5
���)l�L�����=~�o��u��@Fz�3� v�B��-��v\`褲NK����wdM��p�#�T��i�4�d������(�V��ok�SkoCpZf��C9�@��f���8�r�X	���xa�Pu�T�6�I�¶>~�V�xδ�ǹW%6O��3�V�,����9��$Jꑳ=sѾp|ᢅ-��,���f>��D�V����(�/��/����oO�@MQ� ʊ���J"k��(�E�K��"VML;YÙt�2�M-��|���Ye-�QzYˠ�
��3��/��v��p]��PKk#�%r�PK<��Pz��q;[Content_Types].xmlPK<��P��z��I|_rels/.relsPK<��P�|wؑ��docProps/app.xmlPK<��P?��K�gdocProps/core.xmlPK<��Pp��&x��xl/sharedStrings.xmlPK<��P���U_�
fxl/styles.xmlPK<��P�����]xl/workbook.xmlPK<��P�;��3	xl/_rels/workbook.xml.relsPK<��Pk#�%r�2
xl/worksheets/sheet1.xmlPK		?�
`

module.exports = [
  {
    url: '/demo/tabletree/upload',
    type: 'post',
    response: (option) => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/demo/tabletree/download',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: [],
      }
    },
  },
  {
    url: '/demo/tabletree',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/demo/tabletree/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/demo/tabletree',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/demo/tabletree',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
]
