<!--
 * @Description: 区域管理 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-01
 * @Editor:
 * @EditDate: 2021-07-01
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-form :model="model" label-width="120px">
        <el-row>
          <el-col v-for="(item, index) in columnOption" :key="index" :span="12">
            <el-form-item :prop="item.key" :label="item.label">
              {{ model[item.key] }}
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: <PERSON><PERSON><PERSON>,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      columnOption: [
        { key: 'domaName', label: this.$t('asset.area.prop.domaName') },
        { key: 'domaAbbreviation', label: this.$t('asset.area.prop.domaAbbr') },
        { key: 'officeTel', label: this.$t('asset.area.prop.officeTel') },
        { key: 'email', label: this.$t('asset.area.prop.email') },
        { key: 'domaFunc', label: this.$t('asset.area.prop.domaFunc') },
        { key: 'deviceNum', label: this.$t('asset.area.prop.deviceNum') },
        { key: 'domaAddress', label: this.$t('asset.area.prop.domaAddress') },
        { key: 'domaMemo', label: this.$t('asset.area.prop.domaMemo') },
      ],
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
  },
}
</script>
