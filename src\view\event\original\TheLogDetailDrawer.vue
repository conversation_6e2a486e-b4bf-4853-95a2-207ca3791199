<template>
  <detail-drawer :visible="dialogVisible" :detail-data="detailData" :loading="loading" @on-close="clickCancelDrawer">
    <!-- 在源信息部分添加源MAC地址 -->
    <el-descriptions-item v-if="detailData.srcMac" :label="$t('event.original.source.sourceMac')">
      {{ detailData.srcMac }}
    </el-descriptions-item>
    <!-- 在目的信息部分添加目的MAC地址 -->
    <el-descriptions-item v-if="detailData.dstMac" :label="$t('event.original.destination.targetMac')">
      {{ detailData.dstMac }}
    </el-descriptions-item>
  </detail-drawer>
</template>

<script>
import DetailDrawer from '@comp/DetailDrawer'

export default {
  components: {
    DetailDrawer,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    detailData: {
      type: Object,
      default() {
        return {}
      },
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {},
  methods: {
    clickCancelDrawer() {
      this.dialogVisible = false
    },
  },
}
</script>
