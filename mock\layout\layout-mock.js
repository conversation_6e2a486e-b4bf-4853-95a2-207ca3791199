const systemData = {
  userId: '@ID',
  account: 'admin',
  defaultMenu: '',
  systemName: '综合日志审计与管理系统',
  locale_language: 'en-US',
}

const toolsData = []

const userData = {
  userId: '@ID',
  'userFullName|1': ['admin', 'sysmanager', 'sysauditor', ''],
  'userSortName|1': ['系统管理员', '运维管理员', '审计管理员'],
  userMail: '@EMAIL',
  userPhone: /^([0-9]{3,4}-)?[0-9]{7,8}$/,
  userMobile: /^((\+?86)|(\(\+86\)))?(13[*********][0-9]{8}|15[*********][0-9]{8}|18[**********][0-9]{8}|14[57][0-9]{8}|1349[0-9]{7})$/,
  defaultMenu: '',
}

const searchHeaderData = [
  {
    label: 'layout.search.asset',
    url: '/asset/management',
  },
  {
    label: 'layout.search.event',
    url: '/event/security',
  },
  {
    label: 'layout.search.audit',
    url: '/audit/event',
  },
  {
    label: 'layout.search.alarm',
    url: '/alarm/table',
  },
]

const systemNoticeConfig = {
  isSound: 1,
  isMail: 0,
  isSnmp: 0,
  isSms: 0,
}

module.exports = [
  {
    url: '/authentication/logout',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/actuator/home',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: systemData,
      }
    },
  },
  {
    url: '/menumanagement/menu/tools',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: toolsData,
      }
    },
  },
  {
    url: '/usermanagement/user/extend',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: userData,
      }
    },
  },
  {
    url: '/usermanagement/user/extend',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/event/alarm/total',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/systemmanagement/find-system-alarm-notice',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: systemNoticeConfig,
      }
    },
  },
  {
    url: '/systemalarm/findNotIgnoreAlarm',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/systemmanagement/license/remain',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: 365,
      }
    },
  },
  {
    url: '/menumanagement/menu/search',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: searchHeaderData,
      }
    },
  },
]
