<template>
  <div class="table-query-wrapper">
    <template v-if="disposeFilterData.length > 0">
      <el-popover
        v-for="(item, index) in disposeFilterData"
        :key="index"
        class="table-query-tag"
        width="100"
        trigger="click"
        :content="String(item.value)"
        placement="bottom"
      >
        <el-tag slot="reference" size="medium" :disable-transitions="false" closable @close="handleCloseTag(item.label)">
          {{ item.label }}
        </el-tag>
      </el-popover>
    </template>
    <el-tag
      v-if="disposeFilterData.length > 1"
      class="clear-filter-tag table-query-tag"
      size="medium"
      type="danger"
      closable
      @close="handleCloseTagAll"
    >
      {{ $t('button.clear') }}
    </el-tag>
  </div>
</template>

<script>
export default {
  props: {
    // 需要传入的过滤数据
    filterData: {
      required: false,
      type: Array,
      default: () => {
        return [
          {
            label: '', // 过滤类型
            value: '', // 弹出的过滤关键字
            key: '', // 可选 如添加必须与表单绑定的prop值必须保持一致
          },
        ]
      },
    },
  },
  computed: {
    // 对数据进行二次操作，对象中value为空的不展示
    disposeFilterData() {
      return this.filterData.filter((item) => {
        if (item.value instanceof Array) {
          return item.value.length > 0
        } else {
          return item.value !== '' && item.value !== undefined && item.value !== null
        }
      })
    },
  },
  methods: {
    handleCloseTag(tag) {
      const itemData = this.filterData.find((item) => {
        return item.label === tag
      })
      this.filterData.splice(this.filterData.indexOf(itemData), 1)
      // 留给调用组件的关闭接口
      this.$emit('close-tag', itemData)
    },
    handleCloseTagAll() {
      // 清空数组
      // 注：不能使用赋值操作 因为子组件不允许赋值父组件传过来的值
      this.filterData.splice(0)
      this.$emit('close-all')
    },
  },
}
</script>

<style lang="scss" scoped>
.table-query-wrapper {
  .table-query-tag + .table-query-tag {
    margin-left: 10px;
  }

  .el-tag {
    cursor: default;
  }
}
</style>
