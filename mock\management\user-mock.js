const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    userId: '@ID',
    userAccount: '@NAME',
    userPassword: '@WORD',
    'userType|1': ['0', '1'],
    'userStatus|1': ['0', '1'],
    'userStatusText|1': ['手动锁定', '正常'],
    userDescription: '@CPARAGRAPH',
    'accountStatus|1': ['0', '1'],
    'accountStatusText|1': ['启用', '禁用'],
    'passwordStatus|1': ['0', '1'],
    'passwordStatusText|1': ['正常', '重置', '过期'],
    passwordUpdateDate: '@DATETIME',
    startValidDate: '@DATETIME',
    endValidDate: '@DATETIME',
  },
})

module.exports = [
  {
    url: '/usermanagement/user',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/usermanagement/user/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      tableData.delete(option, 'userId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/usermanagement/user',
    type: 'put',
    response: (option) => {
      tableData.update(option, 'userId')
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/usermanagement/users',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/usermanagement/user/roles/[A-Za-z0-9]',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: '',
      }
    },
  },
  {
    url: '/usermanagement/user/[A-Za-z0-9]',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: tableData.detail(option, 'userId'),
      }
    },
  },
  {
    url: '/usermanagement/user/status',
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/usermanagement/user/grant',
    type: 'put',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
]
