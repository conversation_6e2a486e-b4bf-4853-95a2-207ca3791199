<!--
 * @Description: 合规审计
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023/02/27
 * @Editor:
 * @EditDate: 2023/11/13
-->
<template>
  <div class="compliance-container">
    <el-row :gutter="10">
      <el-col :span="6">
        <log-source-number ref="logSourceNumberDom"></log-source-number>
      </el-col>
      <el-col :span="6">
        <log-number ref="logNumberDom"></log-number>
      </el-col>
      <el-col :span="6">
        <log-storage-space ref="logStorageSpaceDom"></log-storage-space>
      </el-col>
      <el-col :span="6">
        <log-storage-duration ref="logStorageDurationDom"></log-storage-duration>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="18">
        <log-number-trend-chart ref="logNumberTrendChartDom" :height="chartHeight"></log-number-trend-chart>
      </el-col>
      <el-col :span="6">
        <system-healthy-chart ref="systemHealthyDom" :height="chartHeight"></system-healthy-chart>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <log-source-type-chart ref="logSourceTypeChartDom" :height="chartHeight"></log-source-type-chart>
      </el-col>
      <el-col :span="8">
        <log-number-chart ref="logNumberChartDom" :height="chartHeight"></log-number-chart>
      </el-col>
      <el-col :span="8">
        <log-duration-chart ref="logDurationChartDom" :height="chartHeight"></log-duration-chart>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="8">
        <log-receiving-status
          ref="logReceivingStatusDom"
          :height="chartHeight"
          @on-set-receive-times="updateLogSourceReceiveTimes"
        ></log-receiving-status>
      </el-col>
      <el-col :span="8">
        <audit-alarm-tread ref="alarmQuantityTrendDom" :height="chartHeight"></audit-alarm-tread>
      </el-col>
      <el-col :span="8">
        <security-event-count ref="securityEventCountDom" :height="chartHeight"></security-event-count>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import LogSourceNumber from './TheLogSourceNumber'
import LogNumber from './TheLogNumber'
import LogStorageSpace from './TheLogStorageSpace'
import LogStorageDuration from './TheLogStorageDuration'
import LogNumberTrendChart from './TheLogNumberTrendChart'
import SystemHealthyChart from './TheSystemHealthyChart'
import LogSourceTypeChart from './TheLogSourceTypeChart'
import LogNumberChart from './TheLogNumberChart'
import LogDurationChart from './TheLogDurationChart'
import AuditAlarmTread from './TheAuditAlarmTrend'
import SecurityEventCount from './TheSecurityEventCount'
import LogReceivingStatus from './TheLogReceivingStatus'
import { updateLogSourceStatus } from '@api/visualization/compliance-api'

export default {
  name: 'ComplianceAudit',
  components: {
    LogSourceNumber,
    LogNumber,
    LogStorageSpace,
    LogStorageDuration,
    SystemHealthyChart,
    LogNumberTrendChart,
    LogSourceTypeChart,
    LogNumberChart,
    LogDurationChart,
    AuditAlarmTread,
    SecurityEventCount,
    LogReceivingStatus,
  },
  data() {
    return {
      refresh: 50000,
      timer: {
        interval: null,
        timeout: null,
      },
      chartHeight: 350,
    }
  },
  mounted() {
    // this.chartHeight = (window.innerHeight - this.$refs.logSourceNumberDom.$el.offsetHeight - 100) / 2;
    // window.onresize = () => {
    //     return (() => {
    //         this.chartHeight = (window.innerHeight - this.$refs.logSourceNumberDom.$el.offsetHeight - 100) / 2;
    //     })();
    // };
    // this.refreshData();
  },
  beforeDestroy() {
    this.clearTimer()
  },
  methods: {
    // refreshData() {
    //     this.timer.interval = setInterval(() => {
    //         this.reloadData();
    //     }, this.refresh);
    // },
    clearTimer() {
      clearTimeout(this.timer.timeout)
      clearInterval(this.timer.interval)
      this.timer.timeout = null
      this.timer.interval = null
    },
    // reloadData() {
    //     Object.getOwnPropertyNames(this.$refs).forEach(domName => {
    //         this.$refs[domName].loadData();
    //     });
    // },
    updateLogSourceReceiveTimes(obj) {
      this.updateLogSourceStatus(obj)
    },
    updateLogSourceStatus(obj) {
      const params = {
        typeId: obj.value,
        receivingDuration: obj.receivingDuration,
      }
      updateLogSourceStatus(params).then((res) => {
        if (res === 1) {
          this.$refs.logReceivingStatusDom.getLogReceivingStatus()
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.compliance-container {
  font-family: 'Open Sans', sans-serif;
  padding: 5px;
  overflow-y: auto;
  ::v-deep .el-row {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    .widget {
      padding: 5px;
      border: 1px solid;
      @include theme('border-color', border-color);
      border-radius: 4px;
      &-header {
        display: flex;
        padding: 0 2px;
        height: 30px;
        justify-content: space-between;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        .more-word {
          font-size: 10px;
          color: #1873d7;
        }
      }
      &-body {
        display: flex;
        width: 100%;
        height: 100px;
        padding: 15px 0 15px 5px;
        flex-direction: row;
        align-items: flex-start;
        .icon-box {
          font-size: 30px;
          width: 56px;
          height: 56px;
          line-height: 56px;
          text-align: center;
          color: #fff;
          border-radius: 100%;
        }
        .detail-box {
          margin-left: 15px;
          width: calc(100% - 80px);
          font-size: 14px;
          font-weight: 400;
          &-word {
            padding-bottom: 15px;
            line-height: 25px;
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            span {
              font-size: 20px;
            }
          }
          &-chart {
            height: 30px;
          }
        }
      }
    }
  }
}
</style>
