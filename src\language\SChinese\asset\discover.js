export default {
  discover: {
    asset: '资产发现',
    findTask: '发现任务',
    taskLog: '任务日志',
    th: '自定义列',
    errorConnect: '连接中断,暂停获取进度',
    assetCode: '资产编号',
    domaName: '隶属区域',
    assetName: '资产名称',
    assetType: '资产类型',
    responsiblePerson: '负责人',
    netWorkId: '网段ID',
    netWorkName: '网段',
    assetModel: '资产型号',
    manufactor: '制造商',
    osType: '操作系统',
    ipvAddress: '资产IP',
    memoryInfo: '内存容量',
    contactPhone: '联系电话',
    email: '邮件地址',
    makerContactPhone: '厂商电话',
    assetDesc: '备注信息',
    securityComponent: '安全组件类型',
    columns: {
      assetName: '资产名称',
      assetType: '资产类型',
      netWorkId: '网段',
      assetModel: '资产型号',
      manufactor: '制造商',
      osType: '操作系统',
      memoryInfo: '内存容量',
      responsiblePerson: '负责人',
      contactPhone: '联系电话',
      email: '邮件地址',
      makerContactPhone: '厂商电话',
      assetCode: '资产编号',
      assetDesc: '备注信息',
      ipvAddress: '资产IP',
    },
    taskName: '任务名称',
    progressPercent: '状态',
    placeholder: {
      startIpv: '起始资产IP',
      endIpv: '终止资产IP',
      keyword: '请输入关键字搜索',
      taskName: '任务名称',
      netWorkId: '网段',
      assetName: '资产名称',
      assetType: '资产分类',
      assetModel: '资产型号',
      manufactor: '制造商',
      osType: '操作系统',
      ipvAddress: '资产IP',
      memoryInfo: '内存容量',
      responsiblePerson: '负责人',
      contactPhone: '联系电话',
      email: '邮件地址',
      makerContactPhone: '厂商电话',
      assetCode: '资产编号',
      domaName: '隶属区域',
      assetDesc: '备注信息',
    },
  },
}
