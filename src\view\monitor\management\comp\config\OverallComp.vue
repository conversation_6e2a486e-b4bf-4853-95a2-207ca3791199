<!--
 * @Description: 监控器配置 - 组件入口
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-28
 * @Editor:
 * @EditDate: 2021-07-28
-->
<template>
  <section>
    <template v-if="infoItems.indexOf('cpu') > -1">
      <cpu-comp ref="cpuRef" :cpu-model="model"></cpu-comp>
    </template>
    <template v-if="infoItems.indexOf('memory') > -1">
      <memory-comp ref="memoryRef" :memory-model="model"></memory-comp>
    </template>
    <template v-if="infoItems.indexOf('disk') > -1">
      <disk-comp ref="diskRef" :disk-model="model"></disk-comp>
    </template>
    <template v-if="infoItems.indexOf('snmp') > -1">
      <snmp-comp ref="snmpRef" :snmp-model="model"></snmp-comp>
    </template>
  </section>
</template>

<script>
import CpuComp from './CpuComp'
import MemoryComp from './MemoryComp'
import DiskComp from './DiskComp'
import SnmpComp from './SnmpComp'
import { queryMonitorComp } from '@api/monitor/management-api'

export default {
  components: {
    CpuComp,
    MemoryComp,
    DiskComp,
    SnmpComp,
  },
  props: {
    model: {
      required: true,
      type: Object,
    },
    monitorType: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      infoItems: '',
    }
  },
  watch: {
    monitorType(type) {
      this.getMonitorCompInfo(type)
    },
  },
  mounted() {
    this.getMonitorCompInfo(this.monitorType)
  },
  methods: {
    validateForm() {
      for (const key in this.$refs) {
        if (this.infoItems.indexOf(key) > -1 && !this.$refs[key].validateForm()) {
          return false
        }
      }
      return true
    },
    resetForm() {
      for (const key in this.$refs) {
        if (this.infoItems.indexOf(key)) {
          this.$refs[key].resetForm()
        }
      }
    },
    getMonitorCompInfo(monitorType) {
      queryMonitorComp(monitorType).then((res) => {
        this.infoItems = res
      })
    },
  },
}
</script>
