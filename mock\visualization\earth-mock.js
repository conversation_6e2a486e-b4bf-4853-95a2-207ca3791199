const Mock = require('mockjs')

const trend = () => {
  return Mock.mock({
    axis: ['0', '4', '8', '12', '16', '20'],
    'lineData|6': [/\d{5,10}/],
    scatterData() {
      return [
        ['0', this.lineData[0], 30],
        ['8', this.lineData[2], 50],
        ['16', this.lineData[4], 20],
      ]
    },
  })
}

const type = () => {
  const obj = Mock.mock({
    'axis|10': ['安全事件_' + '@INCREMENT'],
    'data|10': ['@NATURAL(400, 1200)'],
  })
  obj.data = obj.data.sort((max, min) => max - min)
  return obj
}

const level = () => {
  return Mock.mock({
    'axis|5': [
      {
        'name|1': ['0', '1', '2', '3', '4', '一般', '低级', '中级', '高级', '严重'],
        max: 10000,
      },
    ],
    'value|5': ['@NATURAL(1, 9999)'],
  })
}

module.exports = [
  {
    url: '/visualization/eps',
    type: 'get',
    response: () => {
      return {
        code: 200,
        'data|100-100000': 100,
      }
    },
  },
  {
    url: '/visualization/global/attack',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: [
          {
            value: 101,
            coords: [
              ['133.78', '-25.27'],
              ['118.78', '32.04'],
            ],
          },
          {
            value: 102,
            coords: [
              ['102.73', '25.04'],
              ['118.78', '32.04'],
            ],
          },
          {
            value: 103,
            coords: [
              ['138.25', '36.2'],
              ['118.78', '32.04'],
            ],
          },
          {
            value: 104,
            coords: [
              ['108.95', '34.27'],
              ['118.78', '32.04'],
            ],
          },
          {
            value: 105,
            coords: [
              ['123.38', '41.8'],
              ['118.78', '32.04'],
            ],
          },
          {
            value: 101,
            coords: [
              ['102.73', '25.04'],
              ['104.06', '30.67'],
            ],
          },
          {
            value: 102,
            coords: [
              ['117.2', '39.13'],
              ['104.06', '30.67'],
            ],
          },
          {
            value: 103,
            coords: [
              ['123.38', '41.8'],
              ['104.06', '30.67'],
            ],
          },
          {
            value: 104,
            coords: [
              ['102.73', '25.04'],
              ['104.06', '30.67'],
            ],
          },
          {
            value: 105,
            coords: [
              ['117.2', '39.13'],
              ['104.06', '30.67'],
            ],
          },
          {
            value: 101,
            coords: [
              ['123.38', '41.8'],
              ['104.06', '30.67'],
            ],
          },
          {
            value: 102,
            coords: [
              ['116.46', '39.92'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 103,
            coords: [
              ['138.25', '36.2'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 104,
            coords: [
              ['116.46', '39.92'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 105,
            coords: [
              ['138.25', '36.2'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 101,
            coords: [
              ['114.31', '30.52'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 102,
            coords: [
              ['113.23', '23.16'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 103,
            coords: [
              ['117.2', '39.13'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 104,
            coords: [
              ['104.06', '30.67'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 105,
            coords: [
              ['105.32', '61.52'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 101,
            coords: [
              ['12.57', '41.87'],
              ['138.25', '36.2'],
            ],
          },
          {
            value: 102,
            coords: [
              ['108.95', '34.27'],
              ['123.38', '41.8'],
            ],
          },
          {
            value: 103,
            coords: [
              ['2.21', '46.23'],
              ['123.38', '41.8'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-51.93', '-14.24'],
              ['123.38', '41.8'],
            ],
          },
          {
            value: 105,
            coords: [
              ['12.57', '41.87'],
              ['123.38', '41.8'],
            ],
          },
          {
            value: 101,
            coords: [
              ['108.95', '34.27'],
              ['123.38', '41.8'],
            ],
          },
          {
            value: 102,
            coords: [
              ['110.35', '20.02'],
              ['12.57', '41.87'],
            ],
          },
          {
            value: 103,
            coords: [
              ['123.38', '41.8'],
              ['12.57', '41.87'],
            ],
          },
          {
            value: 104,
            coords: [
              ['8.23', '46.82'],
              ['12.57', '41.87'],
            ],
          },
          {
            value: 105,
            coords: [
              ['104.06', '30.67'],
              ['12.57', '41.87'],
            ],
          },
          {
            value: 101,
            coords: [
              ['138.25', '36.2'],
              ['12.57', '41.87'],
            ],
          },
          {
            value: 102,
            coords: [
              ['102.73', '25.04'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 103,
            coords: [
              ['126.57', '37.32'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 104,
            coords: [
              ['133.78', '-25.27'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 105,
            coords: [
              ['103.73', '36.03'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 101,
            coords: [
              ['102.73', '25.04'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-106.35', '56.13'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 103,
            coords: [
              ['102.73', '25.04'],
              ['113.23', '23.16'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-3.75', '40.46'],
              ['114.31', '30.52'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-95.71', '37.09'],
              ['114.31', '30.52'],
            ],
          },
          {
            value: 101,
            coords: [
              ['133.78', '-25.27'],
              ['114.31', '30.52'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-51.93', '-14.24'],
              ['114.31', '30.52'],
            ],
          },
          {
            value: 103,
            coords: [
              ['10.45', '51.17'],
              ['114.31', '30.52'],
            ],
          },
          {
            value: 104,
            coords: [
              ['138.25', '36.2'],
              ['-106.35', '56.13'],
            ],
          },
          {
            value: 105,
            coords: [
              ['10.45', '51.17'],
              ['-106.35', '56.13'],
            ],
          },
          {
            value: 101,
            coords: [
              ['113.65', '34.76'],
              ['-106.35', '56.13'],
            ],
          },
          {
            value: 102,
            coords: [
              ['12.57', '41.87'],
              ['-106.35', '56.13'],
            ],
          },
          {
            value: 103,
            coords: [
              ['110.35', '20.02'],
              ['-106.35', '56.13'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-51.93', '-14.24'],
              ['125.35', '43.88'],
            ],
          },
          {
            value: 105,
            coords: [
              ['133.78', '-25.27'],
              ['125.35', '43.88'],
            ],
          },
          {
            value: 101,
            coords: [
              ['-51.93', '-14.24'],
              ['125.35', '43.88'],
            ],
          },
          {
            value: 102,
            coords: [
              ['133.78', '-25.27'],
              ['125.35', '43.88'],
            ],
          },
          {
            value: 103,
            coords: [
              ['-51.93', '-14.24'],
              ['125.35', '43.88'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-51.93', '-14.24'],
              ['-3.75', '40.46'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-106.35', '56.13'],
              ['-3.75', '40.46'],
            ],
          },
          {
            value: 101,
            coords: [
              ['138.25', '36.2'],
              ['-3.75', '40.46'],
            ],
          },
          {
            value: 102,
            coords: [
              ['12.57', '41.87'],
              ['-3.75', '40.46'],
            ],
          },
          {
            value: 103,
            coords: [
              ['113.65', '34.76'],
              ['-3.75', '40.46'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-51.93', '-14.24'],
              ['-3.75', '40.46'],
            ],
          },
          {
            value: 105,
            coords: [
              ['102.73', '25.04'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 101,
            coords: [
              ['121.48', '31.22'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 102,
            coords: [
              ['116.46', '39.92'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 103,
            coords: [
              ['-106.35', '56.13'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 104,
            coords: [
              ['102.73', '25.04'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 105,
            coords: [
              ['121.48', '31.22'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 101,
            coords: [
              ['116.46', '39.92'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-106.35', '56.13'],
              ['117.2', '39.13'],
            ],
          },
          {
            value: 103,
            coords: [
              ['-51.93', '-14.24'],
              ['110.35', '20.02'],
            ],
          },
          {
            value: 104,
            coords: [
              ['114.31', '30.52'],
              ['110.35', '20.02'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-3.75', '40.46'],
              ['110.35', '20.02'],
            ],
          },
          {
            value: 101,
            coords: [
              ['121.48', '31.22'],
              ['110.35', '20.02'],
            ],
          },
          {
            value: 102,
            coords: [
              ['113.23', '23.16'],
              ['110.35', '20.02'],
            ],
          },
          {
            value: 103,
            coords: [
              ['126.57', '37.32'],
              ['110.35', '20.02'],
            ],
          },
          {
            value: 104,
            coords: [
              ['133.78', '-25.27'],
              ['116.46', '39.92'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-95.71', '37.09'],
              ['116.46', '39.92'],
            ],
          },
          {
            value: 101,
            coords: [
              ['126.57', '37.32'],
              ['116.46', '39.92'],
            ],
          },
          {
            value: 102,
            coords: [
              ['133.78', '-25.27'],
              ['116.46', '39.92'],
            ],
          },
          {
            value: 103,
            coords: [
              ['105.32', '61.52'],
              ['-95.71', '37.09'],
            ],
          },
          {
            value: 104,
            coords: [
              ['2.21', '46.23'],
              ['-95.71', '37.09'],
            ],
          },
          {
            value: 105,
            coords: [
              ['103.73', '36.03'],
              ['-95.71', '37.09'],
            ],
          },
          {
            value: 101,
            coords: [
              ['133.78', '-25.27'],
              ['-95.71', '37.09'],
            ],
          },
          {
            value: 102,
            coords: [
              ['10.45', '51.17'],
              ['-95.71', '37.09'],
            ],
          },
          {
            value: 103,
            coords: [
              ['125.35', '43.88'],
              ['103.73', '36.03'],
            ],
          },
          {
            value: 104,
            coords: [
              ['123.38', '41.8'],
              ['103.73', '36.03'],
            ],
          },
          {
            value: 105,
            coords: [
              ['110.35', '20.02'],
              ['103.73', '36.03'],
            ],
          },
          {
            value: 101,
            coords: [
              ['113.65', '34.76'],
              ['103.73', '36.03'],
            ],
          },
          {
            value: 102,
            coords: [
              ['113.23', '23.16'],
              ['103.73', '36.03'],
            ],
          },
          {
            value: 103,
            coords: [
              ['138.25', '36.2'],
              ['103.73', '36.03'],
            ],
          },
          {
            value: 104,
            coords: [
              ['102.73', '25.04'],
              ['2.21', '46.23'],
            ],
          },
          {
            value: 105,
            coords: [
              ['121.48', '31.22'],
              ['2.21', '46.23'],
            ],
          },
          {
            value: 101,
            coords: [
              ['114.31', '30.52'],
              ['2.21', '46.23'],
            ],
          },
          {
            value: 102,
            coords: [
              ['126.57', '37.32'],
              ['2.21', '46.23'],
            ],
          },
          {
            value: 103,
            coords: [
              ['116.46', '39.92'],
              ['2.21', '46.23'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-51.93', '-14.24'],
              ['2.21', '46.23'],
            ],
          },
          {
            value: 105,
            coords: [
              ['105.32', '61.52'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 101,
            coords: [
              ['-95.71', '37.09'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 102,
            coords: [
              ['113.65', '34.76'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 103,
            coords: [
              ['114.31', '30.52'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-51.93', '-14.24'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-95.71', '37.09'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 101,
            coords: [
              ['105.32', '61.52'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-95.71', '37.09'],
              ['105.32', '61.52'],
            ],
          },
          {
            value: 103,
            coords: [
              ['-106.35', '56.13'],
              ['121.48', '31.22'],
            ],
          },
          {
            value: 104,
            coords: [
              ['108.95', '34.27'],
              ['121.48', '31.22'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-106.35', '56.13'],
              ['121.48', '31.22'],
            ],
          },
          {
            value: 101,
            coords: [
              ['108.95', '34.27'],
              ['121.48', '31.22'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-106.35', '56.13'],
              ['121.48', '31.22'],
            ],
          },
          {
            value: 103,
            coords: [
              ['108.95', '34.27'],
              ['121.48', '31.22'],
            ],
          },
          {
            value: 104,
            coords: [
              ['110.35', '20.02'],
              ['108.95', '34.27'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-106.35', '56.13'],
              ['108.95', '34.27'],
            ],
          },
          {
            value: 101,
            coords: [
              ['113.23', '23.16'],
              ['108.95', '34.27'],
            ],
          },
          {
            value: 102,
            coords: [
              ['110.35', '20.02'],
              ['108.95', '34.27'],
            ],
          },
          {
            value: 103,
            coords: [
              ['-106.35', '56.13'],
              ['108.95', '34.27'],
            ],
          },
          {
            value: 104,
            coords: [
              ['114.31', '30.52'],
              ['113.65', '34.76'],
            ],
          },
          {
            value: 105,
            coords: [
              ['105.32', '61.52'],
              ['113.65', '34.76'],
            ],
          },
          {
            value: 101,
            coords: [
              ['-51.93', '-14.24'],
              ['113.65', '34.76'],
            ],
          },
          {
            value: 102,
            coords: [
              ['117.2', '39.13'],
              ['113.65', '34.76'],
            ],
          },
          {
            value: 103,
            coords: [
              ['114.31', '30.52'],
              ['113.65', '34.76'],
            ],
          },
          {
            value: 104,
            coords: [
              ['121.48', '31.22'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 105,
            coords: [
              ['125.35', '43.88'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 101,
            coords: [
              ['110.35', '20.02'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 102,
            coords: [
              ['8.23', '46.82'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 103,
            coords: [
              ['118.78', '32.04'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 104,
            coords: [
              ['2.21', '46.23'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 105,
            coords: [
              ['104.06', '30.67'],
              ['126.57', '37.32'],
            ],
          },
          {
            value: 101,
            coords: [
              ['-51.93', '-14.24'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 102,
            coords: [
              ['110.35', '20.02'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 103,
            coords: [
              ['2.21', '46.23'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 104,
            coords: [
              ['114.31', '30.52'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 105,
            coords: [
              ['103.73', '36.03'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 101,
            coords: [
              ['8.23', '46.82'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-51.93', '-14.24'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 103,
            coords: [
              ['110.35', '20.02'],
              ['133.78', '-25.27'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-95.71', '37.09'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 105,
            coords: [
              ['103.73', '36.03'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 101,
            coords: [
              ['104.06', '30.67'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 102,
            coords: [
              ['114.31', '30.52'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 103,
            coords: [
              ['8.23', '46.82'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-95.71', '37.09'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 105,
            coords: [
              ['103.73', '36.03'],
              ['102.73', '25.04'],
            ],
          },
          {
            value: 101,
            coords: [
              ['125.35', '43.88'],
              ['10.45', '51.17'],
            ],
          },
          {
            value: 102,
            coords: [
              ['133.78', '-25.27'],
              ['10.45', '51.17'],
            ],
          },
          {
            value: 103,
            coords: [
              ['138.25', '36.2'],
              ['10.45', '51.17'],
            ],
          },
          {
            value: 104,
            coords: [
              ['2.21', '46.23'],
              ['10.45', '51.17'],
            ],
          },
          {
            value: 105,
            coords: [
              ['123.38', '41.8'],
              ['10.45', '51.17'],
            ],
          },
          {
            value: 101,
            coords: [
              ['113.65', '34.76'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 102,
            coords: [
              ['-51.93', '-14.24'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 103,
            coords: [
              ['10.45', '51.17'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 104,
            coords: [
              ['113.65', '34.76'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 105,
            coords: [
              ['-51.93', '-14.24'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 101,
            coords: [
              ['10.45', '51.17'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 102,
            coords: [
              ['113.65', '34.76'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 103,
            coords: [
              ['138.25', '36.2'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 104,
            coords: [
              ['-106.35', '56.13'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 105,
            coords: [
              ['102.73', '25.04'],
              ['8.23', '46.82'],
            ],
          },
          {
            value: 101,
            coords: [
              ['104.06', '30.67'],
              ['-51.93', '-14.24'],
            ],
          },
          {
            value: 102,
            coords: [
              ['118.78', '32.04'],
              ['-51.93', '-14.24'],
            ],
          },
          {
            value: 103,
            coords: [
              ['113.23', '23.16'],
              ['-51.93', '-14.24'],
            ],
          },
          {
            value: 104,
            coords: [
              ['8.23', '46.82'],
              ['-51.93', '-14.24'],
            ],
          },
          {
            value: 105,
            coords: [
              ['126.57', '37.32'],
              ['-51.93', '-14.24'],
            ],
          },
          {
            value: 101,
            coords: [
              ['10.45', '51.17'],
              ['-51.93', '-14.24'],
            ],
          },
          {
            value: 102,
            coords: [
              ['103.73', '36.03'],
              ['-51.93', '-14.24'],
            ],
          },
        ],
      }
    },
  },
  {
    url: '/visualization/security/trend',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: trend(),
      }
    },
  },
  {
    url: '/visualization/security/category',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: [
          {
            name: '验证/认证失败',
            value: '25',
          },
          {
            name: '可疑的端口活动',
            value: '20',
          },
          {
            name: '恶意程序',
            value: '10',
          },
        ],
      }
    },
  },
  {
    url: '/visualization/security/type',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: type(),
      }
    },
  },
  {
    url: '/visualization/security/level',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: level(),
      }
    },
  },
  {
    url: '/visualization/audit/ranking',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: [
          {
            order: '1ST',
            eventName: '审计类型_01',
            eventLevel: 0,
            eventTotal: '987654321',
          },
          {
            order: '2ND',
            eventName: '审计类型_02',
            eventLevel: 1,
            eventTotal: '87654321',
          },
          {
            order: '3RD',
            eventName: '审计类型_03',
            eventLevel: 2,
            eventTotal: '7654321',
          },
          {
            order: '4TH',
            eventName: '审计类型_04',
            eventLevel: 3,
            eventTotal: '654321',
          },
          {
            order: '5TH',
            eventName: '审计类型_05',
            eventLevel: 4,
            eventTotal: '54321',
          },
          {
            order: '6TH',
            eventName: '审计类型_06',
            eventLevel: 0,
            eventTotal: '4321',
          },
          {
            order: '7TH',
            eventName: '审计类型_07',
            eventLevel: 1,
            eventTotal: '3210',
          },
          {
            order: '8TH',
            eventName: '审计类型_08',
            eventLevel: 2,
            eventTotal: '210',
          },
          {
            order: '9TH',
            eventName: '审计类型_09',
            eventLevel: 3,
            eventTotal: '10',
          },
        ],
      }
    },
  },
]
