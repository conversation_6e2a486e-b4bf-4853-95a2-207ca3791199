<!--
 * @Description: 资产发现
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template xmlns:el-col="http://www.w3.org/1999/html">
  <div class="router-wrap-table">
    <header class="table-header">
      <section v-show="tab == '1'" class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('asset.discover.osType')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQuery('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddAll">
            {{ $t('button.batch.change') }}
          </el-button>
        </section>
      </section>
      <section v-show="tab == '2'" class="table-header-main">
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('asset.discover.taskName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQuery('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddTask">
            {{ $t('button.add') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.osType"
                  class="width-max"
                  clearable
                  :placeholder="$t('asset.discover.placeholder.osType')"
                  @change="inputQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="10">
                <range-picker
                  v-model="queryInput.ipRange"
                  type="ip"
                  :start-placeholder="$t('asset.discover.placeholder.startIpv')"
                  :end-placeholder="$t('asset.discover.placeholder.endIpv')"
                  @change="inputQuery('e')"
                ></range-picker>
              </el-col>
              <el-col :span="4" align="right" :offset="5">
                <el-button v-has="'query'" @click="inputQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="seniorQuery"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <!--切换标签-->
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane :label="$t('asset.discover.asset')" name="first"></el-tab-pane>
        <el-tab-pane :label="$t('asset.discover.findTask')" name="second"></el-tab-pane>
      </el-tabs>
      <!--资产发现列表页-->
      <main v-show="tab == '1'" class="table-body">
        <header class="table-body-header">
          <h2 class="table-body-title">
            {{ $t('asset.discover.asset') }}
          </h2>
        </header>
        <main class="table-body-main">
          <el-table
            ref="Table"
            v-loading="data.loading"
            :data="data.table"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
            height="100%"
            fit
            @current-change="TableRowChange"
            @selection-change="TableSelectsChange"
          >
            <el-table-column type="selection" prop="assetId"></el-table-column>
            <el-table-column prop="osType" :label="$t('asset.discover.osType')" show-overflow-tooltip></el-table-column>
            <el-table-column prop="ipvAddress" :label="$t('asset.discover.ipvAddress')" show-overflow-tooltip></el-table-column>
            <el-table-column fixed="right" width="200">
              <template slot-scope="scope">
                <el-button v-has="'add'" class="el-button--blue" @click="clickAdd(scope.row)">
                  {{ $t('button.change') }}
                </el-button>
                <el-button v-has="'delete'" class="el-button--red" @click="clickDelete('1', scope.row)">
                  {{ $t('button.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </main>
      </main>
      <!--发现任务列表页-->
      <main v-show="tab == '2'" class="table-body">
        <header class="table-body-header">
          <h2 class="table-body-title">
            {{ $t('asset.discover.findTask') }}
          </h2>
        </header>
        <main class="table-body-main">
          <el-table
            ref="Table"
            v-loading="data.loading"
            :data="data.task"
            element-loading-background="rgba(0, 0, 0, 0.3)"
            size="mini"
            highlight-current-row
            tooltip-effect="light"
            fit
            @current-change="TableRowChange"
            @selection-change="TableSelectsChange"
          >
            <el-table-column prop="taskName" :label="$t('asset.discover.taskName')" show-overflow-tooltip></el-table-column>
            <el-table-column prop="netWorkName" :label="$t('asset.discover.netWorkName')" show-overflow-tooltip></el-table-column>
            <el-table-column prop="progressPercent" :label="$t('asset.discover.progressPercent')" show-overflow-tooltip>
              <template slot-scope="scope">
                <el-progress
                  :text-inside="true"
                  :stroke-width="20"
                  :width="300"
                  :percentage="Number(scope.row.progressPercent)"
                  :color="scope.row.runStatus == 3 ? '#f56c6c' : 'rgba(112, 182, 3, 1)'"
                ></el-progress>
              </template>
            </el-table-column>
            <el-table-column width="280" fixed="right">
              <template slot-scope="scope">
                <el-button
                  v-has="'find'"
                  :disabled="scope.row.runStatus == 1"
                  :class="scope.row.runStatus == 1 ? '' : 'el-button--blue'"
                  @click="clickFind(scope.row)"
                >
                  {{ $t('button.nowFind') }}
                </el-button>
                <el-button v-has="'delete'" class="el-button--red" @click="clickDelete('2', scope.row)">
                  {{ $t('button.delete') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </main>
      </main>
    </main>
    <!--页脚-->
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <!--转化弹窗-->
    <table-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form"
      :width="'70%'"
      @on-submit="clickSubmitAdd"
    ></table-dialog>
    <!--批量转化-->
    <table-dialog
      :visible.sync="dialog.visible.addAll"
      :title="dialog.title.addAll"
      :form="dialog.form"
      :width="'70%'"
      @on-submit="clickSubmitAddAll"
    ></table-dialog>
    <!--添加任务弹窗-->
    <add-task-dialog
      :visible.sync="dialog.visible.addTask"
      :title="dialog.title.addTask"
      :form="dialog.addTask"
      :width="'35%'"
      @on-submit="clickSubmitAddTask"
    ></add-task-dialog>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import { prompt } from '@util/prompt'
import { queryTableData, queryData, queryType, queryNet, addAsset, addAssets, deleteAssets, deleteTasks, nowTasks } from '@api/asset/discover-api'
import { validateIp, validateEmail, validateCellphone } from '@util/validate'
import TableDialog from './AudDialog'
import AddTaskDialog from './AddTask'
import RangePicker from '@comp/RangePicker'
import { debounce } from '@util/effect'
import { isEmpty } from '@util/common'

export default {
  name: 'AssetDiscover',
  components: {
    TableDialog,
    AddTaskDialog,
    RangePicker,
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (value === '') {
        // 这个分支代表为空可以过，但是不为空就要校验了
        callback()
      } else if (!validateIp(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }
    const validatorEmail = (rule, value, callback) => {
      if (value === '') {
        // 这个分支代表为空可以过，但是不为空就要校验了
        callback()
      } else if (!validateEmail(value)) {
        callback(new Error(this.$t('validate.comm.email')))
      } else {
        callback()
      }
    }
    const validatorTelephone = (rule, value, callback) => {
      if (value === '') {
        callback()
      } else if (!validateCellphone(value)) {
        callback(new Error(this.$t('validate.comm.cellphone')))
      } else {
        callback()
      }
    }
    return {
      tab: '1',
      activeName: 'first',
      isShow: false,
      queryInput: {
        fuzzyField: '',
        ipRange: ['', ''],
        assetType: '',
        osType: '',
      }, // 搜索框内容
      data: {
        loading: false, // 数据加载时loading
        table: [], // 资产发现列表,
        task: [], // 资产发现任务列表
        selected: [], // 选中的列表的数据
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
        visible: true, // 防止页码错误
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.change', [this.$t('asset.discover.asset')]),
          addAll: this.$t('dialog.title.changeAll', [this.$t('asset.discover.asset')]),
          addTask: this.$t('dialog.title.add', [this.$t('asset.discover.findTask')]),
          detail: this.$t('dialog.title.detail', [this.$t('asset.discover.findTask')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false, // 转化
          addAll: false, // 批量转化
          addTask: false, // 添加任务
        },
        addTask: {
          netList: [], // 网段数据
          model: {
            taskName: '',
            netWorkId: '',
          },
          info: {
            taskName: {
              key: 'taskName',
              label: this.$t('asset.discover.taskName'),
              value: '',
            },
            netWorkId: {
              key: 'netWorkId',
              label: this.$t('asset.discover.netWorkName'),
              value: '',
            },
          },
          rules: {
            taskName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            netWorkId: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
          },
        },
        detail: {
          detail: [],
        },
        form: {
          assetId: '',
          addAllIds: '',
          treeList: [], // 资产类型数据
          netList: [], // 网段数据
          model: {
            // 弹出表单绑定值
            assetName: '',
            assetType: '',
            netWorkId: '',
            assetModel: '',
            manufactor: '',
            osType: '',
            memoryInfo: '',
            responsiblePerson: '',
            contactPhone: '',
            email: '',
            makerContactPhone: '',
            assetCode: '',
            domaId: '',
            assetDesc: '',
            ipvAddress: '',
            securityComponent: '',
          },
          info: {
            // 弹出表单信息
            assetName: {
              key: 'assetName',
              label: this.$t('asset.discover.assetName'),
              value: '',
            },
            assetType: {
              key: 'assetType',
              label: this.$t('asset.discover.assetType'),
              value: '',
            },
            netWorkId: {
              key: 'netWorkId',
              label: this.$t('asset.discover.netWorkName'),
              value: '',
            },
            assetModel: {
              key: 'assetModel',
              label: this.$t('asset.discover.assetModel'),
              value: '',
            },
            manufactor: {
              key: 'manufactor',
              label: this.$t('asset.discover.manufactor'),
              value: '',
            },
            osType: {
              key: 'osType',
              label: this.$t('asset.discover.osType'),
              value: '',
            },
            memoryInfo: {
              key: 'memoryInfo',
              label: this.$t('asset.discover.memoryInfo'),
              value: '',
            },
            responsiblePerson: {
              key: 'responsiblePerson',
              label: this.$t('asset.discover.responsiblePerson'),
              value: '',
            },
            contactPhone: {
              key: 'contactPhone',
              label: this.$t('asset.discover.contactPhone'),
              value: '',
            },
            email: {
              key: 'email',
              label: this.$t('asset.discover.email'),
              value: '',
            },
            makerContactPhone: {
              key: 'makerContactPhone',
              label: this.$t('asset.discover.makerContactPhone'),
              value: '',
            },
            assetCode: {
              key: 'assetCode',
              label: this.$t('asset.discover.assetCode'),
              value: '',
            },
            domaName: {
              key: 'domaId',
              label: this.$t('asset.discover.domaName'),
              value: '',
            },
            assetDesc: {
              key: 'assetDesc',
              label: this.$t('asset.discover.assetDesc'),
              value: '',
            },
            ipvAddress: {
              key: 'ipvAddress',
              label: this.$t('asset.discover.ipvAddress'),
              value: '',
            },
            securityComponent: {
              key: 'securityComponent',
              label: this.$t('asset.discover.securityComponent'),
              value: '',
            },
          },
          rules: {
            assetName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            assetType: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            ipvAddress: [
              {
                validator: validatorIp,
                trigger: 'blur',
              },
            ],
            email: [
              {
                validator: validatorEmail,
                trigger: 'blur',
              },
            ],
            contactPhone: [
              {
                validator: validatorTelephone,
                trigger: 'blur',
              },
            ],
            makerContactPhone: [
              {
                validator: validatorTelephone,
                trigger: 'blur',
              },
            ],
          },
        },
      },
      queryDebounce: null,
    }
  },
  computed: {
    ...mapState({
      status: (state) => state.websocket.status,
      assetDiscover: (state) => state.websocket.assetDiscover,
    }),
  },
  watch: {
    status: {
      handler(value) {
        if (value) {
          this.sendWebsocket(1)
        }
      },
      immediate: true,
    },
    assetDiscover(value) {
      let perList = []
      if (value) {
        perList = [...value.message]
        if (this.data.task && this.data.task.length > 0 && this.status) {
          this.data.task.forEach((row) => {
            perList.forEach((item) => {
              if (item.taskId === row.taskId) {
                row.runStatus = item.runStatus
                row.progressPercent = item.progressPercent
              }
            })
          })
        }
      } else {
        perList = []
      }
    },
  },
  mounted() {
    this.getTableData()
    this.getTreeList()
    this.getNetList()
    this.initDebounce()
  },
  beforeDestroy() {
    this.sendWebsocket(2)
  },
  methods: {
    sendWebsocket(action) {
      if (this.status) {
        this.$store.dispatch('websocket/send', {
          topic: 'asset_discover',
          action,
          message: null,
        })
      }
    },
    initDebounce() {
      this.queryDebounce = debounce(() => {
        this.searchQuery()
      }, 500)
    },
    // 获取资产发现列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 获取资产发现任务列表
    getData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryData(params).then((res) => {
        this.data.task = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 获取资产类型数据
    getTreeList() {
      queryType().then((res) => {
        this.dialog.form.treeList = res
      })
    },
    // 获取网段数据
    getNetList() {
      queryNet().then((res) => {
        this.dialog.form.netList = res
        this.dialog.addTask.netList = res
      })
    },
    // 调用转化接口
    add(obj) {
      const param = {
        ...obj,
        assetType: obj.assetType[1],
        assetClass: obj.assetType[0],
        assetId: this.dialog.form.assetId,
      }
      addAsset(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.change.success',
              type: 'success',
            },
            () => {
              this.clearQuery()
              this.getTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.change.repeat',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.change.number',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'tip.change.running',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.change.error',
            type: 'error',
          })
        }
      })
    },
    // 调用批量转化接口
    addAll(obj) {
      const param = {
        ...obj,
        assetType: obj.assetType[1],
        assetClass: obj.assetType[0],
        assetIds: this.dialog.form.addAllIds,
        ipvAddress: '',
      }
      addAssets(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.change.success',
              type: 'success',
            },
            () => {
              this.clearQuery()
              this.getTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.change.repeat',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.change.number',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'tip.change.running',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.change.error',
            type: 'error',
          })
        }
      })
    },
    // 点击转化操作并弹出对话框操作
    clickAdd(row) {
      this.dialog.form.addAllIds = ''
      this.dialog.form.assetId = row.assetId
      this.dialog.form.model = {
        assetName: '',
        assetClass: row.assetClass,
        assetType: [row.assetClass, row.assetType],
        netWorkId: '',
        assetModel: '',
        manufactor: '',
        osType: row.osType,
        memoryInfo: '',
        responsiblePerson: '',
        contactPhone: '',
        email: '',
        makerContactPhone: '',
        assetCode: '',
        assetDesc: '',
        ipvAddress: row.ipvAddress,
      }
      this.dialog.visible.add = true
    },
    // 点击添加任务按钮
    clickAddTask() {
      this.dialog.addTask.model = {
        taskName: '',
        netWorkId: '',
      }
      this.dialog.visible.addTask = true
    },
    // 点击批量转化操作并弹出对话框操作
    clickAddAll() {
      if (this.data.selected.length > 0) {
        this.dialog.form.addAllIds = this.data.selected.map((item) => item.assetId).toString()
        this.clearDialogFormModel()
        this.dialog.visible.addAll = true
      } else {
        prompt({
          i18nCode: 'tip.change.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 提交转化表单操作
    clickSubmitAdd(formModel) {
      this.add(formModel)
    },
    // 提交批量转化
    clickSubmitAddAll(formModel) {
      this.addAll(formModel)
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.inputQuery()
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQuery()
    },
    // 列表多选改变
    TableSelectsChange(select) {
      this.data.selected = select
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    // 清空搜索条件
    clearQuery() {
      this.queryInput = {
        ipRange: ['', ''],
        assetType: '',
        fuzzyField: '',
        osType: '',
      }
    },
    // 高级查询输入框搜索方法
    inputQuery(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    searchQuery() {
      const params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
        startIpv: this.ipRange(this.queryInput.ipRange),
        assetType: this.queryInput.assetType[1],
        osType: this.queryInput.osType,
        fuzzyField: this.queryInput.fuzzyField,
      }
      if (this.activeName === 'first') {
        this.getTableData(params)
      } else {
        this.getData(params)
      }
    },
    ipRange(ipArr) {
      let ip = ''
      ipArr = ipArr.filter((item) => {
        if (!isEmpty(item)) return item.trim()
      })
      if (ipArr.length > 0) {
        ip = ipArr.join('-')
      }
      return ip
    },
    // 高级查询输入框重置方法
    resetQuery() {
      this.pagination.pageNum = 1
      this.clearQuery()
      this.queryDebounce()
    },
    // 清空弹窗表单绑定数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        assetName: '',
        assetType: '',
        netWorkId: '',
        assetModel: '',
        manufactor: '',
        osType: '',
        memoryInfo: '',
        responsiblePerson: '',
        contactPhone: '',
        email: '',
        makerContactPhone: '',
        assetCode: '',
        assetDesc: '',
        ipvAddress: '',
      }
    },
    // 切换表格数据
    changeTable() {
      this.pagination.pageNum = 1
      this.isShow = false
      if (this.activeName === 'first') {
        this.tab = '1'
        // 清空搜索条件
        this.clearQuery()
        this.queryDebounce()
      } else {
        this.tab = '2'
        // 清空搜索条件
        this.clearQuery()
        this.queryDebounce()
      }
    },
    // tab切换
    handleClick() {
      this.changeTable()
    },
    // 点击删除操作
    clickDelete(type, row) {
      const ids = type === '1' ? row.assetId : row.taskId
      this.delete(type, ids)
    },
    // 调用删除接口
    delete(type, ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        if (type === '1') {
          deleteAssets(ids).then((res) => {
            if (res) {
              prompt(
                {
                  i18nCode: 'tip.delete.success',
                  type: 'success',
                },
                () => {
                  const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                  if (idArray.length === this.data.table.length) {
                    this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                  }
                  this.inputQuery()
                }
              )
            } else {
              prompt({
                i18nCode: 'tip.delete.error',
                type: 'error',
              })
            }
          })
        } else {
          deleteTasks(ids).then((res) => {
            if (res) {
              prompt(
                {
                  i18nCode: 'tip.delete.success',
                  type: 'success',
                },
                () => {
                  const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                  if (idArray.length === this.data.table.length) {
                    this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                  }
                  this.inputQuery('e')
                }
              )
            } else {
              prompt({
                i18nCode: 'tip.delete.error',
                type: 'error',
              })
            }
          })
        }
      })
    },
    // 点击立即发现
    clickFind(row) {
      const taskId = row.taskId
      nowTasks(taskId).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.change.begin',
              type: 'success',
            },
            () => {
              this.clearQuery()
              this.getData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.change.repeat',
            type: 'error',
          })
        } else if (res === 3) {
          prompt({
            i18nCode: 'tip.change.number',
            type: 'error',
          })
        } else if (res === 4) {
          prompt({
            i18nCode: 'tip.change.running',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.change.error',
            type: 'error',
          })
        }
      })
    },
    // 提交添加任务表单操作
    clickSubmitAddTask(formModel) {
      if (formModel === 'true') {
        this.getData()
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.el-button.is-disabled,
.el-button.is-disabled:focus,
.el-button.is-disabled:hover {
  background-color: transparent;
}
</style>
