<!--
 * @Description: 代理服务器 - 中心设置弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-05-06
 * @Editor:
 * @EditDate: 2022-05-06
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.centerConfig', [titleName])"
    width="30%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formTemplate" :model="form.model" :rules="form.rules" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item prop="centerIp" :label="$t('management.proxy.network.centerIp')">
            <el-input v-model.trim="form.model.centerIp" maxlength="128"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { validateIpv4 } from '@util/validate'
import { setAllCenterIp } from '@api/management/proxy-server-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('validate.ip.empty')))
      } else if (!validateIpv4(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }
    return {
      dialogVisible: this.visible,
      form: {
        model: {
          centerIp: '',
        },
        rules: {
          centerIp: [
            {
              required: true,
              validator: validatorIp,
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.form.model.centerIp = ''
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.allCenterIp'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.setCenterIp()
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    setCenterIp() {
      setAllCenterIp(this.form.model.centerIp).then((res) => {
        if (res === '0') {
          prompt({
            i18nCode: 'tip.update.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
