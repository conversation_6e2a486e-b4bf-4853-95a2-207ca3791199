<!--
 * @Description: 日志源接收状态
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023/11/10
 * @Editor:
 * @EditDate: 2023/11/10
-->
<template>
  <el-container class="widget" :style="{ height: height + 'px' }">
    <el-header class="widget-header" height="22px">
      {{ $t('visualization.compliance.title.logReceivingStatus') }}
    </el-header>
    <el-main class="widget-main">
      <el-table height="300" :data="tableData" :row-style="{ height: '0' }" :cell-style="{ padding: '2px 0' }">
        <el-table-column prop="label" :label="$t('visualization.compliance.receiving.logSourceName')" align="left" width="188"></el-table-column>
        <el-table-column prop="status" :label="$t('visualization.compliance.receiving.status')" align="center" width="68">
          <template slot-scope="scope">
            <el-tooltip placement="top" effect="light" :content="showStatusTip(scope.row)">
              <i class="el-icon-bell" :class="scope.row.status === '1' ? 'visualization-status-green' : 'visualization-status-grey'"></i>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column align="right" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-button v-has="'update'" :class="scope.row.receivingDuration === 0 ? 'button-blue' : 'blue-gray'" @click="clickDuration(scope.row, 0)">
              {{ $t('visualization.compliance.receiving.durationHours') }}
            </el-button>
            <el-divider direction="vertical" class="el-divider"></el-divider>
            <el-button v-has="'update'" :class="scope.row.receivingDuration === 1 ? 'button-blue' : 'blue-gray'" @click="clickDuration(scope.row, 1)">
              {{ $t('visualization.compliance.receiving.durationAll') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-main>
  </el-container>
</template>

<script>
import { queryLogReceivingStatus } from '@api/visualization/compliance-api'

export default {
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      tableData: [],
    }
  },
  mounted() {
    this.getLogReceivingStatus()
  },
  methods: {
    getLogReceivingStatus() {
      queryLogReceivingStatus().then((res) => {
        this.tableData = res
      })
    },
    showStatusTip(row) {
      if (row.status === '1') {
        return this.$t('tip.operate.data')
      } else {
        return this.$t('tip.operate.empty')
      }
    },
    clickDuration(row, duration) {
      if (row.receivingDuration !== duration) {
        row.receivingDuration = duration
        this.$emit('on-set-receive-times', row)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.widget {
  &-header {
    padding: 0 2px !important;
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 5px;
    .visualization-status-green {
      font-size: 14px;
      color: #31ab10;
    }
    .visualization-status-grey {
      font-size: 14px;
      color: gray;
    }
    .el-table tr td .cell .el-button:hover {
      color: #3a8ee6;
    }
    .blue-gray {
      color: gray;
    }
    .button-blue {
      color: #3a8ee6;
    }
    .el-divider {
      margin-left: -1px;
      margin-right: -1px;
    }
  }
}
</style>
