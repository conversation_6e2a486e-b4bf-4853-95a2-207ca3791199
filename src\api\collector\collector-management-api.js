import request from '@util/request'

// 查询采集器管理列表数据
export function queryCollectorTableData(obj) {
  return request({
    url: '/collector/management/collectors',
    method: 'get',
    params: obj || {},
  })
}

// 删除采集器管理列表数据
export function deleteCollectorData(id) {
  return request({
    url: `/collector/management/collector/${id}`,
    method: 'delete',
  })
}

// 添加采集器管理列表数据
export function addCollectorData(obj) {
  return request({
    url: '/collector/management/collector',
    method: 'post',
    data: obj || {},
  })
}

// 修改采集器管理列表数据
export function updateCollectorData(obj) {
  return request({
    url: '/collector/management/collector',
    method: 'put',
    data: obj || {},
  })
}

// 修改采集器启用停用状态
export function updateCollectorStatus(id, state) {
  return request({
    url: `/collector/management/collector/${id}/${state}`,
    method: 'put',
  })
}

// 查询设备分类
export function queryDeviceType(obj) {
  return request({
    url: '/collector/management/combo/source-device-types',
    method: 'get',
    params: obj || {},
  })
}

// 查询采集器过滤策略
export function queryFilterStrategies(obj) {
  return request({
    url: '/collector/management/combo/filter-strategies',
    method: 'get',
    params: obj || {},
  })
}

// 查询采集器接入方式
export function queryProtocol(obj) {
  return request({
    url: '/collector/management/combo/collection-protocols',
    method: 'get',
    params: obj || {},
  })
}

// 查询采集器详情
export function queryCollectorTableDetail(id) {
  return request({
    url: `/collector/management/collector/${id}`,
    method: 'get',
  })
}

// 查询代理服务器
export function queryAgent(obj) {
  return request({
    url: '/collector/management/combo/agents',
    method: 'get',
    params: obj || {},
  })
}

// 日志导入
export function uploadLogData(obj) {
  return request(
    {
      url: `/collector/management/uploadlog`,
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

// 日志导入记录
export function queryImportLogRecords(obj) {
  return request({
    url: `/collector/management/importLogRecord`,
    method: 'get',
    params: obj || {},
  })
}

// 查询日志类型
export function queryLogType(obj) {
  return request({
    url: '/collector/management/combo/log-types',
    method: 'get',
    params: obj || {},
  })
}

// 查询存在的接入方式
export function queryExistAccessmode(obj) {
  return request({
    url: '/collector/management/collector-checkip',
    method: 'post',
    data: obj || {},
  })
}

// 添加日志源设备
export function addLogSource(obj) {
  return request({
    url: '/collector/management/addDevType',
    method: 'post',
    data: obj || {},
  })
}

// 查询厂商下拉数据
export function queryManufactCombo() {
  return request({
    url: '/collector/management/combo/manufact',
    method: 'get',
  })
}

// 查询设备类别下拉数据
export function queryCategoryCombo() {
  return request({
    url: '/collector/management/combo/category',
    method: 'get',
  })
}
export function downloadTool(id) {
  return request(
    {
      url: `/collector/management/downloadTool/${id}`,
      method: 'get',
    },
    'download'
  )
}
