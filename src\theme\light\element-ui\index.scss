$base-color: #0050ad;
$main-bg-color: transparent;
$input-hover-color: #1361bd;
$hover-color: #1361bd;
$base-color-bottom: rgb(239, 239, 239);
$base-line: #b8b9b9;
$font-color: #595a5a;
$border-color: #dcdfe6;
$disable-bg-color: #666;
$table-header-color: #7bb6db;
$table-header-bg-color: rgba(230, 230, 230, 0.8);
$table-operation-del-color: #e32a0c;
$primary-color: #0050ad;
$primary-hover-color: #1361bd;
$success-color: #67c23a;
$success-hover-color: #85ce61;
$info-color: #909399;
$info-hover-color: #a6a9ad;
$waring-color: #e6a23c;
$waring-hover-color: #ebb563;
$danger-color: #f56c6c;
$danger-hover-color: #f78989;
$disable-color: #c0c4cc;
$button-disable: #cccbcb7a;

html[data-theme='light'] {
  input,
  button {
    background-color: $CR;
  }

  .el-menu {
    border: none;
    background-color: $main-bg-color;

    i {
      color: #b4bcc2;

      &:hover {
        color: $WT;
      }
    }

    .el-submenu {
      .el-submenu__title {
        color: #b4bcc2;

        &:hover,
        &:focus {
          background-color: $base-color;
          color: $WT;

          i {
            color: $WT;
          }
        }
      }

      .el-menu-item {
        color: #b4bcc2;

        &:focus,
        &:hover {
          background-color: $WT;
          color: $base-color;
        }

        &.is-active {
          color: $base-color;
        }
      }
    }
  }

  ul {
    &[role='menubar'] {
      &.el-menu {
        & > .el-submenu {
          & > .el-submenu__title {
            background-color: $WT;
          }
        }
      }
    }
  }

  .el-menu-item {
    color: #b4bcc2;

    &:focus,
    &:hover {
      background-color: $base-color;
      color: $WT;
    }
  }

  .el-breadcrumb {
    &__inner {
      color: $font-color;
    }

    &__item {
      i {
        color: $font-color;
      }

      &:last-child {
        .el-breadcrumb__inner {
          color: $font-color;

          &:hover {
            color: $font-color;
          }

          a {
            &:hover {
              color: $font-color;
            }
          }
        }
      }
    }
  }

  .el-table {
    color: $font-color;
    background-color: $CR;

    thead {
      color: $base-color;
    }

    th {
      background-color: $WT;
    }

    tr {
      background-color: $WT;
      border-bottom: 1px solid $base-color-bottom;

      .el-table__expand-icon {
        color: $font-color;
      }

      td {
        .cell {
          .el-button {
            border: none;
            box-shadow: none;

            &:hover,
            &:focus {
              font-weight: bold;
              color: $base-color;
            }
          }

          .el-button {
            &.el-button--red {
              color: $table-operation-del-color;

              &:hover {
                color: $table-operation-del-color;
                font-weight: bold;
              }
            }
          }

          .el-button {
            &.el-button--greed {
              color: $success-color;

              &:hover {
                color: $WT;
              }
            }
          }

          .el-button {
            &.el-button--yellow {
              color: $waring-color;

              &:hover {
                color: $WT;
              }
            }
          }

          .el-button {
            &.el-button--blue {
              color: $base-color;

              &:hover {
                color: $base-color;
                font-weight: bold;
              }
            }
          }

          .el-button {
            &.is-disabled {
              color: $button-disable;

              &:hover {
                color: $button-disable;
                font-weight: bold;
              }
            }
          }
        }
      }
    }

    [class*='el-table__row--level'] {
      .el-table__expand-icon {
        color: $font-color;
      }
    }

    .sort-caret {
      &.ascending {
        border-bottom-color: $table-header-color;
      }
    }

    .sort-caret {
      &.descending {
        border-top-color: $table-header-color;
      }
    }

    .el-table__expanded-cell {
      background-color: $CR;
    }

    .el-table__fixed-right-patch {
      background-color: $WT;
      border-bottom: 1px solid $WT;
    }

    .el-table--striped {
      .el-table__body {
        tr {
          &.el-table__row--striped {
            td {
              background-color: $WT;
            }
          }
        }
      }
    }

    .el-table--enable-row-hover {
      .el-table__body {
        tr {
          &:hover {
            & > td {
              background-color: $table-header-bg-color;
            }
          }
        }
      }
    }

    .el-table__body {
      tr {
        &.current-row {
          & > td {
            background-color: $table-header-bg-color;
          }
        }
      }
    }

    & td {
      border: none;
    }

    & th {
      &.is-leaf {
        border: none;
      }
    }

    .el-table__body,
    .el-table__footer,
    .el-table__header {
      border-collapse: collapse;
    }

    .el-table__footer-wrapper,
    .el-table__header-wrapper {
      background-color: $table-header-bg-color;
    }

    .el-table--group {
      border: none;
    }

    .el-table--border {
      border: none;

      &::after {
        background-color: $base-line;
      }

      th {
        &.gutter {
          &:last-of-type {
            border-bottom-color: $border-color;
          }
        }
      }
    }

    .el-table--striped {
      .el-table__body {
        tr {
          &.el-table__row--striped {
            &.current-row {
              td {
                background-color: $WT;
              }
            }
          }
        }
      }
    }
  }

  .el-button {
    background-color: $WT;
    color: $base-color;

    i ~ span {
      padding-left: 5px;
    }

    &.el-button--default {
      &.is-plain {
        box-shadow: none;

        &:hover {
          color: $WT;
        }
      }
    }

    &.el-button--primary {
      border: 1px solid $border-color;

      &.is-plain {
        border: none;
        box-shadow: none;
        background-color: $CR;
        color: $base-color;

        &:hover {
          background-color: $base-color;
          border-color: $base-color;
          color: $WT;
        }
      }
    }

    &.el-button--success {
      background-color: $success-color;
      border-color: $success-color;
      box-shadow: none;
      color: $WT;

      &:hover {
        background-color: $success-hover-color;
        border-color: $success-hover-color;
        color: $WT;
      }

      &.is-plain {
        background-color: $CR;
        color: $success-color;

        &:hover {
          border-color: $success-color;
          color: $WT;
        }
      }
    }

    &.el-button--info {
      background-color: $info-color;
      border-color: $info-color;
      box-shadow: none;
      color: $WT;

      &:hover {
        background-color: $info-hover-color;
        border-color: $info-hover-color;
        color: $WT;
      }

      &.is-plain {
        background-color: $CR;
        color: $info-color;

        &:hover {
          border-color: $info-color;
          color: $WT;
        }
      }
    }

    &.el-button--warning {
      background-color: $waring-color;
      border-color: $waring-color;
      box-shadow: none;
      color: $WT;

      &:hover {
        background-color: $waring-hover-color;
        border-color: $waring-hover-color;
        color: $WT;
      }

      &.is-plain {
        background-color: $CR;
        color: $waring-color;

        &:hover {
          border-color: $waring-color;
          color: $WT;
        }
      }
    }

    &.el-button--danger {
      background-color: $danger-color;
      border-color: $danger-color;
      box-shadow: none;
      color: $WT;

      &:hover {
        background-color: $danger-hover-color;
        border-color: $danger-hover-color;
        color: $WT;
      }

      &.is-plain {
        background-color: $CR;
        color: $danger-color;

        &:hover {
          border-color: $danger-color;
          color: $WT;
        }
      }
    }

    &:focus,
    &:hover {
      background-color: $base-color;
      border-color: $base-color;
      color: $WT;
    }

    &.is-loading {
      &:before {
        background-color: rgba($disable-bg-color, 0.5);
        color: #000;
      }
    }

    &.is-plain {
      &:hover,
      &:focus {
        background-color: $base-color;
        border-color: $base-color;
        color: $WT;
      }
    }
  }

  .el-button-group {
    .el-button--primary {
      &:first-child {
        border-color: $base-color;
      }

      &:last-child {
        border-color: $base-color;
      }

      &:not(:first-child) {
        &:not(:last-child) {
          border-color: $base-color;
        }
      }
    }
  }

  .el-tree {
    background: $WT;
    color: $font-color;
    padding: 5px;
    height: 100%;
    min-width: 180px;
    overflow: scroll;
  }

  .el-tree-node__content {
    border-bottom: 1px dashed $base-color-bottom;
    &:hover {
      background-color: $CR;
      color: $base-color;
    }
  }

  .el-tree-node {
    margin-top: 5px;
    min-width: 180px;

    &:focus {
      & > .el-tree-node__content {
        background-color: $CR;
        color: $base-color;
      }
    }
  }

  .el-tree--highlight-current {
    .el-tree-node {
      &.is-current {
        & > .el-tree-node__content {
          background-color: $CR;
          color: $base-color;
        }
      }
    }
  }

  .el-dialog {
    background-color: $WT;

    .el-dialog__header {
      border-bottom: 1px solid rgb(195, 195, 195) !important;
      box-sizing: border-box;

      .el-dialog__title {
        color: $BK;
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          color: $base-line;
        }
      }

      .el-dialog__headerbtn {
        .el-dialog__close {
          &:hover {
            color: $font-color;
          }
        }
      }
    }

    .el-dialog__body {
      .el-input__inner {
        &:hover {
          border: 1px solid $input-hover-color;
        }
      }

      .el-input {
        input {
          color: $BK;
        }
      }
    }

    .el-dialog__footer {
      border-top: 1px solid rgb(195, 195, 195) !important;
      box-sizing: border-box;

      .el-button {
        background-color: $base-color;
        border-color: $base-color;
        color: $WT;

        &:hover {
          background-color: $base-color;
          border-color: $base-color;
          color: $WT;
          font-weight: bold;
        }

        &:focus {
          background-color: $base-color;
          border-color: $base-color;
          color: $WT;
          font-weight: bold;
        }
      }
    }
  }

  .el-message--info {
    background-color: #edf2fc;
  }

  .el-message--success {
    background-color: #f0f9eb;
  }

  .el-message--warning {
    background-color: #fdf6ec;
  }

  .el-message--error {
    background-color: #fef0f0;
    border: none;
  }

  .el-dropdown {
    margin-left: 10px;

    .el-dropdown-menu {
      background-color: $WT;
      border: none;
      color: $base-color;

      .el-dropdown-menu__item {
        color: $base-color;

        &:focus {
          background-color: #f0f3fa;
        }

        &:not(.is-disabled) {
          &:hover {
            background-color: #f0f3fa;
          }
        }
      }
    }
  }

  .el-form-item__label {
    color: $base-color;
  }

  .el-textarea.is-disabled .el-textarea__inner {
    color: $disable-color;
  }
  .el-input.is-disabled .el-input__inner {
    color: $disable-color !important;
  }
}
