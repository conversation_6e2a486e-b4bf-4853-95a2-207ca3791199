export default {
  management: {
    asset: '资产管理',
    baseInfo: '基本信息',
    moreInfo: '扩展信息',
    noneInfo: '暂无扩展属性',
    logInfo: '日志信息',
    monitorInfo: '监控信息',
    faultEvent: '故障事件',
    perfEvent: '性能事件',
    ipArrayInfo: 'IP范围不能超过255',
    th: '自定义列',
    allCheck: '全选',
    assetCode: '资产编号',
    assetName: '资产名称',
    assetType: '资产类型',
    assetTypeName: '资产类型',
    responsiblePerson: '负责人',
    startTime: '起始时间',
    endTime: '终止时间',
    netWorkId: '网段',
    netWorkName: '网段',
    assetModel: '资产型号',
    manufactor: '制造商',
    osType: '操作系统',
    ipvAddress: '资产IP',
    startIP: '起始资产IP',
    endIP: '终止资产IP',
    memoryInfo: '内存容量',
    contactPhone: '联系电话',
    email: '邮件地址',
    makerContactPhone: '厂商电话',
    domaName: '隶属区域',
    securityComponent: '安全组件类型',
    riskRating: '风险等级',
    useStatusText: '资产在线状态',
    assetValue: '资产权值',
    assetDesc: '备注信息',
    exceed: '当前限制选择 1 个文件，请删除后再上传',
    successUpload: '操作完成，已成功导入{0}条数据!',
    isOnlineCheck: '是否在线检测',
    log: {
      logSourceInfo: '日志源信息',
      logTotal: '日志接收总数',
      logDuration: '日志存储时长',
      logTrend: '日志采集趋势',
      originalLog: '原始日志',
      generalLog: '通用日志',
      logAccessTime: '日志接收时间',
      logLatestTime: '最新日志时间',
      type2Name: '原始日志名称',
      eventName: '事件类型',
      eventCategoryName: '事件类别',
      level: '事件等级',
      deviceCategoryName: '设备类别',
      deviceTypeName: '设备类型',
      time: '日志接收时间',
      logTime: '日志时间',
      code: '特征值',
      eventDesc: '事件描述',
      fromIp: '发生源IP',
    },
    columns: {
      assetName: '资产名称',
      assetType: '资产类型',
      netWorkId: '网段',
      assetModel: '资产型号',
      manufactor: '制造商',
      osType: '操作系统',
      memoryInfo: '内存容量',
      responsiblePerson: '负责人',
      contactPhone: '联系电话',
      email: '邮件地址',
      makerContactPhone: '厂商电话',
      assetCode: '资产编号',
      assetDesc: '备注信息',
      ipvAddress: '资产IP',
      domaName: '隶属区域',
      securityComponent: '安全组件类型',
      riskRating: '风险等级',
      useStatusText: '资产在线状态',
    },
    placeholder: {
      info: '请补充扩展信息',
      selectPlace: '请选择',
      time: '选择日期时间',
      keyword: '请输入资产名称/资产IP',
      ipvAddress: '资产IP',
      manufactor: '制造商',
      responsiblePerson: '负责人',
      assetArr: '资产分类',
      assetName: '资产名称',
      netWorkId: '网段',
      assetModel: '资产型号',
      osType: '操作系统',
      memoryInfo: '内存容量',
      contactPhone: '联系电话',
      email: '邮件地址',
      makerContactPhone: '厂商电话',
      assetCode: '资产编号',
      domaName: '隶属区域',
      securityComponent: '安全组件类型',
      assetDesc: '备注信息',
      startIP: '起始资产IP',
      endIP: '终止资产IP',
      riskRating: '风险等级',
    },
    importRule: {
      new: '覆盖规则',
      old: '忽略规则',
    },
    downLoad: '下载模板',
    chooseFile: '请选择文件',
    uploadRemind: '当导入资产名称与系统资产名称相同时，以导入数据为准',
    uploadTalk: '当导入资产名称与系统资产名称相同时，以系统数据为准',
    originalLog: {
      title: '原始日志',
      eventName: '事件名称',
      level: '事件等级',
      sourceIp: '源IP',
      targetIp: '目的IP',
      time: '日志接收时间',
    },
  },
}
