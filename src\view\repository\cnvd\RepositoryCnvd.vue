<!--
 * @Description: CNVD漏洞库
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.inputVal"
              :placeholder="$t('tip.placeholder.query', [$t('repository.cnvd.fuzzyQuery')])"
              clearable
              @keyup.enter.native="pageQuery('e')"
              @change="pageQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="pageQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <section class="table-header-button"></section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.cnvdId"
                  clearable
                  :placeholder="$t('repository.cnvd.table.cnvdId')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.cnvdTitle"
                  clearable
                  :placeholder="$t('repository.cnvd.table.cnvdTitle')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="10">
                <el-date-picker
                  v-model="query.seniorQuery.releaseDate"
                  clearable
                  type="datetimerange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :start-placeholder="$t('repository.cnvd.table.startDate')"
                  :end-placeholder="$t('repository.cnvd.table.endDate')"
                  @change="pageQuery('e')"
                ></el-date-picker>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.cnvdLevel"
                  clearable
                  :placeholder="$t('repository.cnvd.table.cnvdLevel')"
                  @change="pageQuery('e')"
                >
                  <el-option v-for="item in option.levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col align="right" :offset="15" :span="4">
                <el-button @click="pageQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetSeniorQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体内容-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('repository.cnvd.header') }}
        </h2>
      </header>
      <main v-loading="data.loading" class="table-body-main">
        <el-table
          v-show="btnRef"
          ref="auditTable"
          v-el-table-scroll="scrollTable"
          infinite-scroll-disabled="disableScroll"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
        >
          <el-table-column
            v-for="(item, index) in option.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`repository.cnvd.table.${item}`)"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <level-tag v-if="item === 'cnvdLevel'" :level="levelEnum(scope.row.cnvdLevel)"></level-tag>
              <p v-else>
                {{ scope.row[item] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="120">
            <template slot-scope="scope">
              <el-button class="el-button--blue" @click="clickDetailButton(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
        <i v-show="data.totalLoading" class="el-icon-loading"></i>
      </section>
      <section class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span>{{ data.total }}</span>
      </section>
    </footer>
    <!--详情框-->
    <detail-dialog
      :visible.sync="dialog.detailDialog.visible"
      :title="dialog.detailDialog.title"
      :form="dialog.detailDialog.form"
      :column-bottom="dialog.detailDialog.columnBottom"
      :column-top="dialog.detailDialog.columnTop"
    ></detail-dialog>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import DetailDialog from './RepositoryCnvdDetailDialog'
import levelTag from '@comp/LevelTag'
import { debounce } from '@/util/effect'
import { queryCnvdTableData, queryCnvdDetail, queryTotal } from '@api/repository/cnvd-api'
export default {
  name: 'RepositoryCnvd',
  directives: {
    elTableScroll,
  },
  components: {
    DetailDialog,
    levelTag,
  },
  data() {
    return {
      btnRef: true,
      data: {
        loading: false,
        table: [],
        total: 0,
        nomore: false,
        totalLoading: false,
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
      }, // 列表基本数据
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      }, // 分页信息
      option: {
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '严重',
          },
          {
            label: this.$t('level.high'),
            value: '高',
          },
          {
            label: this.$t('level.middle'),
            value: '中',
          },
          {
            label: this.$t('level.low'),
            value: '低',
          },
          {
            label: this.$t('level.general'),
            value: '一般',
          },
        ],
        columnOption: ['cnvdId', 'cnvdTitle', 'cnvdLevel', 'affectedProducts', 'releaseDate', 'cnvdDesc'],
      }, // 下拉框Option
      dialog: {
        detailDialog: {
          visible: false,
          title: this.$t('repository.cnvd.dialog.detailTitle'),
          columnTop: [
            { key: 'cnvdId', label: this.$t('repository.cnvd.table.cnvdId') },
            { key: 'cnvdTitle', label: this.$t('repository.cnvd.table.cnvdTitle') },
            { key: 'cnvdLevel', label: this.$t('repository.cnvd.table.cnvdLevel') },
            { key: 'rollOutFlag', label: this.$t('repository.cnvd.table.rollOutFlag') },
            { key: 'releaseDate', label: this.$t('repository.cnvd.table.releaseDate') },
            { key: 'relateThreat', label: this.$t('repository.cnvd.table.relateThreat') },
          ],
          columnBottom: [
            { key: 'affectedProducts', label: this.$t('repository.cnvd.table.affectedProducts') },
            { key: 'cnvdDesc', label: this.$t('repository.cnvd.table.cnvdDesc') },
            { key: 'cnvdReference', label: this.$t('repository.cnvd.table.cnvdReference') },
            { key: 'authInfo', label: this.$t('repository.cnvd.table.authInfo') },
            { key: 'cnvdResolve', label: this.$t('repository.cnvd.table.cnvdResolve') },
            { key: 'cnvdProvider', label: this.$t('repository.cnvd.table.cnvdProvider') },
          ],
          form: {
            model: {
              cnvdId: '',
              cnvdTitle: '',
              cnvdLevel: '',
              rollOutFlag: '',
              releaseDate: '',
              relateThreat: '',
              affectedProducts: '',
              cnvdDesc: '',
              cnvdReference: '',
              authInfo: '',
              cnvdResolve: '',
              cnvdProvider: '',
            },
          },
        },
      }, // dialog基本信息
      query: {
        inputVal: '',
        seniorQuery: {
          cnvdId: '',
          cnvdTitle: '',
          cnvdLevel: '',
          releaseDate: '',
        },
      }, // 页面查询信息
      show: {
        seniorQueryShow: false,
      }, // 页面显示
    }
  },
  computed: {
    levelEnum() {
      return (level) => {
        const levelEnum = {
          严重: '0',
          高: '1',
          中: '2',
          低: '3',
          一般: '4',
        }
        return levelEnum[level]
      }
    }, // level枚举方法
    disableScroll() {
      return this.data.loading
    },
  },
  mounted() {
    this.init()
  },
  updated() {
    if (this.data.table.length > 0) this.$refs.auditTable.doLayout()
  },
  methods: {
    // 初始化方法
    init() {
      this.queryTableData()
      this.initDebounce()
      this.getTotal()
    },
    // 初始化防抖函数
    initDebounce() {
      this.data.debounce.query = debounce(() => {
        let params = {}
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.seniorQuery, {
            releaseDate: this.query.seniorQuery.releaseDate,
            pageSize: this.pagination.pageSize,
          })
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            fuzzyField: this.query.inputVal,
          }
        }
        this.queryTableData(params)
        this.getTotal(params)
      }, 500)
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.query.seniorQuery = {
          cnvdId: '',
          cnvdTitle: '',
          cnvdLevel: '',
          releaseDate: '',
        }
        this.pagination.pageNum = 1
        setTimeout(() => {
          this.queryTableData()
          this.getTotal()
        }, 150)
      }, 500)
    },
    // 查询cnvd列表数据
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
      }
    ) {
      this.data.loading = true
      this.data.nomore = false
      this.btnRef = false
      queryCnvdTableData(params).then((res) => {
        this.data.table = res
        this.data.loading = false
        if (res.length >= 20) {
          this.data.nomore = false
        } else {
          this.data.nomore = true
        }
        this.$nextTick(() => {
          if (this.data.table.length > 0) this.$refs.auditTable.doLayout()
        })
        this.btnRef = true
      })
    },
    // 高级查询模糊查询
    pageQuery(flag) {
      if (flag) this.pagination.pageNum = 1
      this.data.debounce.query()
    },
    // 点击页面查询按钮
    clickQueryButton() {
      this.query.inputVal = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetSeniorQuery()
      this.initDebounce()
    },
    // 点击页面向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetSeniorQuery()
      this.initDebounce()
    },
    // 点击查询cnvd详细信息
    clickDetailButton(row) {
      this.dialog.detailDialog.visible = true
      queryCnvdDetail(row.cnvdId).then((res) => {
        this.dialog.detailDialog.form.model = res
      })
    },
    // 重置查询
    resetSeniorQuery() {
      this.data.debounce.resetQueryDebounce()
    },
    scrollTable(param = {}) {
      if (!this.data.nomore) {
        param = {
          pageSize: this.pagination.pageSize,
          cnvdTitle: this.query.seniorQuery.cnvdTitle,
          cnvdLevel: this.query.seniorQuery.cnvdLevel,
          fuzzyField: this.query.inputVal,
          releaseDate: this.query.seniorQuery.releaseDate.toString(),
          SerialNumber: this.data.table.length > 0 ? this.data.table[this.data.table.length - 1]['cnvdId'] : '',
        }
        this.data.loading = true
        queryCnvdTableData(param).then((res) => {
          if (res.length < 20) this.data.nomore = true
          this.data.table.push(...res)
          this.data.loading = false
        })
      }
    },
    // 查询审计事件总数
    getTotal(params = { pageSize: 20 }) {
      this.data.totalLoading = true
      queryTotal(params).then((res) => {
        this.data.total = res
        this.data.totalLoading = false
      })
    },
  },
}
</script>
