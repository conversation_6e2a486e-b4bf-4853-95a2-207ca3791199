import request from '@util/request'

// 查询代理列表
export function queryProxyTable(obj) {
  return request({
    url: '/agentmanagement/agents',
    method: 'get',
    params: obj || {},
  })
}

export function detectSystemHealthy() {
  return request({
    url: '/collector/management/oneKeyDetection/add',
    method: 'get',
  })
}

// 查询代理列表
export function queryHistoryHealthy(obj) {
  return request({
    url: '/collector/management/oneKeyDetection/history',
    method: 'get',
    params: obj || {},
  })
}

export function updateAgentStatus(id, state) {
  return request({
    url: `/agentmanagement/agents/${id}/${state}`,
    method: 'put',
  })
}

// 网口相关
export function queryNetWork(agentId) {
  return request(
    {
      url: `/agentmanagement/configuration/${agentId}`,
      method: 'get',
    },
    'default',
    '180000'
  )
}

export function updateNetworkData(obj) {
  return request({
    url: '/agentmanagement/configuration',
    method: 'put',
    data: obj || {},
  })
}

export function queryNetworkPanel(agentId) {
  return request({
    url: `/agentmanagement/panel/${agentId}`,
    method: 'get',
  })
}

export function testNetworkCard(id, agentId) {
  return request({
    url: `/agentmanagement/checkCard/${id}/${agentId}`,
    method: 'get',
  })
}

// 服务相关
export function querySSHStatus(agentId) {
  return request({
    url: `/agentmanagement/querySshdStatus/${agentId}`,
    method: 'get',
  })
}

export function startSSHStatus(agentId) {
  return request({
    url: `/agentmanagement/startSshd/${agentId}`,
    method: 'put',
  })
}

export function stopSSHStatus(agentId) {
  return request({
    url: `/agentmanagement/stopSshd/${agentId}`,
    method: 'put',
  })
}

export function queryRestartDeviceData(agentId) {
  return request({
    url: `/agentmanagement/device/restart/${agentId}`,
    method: 'get',
  })
}

export function queryShutdownDeviceData(agentId) {
  return request({
    url: `/agentmanagement/device/shutdown/${agentId}`,
    method: 'get',
  })
}

export function queryCenterIp(agentId) {
  return request({
    url: `/agentmanagement/getCenterIp/${agentId}`,
    method: 'get',
  })
}

export function updateCenterIp(agentId, centerIp) {
  return request({
    url: `/agentmanagement/setCenterIp/${agentId}/${centerIp}`,
    method: 'put',
  })
}

export function setAllCenterIp(centerIp) {
  return request({
    url: `/agentmanagement/setAllCenterIp/${centerIp}`,
    method: 'put',
  })
}

export function queryNetworkAvaliable(agentId) {
  return request({
    url: `/agentmanagement/agent/${agentId}`,
    method: 'get',
  })
}
