<!--
 * @Description: 异常行为告警 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-02
 * @Editor:
 * @EditDate: 2021-11-02
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-form :model="model" label-width="120px">
        <el-row>
          <el-col v-for="(item, index) in columnOption" :key="index" :span="item.key === 'desc' || item.key === 'raw' ? 24 : 12">
            <el-form-item :prop="item.key" :label="item.label">
              <span v-if="item.key === 'status' || item.key === 'anomalyType'">
                {{ columnText(model[item.key], item.key) }}
              </span>
              <span v-else>
                {{ model[item.key] }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      columnOption: [
        { key: 'infoSystemName', label: this.$t('alarm.abnormalBehavior.label.infoSystemName') },
        { key: 'infoSystemIp', label: this.$t('alarm.abnormalBehavior.label.infoSystemIp') },
        { key: 'action', label: this.$t('alarm.abnormalBehavior.label.action') },
        { key: 'status', label: this.$t('alarm.abnormalBehavior.label.status') },
        { key: 'anomalyType', label: this.$t('alarm.abnormalBehavior.label.anomalyType') },
        { key: 'role', label: this.$t('alarm.abnormalBehavior.label.role') },
        { key: 'occurTime', label: this.$t('alarm.abnormalBehavior.label.occurTime') },
        { key: 'updateTime', label: this.$t('alarm.abnormalBehavior.label.updateTime') },
        { key: 'total', label: this.$t('alarm.abnormalBehavior.label.total') },
        { key: 'desc', label: this.$t('alarm.abnormalBehavior.label.desc') },
        { key: 'raw', label: this.$t('alarm.abnormalBehavior.label.raw') },
      ],
    }
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
  },
}
</script>
