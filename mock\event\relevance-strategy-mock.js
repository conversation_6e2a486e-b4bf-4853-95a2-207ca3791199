const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    alarmName: '@NAME',
    alarmType: '@CNAME',
    description: '@word(4)',
    updateTime: '@DATETIME',
    'state|0-1': '1',
  },
})

const json = {
  filter: {
    logic: 'and',
    conditions: [{}],
  },
}

module.exports = [
  {
    url: '/relevancestrategy/relevances',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/relevancestrategy/get',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: json,
      }
    },
  },
]
