<!--
 * @Description: 预测分析 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-11
 * @Editor:
 * @EditDate: 2021-11-11
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
      >
        <el-table-column v-for="(item, index) in columns" :key="index" :prop="item.key" :label="item.label" show-overflow-tooltip>
          <template slot-scope="scope">
            <p v-if="item.key === 'type'">
              {{ columnText(scope.row[item.key]) }}
            </p>
            <p v-else>
              {{ scope.row[item.key] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="80">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    forecastTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      columns: [
        { key: 'type', label: this.$t('forecast.forecastAnalysis.label.type') },
        { key: 'lableName', label: this.$t('forecast.forecastAnalysis.label.infoItem') },
        { key: 'updateTime', label: this.$t('forecast.forecastAnalysis.label.enterTime') },
      ],
    }
  },
  computed: {
    columnText() {
      return (code) => {
        let text = ''
        this.forecastTypeOption.forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  methods: {
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
  },
}
</script>
