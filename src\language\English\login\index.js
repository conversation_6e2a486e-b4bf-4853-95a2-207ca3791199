export default {
  login: {
    systemName: 'Comprehensive Log Audit and Management System',
    copyright: 'Copyright',
    text: 'Login',
    account: {
      invalid: 'Incorrect username or password',
      overdue: 'Account is not within valid period',
      illegalTime: 'Account login at illegal time',
      illegalUser: 'Account does not exist',
      lock: 'Account is locked',
      toImproved: `First login, please <b class="{0}" title="Complete user information">complete</b> user information`,
      unauth: 'Account is not authorized for any menu',
      empty: 'Username or password cannot be empty',
    },
    password: {
      overdue: `Account <b class="{0}" title="Change password">password</b> has expired`,
      beReset: `Password has been <b class="{0}" title=\"Change password\">reset</b>`,
      invalid: 'Incorrect password',
    },
    captcha: {
      invalid: 'Incorrect verification code',
      empty: 'Verification code cannot be empty',
      switch: 'Cannot see clearly? Click to change verification code',
    },
    mail: {
      getCaptchaSuccess: 'Verification code has been sent to your email',
      getCaptchaFailed: 'Failed to send verification code, please try again later',
      getCaptcha: 'Get Verification Code',
      invalid: 'Incorrect email verification code',
      getAgainCaptcha: 'Get verification code again in {0} seconds',
      noExist: 'Email does not exist',
      empty: 'Email and verification code cannot be empty',
      servicedown: 'Email service is unavailable',
    },
    validate: {
      success: 'Verification code sent successfully, please check your email',
      invalid: 'Invalid email address',
      failed: 'Failed to send verification code, please try again later',
      emailName: 'Email address cannot be empty',
    },
    session: {
      invalid: 'Session has expired or not logged in',
    },
    upgrade: {
      doing: 'System upgrading, please try again later!',
    },
    license: {
      sequence: 'Serial Number',
      overdue: 'License is invalid, please contact the administrator',
      admin: 'License is invalid, please {0} again',
    },
    success: {
      welcome: `Welcome back, {0}`,
      morning: 'Good morning, today is full of hope!',
      noon: 'Good afternoon, time to show your real skills!',
      afternoon: 'Good afternoon, may your afternoon be pleasant and troubles go away!',
      evening: 'Good evening, turn every ordinary day into a wonderful time!',
      night: 'Good night, remember to rest!',
    },
    tab: {
      account: 'Account Login',
      mail: 'Email Login',
    },
    placeholder: {
      username: 'Please enter username',
      password: 'Please enter password',
      captcha: 'Please enter verification code',
      mail: 'Please enter email',
      mailCaptcha: 'Please enter verification code',
    },
    privacy: {
      agreePrefix: 'I have read and agree to the ',
      protocol: 'Privacy Policy',
      pleaseread: 'Please read and agree to the privacy policy before logging in!',
    },
    electricity: '[For Electricity Only]',
    tip: {
      loginExpired: 'Login expired, please log in again',
      loginSuccess: 'Login successful',
      loginFailed: 'Login failed, please try again',
    },
  },
}
