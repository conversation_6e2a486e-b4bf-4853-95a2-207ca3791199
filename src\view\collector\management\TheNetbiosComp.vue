<!--
 * @Description: 采集器管理 - Netbios连接
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-04-18
 * @Editor:
 * @EditDate: 2022-04-18
-->
<template>
  <el-form ref="netbiosForm" :model="form.model" :rules="rules" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="ip" :label="form.info.ip.label">
          <el-input v-model.trim="form.model.ip" maxlength="400"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item prop="strategy" :label="form.info.strategy.label">
          <el-select v-model="form.model.strategy" filterable clearable :placeholder="$t('collector.management.placeholder.strategy')">
            <el-option v-for="item in filterOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col v-if="sourcePage === 'add'" :span="12">
        <el-form-item prop="isAsset" :label="form.info.isAsset.label">
          <el-checkbox v-model="form.model.isAsset" :checked="form.model.isAsset === 1 ? true : false" :true-label="1" :false-label="0"></el-checkbox>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
export default {
  props: {
    form: {
      required: true,
      type: Object,
    },
    validate: {
      type: Boolean,
      default: true,
    },
    filterOption: {
      required: true,
      type: Array,
    },
    sourcePage: {
      type: String,
      default: 'add',
    },
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  methods: {
    validateForm() {
      let validate = false
      this.$refs.netbiosForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
  },
}
</script>
