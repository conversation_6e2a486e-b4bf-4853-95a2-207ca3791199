<!--
 * @Description: 监控器 - 修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-05
 * @Editor:
 * @EditDate: 2021-08-05
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.update', [titleName])"
    width="60%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <template v-if="dialogVisible">
      <basic-comp ref="basicRef" :model="model" func="upd" @on-change-type="changeMonitorType"></basic-comp>
      <overall-comp ref="allRef" :model="model" :monitor-type="model.monitorType"></overall-comp>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import BasicComp from './comp/config/BasicComp'
import OverallComp from './comp/config/OverallComp'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
    BasicComp,
    OverallComp,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    changeMonitorType(value) {
      this.model.monitorType = value
    },
    clickSubmit() {
      const valid = this.validateForm()
      if (valid) {
        this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          const model = this.handleModelItems()
          this.$emit('on-submit', model)
          this.clickCancel()
        })
      } else {
        prompt(
          {
            i18nCode: 'validate.form.warning',
            type: 'warning',
            print: true,
          },
          () => {
            return false
          }
        )
      }
      this.$refs.dialogTemplate.end()
    },
    validateForm() {
      const basicValid = this.$refs.basicRef.validateForm()
      const allValid = this.$refs.allRef.validateForm()
      return basicValid && allValid
    },
    handleModelItems() {
      const assetId = this.model.assetId
      if (assetId.length > 0) {
        this.model.assetIds = assetId[assetId.length - 1]
      }
      return this.model
    },
  },
}
</script>
