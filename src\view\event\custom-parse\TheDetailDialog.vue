<!--
 * @Description: 自定义解析 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-12-31
 * @Editor:
 * @EditDate: 2022-12-31
-->
<template>
  <el-dialog
    ref="dialogDom"
    v-el-dialog-drag
    :visible.sync="visible"
    :title="$t('dialog.title.rule', [titleName])"
    :close-on-click-modal="false"
    width="70%"
  >
    <el-form ref="formTemplate" :model="model" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="devType" :label="$t('event.customParse.label.devType')">
            <el-select v-model="model.devType" disabled clearable>
              <el-option v-for="item in options.devType" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="status" :label="$t('event.customParse.label.status')">
            <el-switch v-model="model.status" disabled active-value="1" inactive-value="0"></el-switch>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row>
        <el-form-item prop="message" :label="$t('event.customParse.label.message')">
          <el-input
            id="logMessage"
            v-model="model.message"
            type="textarea"
            :rows="4"
            maxlength="2048"
            readonly
            @mouseup.native="selectKeyword"
          ></el-input>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item prop="points" :label="$t('event.customParse.label.patternInfo')">
          <section class="parse-item">
            <div v-for="(item, index) in model.points" :key="index" class="parse-item-row">
              <el-form-item :key="item.id" class="parse-item-col" :label="$t('event.customParse.label.propDetail')" label-width="100px">
                <span :title="item.content">{{ item.content }}</span>
              </el-form-item>
              <el-form-item :prop="item.key" class="parse-item-col" :label="$t('event.customParse.label.multiGroup')" label-width="60px">
                <el-select v-model="item.key" filterable clearable disabled class="select-key">
                  <el-option
                    v-for="item in options.multiGroup"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.disabled"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item style="width: 25%;" label-width="0">
                <el-select v-if="item.key === 'code'" v-model="item.eventType" filterable clearable disabled>
                  <el-option
                    v-for="item in options.eventType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                    :disabled="item.eventTypeDisabled"
                  ></el-option>
                </el-select>
                <el-select v-if="item.key === 'level'" v-model="item.level" filterable clearable disabled>
                  <el-option v-for="item in options.level" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-form-item>
              <el-button class="del-button" disabled @click="clickDelete(item)">
                {{ $t('button.delete') }}
              </el-button>
            </div>
          </section>
        </el-form-item>
      </el-row>
      <el-row>
        <el-form-item prop="pattern" :label="$t('event.customParse.label.pattern')">
          <el-input v-model="model.pattern" type="textarea" :rows="3" readonly maxlength="1024"></el-input>
        </el-form-item>
      </el-row>
    </el-form>
    <footer slot="footer">
      <el-button @click="clickCancel">
        {{ $t('button.cancel') }}
      </el-button>
    </footer>
  </el-dialog>
</template>

<script>
import elDialogDrag from '@/directive/el-dialog-drag'
export default {
  directives: {
    elDialogDrag,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.dialogVisible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.parse-item {
  width: 90%;
  min-height: 80px;
  max-height: 140px;
  border: 1px solid;
  @include theme('border-color', el-input-border);
  overflow-y: auto;
  &-row {
    display: flex;
    justify-content: space-between;
    width: 90%;
    height: 40px;
    min-height: 40px;
    margin: 5px auto;
    border-bottom: 1px solid;
    @include theme('border-bottom-color', el-input-border);
    .parse-item-col {
      display: inline-block;
      width: 45%;
      span {
        display: inline-block;
        width: 140px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        @include theme('color', font-color);
      }
      .el-select {
        width: 70% !important;
      }
      ::v-deep.el-form-item__label {
        @include theme('color', font-color);
      }
    }
    .del-button {
      border: none;
      box-shadow: none;
      @include theme('color', error-text-color);
    }
    .el-button:focus,
    .del-button:hover {
      @include theme('color', error-text-color);
      font-weight: bold;
      @include theme('background-color', tag-bg-color);
    }
  }
}
</style>
