export default {
  security: {
    table: {
      type2Name: 'Event Name',
      alarmTypeName: 'Event Type',
      alarmCategoryName: 'Event Category',
      level: 'Event Level',
      count: 'Aggregation Count',
      srcIpv: 'Source IP',
      srcPort: 'Source Port',
      dstIpv: 'Destination IP',
      dstPort: 'Destination Port',
      aggrStartDate: 'Aggregation Start Time',
      aggrEndDate: 'Aggregation End Time',
      fromDeviceType: 'Source Device Type',
      deviceTypeName: 'Log Source Device',
      protocol: 'Protocol',
      advice: 'Handling Advice',
      raw: 'Log Original Text',
      date: 'Time',
      fromIpv: 'Source IP',
      alarmDesc: 'Event Description',
    },
    dialog: {
      detailTitle: 'Security Event Details',
      colTitle: 'Security Event Custom Columns',
    },
    detailColumns: {
      type2Name: 'Original Event Name',
      eventName: 'Event Type',
      eventCategoryName: 'Event Category',
      level: 'Event Level',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      dateTime: 'Time',
      raw: 'Log Original Text',
    },
    placeholder: {
      type2Name: 'Event Name',
      inputSearch: 'Event Name/Event Type/Source IP/Log Source Device',
      startIp: 'Source Start IP',
      endIp: 'Source End IP',
      srcStartIp: 'Source Start IP',
      srcEndIp: 'Source End IP',
      dstStartIp: 'Destination Start IP',
      dstEndIp: 'Destination End IP',
    },
    header: 'Security Event',
    checkBox: {
      type2Name: 'Event Name',
      alarmTypeName: 'Event Type',
      alarmCategoryName: 'Event Category',
      level: 'Event Level',
      count: 'Aggregation Count',
      srcIpv: 'Source IP',
      srcPort: 'Source Port',
      dstIpv: 'Destination IP',
      dstPort: 'Destination Port',
      aggrStartDate: 'Aggregation Start Time',
      aggrEndDate: 'Aggregation End Time',
      deviceTypeName: 'Log Source Device',
      fromIpv: 'Source IP',
      protocol: 'Protocol',
    },
    panel: {
      original: 'Original Log Query',
      detail: 'Detail Information',
    },
  },
}
