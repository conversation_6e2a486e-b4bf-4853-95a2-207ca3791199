<!--
 * @Description: 角色管理 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="dialogForm.model" :rules="dialogForm.rules" label-width="25%">
      <template v-if="readonly">
        <el-form-item :label="$t('management.role.infoItem.roleName')">
          {{ form.roleName }}
        </el-form-item>
        <el-form-item :label="$t('management.role.infoItem.roleStatus')">
          {{ [this.$t('management.role.roleStatus.show'), this.$t('management.role.roleStatus.hidden')][form.roleStatus] }}
        </el-form-item>
        <el-form-item :label="$t('management.role.infoItem.roleDescription')">
          {{ form.roleDescription }}
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item :label="$t('management.role.infoItem.roleName')" prop="roleName">
          <el-input v-model.trim="dialogForm.model.roleName" maxlength="64" class="width-mini"></el-input>
        </el-form-item>
        <el-form-item :label="$t('management.role.infoItem.roleStatus')" prop="roleStatus">
          <el-select v-model="dialogForm.model.roleStatus" class="width-mini" clearable>
            <el-option v-for="item in option" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item class="dialog-form-list" :label="$t('management.role.infoItem.grantResources')" props="treeProps">
          <el-tree
            ref="AllResourcesTree"
            style="height: 200px;overflow: scroll;"
            class="width-mini"
            :props="treeProps"
            :data="dialogForm.model.allResources"
            show-checkbox
            node-key="value"
            :default-checked-keys="dialogForm.model.actions"
          ></el-tree>
        </el-form-item>
        <el-form-item :label="$t('management.role.infoItem.roleDescription')" prop="roleDescription">
          <el-input v-model.trim="dialogForm.model.roleDescription" maxlength="128" type="textarea" :rows="4" class="width-mini"></el-input>
        </el-form-item>
      </template>
    </el-form>
    <template v-if="!actions" slot="action">
      <fragment></fragment>
    </template>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      required: true,
      type: Object,
      default() {
        return {}
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    // dialog底部功能是否存在
    actions: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      option: [
        {
          value: '0',
          label: '显示',
        },
        {
          value: '1',
          label: '隐藏',
        },
      ],
      dialogForm: {
        model: {
          roleId: '',
          roleName: '',
          roleStatus: '',
          roleDescription: '',
          checked: [],
          actions: [],
          resources: [],
          allResources: [],
        },
        rules: {
          roleName: [
            {
              required: true,
              trigger: 'blur',
              message: '此项不能为空',
            },
          ],
          roleStatus: [
            {
              required: true,
              trigger: 'change',
              message: '此项不能为空',
            },
          ],
        },
      },
      treeProps: {
        children: 'children',
        label: 'label',
      },
    }
  },
  watch: {
    form(form) {
      this.dialogForm.model = {
        roleId: form.roleId,
        roleName: form.roleName,
        roleStatus: form.roleStatus,
        roleDescription: form.roleDescription,
        resources: form.resources,
        actions: form.actions,
      }
    },
    'form.allResources': {
      deep: true,
      handler: function(newV) {
        this.dialogForm.model.allResources = newV
      },
    },
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.dialogForm.model = {
        roleId: '',
        roleName: '',
        roleStatus: '',
        roleDescription: '',
        resources: [],
        actions: [],
      }
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      const checked = this.$refs.AllResourcesTree.getCheckedNodes(false, true)
      const arr = []
      const Arr = []
      if (checked !== []) {
        checked.map((i) => {
          if (i.children) {
            arr.push(i.value)
          } else {
            Arr.push(i.value)
          }
        })
        this.dialogForm.model.resources = arr
        this.dialogForm.model.actions = Arr
      }
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const params = Object.assign({}, this.dialogForm.model)
            this.$emit('on-submit', params)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })

      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
