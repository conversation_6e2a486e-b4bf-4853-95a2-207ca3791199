<!--
 * @Description: 审计告警数量趋势
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023/11/13
 * @Editor:
 * @EditDate: 2023/11/13
-->
<template>
  <el-container class="widget" :style="{ height: height + 'px' }">
    <el-header class="widget-header" height="30px">
      <el-col :span="10" class="widget-header-title">
        {{ $t('visualization.compliance.title.auditAlarmTread') }}
      </el-col>
      <el-col :span="8" align="right">
        <el-select v-model="cycleValue" size="mini" @change="changeCycle">
          <el-option v-for="item in options.cycle" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-col>
    </el-header>
    <el-container class="widget-main">
      <el-main>
        <line-chart ref="" :option="option"></line-chart>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import LineChart from '@comp/ChartFactory/forecast/LineChart'
import { queryAuditAlarmTrend } from '@api/visualization/compliance-api'

export default {
  components: {
    LineChart,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      trendData: [],
      cycleValue: 3,
      options: {
        cycle: [
          { value: 1, label: this.$t('visualization.compliance.cycle.lastDay') },
          { value: 2, label: this.$t('visualization.compliance.cycle.lastWeek') },
          { value: 3, label: this.$t('visualization.compliance.cycle.lastMonth') },
          { value: 4, label: this.$t('visualization.compliance.cycle.lastHalfYear') },
          { value: 5, label: this.$t('visualization.compliance.cycle.lastYear') },
        ],
      },
    }
  },
  computed: {
    option() {
      return {
        type: 'line-stack',
        axis: 'x',
        data: this.trendData,
      }
    },
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getAuditAlarmTrend()
    },
    changeCycle(value) {
      this.cycleValue = value
      this.getAuditAlarmTrend()
    },
    getAuditAlarmTrend(
      params = {
        type: this.cycleValue,
      }
    ) {
      queryAuditAlarmTrend(params).then((res) => {
        this.trendData = res
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.widget {
  &-header {
    &-title {
      padding-left: 0 !important;
    }
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    ::v-deep .el-input--small .el-input__inner {
      height: 26px;
      line-height: 26px;
      .el-input__prefix,
      ::v-deep .el-input__suffix {
        top: 2px;
      }
    }
  }
}
</style>
