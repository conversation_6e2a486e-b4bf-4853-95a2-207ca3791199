import { staticRouters, dynamicRouters } from '@/router'

const state = {
  routers: [],
  addRouters: [],
}

const mutations = {
  SET_ROUTES(state, routes) {
    state.addRouters = routes
    state.routers = staticRouters.concat(routes)
  },
}

const actions = {
  generateRoutes({ commit }) {
    return new Promise((resolve) => {
      const accessedRoutes = dynamicRouters || []
      // 此处服务端支持可进行扩展
      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
