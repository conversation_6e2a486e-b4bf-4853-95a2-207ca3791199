const Mock = require('mockjs')

/**
 * @func 获取浏览器url最后一个"/"后面的值
 * @param {string} url - 传入的URL的值
 * @return {string}
 * <AUTHOR> @date 2020/4/28
 */
function getURLLastPath(url) {
  const site = url.lastIndexOf('/')
  return url.substring(site + 1, url.length)
}

/**
 * @func 将get请求的url字符串转化为对象
 * @param {string} url -  window.location.search内容
 * @return {object}
 * <AUTHOR> @date 2019/09/15
 */
function paramToObj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searches = search.split('&')
  searches.forEach((value) => {
    const index = value.indexOf('=')
    if (index !== -1) {
      const name = value.substring(0, index)
      obj[name] = value.substring(index + 1, value.length)
    }
  })
  return obj
}

/**
 * @func 合并并返回require.context函数带入的文件内容
 * @param {function} context - require.context函数的返回值
 * @return {object} 返回匹配文件夹内容的文件，key为文件名，value为文件内容
 * @example getFolderContext(require.context("./src", true, /\.js$/)) 该返回值为src下所有有关js的文件
 * <AUTHOR> @date 2020/7/17
 */
function getFolderContext(context) {
  return context.keys().reduce((module, modulePath) => {
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
    const value = context(modulePath)
    if (value) {
      module[moduleName] = value
    }
    return module
  }, {})
}

/**
 * @func 过滤数组对象
 * @param {object[]} source - 需要过滤的数组对象全集
 * @param {object} keyObj - 过滤条件
 * @return {object[]}
 * <AUTHOR> @date 2019/09/15
 */
function filterObj(source, keyObj) {
  const keys = Object.keys(keyObj)
  return source.filter((item) => {
    return keys.every((key) => item[key] === keyObj[key])
  })
}

/**
 * @func Mock数据生成
 * @param {object} template - 列表列展示内容
 * @param {number} total - 总条数
 * <AUTHOR> @date 2020/4/14
 */
function createMockTable(template, total) {
  const rows = []
  for (let i = 0; i < total; i++) {
    rows.push(Mock.mock(template))
  }
  return rows
}

/**
 * @func Mock数据的生成及增删改查
 * @param {object} option - 配置项
 * @note  - option内部key 解释
 *          {number} pageSize - 每个页面展示的条数
 *          {number} pageNum -  页码
 *          {number} total - 总条数
 *          {number} pages - 总页数
 *          {object} template - 列表列展示内容
 *          add - 添加条数
 *          delete - 删除行
 *          update - 更新行
 *          query - 查询列表数据
 *          getMockData - 获取当前数据
 * @example const tableData = new TableMock({...})
 * <AUTHOR> @date 2020/4/15
 */
class TableMock {
  constructor(option) {
    this.pageSize = typeof option.pageSize === 'number' ? option.pageSize : 20
    this.pageNum = typeof option.pageNum === 'number' ? option.pageNum : 1
    this.total = typeof option.total === 'number' ? option.total : 0
    this.pages = typeof option.pages === 'number' ? option.pages : 1
    this.mockTemp = option.template.constructor === Object ? option.template : {}
    this.rows = []
    this.rowsTemp = []
    this.totalTemp = typeof option.total === 'number' ? option.total : 0
    this.createMockRowData()
  }

  createMockRowData() {
    this.rowsTemp = createMockTable(this.mockTemp, this.total)
  }

  getMockData() {
    return {
      pageSize: this.pageSize,
      pageNum: this.pageNum,
      total: this.totalTemp,
      pages: this.pages,
      rows: [...this.rows],
    }
  }

  add(option) {
    const param = option.body
    if (!param.hasOwnProperty('id')) {
      param['id'] = String(Date.parse(new Date()))
    }
    this.totalTemp += 1
    this.rowsTemp.push(param)
  }

  delete(option, key = 'id') {
    const keys = option.body ? option.body.split(',') : getURLLastPath(option.url).split(',')
    this.rowsTemp = this.rowsTemp.filter((item) => !keys.includes(item[key]))
    this.totalTemp = this.totalTemp - keys.length
  }

  update(option, key = 'id') {
    const param = option.body
    this.rowsTemp.some((item) => {
      if (item[key] === param[key]) {
        Object.getOwnPropertyNames(param).forEach((paramKey) => {
          item[paramKey] = param[paramKey]
        })
      }
    })
  }

  query(option) {
    const paramsObj = option.query
    const paramsKeys = Object.keys(paramsObj)
    let startPage = 0
    if (paramsKeys.length === 0) {
      startPage = (this.pageNum - 1) * this.pageSize
      this.rows = this.rowsTemp.slice(startPage, startPage + this.pageSize)
    } else if (
      paramsKeys.length === 3 &&
      paramsKeys.indexOf('pageSize') > -1 &&
      paramsKeys.indexOf('pageNum') > -1 &&
      paramsKeys.indexOf('fuzzyField') > -1
    ) {
      this.pageSize = Number.parseInt(paramsObj.pageSize)
      this.pageNum = Number.parseInt(paramsObj.pageNum)
      const fuzzyField = paramsObj.fuzzyField.toLowerCase()
      startPage = (this.pageNum - 1) * this.pageSize
      this.rows = this.rowsTemp
        .filter((item) => {
          return Object.keys(item).some((key) => {
            return (
              String(item[key])
                .toLowerCase()
                .indexOf(fuzzyField) > -1
            )
          })
        })
        .slice(startPage, startPage + this.pageSize)
      this.totalTemp = this.rowsTemp.length
    } else {
      Object.getOwnPropertyNames(paramsObj).forEach((key) => {
        if (paramsObj[key] === '' || key === 'pageSize' || key === 'pageNum') {
          delete paramsObj[key]
        }
      })
      const filterArray = filterObj(this.rowsTemp, paramsObj)
      this.pageNum = 1
      this.totalTemp = filterArray.length
      startPage = (this.pageNum - 1) * this.pageSize
      this.rows = filterArray.slice(startPage, startPage + this.pageSize)
    }
  }

  detail(option, key = 'id') {
    const keys = option.body ? option.body.split(',') : getURLLastPath(option.url)
    let detail = {}
    this.rows.forEach((item) => {
      if (item[key] === keys) {
        detail = item
      }
    })
    return detail
  }

  updateColumn(option, key = 'id', columnVal = 1, columnName = 'status') {
    const keys = option.body ? option.body.split(',') : getURLLastPath(option.url)
    this.rows.forEach((item) => {
      keys.split(',').forEach((keyVal) => {
        if (item[key] === keyVal) {
          item[columnName] = columnVal
        }
      })
    })
  }
}
class ScrollTableMock {
  constructor(option) {
    this.pageSize = typeof option.pageSize === 'number' ? option.total : 20
    this.pageNum = typeof option.pageNum === 'number' ? option.pageNum : 1
    this.total = typeof option.total === 'number' ? option.total : 0
    this.pages = 1
    this.mockTemp = option.template.constructor === Object ? option.template : {}
    this.rows = []
    this.rowsTemp = []
    this.totalTemp = typeof option.total === 'number' ? option.total : 0
    this.createMockRowData()
  }
  createMockRowData() {
    this.rowsTemp = createMockTable(this.mockTemp, this.total)
  }
  getMockData() {
    return {
      total: this.totalTemp,
      rows: [...this.rows],
    }
  }
  query(option) {
    const paramsObj = option.query
    const paramsKeys = Object.keys(paramsObj)
    delete paramsObj['pageSize']
    if (paramsKeys.indexOf('fuzzyField') > -1 && paramsObj.fuzzyField !== '') {
      const fuzzyField = paramsObj.fuzzyField.toLowerCase()
      this.rows = this.rowsTemp.filter((item) => {
        return Object.keys(item).some((key) => {
          return (
            String(item[key])
              .toLowerCase()
              .indexOf(fuzzyField) > -1
          )
        })
      })
      this.totalTemp = this.rowsTemp.length
    } else {
      Object.getOwnPropertyNames(paramsObj).forEach((key) => {
        if (paramsObj[key] === '') {
          delete paramsObj[key]
        }
      })
      const filterArray = filterObj(this.rowsTemp, paramsObj)
      this.totalTemp = filterArray.length
      this.rows = filterArray
    }
  }
  detail(option, key = 'id') {
    const keys = option.body ? option.body.split(',') : getURLLastPath(option.url)
    let detail = {}
    this.rows.forEach((item) => {
      if (item[key] === keys) {
        detail = item
      }
    })
    return detail
  }
}

module.exports = {
  getURLLastPath,
  paramToObj,
  createMockTable,
  getFolderContext,
  TableMock,
  ScrollTableMock,
}
