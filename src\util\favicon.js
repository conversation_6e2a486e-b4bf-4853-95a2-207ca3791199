/**
 * 将base64编码的logo转换为favicon并设置
 * @param {string} logoBase64 - Logo的base64编码字符串
 */
export function updateFaviconFromLogo(logoBase64) {
  if (!logoBase64) return;

  try {
    // 创建一个图片元素
    const img = new Image();

    img.onload = function() {
      // 创建canvas元素调整大小
      const canvas = document.createElement('canvas');
      canvas.width = 32; // favicon标准尺寸
      canvas.height = 32;
      const ctx = canvas.getContext('2d');

      // 将图片绘制到canvas
      ctx.drawImage(img, 0, 0, 32, 32);

      // 将canvas转换为favicon URL
      const faviconUrl = canvas.toDataURL('image/png');

      // 更新页面上的favicon
      let link = document.querySelector("link[rel*='icon']");

      // 如果没有找到favicon链接，则创建一个
      if (!link) {
        link = document.createElement('link');
        link.rel = 'shortcut icon';
        document.head.appendChild(link);
      }

      // 设置favicon
      link.type = 'image/png';
      link.href = faviconUrl;
    };

    img.onerror = function() {
      console.error('无法加载Logo图片');
    };

    // 设置图片源为base64字符串
    img.src = logoBase64;
  } catch (error) {
    console.error('更新favicon失败:', error);
  }
}

/**
 * 初始化favicon，从localStorage读取保存的logo
 */
export function initFavicon() {
  const logoBase64 = localStorage.getItem('systemLogo');
  if (logoBase64) {
    updateFaviconFromLogo(logoBase64);
  }
}
