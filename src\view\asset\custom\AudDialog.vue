<!--
 * @Description: 自定义资产属性 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :loading="loading"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="40%">
      <template>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="form.info.name.label" :prop="form.info.name.key">
              <el-input
                v-model.trim="form.model.name"
                :placeholder="$t('asset.custom.placeholder.attributeName')"
                maxlength="10"
                show-word-limit
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="form.info.required.label" :prop="form.info.required.key">
              <el-select v-model="form.model.required" clearable :placeholder="$t('asset.custom.placeholder.reqType')">
                <el-option v-for="item in form.reqList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.showComponent">
          <el-col :span="12">
            <el-form-item :label="form.info.componentType.label" :prop="form.info.componentType.key">
              <el-select
                v-model="form.model.componentType"
                clearable
                :placeholder="$t('asset.custom.placeholder.controlType')"
                @change="isChecked(form.model.componentType)"
              >
                <el-option v-for="item in form.controlList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="form.info.assetClass.label" :prop="form.info.assetClass.key">
              <el-cascader
                v-model="form.model.assetClass"
                :placeholder="$t('asset.custom.placeholder.assetClass')"
                :options="form.typeList"
                :props="{ expandTrigger: 'hover' }"
              ></el-cascader>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item :label="form.info.length.label" :prop="form.info.length.key">
              <el-input-number
                v-model="form.model.length"
                :min="1"
                :max="lengthMax"
                :disabled="form.model.componentType != 1 && form.model.componentType != 4"
                show-word-limit
                :placeholder="$t('asset.custom.placeholder.attributeLength')"
              ></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="form.info.multiple.label" :prop="form.info.multiple.key">
              <el-select v-model="form.model.multiple" clearable :placeholder="$t('asset.custom.placeholder.checkType')" :disabled="noCheck">
                <el-option v-for="item in form.checkList" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="form.info.remark.label" :prop="form.info.remark.key" label-width="20%">
              <el-input
                v-model.trim="form.model.remark"
                :placeholder="$t('asset.custom.placeholder.remark')"
                type="textarea"
                :rows="5"
                class="width-max"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  name: 'AudDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '800',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    const validatorRadio = (rule, value, callback) => {
      if (value || !this.form.showComponent) {
        callback()
      } else {
        callback(new Error(this.$t('validate.choose')))
      }
    }
    return {
      dialogVisible: this.visible,
      rules: {
        // 验证规则
        name: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        length: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        componentType: [
          {
            required: true,
            validator: validatorRadio,
            trigger: 'change',
          },
        ],
        assetClass: [
          {
            required: true,
            validator: validatorRadio,
            trigger: 'change',
          },
        ],
        required: [
          {
            required: true,
            validator: validatorRadio,
            trigger: 'change',
          },
        ],
        multiple: [
          {
            required: true,
            validator: validatorRadio,
            trigger: 'change',
          },
        ],
      },
      noCheck: this.form.model.componentType !== 2,
    }
  },
  computed: {
    lengthMax() {
      return this.form.model.componentType === 4 ? 128 : 10
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const params = Object.assign({}, this.form.model)
            // 给父级调用数据
            this.$emit('on-submit', params, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    // 控件类型change
    isChecked(e) {
      if (e === 2) {
        this.noCheck = false
        this.form.model.multiple = 2
      } else if (e === 6) {
        this.noCheck = true
        this.form.model.multiple = 1
      } else {
        this.noCheck = true
        this.form.model.multiple = 2
      }
      if (e === 4) {
        this.lengthMax = 128
        this.form.model.length = 128
      } else {
        this.lengthMax = 10
        this.form.model.length = 10
      }
    },
  },
}
</script>
