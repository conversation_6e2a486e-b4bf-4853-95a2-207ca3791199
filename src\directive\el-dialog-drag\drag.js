export default {
  bind(el, binding, vnode) {
    const [dialogHeaderEl, dragDom] = [el.querySelector('.el-dialog__header'), el.querySelector('.el-dialog')]
    dialogHeaderEl.style.cssText += ';cursor:move;'
    dragDom.style.cssText += ';top:0px;'

    // 获取原有属性 ie dom元素.currentStyle 火狐谷歌 window.getComputedStyle(dom元素, null);
    const getStyle = (() => {
      return window.document.currentStyle ? (dom, attr) => dom.currentStyle[attr] : (dom, attr) => getComputedStyle(dom, false)[attr]
    })()

    dialogHeaderEl.onmousedown = (e) => {
      // 鼠标按下，计算当前元素距离可视区的距离
      const [disX, disY, dragDomWidth, dragDomHeight, screenWidth, screenHeight] = [
          e.clientX - dialogHeaderEl.offsetLeft,
          e.clientY - dialogHeaderEl.offsetTop,
          dragDom.offsetWidth,
          dragDom.offsetHeight,
          document.body.clientWidth,
          document.body.clientHeight,
        ],
        [minDragDomLeft, maxDragDomLeft, minDragDomTop, maxDragDomTop] = [
          dragDom.offsetLeft,
          screenWidth - dragDom.offsetLeft - dragDomWidth,
          dragDom.offsetTop,
          screenHeight - dragDom.offsetTop - dragDomHeight,
        ]

      // 获取到的值带px 正则匹配替换
      let [getLeft, getTop] = [getStyle(dragDom, 'left'), getStyle(dragDom, 'top')]

      if (getLeft.includes('%')) {
        getLeft = +document.body.clientWidth * (+getLeft.replace(/%/g, '') / 100)
        getTop = +document.body.clientHeight * (+getTop.replace(/%/g, '') / 100)
      } else {
        getLeft = +getLeft.replace(/px/g, '')
        getTop = +getTop.replace(/px/g, '')
      }

      document.onmousemove = (e) => {
        // 通过事件委托，计算移动的距离
        let [left, top] = [e.clientX - disX, e.clientY - disY]

        // 边界处理
        if (-left > minDragDomLeft) {
          left = -minDragDomLeft
        } else if (left > maxDragDomLeft) {
          left = maxDragDomLeft
        }

        if (-top > minDragDomTop) {
          top = -minDragDomTop
        } else if (top > maxDragDomTop) {
          top = maxDragDomTop
        }

        // 移动当前元素
        dragDom.style.cssText += `;left:${left + getLeft}px;top:${top + getTop}px;`

        // emit onDrag 事件
        vnode.child.$emit('dragDialog')
      }

      document.onmouseup = () => {
        document.onmousemove = document.onmouseup = null
      }
    }
  },
}
