<template>
  <div ref="logDurationDom" class="widget">
    <header class="widget-header">
      <h2 class="widget-header-title">
        {{ $t('visualization.compliance.title.logStorageDuration') }}
      </h2>
    </header>
    <main class="widget-body">
      <div class="icon-box">
        <i class="el-icon-date"></i>
      </div>
      <div class="detail-box">
        <p class="detail-box-word">
          <span>{{ highBit }}</span>
          {{ lowBit }}
          <br />
          <b>共存储日志{{ storageDuration }}天</b>
        </p>
      </div>
    </main>
  </div>
</template>

<script>
import { isEmpty } from '../../../../util/common'
import { queryLogStorageDuration } from '@api/asset/management-api'

export default {
  props: {
    data: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      storageDuration: '',
      highBit: '',
      lowBit: '',
    }
  },
  methods: {
    loadData() {
      this.getLogStorageDuration()
    },
    conversion(day) {
      if (day < 30) {
        this.highBit = day + '天'
      } else if (day < 365) {
        const month = Math.floor(day / 30)
        const days = Math.round((day / 30 - month) * 30)
        if (days === 0) {
          this.highBit = month + '个月'
        } else {
          this.highBit = month + '个月'
          this.lowBit = days + '天'
        }
      } else {
        const year = Math.floor(day / 365)
        const month = Math.floor(((day / 365 - year) * 365) / 30)
        const days = Math.round((((day / 365 - year) * 365) / 30 - month) * 30)
        if (month === 0 && days === 0) {
          this.highBit = year + '年'
        } else if (days === 0) {
          this.highBit = year + '年'
          this.lowBit = '零' + month + '个月'
        } else {
          this.highBit = year + '年'
          this.lowBit = '零' + month + '个月' + days + '天'
        }
      }
    },
    getLogStorageDuration() {
      this.resetFields()
      const params = {
        devId: this.data.devId,
        categoryID: this.data.categoryID,
        typeId: this.data.typeId,
        ip: this.data.ip,
      }
      queryLogStorageDuration(params).then((res) => {
        if (!isEmpty(res)) {
          this.storageDuration = res
          this.conversion(res)
        }
      })
    },
    resetFields() {
      this.storageDuration = ''
      this.highBit = ''
      this.lowBit = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.widget {
  &-body {
    .icon-box {
      background-color: #ffb136;
    }
    .detail-box {
      &-word {
        padding-bottom: 15px !important;
        line-height: 25px !important;
        color: #ffb136;
        b {
          @include theme('color', font-color);
        }
      }
    }
  }
}
::v-deep .el-progress-bar {
  padding-right: 5px;
}
</style>
