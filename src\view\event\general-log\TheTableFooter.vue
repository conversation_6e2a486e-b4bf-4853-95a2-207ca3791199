<!--
 * @Description: 威胁情报库 - 底部翻页
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <section class="table-footer infinite-scroll">
    <section class="infinite-scroll-nomore">
      <span v-show="nomore">{{ $t('validate.data.nomore') }}</span>
    </section>
    <section class="infinite-scroll-total">
      <!--{{ $t('event.generalLog.parseRateDesc', [parseRate]) }}，-->
      <b>{{ $t('tip.total') + ':' }}</b>
      <i v-if="totalLoading" class="el-icon-loading"></i>
      <span v-else>
        {{ total }}
      </span>
    </section>
  </section>
</template>

<script>
import { queryRarseRate } from '@api/event/general-log-api'

export default {
  props: {
    total: {
      required: true,
      type: Number,
    },
    totalLoading: {
      required: true,
      type: Boolean,
    },
    nomore: {
      required: true,
      type: Boolean,
    },
  },
  data() {
    return {
      parseRate: 0,
      refresh: {
        timer: null,
        duration: 5000,
      },
    }
  },
  // mounted() {
  //     this.getRarseRate();
  //     this.refreshParseRate();
  // },
  // beforeDestroy() {
  //     this.clearAllInterval();
  // },
  methods: {
    refreshParseRate() {
      this.refresh.timer = setInterval(() => {
        this.getRarseRate()
      }, this.refresh.duration)
    },
    clearAllInterval() {
      clearInterval(this.refresh.timer)
      this.refresh.timer = null
    },
    getRarseRate() {
      queryRarseRate().then((res) => {
        if (!isNaN(res)) {
          this.parseRate = res
        }
      })
    },
  },
}
</script>
