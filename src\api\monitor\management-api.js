import request from '@util/request'

// 查询监控器类型
export function queryMonitorType() {
  return request({
    url: '/monitormanagement/combo/types',
    method: 'get',
  })
}

// 查询监控器列表
export function queryMonitor(obj) {
  return request({
    url: '/monitormanagement/queryMonitors',
    method: 'get',
    params: obj || {},
  })
}

// (批量)删除监控器
export function deleteMonitor(ids) {
  return request({
    url: `/monitormanagement/deleteMonitors/${ids}`,
    method: 'delete',
  })
}

// (批量)停用监控器
export function stopMonitor(ids) {
  return request({
    url: `/monitormanagement/stopMonitors/${ids}`,
    method: 'put',
  })
}

// (批量)启用监控器
export function startMonitor(ids) {
  return request({
    url: `/monitormanagement/startMonitors/${ids}`,
    method: 'put',
  })
}

// 查询代理下拉数据
export function queryAgentOptionData() {
  return request({
    url: '/monitormanagement/combo/agents',
    method: 'get',
  })
}

// 查询监控器资产下拉数据
export function queryAsset(monitorType, monitorId) {
  return request({
    url: `/monitormanagement/monitorAssets/${monitorType}/${monitorId}`,
    method: 'get',
  })
}

// 查询监控器组件信息
export function queryMonitorComp(monitorType) {
  return request({
    url: `/monitormanagement/compInfo/${monitorType}`,
    method: 'get',
  })
}

// 添加监控器
export function addMonitor(obj) {
  return request({
    url: '/monitormanagement/addMonitor',
    method: 'post',
    data: obj || {},
  })
}

// 修改监控器
export function updateMonitor(obj) {
  return request({
    url: '/monitormanagement/updateMonitor',
    method: 'put',
    data: obj || {},
  })
}
