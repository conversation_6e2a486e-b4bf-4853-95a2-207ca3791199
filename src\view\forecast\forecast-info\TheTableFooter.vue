<!--
 * @Description: 预测信息 - 底部翻页
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-26
 * @Editor:
 * @EditDate: 2021-10-26
-->
<template>
  <section class="table-footer">
    <el-pagination
      v-if="filterCondition.visible"
      small
      background
      align="right"
      :current-page="filterCondition.pageNum"
      :page-sizes="[10, 20, 50, 100]"
      :page-size="filterCondition.pageSize"
      layout="total, sizes, prev, pager, next, jumper"
      :total="filterCondition.total"
      @size-change="clickSize"
      @current-change="clickCurrent"
    ></el-pagination>
  </section>
</template>

<script>
export default {
  props: {
    pagination: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.pagination,
    }
  },
  watch: {
    pagination(newCondition) {
      this.filterCondition = newCondition
    },
    filterCondition(newCondition) {
      this.$emit('update:pagination', newCondition)
    },
  },
  methods: {
    clickSize(size) {
      this.$emit('on-change-size', size)
    },
    clickCurrent(pageNum) {
      this.$emit('on-current', pageNum)
    },
  },
}
</script>

<style scoped></style>
