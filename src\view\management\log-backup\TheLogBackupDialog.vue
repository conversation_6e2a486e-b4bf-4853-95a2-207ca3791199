<!--
 * @Description: 日志备份 - 备份设置弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="jobForm" class="dialog-form" label-width="25%" :rules="form.rules" :model="form.model">
      <el-form-item :label="$t('management.logBackup.dialog.task.type')" prop="jobType">
        <el-select v-model="form.model.jobType" clearable class="width-mini" @change="changeJobType">
          <el-option v-for="item in options.jobType" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.model.jobType === '0'" :label="$t('management.logBackup.dialog.task.cycle')" prop="week">
        <el-select v-model="form.model.week" clearable class="width-mini">
          <el-option v-for="item in options.week" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        v-if="form.model.jobType === '2' || form.model.jobType === '3' || form.model.jobType === '4'"
        :label="$t('management.logBackup.dialog.task.cycle')"
        prop="month"
      >
        <el-select v-model="form.model.month" clearable class="width-mini">
          <el-option v-for="item in options.month" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="form.model.jobType && form.model.jobType !== '0' && form.model.jobType !== ''" :label="dialog.label" prop="day">
        <el-select v-model="form.model.day" clearable class="width-mini">
          <el-option v-for="item in options.day" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item :label="$t('management.logBackup.dialog.task.exeTime')" prop="date">
        <el-time-picker v-model="form.model.date" clearable class="width-mini" value-format="HH:mm:ss"></el-time-picker>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      dialog: {
        label: this.$t('management.logBackup.dialog.task.cycle'),
      },
      options: {
        jobType: [
          { value: '0', label: this.$t('time.cycle.week') },
          { value: '1', label: this.$t('time.cycle.month') },
          { value: '2', label: this.$t('time.cycle.quarter') },
          { value: '3', label: this.$t('time.cycle.halfYear') },
          { value: '4', label: this.$t('time.cycle.year') },
        ],
        week: [
          { value: '2', label: this.$t('time.week.mon') },
          { value: '3', label: this.$t('time.week.tue') },
          { value: '4', label: this.$t('time.week.wed') },
          { value: '5', label: this.$t('time.week.thu') },
          { value: '6', label: this.$t('time.week.fri') },
          { value: '7', label: this.$t('time.week.sat') },
          { value: '1', label: this.$t('time.week.sun') },
        ],
        sessionMonth: [],
        halfYearMonth: [],
        yearMonth: [],
        month: [],
        day: [],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      this.setTaskCycle(this.form.model.jobType)
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.initOptionData()
  },
  methods: {
    initOptionData() {
      for (let i = 1; i < 29; i++) {
        this.options.day.push({ value: `${i}`, label: `${i}${this.$t('time.unit.day')}` })
      }
      for (let i = 1; i < 13; i++) {
        this.options.yearMonth.push({ value: `${i}`, label: `${i}${this.$t('time.unit.month')}` })
      }
      for (let i = 1; i < 7; i++) {
        this.options.halfYearMonth.push({
          value: `${i}`,
          label: `${this.$t('time.many.month', [i])}`,
        })
      }
      for (let i = 1; i < 4; i++) {
        this.options.sessionMonth.push({
          value: `${i}`,
          label: `${this.$t('time.many.month', [i])}`,
        })
      }
    },
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.jobForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-submit', this.form.model)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    changeJobType(value) {
      this.resetForm(value)
      this.setTaskCycle(value)
    },
    setTaskCycle(value = this.form.model.jobType) {
      if (value === '0') {
        this.setRequired('week')
      } else if (value === '1') {
        this.setRequired('day')
      } else if (value === '2') {
        this.setRequired('month', 'day')
        this.options.month = this.options.sessionMonth
      } else if (value === '3') {
        this.setRequired('month', 'day')
        this.options.month = this.options.halfYearMonth
      } else if (value === '4') {
        this.setRequired('month', 'day')
        this.options.month = this.options.yearMonth
      }
      this.dialog.label = value === '1' ? this.$t('management.logBackup.dialog.task.cycle') : ''
    },
    setRequired(...names) {
      this.form.rules.week[0].required = false
      this.form.rules.month[0].required = false
      this.form.rules.day[0].required = false
      names.forEach((v) => {
        this.form.rules[v][0].required = true
      })
    },
    resetForm(value) {
      this.form.model = {
        jobType: value + '',
        month: '',
        week: '',
        day: '',
        date: '',
      }
      this.$refs['jobForm'].resetFields()
    },
  },
}
</script>
