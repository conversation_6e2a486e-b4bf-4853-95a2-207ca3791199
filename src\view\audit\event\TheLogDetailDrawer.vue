<!--
 * @Description: 审计事件 - 详情弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <detail-drawer :visible="dialogVisible" :detail-data="detailData" :modal="false" :size="'50%'" @on-close="clickCancelDrawer"></detail-drawer>
</template>

<script>
import DetailDrawer from '@comp/DetailDrawer'

export default {
  components: {
    DetailDrawer,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    detailData: {
      type: Object,
      default() {
        return {}
      },
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {},
  methods: {
    clickCancelDrawer() {
      this.dialogVisible = false
    },
  },
}
</script>
