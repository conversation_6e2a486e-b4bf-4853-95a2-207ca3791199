<!--
 * @Description: 查看日志
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023-03-09
 * @Editor:
 * @EditDate: 2023-03-09
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" height="90%" :action="false" @on-close="clickCancel">
    <section class="compliance-container">
      <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
        <el-tab-pane v-for="(item, index) in logSources" :key="item.devId" :label="item.collectorName" :name="`active` + index">
          <template v-if="activeName === `active` + index">
            <el-row :gutter="20" class="tr-wrapper">
              <el-col :span="8">
                <log-source-info ref="logSourceInfoDom" :data="item"></log-source-info>
              </el-col>
              <el-col :span="8">
                <log-total-number ref="logNumberDom" :data="item"></log-total-number>
              </el-col>
              <el-col :span="8">
                <log-storage-duration ref="logDurationDom" :data="item"></log-storage-duration>
              </el-col>
            </el-row>
            <el-row class="tr-wrapper">
              <el-col :span="24">
                <log-number-trend-chart ref="logNumberTrendChartDom" :data="item"></log-number-trend-chart>
              </el-col>
            </el-row>
            <el-row class="tr-wrapper">
              <el-tabs v-model="activeLogPane" @tab-click="handleLogPaneClick">
                <el-tab-pane :label="$t('asset.management.log.originalLog')" name="first">
                  <original-log-table ref="originalLogTableDom" :data="item"></original-log-table>
                </el-tab-pane>
                <el-tab-pane :label="$t('asset.management.log.generalLog')" name="second">
                  <general-log-table ref="generalLogTableDom" :data="item"></general-log-table>
                </el-tab-pane>
              </el-tabs>
              <!--<original-log-table ref="originalLogTableDom" :data="item"></original-log-table>-->
            </el-row>
          </template>
        </el-tab-pane>
      </el-tabs>
    </section>
  </custom-dialog>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import CustomDialog from '@comp/CustomDialog'
import LogSourceInfo from './TheLogSourceInfo'
import LogTotalNumber from './TheLogTotalNumber'
import LogStorageDuration from './TheLogStorageDuration'
import LogNumberTrendChart from './TheLogNumberTrendChart'
import OriginalLogTable from './TheOriginalLogTable'
import GeneralLogTable from './TheGeneralLogTable'
import { queryAssetLogSources } from '@api/asset/management-api'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    CustomDialog,
    LogSourceInfo,
    LogTotalNumber,
    LogStorageDuration,
    LogNumberTrendChart,
    OriginalLogTable,
    GeneralLogTable,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    model: {
      required: true,
      type: Object,
    },
    width: {
      type: String,
      default: '30%',
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      title: this.$t('asset.management.logInfo'),
      activeName: 'active0',
      activeLogPane: 'first',
      logSources: [],
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.activeName = 'active0'
        this.activeLogPane = 'first'
        this.initData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    async initData() {
      this.activeName = 'active0'
      await this.getAssetLogSources()
      this.loadData()
    },
    loadData() {
      Object.getOwnPropertyNames(this.$refs).forEach((domName) => {
        if (domName !== 'dialogTemplate') {
          this.$refs[domName][0].loadData()
        }
      })
    },
    handleClick(tab, event) {
      this.activeName = tab.name
      this.$nextTick(() => {
        this.loadData()
      })
    },
    handleLogPaneClick(tab, event) {
      this.activeLogPane = tab.name
    },
    async getAssetLogSources() {
      await queryAssetLogSources(this.model.ipvAddress).then((res) => {
        this.logSources = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 10px 20px;
}
.compliance-container {
  font-family: 'Open Sans', sans-serif;
  ::v-deep .tr-wrapper {
    margin-bottom: 10px;
    &:last-child {
      margin-bottom: 0;
    }
    .widget {
      padding: 5px;
      /*                min-height: 160px;*/
      border: 1px solid #e5ebec;
      border-radius: 4px;
      &-header {
        height: 30px;
      }
      &-body {
        display: flex;
        padding: 15px 5px;
        flex-direction: row;
        align-items: flex-start;
        .icon-box {
          font-size: 30px;
          width: 56px;
          height: 56px;
          line-height: 56px;
          text-align: center;
          color: #fff;
          border-radius: 100%;
        }
        .detail-box {
          margin-left: 15px;
          width: calc(100% - 80px);
          font-size: 14px;
          font-weight: 400;
          &-word {
            padding-bottom: 10px;
            line-height: 25px;
            font-size: 14px;
            word-wrap: break-word;
            span {
              font-size: 22px;
            }
          }
          &-chart {
            height: 30px;
          }
        }
      }
    }
  }
}
</style>
