import request from '@util/request'

export function getRegisterData() {
  return request({
    url: '/authentication/register',
    method: 'get',
  })
}

export function getCaptchaData() {
  return request({
    url: '/authentication/captcha',
    method: 'get',
  })
}

export function getLoginData(obj) {
  return request({
    url: '/authentication/login',
    method: 'get',
    params: obj || {},
  })
}

export function getLoginMailData(obj) {
  return request({
    url: '/authentication/login-mail',
    method: 'get',
    params: obj || {},
  })
}

export function queryMenuData() {
  return request({
    url: '/menumanagement/menu/navigation',
    method: 'get',
  })
}

export function resetPasswordData(obj) {
  return request({
    url: '/usermanagement/password',
    method: 'post',
    data: obj || {},
  })
}

export function updateUserExtendData(obj) {
  return request({
    url: '/usermanagement/user/extend/init',
    method: 'post',
    data: obj || {},
  })
}

export function uploadLicenseData(obj) {
  return request(
    {
      url: '/systemmanagement/license/upload',
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

// 获取邮箱验证码
export function queryCaptchaMailData(mail) {
  return request({
    url: `/authentication/captcha-mail/${mail}`,
    method: 'get',
  })
}

export function validateCaptchaData(distance) {
  return request({
    url: `/authentication/check/captcha/${distance}`,
    method: 'get',
  })
}
