<!--
 * @Description: 系统管理 - 情报库设置
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <div
    v-loading.fullscreen="loading"
    :element-loading-text="$t('management.system.threat.upload.uploadText')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    class="router-wrap-table"
  >
    <div class="tab-context-wrapper">
      <section class="form-container" style="display: none">
        <h2>{{ $t('management.system.threat.cuttingTitle') }}</h2>
        <el-form ref="threatForm" :model="form.model" :rules="form.rule" label-width="180px">
          <!--
                    <el-row>
                        <el-col :span="12">
                            <el-form-item
                                :label="$t('management.system.label.path')"
                                prop="path">
                                <el-input v-model="form.model.url" class="width-small"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    -->
          <!--
                    <el-form-item :label="$t('management.system.threat.backupCycle')" style="margin:0">
                        <el-radio-group v-model="form.model.cycleType" @change="form.model.period = ''">
                            <el-radio label="day">
                                {{ $t("time.cycle.day") }}
                            </el-radio>
                            <el-radio label="week">
                                {{ $t("time.cycle.week") }}
                            </el-radio>
                            <el-radio label="month">
                                {{ $t("time.cycle.month") }}
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-row>
                        <el-col :span="1">
                            <el-form-item :label="$t('management.system.threat.backupTime')"></el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                v-if="form.model.cycleType !== 'day'"
                                :label="form.model.cycleType === 'week' ? $t('time.cycle.week') : $t('time.cycle.month')"
                                prop="timeValue">
                                <el-select
                                    v-model="form.model.period"
                                    clearable
                                    class="width-small">
                                    <template v-if="form.model.cycleType === 'week'">
                                        <el-option :value="1" :label="$t('time.week.mon')"></el-option>
                                        <el-option :value="2" :label="$t('time.week.tue')"></el-option>
                                        <el-option :value="3" :label="$t('time.week.wed')"></el-option>
                                        <el-option :value="4" :label="$t('time.week.thu')"></el-option>
                                        <el-option :value="5" :label="$t('time.week.fri')"></el-option>
                                        <el-option :value="6" :label="$t('time.week.sat')"></el-option>
                                        <el-option :value="0" :label="$t('time.week.sun')"></el-option>
                                    </template>
                                    <template v-else>
                                        <el-option
                                            v-for="index in 31"
                                            :key="index"
                                            :value="index + 1"
                                            :label="index + $t('time.unit.day')">
                                        </el-option>
                                    </template>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="8">
                            <el-form-item
                                :label="$t('time.option.time')"
                                prop="time">
                                <el-time-picker
                                    v-model="form.model.time"
                                    format="HH:mm:ss"
                                    value-format="HH:mm:ss"
                                    clearable
                                    class="width-small">
                                </el-time-picker>
                            </el-form-item>
                        </el-col>
                    </el-row>
    -->
          <el-row>
            <el-col :span="7" :offset="1">
              <el-form-item :label="$t('management.system.threat.backupTime')">
                <el-switch v-model="formData.status"></el-switch>
                <section class="form-validate-tip">
                  {{ $t('management.system.threat.upload.backupTimeText') }}
                </section>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <section class="tab-footer-button">
          <el-button v-has="'upload'" @click="clickSaveThreatConfig">
            {{ $t('button.save') }}
          </el-button>
          <!--
                    <el-button @click="clickResetThreatConfig">
                        {{ $t("button.reset.default") }}
                    </el-button>
                    -->
        </section>
      </section>
      <el-divider></el-divider>
      <section class="form-container">
        <h2>{{ $t('management.system.threat.upload.title') }}</h2>
        <el-row>
          <el-col :span="12">
            <el-form ref="formTemplate" :model="uploadData" label-width="27%">
              <el-form-item :label="$t('management.system.threat.upload.titleInput')">
                <el-upload
                  ref="upload"
                  v-has="'upload'"
                  class="header-button-upload"
                  action="#"
                  :headers="uploadData.header"
                  auto-upload
                  :show-file-list="true"
                  :limit="1"
                  accept=".zip"
                  :file-list="uploadData.files"
                  :on-exceed="handleExceed"
                  :on-change="onUploadFileChange"
                  :http-request="submitUploadFile"
                  :on-remove="handleRemove"
                  :before-upload="beforeUploadValidate"
                  @click="clickUploadTable"
                >
                  <el-input
                    style="cursor: pointer"
                    :placeholder="$t('management.system.threat.upload.choice')"
                    suffix-icon="el-icon-folder-opened"
                  ></el-input>
                </el-upload>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </section>
      <section class="tab-footer">
        <el-button v-has="'upload'" type="primary" @click="clickUp">
          {{ $t('management.system.threat.upload.Submit') }}
        </el-button>
        <el-button v-has="'query'" @click="clickResetThreatUploadConfig">
          {{ $t('button.reset.default') }}
        </el-button>
      </section>
    </div>
  </div>
</template>
<script>
import { JSEncrypt } from 'jsencrypt'
import { prompt } from '@util/prompt'

export default {
  props: {
    formData: {
      type: Object,
      default() {
        return {}
      },
    },
    uploadData: {
      required: true,
      type: Object,
    },
    loading: {
      type: Boolean,
    },
  },
  data() {
    return {
      file: {},
      form: {
        model: {
          // id: "",
          // cycleType: "week",
          // period: 1,
          // time: "",
          // url: "",
        },
        rule: {
          period: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'change',
            },
          ],
          time: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
          url: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      if (Object.keys(this.formData).length > 0) {
        this.form.model.status = this.formData.status
      }
    },
    clickSaveThreatConfig() {
      this.$refs.threatForm.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.save'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.$emit('on-save', this.handleParam())
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
    },
    clickResetThreatConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('on-reset')
      })
    },
    handleParam() {
      const encryptor = new JSEncrypt()
      encryptor.setPublicKey(this.$store.getters.publicKey)
      return {
        status: this.formData.status ? 0 : 1,
        // id: this.form.model.id,
        // cycleType: this.form.model.cycleType,
        // period: this.form.model.period,
        // time: this.form.model.time,
        // url: this.form.model.url
      }
    },
    // 超出限制提示
    handleExceed(files, fileList) {
      const text = this.$t('asset.management.exceed', [files.length, files.length, fileList.length])
      this.$message.warning(text)
    },
    // 上传文件改变时调用
    onUploadFileChange(file) {
      this.uploadData.files.push(file)
    },
    // 提交上传
    submitUploadFile(param) {
      if (param.file && this.uploadData.files.length > 0) {
        const formData = new FormData()
        // formData.name = "upload";
        // formData.file = param.file;
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.file = formData
      }
    },
    // 移除上传的文件
    handleRemove(file, fileList) {
      this.uploadData.files.splice(0, 1)
    },
    // 上传之前的文件校验
    beforeUploadValidate(file) {
      if (this.uploadData.files.length > 0) {
        // 限制上传文件的类型
        const suffix = file.name.substring(file.name.lastIndexOf('.zip') + 1)
        const isRight = suffix === 'zip'
        if (!isRight) {
          prompt({
            i18nCode: 'tip.upload.typeError',
            type: 'warning',
          })
          return false
        }
      }
      if (this.uploadData.files.length > 0) {
        // 限制上传文件的大小
        const isLt = file.size / 1024 / 1024 / 4 <= 100
        if (!isLt) {
          const text = this.$t('management.systemUpgrade.fileSize')
          this.$message.error(text)
          return false
        }
      }
      // if (this.upload.files.length > 0) {
      //     const reg = /^patch-\d{1,}to\d{1,}$/;
      //     const fileName = file.name.replace(".zip", "");
      //     if (reg.test(fileName)) {
      //         const [firstName, lastName] = fileName.split("to");
      //         const replaceStr = firstName.replace(/^patch-/, "");
      //         if (replaceStr === lastName) {
      //             prompt({
      //                 i18nCode: "management.systemUpgrade.updatePackageSameError",
      //                 type: "warning"
      //             });
      //             return false;
      //         } else if (replaceStr !== this.data.edition) {
      //             prompt({
      //                 i18nCode: "management.systemUpgrade.updatePackageCurrentError",
      //                 type: "warning"
      //             });
      //             return false;
      //         } else if (parseInt(replaceStr) > parseInt(lastName)) {
      //             prompt({
      //                 i18nCode: "management.systemUpgrade.updatePackageNameError",
      //                 type: "warning"
      //             });
      //             return false;
      //         }
      //     } else {
      //         prompt({
      //             i18nCode: "management.systemUpgrade.updatePackageNameError",
      //             type: "warning"
      //         });
      //         return false;
      //     }
      // }
      return true
    },
    // 点击上传文件
    clickUploadTable() {
      this.uploadData.files = []
      this.$refs.upload.submit()
    },
    clickUp() {
      if (this.uploadData.uploadFile && this.uploadData.files.length > 0) {
        this.uploadTable(this.uploadData.uploadFile)
      } else {
        prompt({
          i18nCode: 'validate.upload.empty',
          type: 'error',
        })
      }
    },
    // 上传信息
    async uploadTable(formData) {
      this.$emit('on-submit', this.file)
    },
    clickResetThreatUploadConfig() {
      this.$confirm(this.$t('tip.confirm.reset'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.$emit('reset-upload', this.$refs.upload)
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.form-validate-tip {
  color: #409eff;
}

.el-tabs__content {
  .el-tab-pane {
    .tab-context-wrapper {
      h2 {
        color: #1873d7;
      }
    }
  }
}

::v-deep.el-input--small {
  .el-input__inner {
    width: 100%;
  }
}

.tab-context-wrapper {
  display: flex;
  flex-direction: column;
  height: 100%;

  .form-container {
    position: relative;
  }

  .tab-footer {
    width: 50%;
    text-align: right;
  }

  .table-container {
    .table-header {
      justify-content: normal;
      background-color: $CR;
    }
  }
}
</style>
