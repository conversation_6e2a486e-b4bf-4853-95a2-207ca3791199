export default {
  ajax: {
    success: '访问成功',
    exception: {
      system: '系统异常',
      server: '服务故障异常',
      session: 'Session异常',
      access: '访问令牌异常',
      certification: '认证异常',
      auth: '访问资源权限异常',
      token: '访问资源功能权限异常',
      param: '您输入的内容有误，请检查后重新输入',
      idempotency: '幂等性异常',
      ip: 'IP禁止访问',
      upload: '上传附件格式校验失败',
      code: '未检测到有效的系统返回码',
      mock: '缺失对应api的mock数据，请完善',
    },
    attack: {
      xss: 'xss脚本攻击',
    },
    interaction: {
      error: 'API异常，请检查前后端API是否正确',
    },
    interceptors: {
      error: '请求拦截器拦截失败',
    },
    service: {
      upload: '上传文件失败，可能原因文件被修改或服务器连接失败',
      timeout: '检测到您未连接到有效API或者连接服务器超时',
    },
  },
}
