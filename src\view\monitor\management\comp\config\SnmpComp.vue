<!--
 * @Description: 监控器配置 - snmp组件
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-04
 * @Editor:
 * @EditDate: 2021-08-04
-->
<template>
  <section>
    <el-form ref="snmpForm" :model="snmpModel" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item prop="snmpVersion" :label="$t('monitor.management.config.snmp.version')">
            <el-select v-model="snmpModel.snmpVersion" @change="changeVersion">
              <el-option v-for="item in options.versionOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="snmpPort" :label="$t('monitor.management.config.snmp.port')">
            <el-input v-model="snmpModel.snmpPort" maxlength="5" oninput="value=value.replace(/[^0-9]/g,'')"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row v-if="snmpModel.snmpVersion === '1' || snmpModel.snmpVersion === '2'">
        <el-col :span="12">
          <el-form-item prop="readCommunity" :rules="readCommunityRule" :label="$t('monitor.management.config.snmp.readCommunity')">
            <el-input v-model="snmpModel.readCommunity"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item prop="writeCommunity" :label="$t('monitor.management.config.snmp.writeCommunity')">
            <el-input v-model="snmpModel.writeCommunity"></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div v-if="snmpModel.snmpVersion === '3'">
        <el-row>
          <el-col :span="12">
            <el-form-item prop="authWay" :label="$t('monitor.management.config.snmp.authWay')">
              <el-select v-model="snmpModel.authWay" @change="changeAuthWay">
                <el-option v-for="item in options.authWayOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="authPwd" :rules="authPwdRule" :label="$t('monitor.management.config.snmp.authPwd')">
              <el-input v-model="snmpModel.authPwd" type="password" :disabled="disabled.authPwd"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="encryptionWay" :label="$t('monitor.management.config.snmp.encryptionWay')">
              <el-select v-model="snmpModel.encryptionWay" :disabled="disabled.encryptionWay" @change="changeEncryptionWay">
                <el-option v-for="item in options.encryptionWayOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="encryptionPwd" :rules="encryptionPwdRule" :label="$t('monitor.management.config.snmp.encryptionPwd')">
              <el-input v-model="snmpModel.encryptionPwd" type="password" :disabled="disabled.encryptionPwd"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="context" :label="$t('monitor.management.config.snmp.context')">
              <el-input v-model="snmpModel.context" maxlength="64"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="contextName" :label="$t('monitor.management.config.snmp.contextName')">
              <el-input v-model="snmpModel.contextName" maxlength="64"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item prop="snmpUserName" :label="$t('monitor.management.config.snmp.snmpUserName')">
              <el-input v-model="snmpModel.snmpUserName"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item prop="securityLevel" :label="$t('monitor.management.config.snmp.securityLevel')">
              <el-select v-model="snmpModel.secLev">
                <el-option v-for="item in options.securityLevelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
    </el-form>
  </section>
</template>

<script>
import { isEmpty } from '@util/common'
import { validatePwd, validateName } from '@util/validate'
import { querySnmpVersion, queryAuthWay, queryEncryptionWay, querySecurityLevel } from '@api/monitor/snmp-api'

export default {
  props: {
    snmpModel: {
      required: true,
      type: Object,
    },
  },
  data() {
    const validatorPort = (rule, value, callback) => {
      if (isEmpty(value)) {
        callback(new Error(this.$t('validate.empty')))
      } else if (value < 1 || value > 65535) {
        callback(new Error(this.$t('validate.monitor.port')))
      } else {
        callback()
      }
    }
    const validatorPwd = (rule, value, callback) => {
      if (rule.required === true && isEmpty(value)) {
        callback(new Error(this.$t('validate.empty')))
      } else if (!isEmpty(value) && !validatePwd(value)) {
        callback(new Error(this.$t('validate.comm.pwd')))
      } else {
        callback()
      }
    }
    const validatorUserName = (rule, value, callback) => {
      if (!isEmpty(value) && !validateName(value, 1)) {
        callback(new Error(this.$t('validate.nameInput.rule')))
      } else {
        callback()
      }
    }
    return {
      disabled: {
        authPwd: true,
        encryptionWay: true,
        encryptionPwd: true,
      },
      options: {
        versionOption: [],
        authWayOption: [],
        encryptionWayOption: [],
        securityLevelOption: [],
      },
      rules: {
        snmpPort: [
          {
            required: true,
            validator: validatorPort,
            trigger: 'blur',
          },
        ],
        readCommunity: [
          {
            required: true,
            trigger: 'blur',
            message: this.$t('validate.empty'),
          },
        ],
        authPwd: [
          {
            required: true,
            validator: validatorPwd,
            trigger: 'blur',
            message: this.$t('validate.empty'),
          },
        ],
        encryptionPwd: [
          {
            required: true,
            validator: validatorPwd,
            trigger: 'blur',
          },
        ],
        snmpUserName: [
          {
            validator: validatorUserName,
            trigger: 'blur',
          },
        ],
      },
    }
  },
  computed: {
    readCommunityRule() {
      return this.snmpModel.snmpVersion !== '3'
        ? this.rules.readCommunity
        : [
            {
              required: false,
            },
          ]
    },
    authPwdRule() {
      return this.snmpModel.authWay !== '-1'
        ? this.rules.authPwd
        : [
            {
              required: false,
            },
          ]
    },
    encryptionPwdRule() {
      return this.snmpModel.encryptionWay !== '-1'
        ? this.rules.encryptionPwd
        : [
            {
              required: false,
            },
          ]
    },
  },
  mounted() {
    this.initOption()
    this.initModelData()
  },
  methods: {
    initModelData() {
      this.changeEncryptionWay(this.snmpModel.encryptionWay)
      this.changeAuthWay(this.snmpModel.authWay)
    },
    changeVersion() {
      this.snmpModel = Object.assign(this.snmpModel, {
        snmpPort: '161',
        readCommunity: 'public',
        writeCommunity: '',
        authWay: '-1',
        authPwd: '',
        encryptionWay: '-1',
        encryptionPwd: '',
        context: '',
        contextName: '',
        snmpUserName: '',
        securityLevel: '-1',
      })
    },
    changeAuthWay(value) {
      if (value === '-1') {
        this.disabled.authPwd = true
        this.snmpModel.authPwd = ''
        this.disabled.encryptionWay = true
        this.snmpModel.encryptionWay = '-1'
        this.disabled.encryptionPwd = true
        this.snmpModel.encryptionPwd = ''
      } else {
        this.disabled.authPwd = false
        this.disabled.encryptionWay = false
      }
    },
    changeEncryptionWay(value) {
      if (value === '-1') {
        this.disabled.encryptionPwd = true
        this.snmpModel.encryptionPwd = ''
      } else {
        this.disabled.encryptionPwd = false
      }
    },
    validateForm() {
      let validate = false
      this.$refs.snmpForm.validate((valid) => {
        validate = valid
      })
      return validate
    },
    resetForm() {
      this.$refs.snmpForm.resetFields()
    },
    initOption() {
      querySnmpVersion().then((res) => {
        this.options.versionOption = res
      })
      queryAuthWay().then((res) => {
        this.options.authWayOption = res
      })
      queryEncryptionWay().then((res) => {
        this.options.encryptionWayOption = res
      })
      querySecurityLevel().then((res) => {
        this.options.securityLevelOption = res
      })
    },
  },
}
</script>
