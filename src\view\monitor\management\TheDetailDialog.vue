<!--
 * @Description: 监控器 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-06
 * @Editor:
 * @EditDate: 2021-08-06
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-form :model="model" label-width="120px">
        <el-row>
          <el-col v-for="(item, index) in columnOption" :key="index" :span="12">
            <el-form-item :prop="item.key" :label="item.label">
              <span v-if="item.key === 'monitorEnabled'">
                {{ model[item.key] === '1' ? $t('monitor.management.status.on') : $t('monitor.management.status.off') }}
              </span>
              <span v-else-if="item.key === 'authPwd' || item.key === 'encryptionPwd'">
                {{ model[item.key] !== '' ? '*********' : '' }}
              </span>
              <span v-else>
                {{ model[item.key] }}
              </span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import monitorColumn from '@asset/js/attr/monitor-detail-column'
import { queryMonitorComp } from '@api/monitor/management-api'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      infoItems: '',
      columnOption: [],
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.getMonitorCompInfo(this.model.monitorType)
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    getMonitorCompInfo(monitorType) {
      queryMonitorComp(monitorType).then((res) => {
        this.infoItems = res
        this.handleColumns()
      })
    },
    handleColumns() {
      const infoArr = this.infoItems.split(',')
      let columns = monitorColumn['basic']
      infoArr.map((key) => {
        if (key === 'snmp' && this.model.snmpVersion === '3') {
          columns = columns.concat(monitorColumn['snmpV3'])
        } else {
          columns = columns.concat(monitorColumn[key])
        }
      })
      this.columnOption = Object.assign({}, columns)
    },
  },
}
</script>
