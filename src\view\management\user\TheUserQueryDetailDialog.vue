<!--
 * @Description: 用户管理 -详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :action="!readonly"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <el-form ref="formTemplate" :model="form.model" label-width="25%">
      <template v-if="readonly">
        <el-form-item :label="$t('management.user.label.account')">
          {{ form.userAccount }}
        </el-form-item>
        <el-form-item :label="$t('management.user.label.userState')">
          {{ form.userStatusText }}
        </el-form-item>
        <el-form-item :label="$t('management.user.label.accountState')">
          {{ form.accountStatusText }}
        </el-form-item>
        <el-form-item :label="$t('management.user.label.passwordState')">
          {{ form.passwordStatusText }}
        </el-form-item>
        <el-form-item :label="$t('management.user.label.timeRange')">{{ form.startValidDate }} ~ {{ form.endValidDate }}</el-form-item>
        <el-form-item :label="$t('management.user.label.description')">
          {{ form.userDescription }}
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item :label="form.info.userAccount.label">
          <el-input v-model="form.model.userAccount" class="width-small"></el-input>
        </el-form-item>
        <el-form-item :label="form.info.userStatus.label">
          <el-select v-model="form.model.userStatus" clearable class="width-small">
            <el-option v-for="item in option.user" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.info.accountStatus.label">
          <el-select v-model="form.model.accountStatus" clearable class="width-small">
            <el-option v-for="item in option.account" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.info.passwordStatus.label">
          <el-select v-model="form.model.passwordStatus" clearable class="width-small">
            <el-option v-for="item in option.password" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="form.info.userDescription.label">
          <el-input v-model="form.model.userDescription" class="width-small"></el-input>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    form: {
      type: Object,
      default() {
        return {}
      },
    },
    actions: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      option: {
        user: [
          {
            value: '0',
            label: this.$t('management.user.option.normal'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.manualLock'),
          },
          {
            value: '2',
            label: this.$t('management.user.option.systemLock'),
          },
        ],
        account: [
          {
            value: '0',
            label: this.$t('management.user.option.enable'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.disable'),
          },
        ],
        password: [
          {
            value: '0',
            label: this.$t('management.user.option.normal'),
          },
          {
            value: '1',
            label: this.$t('management.user.option.reset'),
          },
          {
            value: '2',
            label: this.$t('management.user.option.init'),
          },
        ],
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.handleFormInfo()
            this.$emit('on-submit', this.form.model, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    handleFormInfo() {
      Object.getOwnPropertyNames(this.form.model).forEach((modelKey) => {
        Object.getOwnPropertyNames(this.form.info).forEach((item) => {
          if (this.form.info[item]['key'] === modelKey) {
            this.form.info[item]['value'] = this.form.model[modelKey]
          }
        })
      })
    },
  },
}
</script>
