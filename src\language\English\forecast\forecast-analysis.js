export default {
  forecastAnalysis: {
    title: 'Forecast Analysis',
    label: {
      type: 'Category',
      infoItem: 'Information Item',
      enterTime: 'Update Time',
      funcEnable: 'Function Enabled',
      externalSystem: 'Forward to External System',
      externalMail: 'Forward to Email',
    },
    placeholder: {
      infoItem: 'Information Item',
    },
    detail: {
      actualVal: 'Actual Value',
      expectedVal: 'Predicted Value',
      key: 'Anomaly Model',
      name: 'Model Name',
      time: 'Occurrence Time',
    },
    chart: {
      baselineChart: 'Baseline Comparison Chart',
      mae<PERSON><PERSON>: 'MAE Chart',
      mapeeChart: 'MAPEE Chart',
      maseChart: 'MASE Chart',
      mapeChart: 'MAPE Chart',
      smapeChart: 'SMAPE Chart',
    },
  },
}
