export default [
  {
    name: 'ManagementMenu',
    path: '/management/menu',
    component: () => import('@view/management/menu/ManagementMenu'),
  },
  {
    name: 'ManagementResource',
    path: '/management/resource',
    component: () => import('@view/management/resource/ManagementResource'),
  },
  {
    name: 'ManagementRole',
    path: '/management/role',
    component: () => import('@view/management/role/ManagementRole'),
  },
  {
    name: 'ManagementSystem',
    path: '/management/system',
    component: () => import('@view/management/system/ManagementSystem'),
  },
  {
    name: 'ManagementUser',
    path: '/management/user',
    component: () => import('@view/management/user/ManagementUser'),
  },
  {
    name: 'ManagementForwardServer',
    path: '/management/forward-server',
    component: () => import('@view/management/forward-server/ManagementForwardServer'),
  },
  {
    name: 'ManagementLogAudit',
    path: '/management/log-audit',
    component: () => import('@view/management/log-audit/ManagementLogAudit'),
  },
  {
    name: 'ManagementLogBackup',
    path: '/management/log-backup',
    component: () => import('@view/management/log-backup/ManagementLogBackup'),
  },
  {
    name: 'ManagementNetwork',
    path: '/management/network',
    component: () => import('@view/management/network/ManagementNetwork'),
  },
  {
    name: 'ManagementSystemUpgrade',
    path: '/management/system-upgrade',
    component: () => import('@view/management/system-upgrade/ManagementSystemUpgrade'),
  },
  {
    name: 'ManagementAccessControl',
    path: '/management/access-control',
    component: () => import('@view/management/access-control/ManagementAccessControl'),
  },
  {
    name: 'ManagementProxyServer',
    path: '/management/proxy-server',
    component: () => import('@view/management/proxy-server/ManagementProxyServer'),
  },
]
