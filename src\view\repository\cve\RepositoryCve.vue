<!--
 * @Description: CVE漏洞库
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('repository.cve.fuzzyQuery')])"
              clearable
              @change="pageQuery('e')"
              @keyup.enter.native="pageQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="pageQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="pageQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button"></section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.name"
                  clearable
                  :placeholder="$t('repository.cve.table.name')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.status"
                  clearable
                  :placeholder="$t('repository.cve.table.status')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.phase"
                  clearable
                  :placeholder="$t('repository.cve.table.phase')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col align="right" :offset="5" :span="4">
                <el-button @click="pageQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQueryForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体内容-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('repository.cve.header') }}
        </h2>
      </header>
      <main v-loading="data.loading" class="table-body-main">
        <el-table
          v-show="btnRef"
          ref="auditTable"
          v-loading="data.loading"
          v-el-table-scroll="scrollTable"
          infinite-scroll-disabled="disableScroll"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
        >
          <el-table-column
            v-for="(item, index) in options.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`repository.cve.table.${item}`)"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column fixed="right" width="120">
            <template slot-scope="scope">
              <el-button class="el-button--blue" @click="clickDetailButton(scope.row)">
                {{ $t('button.detail') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer infinite-scroll">
      <section class="infinite-scroll-nomore">
        <span v-show="data.nomore">{{ $t('validate.data.nomore') }}</span>
        <i v-show="data.totalLoading" class="el-icon-loading"></i>
      </section>
      <section class="infinite-scroll-total">
        <b>{{ $t('event.original.total') + ':' }}</b>
        <span>{{ data.total }}</span>
      </section>
    </footer>
    <!--详情页-->
    <detail-dialog :visible.sync="dialog.detailDialog.visible" :title="dialog.detailDialog.title" :form="dialog.detailDialog.form"></detail-dialog>
  </div>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import detailDialog from './RepositoryCveDetailDialog'
import { debounce } from '@/util/effect'
import { queryCveTableData, queryCveDetail, queryTotal } from '@api/repository/cve-api'

export default {
  name: 'RepositoryCve',
  directives: {
    elTableScroll,
  },
  components: {
    detailDialog,
  },
  data() {
    return {
      btnRef: true,
      data: {
        loading: false,
        table: [],
        total: 0,
        nomore: false,
        totalLoading: false,
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
      }, // 列表信息
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      }, // 分页信息
      query: {
        fuzzyField: '',
        seniorQuery: {
          name: '',
          phase: '',
          status: '',
        },
      }, // 查询内容
      dialog: {
        detailDialog: {
          visible: false,
          title: this.$t('repository.cve.dialog.detailTitle'),
          form: {
            model: {
              name: '',
              status: '',
              references: '',
              phase: '',
              votes: '',
              comments: '',
              description: '',
            },
          },
        },
      }, // 弹出框
      show: {
        seniorQueryShow: false,
      }, // 显示隐藏
      options: {
        columnOption: ['name', 'status', 'phase', 'description'],
      }, // 下拉框option
    }
  },
  computed: {
    disableScroll() {
      return this.data.loading
    },
  },
  mounted() {
    this.init()
  },
  updated() {
    if (this.data.table.length > 0) this.$refs.auditTable.doLayout()
  },
  methods: {
    // 初始化方法
    init() {
      this.queryCveTable()
      this.initDebounce()
      this.getTotal()
    },
    // 初始化防抖函数
    initDebounce() {
      this.data.debounce.query = debounce(() => {
        let params = {}
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.seniorQuery, {
            pageSize: this.pagination.pageSize,
          })
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            fuzzyField: this.query.fuzzyField,
          }
        }
        this.queryCveTable(params)
        this.getTotal(params)
      })
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.query.seniorQuery = {
          name: '',
          phase: '',
          status: '',
        }
        this.pagination.pageNum = 1
        setTimeout(() => {
          this.queryCveTable()
          this.getTotal()
        }, 150)
      }, 500)
    },
    // 查询列表
    queryCveTable(
      params = {
        pageSize: this.pagination.pageSize,
      }
    ) {
      this.data.nomore = false
      this.btnRef = false
      this.data.loading = true
      queryCveTableData(params).then((res) => {
        this.data.table = res
        this.data.loading = false
        if (res.length >= 20) {
          this.data.nomore = false
        } else {
          this.data.nomore = true
        }
        this.$nextTick(() => {
          if (this.data.table.length > 0) this.$refs.auditTable.doLayout()
        })
        this.btnRef = true
      })
    },
    pageQuery(flag) {
      if (flag) this.pagination.pageNum = 1
      this.data.debounce.query()
    },
    // 点击查询按钮
    clickQueryButton() {
      this.query.fuzzyField = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initDebounce()
      this.resetQueryForm()
    },
    // 点击向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initDebounce()
      this.resetQueryForm()
    },
    // 点击详情按钮
    clickDetailButton(row) {
      this.dialog.detailDialog.visible = true
      queryCveDetail(row.name).then((res) => {
        this.dialog.detailDialog.form.model = res
      })
    },
    // 重置查询条件
    resetQueryForm() {
      this.data.debounce.resetQueryDebounce()
    },
    scrollTable(param = {}) {
      if (!this.data.nomore) {
        param = {
          pageSize: this.pagination.pageSize,
          name: this.query.seniorQuery.name,
          status: this.query.seniorQuery.status,
          fuzzyField: this.query.fuzzyField,
          phase: this.query.seniorQuery.phase,
          SerialNumber: this.data.table.length > 0 ? this.data.table[this.data.table.length - 1]['name'] : '',
        }
        this.data.loading = true
        queryCveTableData(param).then((res) => {
          if (res.length < 20) this.data.nomore = true
          this.data.table.push(...res)
          this.data.loading = false
        })
      }
    },
    // 查询审计事件总数
    getTotal(params = { pageSize: 20 }) {
      this.data.totalLoading = true
      queryTotal(params).then((res) => {
        this.data.total = res
        this.data.totalLoading = false
      })
    },
  },
}
</script>
