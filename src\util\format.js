/**
 * @func 格式化时间
 * @param {number | object} time - 需要格式化的时间
 * @param {object} cFormat - 自定义的格式
 * @return {string}
 * <AUTHOR> @date 2019/07/15
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0) return null
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (('' + time).length === 10) time = parseInt(time) * 1000
    date = new Date(time)
  }

  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }

  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // 0是周日
    if (key === 'a') return ['日', '一', '二', '三', '四', '五', '六'][value]
    if (result.length > 0 && value < 10) value = '0' + value
    return value || 0
  })
}

/**
 * @func 格式化时间
 * @param {number} time - 需要格式化的时间
 * @param {object} option - 自定义的格式
 * @return {string}
 * <AUTHOR> @date 2019/07/15
 */

export function formatTime(time, cFormat) {
  if (arguments.length === 0) return null
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if (('' + time).length === 10) time = parseInt(time) * 1000
    date = new Date(time)
  }

  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay(),
  }

  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key]
    // 0是周日
    if (key === 'a') return ['日', '一', '二', '三', '四', '五', '六'][value]
    if (result.length > 0 && value < 10) value = '0' + value
    return value || 0
  })
}

/**
 * @func 格式化时间是否在最近
 * @param {number} time - 需要格式化的时间
 * @param {object} option - 自定义的格式
 * @return {string}
 * <AUTHOR> @date 2019/07/15
 */
export function formatTimeIsRecent(time, option) {
  time = +time * 1000
  const date = new Date(time)
  const now = Date.now()
  const diff = (now - date) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }

  return option ? formatTime(time, option) : `${date.getMonth() + 1}月${date.getDate()}日${date.getHours()}时${date.getMinutes()}分`
}
/**
 * @func 对传入数据进行深拷贝
 * @param {object | array} source - 将要拷贝的数据
 * @return {object | array} 深拷贝之后数据
 * <AUTHOR> @date 2019/7/22
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    console.error('argument type error')
    return
  }
  const target = source.constructor === Array ? [] : {}
  Object.keys(source).forEach((key) => {
    target[key] = source[key] && typeof source[key] === 'object' ? deepClone(source[key]) : (target[key] = source[key])
  })
  return target
}

/**
 * @func 将get请求的url字符串转化为对象
 * @param {string} url -  window.location.search内容
 * @return {object}
 * <AUTHOR> @date 2019/09/15
 */
export function urlParamsToObj(url) {
  const obj = {}
  const arr = url.split('?')
  const arrayTemplate = arr[1].split('&')
  for (let i = 0; i < arrayTemplate.length; i++) {
    const res = arrayTemplate[i].split('=')
    obj[res[0]] = res[1]
  }
  return obj
}

/**
 * @func 将get请求的url字符串转化为对象
 * @param {string} url -  window.location.search内容
 * @return {object}
 * <AUTHOR> @date 2019/09/15
 */
export function paramToObj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searches = search.split('&')
  searches.forEach((value) => {
    const index = value.indexOf('=')
    if (index !== -1) {
      const name = value.substring(0, index)
      obj[name] = value.substring(index + 1, value.length)
    }
  })
  return obj
}

/**
 * @func 过滤数组对象
 * @param {object[]} source - 需要过滤的数组对象全集
 * @param {object} keyObj - 过滤条件
 * @return {object[]}
 * <AUTHOR> @date 2019/09/15
 */
export function filterObj(source, keyObj) {
  const keys = Object.keys(keyObj)
  return source.filter((v) => {
    return keys.every((key) => v[key] === keyObj[key])
  })
}

/**
 * @func 去除数组对象中存在相同值
 * @param {array} arrObj - 需要过滤的数组
 * @param {array} key - 相同的key过滤掉
 * @return {string}
 * <AUTHOR> @date 2020/1/16
 */
export function uniqueArrayObject(arrObj, key) {
  const hash = {}
  return arrObj.reduce((item, next) => {
    hash[next.label] ? '' : (hash[next.label] = item.push(next))
    return item
  }, [])
}

/**
 * @func 将ipV4转换成int类型
 * @param {String} IP - IP地址
 * @return {string}
 * <AUTHOR> @date 2020/2/12
 */
export function ipToInt(IP) {
  if (IP === null || IP === '') {
    return 0
  }
  const a = IP.split('.')
  for (let i = 0; i < 4; i++) {
    a[i] = parseInt(a[i])
    if (isNaN(a[i])) a[i] = 0
    if (a[i] < 0) a[i] += 256
    if (a[i] > 255) a[i] -= 256
  }
  return (a[0] << 16) * 256 + ((a[1] << 16) | (a[2] << 8) | a[3])
}

/**
 * @func 将int类型转换成ip
 * @param {int} param - IP地址number
 * @return {string}
 * <AUTHOR> @date 2020/2/12
 */
export function intToIp(param) {
  let IP
  let tmp
  const val = param

  tmp = val >>> 24
  IP = tmp + '.'

  tmp = (val >>> 16) & 0xff
  IP = IP + tmp + '.'

  tmp = (val >>> 8) & 0xff
  IP = IP + tmp + '.'

  tmp = val & 0xff
  IP = IP + tmp

  return IP
}

/**
 * @func 将扁平化数组对象按照某一个key分类
 * @param {array} arr - 将要分类的数组对象
 * @param {string} key - 按照数组对象某个key分类
 * @param {string} children - 返回数组tree的分类key
 * @return {array} groupTree - 返回组装好的数组
 * <AUTHOR> @date 2020/2/8
 */
export function classify(arr, key, children = 'children') {
  const [groupKeys, groupTree] = [[], []]
  arr.forEach((item) => {
    if (item[key] && groupKeys.indexOf(item[key]) === -1) {
      groupKeys.push(item[key])
    }
  })
  groupKeys.forEach((item) => {
    const obj = {}
    obj[key] = item
    obj[children] = arr.filter((col) => item === col[key])
    groupTree.push(obj)
  })

  return groupTree
}

/**
 * @func 两个对象的深度合并
 * @param {object} target - 被合并的对象，如果有相同key会被覆盖
 * @param {object} arg - 多个想要合并的对象
 * @return {object} 合并之后的对象
 * <AUTHOR> @date 2021/9/8
 */
export function deepMerge(target, ...arg) {
  return arg.reduce((previousValue, currentValue) => {
    return Object.keys(currentValue).reduce((subPreviousValue, subCurrentValue) => {
      const sourceValue = currentValue[subCurrentValue]
      if (sourceValue.constructor === Object) {
        subPreviousValue[subCurrentValue] = deepMerge(subPreviousValue[subCurrentValue] ? subPreviousValue[subCurrentValue] : {}, sourceValue)
      } else if (sourceValue.constructor === Array) {
        subPreviousValue[subCurrentValue] = sourceValue.map((item, idx) => {
          if (item.constructor === Object) {
            const currentSubPreviousValue = subPreviousValue[subCurrentValue] ? subPreviousValue[subCurrentValue] : []
            return deepMerge(currentSubPreviousValue[idx] ? currentSubPreviousValue[idx] : {}, item)
          } else {
            return item
          }
        })
      } else {
        subPreviousValue[subCurrentValue] = sourceValue
      }
      return subPreviousValue
    }, previousValue)
  }, target)
}
