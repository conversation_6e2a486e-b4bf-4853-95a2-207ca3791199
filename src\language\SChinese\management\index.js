import menu from './menu'
import resource from './resource'
import role from './role'
import system from './system'
import user from './user'
import logAudit from './log-audit'
import logBackup from './log-backup'
import forwardServer from './forward-server'
import systemUpgrade from './system-upgrade'
import network from './network'
import proxyServer from './proxy-server'

export default {
  management: {
    ...menu,
    ...resource,
    ...role,
    ...system,
    ...user,
    ...logAudit,
    ...logBackup,
    ...forwardServer,
    ...systemUpgrade,
    ...network,
    ...proxyServer,
  },
}
