<template>
  <div class="simplemde-container" :style="{ height: height, zIndex: zIndex }">
    <textarea :id="id"></textarea>
  </div>
</template>
<script>
import 'font-awesome/css/font-awesome.min.css'
import 'simplemde/dist/simplemde.min.css'
import SimpleMDE from 'simplemde'

export default {
  name: 'Markdown',
  props: {
    value: String,
    id: {
      type: String,
    },
    autofocus: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: '',
    },
    height: {
      type: [Number, String],
      default: '150px',
    },
    zIndex: {
      type: Number,
      default: 10,
    },
    toolbar: {
      type: Array,
    },
  },
  data() {
    return {
      simplemde: null,
      hasChange: false,
    }
  },
  mounted() {
    this.createMarkdownEdit()
  },
  destroyed() {
    this.clearMarkdownEdit()
  },
  methods: {
    createMarkdownEdit() {
      this.simplemde = new SimpleMDE({
        element: document.getElementById(this.id || 'markdown_editor-' + new Date()),
        autoDownloadAwesome: false,
        lineWrapping: true,
        autofocus: this.autofocus,
        toolbar: this.toolbar,
        spellChecker: false,
        placeholder: this.placeholder,
        // hideIcons: ["guide", "heading", "quote", "image", "preview", "side-by-side", "fullscreen"],
        insertTexts: {
          link: ['[', ']( )'],
        },
      })
      if (this.value) {
        this.simplemde.value(this.value)
      }

      this.simplemde.codemirror.on('change', () => {
        if (this.hasChange) {
          this.hasChange = true
        }
        this.$emit('input', this.simplemde.value())
      })
    },
    clearMarkdownEdit() {
      this.simplemde.toTextArea()
      this.simplemde = null
    },
  },
}
</script>
<style lang="scss">
.simplemde-container {
  /deep/ .editor-toolbar {
    height: 50px;
  }

  /deep/ .CodeMirror {
    min-height: 150px;
    line-height: 20px;
    height: calc(100% - 82px);
  }

  /deep/ .CodeMirror-scroll {
    min-height: 150px;
  }

  /deep/ .CodeMirror-code {
    padding-bottom: 40px;
  }

  /deep/ .editor-statusbar {
    /*display: none;*/
    height: 32px;
  }

  /deep/ .CodeMirror .CodeMirror-code .cm-link {
    color: #1890ff;
  }

  /deep/ .CodeMirror .CodeMirror-code .cm-string.cm-url {
    color: #2d3b4d;
  }

  /deep/ .CodeMirror .CodeMirror-code .cm-formatting-link-string.cm-url {
    padding: 0 2px;
    color: #e61e1e;
  }

  /deep/ .CodeMirror-fullscreen,
  /deep/ .editor-toolbar.fullscreen {
    z-index: 1003;
  }
}
</style>
