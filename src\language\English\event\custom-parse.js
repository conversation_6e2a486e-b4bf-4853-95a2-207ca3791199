export default {
  customParse: {
    title: 'Custom Parsing',
    label: {
      patternValue: 'Field Value',
      patternKey: 'Multi-tuple',
      patternName: 'Multi-tuple',
      pattern: 'Expression',
      devType: 'Log Source Device',
      devTypeName: 'Log Source Device',
      status: 'Status',
      message: 'Log Original Text',
      patternInfo: 'Selection Information',
      createTime: 'Storage Time',
      propDetail: 'Field Value Details:',
      multiGroup: 'Multi-tuple',
    },
    placeholder: {
      patternValue: 'Field Value',
      patternKey: 'Multi-tuple',
      devType: 'Log Source Device',
      status: 'Status',
      fuzzyField: 'Field Value',
    },
    tip: {
      wordTranslation: 'Custom Parsing Rules',
      selectContent: 'Select your content with the mouse',
      inputCharValid: 'Only letters and numbers can be used in fields, such as [a-zA-X0-9_]',
      validKeyword: 'Please re-select, spaces are not allowed at the beginning or end of the selection.',
      repeatKeyword: 'Duplicate selection not allowed, please select again.',
      selDevType: 'Please select device type first before checking feature value information.',
      eventTypeRequired: 'Please select feature value multi-tuple in the selection, this information is required.',
      neighborNoSelected: 'Adjacent selections cannot be selected.',
    },
  },
}
