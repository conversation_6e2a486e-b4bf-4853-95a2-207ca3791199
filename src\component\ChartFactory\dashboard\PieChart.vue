<template>
  <div :id="id" ref="pieChart" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'
import resize from '../mixin/resize'
import empty from '../mixin/empty'
export default {
  mixins: [resize, empty],
  props: {
    className: {
      type: String,
      default: 'chart-pie',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    mouseEvent: {
      type: Boolean,
      default: false,
    },
    proto: {
      type: Boolean,
      default: false,
    },
    pieData: {
      type: Object,
      default() {
        return {
          title: '',
          legend: [],
          series: [],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    pieData: {
      handler(data) {
        this.configChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart(data = this.pieData) {
      this.chart = echarts.init(this.$refs.pieChart, this.$store.getters.theme)
    },
    configChart(data = this.pieData) {
      !data || Object.keys(data).length === 0 || data.series.length === 0 ? this.empty() : this.drawChart(data)
    },
    drawChart(data) {
      const option = this.proto ? data : this.chartOptionConfig(data)
      this.chart.clear()
      this.chart.setOption(option, true)
    },
    chartOptionConfig(data) {
      const option = {
        backgroundColor: 'transparent',
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          text: data.title ? data.title : '',
        },
        tooltip: {
          show: false,
        },
        grid: {
          top: '36px',
          left: '5%',
          right: '5%',
          bottom: '5%',
          containLabel: true,
        },
        legend: {
          type: 'scroll',
          orient: 'horizontal',
          data: [],
        },
        series: [
          {
            type: 'pie',
            radius: '60%',
            label: {
              show: false,
            },
            data: [],
          },
        ],
      }

      if (this.mouseEvent) {
        this.chartHasMouseEvent(option)
      }

      this.chartSeriesConfig(option, data)
      return option
    },
    chartHasMouseEvent(option) {
      if (option.legend && option.legend.length > 1) {
        option.legend = Object.assign(option.legend, {
          orient: 'horizontal',
          right: '4%',
        })
      }

      option.tooltip = Object.assign(option.tooltip, {
        show: true,
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      })

      return option
    },
    chartSeriesConfig(option, data) {
      data.series.forEach((item, index) => {
        if (this.mouseEvent) {
          option['legend']['data'].push(data.legend[index])
        }
        option.series[0].data.push({
          value: item,
          name: data.legend[index],
        })
      })
    },
    getChart() {
      return this.chart
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
