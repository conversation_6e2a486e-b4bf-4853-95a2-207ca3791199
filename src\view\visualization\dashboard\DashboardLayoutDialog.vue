<!--
 * @Description: 仪表盘 - 添加弹窗
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialog" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="form" :model="form.model" :rules="form.rules" label-width="15%">
      <el-form-item v-if="hasName" :label="$t('visualization.dashboard.dialog.name')" prop="name">
        <el-input
          v-model="form.model.name"
          maxlength="16"
          show-word-limit
          class="width-small"
          @blur="form.model.name = $event.target.value.trim()"
        ></el-input>
      </el-form-item>
      <el-form-item :label="$t('visualization.dashboard.dialog.layout')">
        <el-row v-for="(row, rowIndex) in layout.setting" :key="rowIndex" class="width-small layout-row">
          <el-col :span="14">
            <el-col
              v-for="(col, colIndex) in row.layout"
              :key="colIndex"
              :span="col.span"
              :offset="col.offset"
              class="layout-col"
              :class="{ 'layout-col-selected': row.include }"
            >
              {{ col.unit }}
            </el-col>
          </el-col>
          <el-col :span="8">
            <el-form-item label-width="50px" :label="$t('visualization.dashboard.dialog.height')" class="layout-row-height">
              <el-input-number v-model="row.height" :min="300" :max="1200" controls-position="right" class="width-small"></el-input-number>
            </el-form-item>
          </el-col>
          <el-col :span="1" :offset="1">
            <el-button
              :type="row.include ? 'success' : null"
              icon="el-icon-check"
              circle
              size="mini"
              class="layout-row-button"
              @click="changeSelectLayoutState(row)"
            ></el-button>
          </el-col>
        </el-row>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    title: {
      required: true,
      type: String,
    },
    width: {
      type: String,
      default: '600',
    },
    hasName: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      layout: {
        setting: [
          {
            layout: [
              {
                span: 7,
                offset: 0,
                unit: 1,
              },
              {
                span: 7,
                offset: 1,
                unit: 1,
              },
              {
                span: 7,
                offset: 1,
                unit: 1,
              },
            ],
            list: [{ span: 8 }, { span: 8 }, { span: 8 }],
            height: 300,
            include: false,
          },
          {
            layout: [
              {
                span: 7,
                offset: 0,
                unit: 1,
              },
              {
                span: 15,
                offset: 1,
                unit: 2,
              },
            ],
            list: [{ span: 8 }, { span: 16 }],
            height: 300,
            include: false,
          },
          {
            layout: [
              {
                span: 15,
                offset: 0,
                unit: 2,
              },
              {
                span: 7,
                offset: 1,
                unit: 1,
              },
            ],
            list: [{ span: 16 }, { span: 8 }],
            height: 300,
            include: false,
          },
          {
            layout: [
              {
                span: 11,
                offset: 0,
                unit: 1,
              },
              {
                span: 11,
                offset: 1,
                unit: 1,
              },
            ],
            list: [{ span: 12 }, { span: 12 }],
            height: 300,
            include: false,
          },
          {
            layout: [
              {
                span: 23,
                offset: 0,
                unit: 1,
              },
            ],
            list: [{ span: 24 }],
            height: 300,
            include: false,
          },
        ],
      },
      form: {
        model: {
          name: '',
        },
        rules: {
          name: [
            {
              required: true,
              message: this.$t('validate.empty'),
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  watch: {
    visible(visible) {
      this.dialogVisible = visible
    },
    dialogVisible(visible) {
      this.$emit('update:visible', visible)
      if (!visible) {
        this.layout = {
          setting: [
            {
              layout: [
                {
                  span: 7,
                  offset: 0,
                  unit: 1,
                },
                {
                  span: 7,
                  offset: 1,
                  unit: 1,
                },
                {
                  span: 7,
                  offset: 1,
                  unit: 1,
                },
              ],
              list: [{ span: 8 }, { span: 8 }, { span: 8 }],
              height: 300,
              include: false,
            },
            {
              layout: [
                {
                  span: 7,
                  offset: 0,
                  unit: 1,
                },
                {
                  span: 15,
                  offset: 1,
                  unit: 2,
                },
              ],
              list: [{ span: 8 }, { span: 16 }],
              height: 300,
              include: false,
            },
            {
              layout: [
                {
                  span: 15,
                  offset: 0,
                  unit: 2,
                },
                {
                  span: 7,
                  offset: 1,
                  unit: 1,
                },
              ],
              list: [{ span: 16 }, { span: 8 }],
              height: 300,
              include: false,
            },
            {
              layout: [
                {
                  span: 11,
                  offset: 0,
                  unit: 1,
                },
                {
                  span: 11,
                  offset: 1,
                  unit: 1,
                },
              ],
              list: [{ span: 12 }, { span: 12 }],
              height: 200,
              include: false,
            },
            {
              layout: [
                {
                  span: 23,
                  offset: 0,
                  unit: 1,
                },
              ],
              list: [{ span: 24 }],
              height: 300,
              include: false,
            },
          ],
        }
      }
    },
  },
  methods: {
    clickCancelDialog() {
      this.$refs.dialog.end()
      this.clearDialogForm()
      this.dialogVisible = false
    },
    clickSubmitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.handleLayoutData()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialog.end()
    },
    changeSelectLayoutState(row) {
      row.include = !row.include
    },
    handleLayoutData() {
      const layoutData = []
      this.layout.setting.forEach((item) => {
        if (item.include) {
          layoutData.push({
            type: 'grid',
            // id: `grid_${Date.parse(new Date())}_${Math.ceil(Math.random() * 99999)}`,
            height: item.height,
            list: item.list,
          })
        }
      })

      if (layoutData.length > 0) {
        this.$emit('on-submit', {
          dashboardName: this.form.model.name,
          dashboardData: layoutData,
        })
        this.clickCancelDialog()
      } else {
        prompt({
          i18nCode: 'visualization.dashboard.dialog.validate.layout',
          type: 'warning',
          print: true,
        })
      }
    },
    clearDialogForm() {
      this.layout.setting.forEach((item) => {
        item.include = false
      })
      this.form.model.name = ''
    },
  },
}
</script>

<style lang="scss" scoped>
.layout-row {
  margin-bottom: 10px;

  .el-button.el-button--success {
    color: #fff !important;
    background-color: #67c23a !important;
    border-color: #67c23a !important;
  }

  .el-button:focus {
    color: #2a73aa;
    background-color: transparent;
  }

  .layout-col {
    text-align: center;
    border-radius: 10px;
    @include theme('background-color', border-color);
  }

  .layout-col-selected {
    color: #fff;
    background-color: #1873d7 !important;
  }
}
</style>
