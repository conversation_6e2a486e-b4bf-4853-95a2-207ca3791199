<template>
  <section class="chart-wrapper" :style="{ height: height + 'px' }">
    <header class="chunk-header">
      <h2 class="chunk-header-title">
        {{ title }}
      </h2>
    </header>
    <section class="chart">
      <line-chart :option="option"></line-chart>
    </section>
  </section>
</template>

<script>
import LineChart from '@comp/ChartFactory/forecast/LineChart'
export default {
  name: 'BaselineChart',
  components: {
    LineChart,
  },
  props: {
    chartData: {
      required: true,
      type: Array,
    },
    height: {
      type: [Number, String],
      default: '250',
    },
    title: {
      type: String,
      default: '',
    },
  },
  computed: {
    option() {
      return {
        type: 'line',
        axis: 'x',
        data: this.chartData,
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.chart-wrapper {
  width: 100%;
  height: 100%;
  .chunk-header {
    display: flex;
    height: 30px;
    padding: 5px 10px;
    justify-content: space-between;
    &-title {
      width: 100%;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      text-align: left;
    }
  }
  .chart {
    width: 100%;
    height: calc(100% - 40px);
  }
}
</style>
