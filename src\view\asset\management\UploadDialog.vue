<!--
 * @Description: 资产发现 - 导入弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form" :rules="rules" label-width="27%">
      <template>
        <el-row>
          <el-col :span="16">
            <el-form-item :label="$t('asset.management.chooseFile')" prop="files">
              <el-upload
                ref="upload"
                v-has="'upload'"
                style="margin-left: 10px"
                class="header-button-upload width-mini"
                action="#"
                :headers="form.header"
                :show-file-list="true"
                :limit="1"
                auto-upload
                accept=".xlsm"
                :file-list="form.files"
                :on-exceed="handleExceed"
                :on-remove="handleRemove"
                :on-change="onUploadFileChange"
                :http-request="submitUploadFile"
                :before-upload="beforeUploadValidate"
                @click="clickUploadTable"
              >
                <el-input suffix-icon="el-icon-folder"></el-input>
              </el-upload>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <div v-debounce="downLoad" class="downText">
              {{ $t('asset.management.downLoad') }}
            </div>
          </el-col>
        </el-row>
        <div>
          <el-row>
            <el-col :span="16">
              <el-form-item prop="importRule">
                <el-radio-group v-model="form.importRule">
                  <el-radio :label="'1'">
                    {{ $t('asset.management.importRule.new') }}
                  </el-radio>
                  <el-radio :label="'2'">
                    {{ $t('asset.management.importRule.old') }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-form-item label-width="18%">
                <span style="color: #F56C6C">
                  {{ form.importRule === '1' ? $t('asset.management.uploadRemind') : $t('asset.management.uploadTalk') }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { downloadAsset } from '@api/asset/management-api'

export default {
  name: 'UploadDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      fileName: '',
      file: {},
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 导入文件改变时调用
    onUploadFileChange(file) {
      this.form.files.push(file)
    },
    // 超出限制提示
    handleExceed() {
      const text = this.$t('asset.management.exceed')
      this.$message.warning(text)
    },
    // 提交导入文件
    submitUploadFile(param) {
      if (param.file && this.form.files.length > 0) {
        this.fileName = this.form.files.map((item) => item.name)
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.file = formData
      }
    },
    handleRemove() {
      this.form.files.splice(0, 1)
    },
    // 导入之前的文件校验
    beforeUploadValidate(file) {
      if (this.form.files.length > 0) {
        const suffix = file.name.substring(file.name.lastIndexOf('.') + 1)
        const isXLSM = suffix === 'xlsm'
        if (!isXLSM) {
          prompt({
            i18nCode: 'tip.upload.typeError',
            type: 'warning',
          })
        }
        return isXLSM
      }
    },
    // 点击上传文件
    clickUploadTable() {
      this.form.files = []
      this.$refs.upload.submit()
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.file.append('importRule', this.form.importRule)
            // 给父级调用数据
            this.$emit('on-submit', this.file)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    // 下载导入模板
    downLoad() {
      downloadAsset().then((res) => {
        const fileName = res.fileName
        if (window.navigator.msSaveOrOpenBlob) {
          window.navigator.msSaveBlob(res.data, fileName)
        } else {
          const blob =
            typeof res.data === 'string' || typeof res.data === 'object' ? new Blob([res.data], { type: 'application/octet-stream' }) : res.data
          const link = document.createElement('a')
          link.href = window.URL.createObjectURL(blob)
          link.download = fileName
          link.click()
          window.URL.revokeObjectURL(link.href)
        }
      })
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-upload-list__item .el-icon-close {
  top: 25%;
}
.downText {
  height: 34px;
  line-height: 34px;
  color: #1873d7;
  cursor: pointer;
  width: 42%;
  text-align: center;
}
</style>
