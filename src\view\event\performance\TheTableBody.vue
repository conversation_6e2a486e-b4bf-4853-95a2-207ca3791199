<!--
 * @Description: 性能事件 - 中部列表
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
        @selection-change="clickSelectRows"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column prop="perfName" :label="$t('event.perf.perfName')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="currentStatus" :label="$t('event.perf.currentStatus')" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ columnText(scope.row.currentStatus, 'currentStatus') }}
          </template>
        </el-table-column>
        <el-table-column prop="perfClassName" :label="$t('event.perf.perfClassName')" show-overflow-tooltip></el-table-column>
        <el-table-column prop="perfLevel" :label="$t('event.perf.perfLevel')" show-overflow-tooltip>
          <template slot-scope="scope">
            <level-tag :level="scope.row.perfLevel"></level-tag>
          </template>
        </el-table-column>
        <el-table-column prop="edName" :label="$t('event.perf.edName')" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="hyper-link-column" @click="clickColumnJump(scope.row.edId, 'edName')">
              {{ scope.row.edName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="monitorName" :label="$t('event.perf.monitorName')" show-overflow-tooltip>
          <template slot-scope="scope">
            <div class="hyper-link-column" @click="clickColumnJump(scope.row.monitorId, 'monitorName')">
              {{ scope.row.monitorName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="updateDate" :label="$t('event.perf.updateDate')" show-overflow-tooltip></el-table-column>
        <el-table-column fixed="right" width="80">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
import levelTag from '@comp/LevelTag'
export default {
  components: {
    levelTag,
  },
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  computed: {
    columnText() {
      return (code, key) => {
        let text = ''
        this.options[key].forEach((item) => {
          if (code === item.value) {
            text = item.label
          }
        })
        return text
      }
    },
  },
  methods: {
    clickSelectRows(select) {
      this.$emit('on-select', select)
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    clickColumnJump(columnKey, prop) {
      let jumpPath = '/asset/management'
      if (prop === 'monitorName') {
        jumpPath = '/monitor/management'
      }
      this.$emit('on-jump', { columnKey: columnKey, path: jumpPath })
    },
  },
}
</script>
<style lang="scss" scoped>
.hyper-link-column {
  color: #409eff;
  cursor: pointer;
}
</style>
