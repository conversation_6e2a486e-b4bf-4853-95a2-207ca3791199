export default {
  abnormalBehavior: {
    title: '异常行为告警',
    label: {
      infoSystemName: '信息系统名称',
      infoSystemIp: '信息系统IP',
      action: '行为名称',
      role: '入侵者',
      occurTime: '发生时间',
      updateTime: '更新时间',
      anomalyType: '异常类型',
      status: '状态',
      total: '数量',
      desc: '描述',
      raw: '日志原文',
      occurStartTime: '发生开始时间',
      occurEndTime: '发生结束时间',
      updateStartTime: '更新开始时间',
      updateEndTime: '更新结束时间',
    },
  },
}
