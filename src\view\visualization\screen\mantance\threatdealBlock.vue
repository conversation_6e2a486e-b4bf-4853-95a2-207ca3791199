<template>
  <div class="onechart">
    <div class="charttitle">
      <!-- <img src="@/asset/image/visualization/mantance/blockbg.png" /> -->
      <span>{{ title }}</span>
    </div>
    <div class="chartcontent" :style="{ display: flexContent ? 'flex' : 'block' }">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Block',
  props: {
    title: {
      type: String,
      default: '',
    },
    direction: {
      type: String,
      default: '',
    },
    flexContent: {
      type: Boolean,
      default: false,
    },
  },
}
</script>
<style scoped>
.onechart {
  border-radius: 6px;
  padding: 8px;
  overflow: hidden;
}
.charttitle {
  color: #ddd;
  font-size: 16px;
  font-weight: 700;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  height: 36px;
  position: relative;
  img {
    position: absolute;
    /* width: 100%; */
    height: 100%;
  }
  span {
    position: relative;
  }
}
.chartcontent {
  height: calc(100% - 42px);
}
</style>
