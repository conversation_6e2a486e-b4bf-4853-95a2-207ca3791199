const { notEmpty } = require('../util.js')

module.exports = {
  description: 'generate a view',
  prompts: [
    {
      type: 'input',
      name: 'module',
      message: 'view module please',
      validate: notEmpty('module'),
    },
    {
      type: 'input',
      name: 'name',
      message: 'view name please',
      validate: notEmpty('name'),
    },
    {
      type: 'checkbox',
      name: 'blocks',
      message: 'Blocks:',
      choices: [
        {
          name: '<template>',
          value: 'template',
          checked: true,
        },
        {
          name: '<script>',
          value: 'script',
          checked: true,
        },
        {
          name: '<style>',
          value: 'style',
          checked: true,
        },
      ],
      validate(value) {
        if (value.indexOf('script') === -1 && value.indexOf('template') === -1) {
          return 'View require at least a <script> or <template> tag.'
        }
        return true
      },
    },
  ],
  actions: (data) => {
    const module = '{{module}}'
    const name = '{{name}}'
    return [
      {
        type: 'add',
        path: `src/view/{{ lowerCase module }}/{{ lowerCase name }}/{{ properCase module }}{{ properCase name }}.vue`,
        templateFile: 'template/view/view.hbs',
        data: {
          module: module,
          name: name,
          template: data.blocks.includes('template'),
          script: data.blocks.includes('script'),
          style: data.blocks.includes('style'),
        },
      },
      {
        type: 'add',
        path: `src/router/module/{{ lowerCase module }}.js`,
        templateFile: 'template/view/router.hbs',
        data: {
          module: module,
          name: name,
        },
      },
      {
        type: 'add',
        path: `src/api/{{ lowerCase module }}/{{ lowerCase name }}-api.js`,
        template: `import request from "@util/request";`,
      },
      {
        type: 'add',
        path: `src/language/SChinese/{{ lowerCase module }}/{{ lowerCase name }}.js`,
        template: `export default { {{ lowerCase name }}: {}}`,
      },
      {
        type: 'add',
        path: `mock/{{ lowerCase module }}/{{ lowerCase name }}-mock.js`,
        template: `module.exports = [];`,
      },
    ]
  },
}
