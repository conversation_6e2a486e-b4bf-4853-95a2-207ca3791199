import Vue from 'vue'
import elInfiniteScroll from '../../../node_modules/element-ui/lib/infinite-scroll'

const elScope = 'ElInfiniteScroll'
const msgTitle = `[el-table-infinite-scroll]: `
const elTableScrollWrapperClass = '.el-table__body-wrapper'

/**
 * @func 同步 el-infinite-scroll 的配置项
 * @param {dom} sourceVNode - VUENode
 * @param {dom} sourceElem - 外层整个el-table，加的属性都在这个外层上面
 * @param {dom} targetElem - 需要绑定无限滚动的 ".el-table__body-wrapper"
 * <AUTHOR> @date 2020/9/18
 */
function asyncElOptions(sourceVNode, sourceElem, targetElem) {
  const context = sourceVNode.context
  // 将infinite-scroll-disabled,infinite-scroll-delay,infinite-scroll-immediate，从el-table 复制到 .el-table__body-wrapper上
  let value
  ;['disabled', 'delay', 'immediate'].forEach((name) => {
    name = 'infinite-scroll-' + name
    value = sourceElem.getAttribute(name)
    if (value !== null) {
      targetElem.setAttribute(name, context[value] || value)
    }
  })
  // fix: windows/chrome 的 scrollTop + clientHeight 与 scrollHeight 不一致的 BUG
  const name = 'infinite-scroll-distance'
  value = sourceElem.getAttribute(name)
  value = context[value] || value
  targetElem.setAttribute(name, value < 1 ? 1 : value)
}

export default {
  inserted(el, binding, vnode, oldVnode) {
    const scrollElem = el.querySelector(elTableScrollWrapperClass)

    if (!scrollElem) {
      console.error(`${msgTitle} 找不到 ${elTableScrollWrapperClass} 容器`)
    }

    // 设置自动滚动
    scrollElem.style.overflowY = 'auto'

    Vue.nextTick(() => {
      if (!el.style.height) {
        scrollElem.style.height = '590px'
      }

      // 同步无限滚动的配置项目
      asyncElOptions(vnode, el, scrollElem)

      // 绑定 infinite-scroll
      elInfiniteScroll.inserted(scrollElem, binding, vnode, oldVnode)

      // 将子集的引用放入 el 上，用于 unbind 中销毁事件
      el[elScope] = scrollElem[elScope]
    })
  },
  update(el, binding, vnode) {
    asyncElOptions(vnode, el, el.querySelector(elTableScrollWrapperClass))
  },
  unbind(el) {
    if (el && el.container) {
      elInfiniteScroll.unbind(el)
    }
  },
}
