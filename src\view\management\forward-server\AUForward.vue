<!--
 * @Description: 转发服务器 - 添加、修改弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <custom-dialog ref="dialogTemplate" :visible="visible" :title="title" :width="width" @on-close="clickCancelDialog" @on-submit="clickSubmitForm">
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="25%">
      <template>
        <el-form-item :label="form.info.type.label" :prop="form.info.type.key">
          <el-select
            v-model="form.model.type"
            :placeholder="$t('management.forwardServer.placeholder.type')"
            class="width-mini"
            clearable
            filterable
            @change="changeType"
          >
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-show="form.show" :label="form.info.community.label" :prop="form.info.community.key">
          <el-input
            v-model.trim="form.model.community"
            :placeholder="$t('management.forwardServer.placeholder.community')"
            class="width-mini"
          ></el-input>
        </el-form-item>
        <el-form-item v-show="form.show" :label="form.info.poid.label" :prop="form.info.poid.key">
          <el-input
            v-model.trim="form.model.poid"
            :placeholder="$t('management.forwardServer.placeholder.poid')"
            class="width-mini"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item v-show="form.show" :label="form.info.coid.label" :prop="form.info.coid.key">
          <el-input
            v-model.trim="form.model.coid"
            :placeholder="$t('management.forwardServer.placeholder.coid')"
            class="width-mini"
            disabled
          ></el-input>
        </el-form-item>
        <el-form-item :label="form.info.reIP.label" :prop="form.info.reIP.key">
          <el-input
            v-model.trim="form.model.reIP"
            :placeholder="$t('management.forwardServer.placeholder.reIP')"
            class="width-mini"
            maxlength="255"
          ></el-input>
        </el-form-item>
        <el-form-item :label="form.info.port.label" :prop="form.info.port.key">
          <el-input-number v-model="form.model.port" :min="0" :max="65535" class="width-mini"></el-input-number>
        </el-form-item>
        <el-form-item v-show="kafkaShow" label="主题" :prop="form.info.community.key">
          <el-input v-model.trim="form.model.community" placeholder="请输入Kafka Topic" class="width-mini"></el-input>
        </el-form-item>
        <el-form-item v-show="kafkaShow" label="用户名" :prop="form.info.poid.key">
          <el-input v-model.trim="form.model.poid" placeholder="请输入用户名" class="width-mini"></el-input>
        </el-form-item>
        <el-form-item v-show="kafkaShow" label="密码" :prop="form.info.coid.key">
          <el-input v-model.trim="form.model.coid" placeholder="请输入密码" class="width-mini" type="password"></el-input>
        </el-form-item>
        <el-form-item :label="form.info.remark.label" :prop="form.info.remark.key">
          <el-input
            v-model.trim="form.model.remark"
            :placeholder="$t('management.forwardServer.placeholder.remark')"
            type="textarea"
            :rows="5"
            :maxlength="30000"
            class="width-mini"
          ></el-input>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'
import { queryType } from '@api/management/forward-server-api'
import { validateIp } from '@util/validate'
export default {
  name: 'AuDialog',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (!validateIp(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }
    const validatorCom = (rule, value, callback) => {
      if (this.form.model.type === '1') {
        callback()
      } else if (this.form.model.type === '3') {
        // Kafka主题校验规则，支持星号(*)
        if (!value) {
          callback(new Error('Kafka主题不能为空'))
        } else if (!/^[a-zA-Z0-9._\-*]+$/.test(value)) {
          callback(new Error('Kafka主题只能包含字母、数字、下划线、点号、连字符和星号(*)'))
        } else if (value.length > 255) {
          callback(new Error('Kafka主题长度不能超过255个字符'))
        } else {
          callback()
        }
      } else {
        if (!value) {
          callback(new Error(this.$t('validate.ip.incorrect')))
        } else {
          callback()
        }
      }
    }
    return {
      options: [],
      dialogVisible: this.visible,
      rules: {
        // 弹出表单的校验规则
        type: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        reIP: [
          {
            required: true,
            validator: validatorIp,
            trigger: 'blur',
          },
        ],
        port: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        community: [
          {
            required: true,
            validator: validatorCom,
            trigger: 'blur',
          },
        ],
      },
      kafkaShow: false,
    }
  },
  watch: {
    visible(nVal) {
      if (!nVal) {
        this.kafkaShow = false
        this.form.show = false
      } else {
        const e = this.form.model.type
        if (String(e) === '2') {
          this.form.show = true
          this.kafkaShow = false
        } else {
          this.form.show = false
          if (String(e) === '3') {
            this.kafkaShow = true
          } else {
            this.kafkaShow = false
          }
        }
      }
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  mounted() {
    this.query()
  },
  methods: {
    // 查询转发方式
    query() {
      queryType().then((res) => {
        if (res) {
          this.options = res
        } else {
          this.options = []
        }
      })
    },
    // 改变转发方式的函数
    changeType(e) {
      if (String(e) === '2') {
        this.form.show = true
        this.kafkaShow = false
        this.form.model.reIP = ''
        this.form.model.remark = ''
        this.form.model.port = '162'
        this.form.model.community = 'PUBLIC'
        this.form.model.coid = '*******.4.1.8886.*******.1.2.1'
        this.form.model.poid = '*******.4.1.8886.2.3.1'
      } else {
        this.form.show = false
        this.form.model.port = '514'
        this.form.model.community = ''
        this.form.model.coid = ''
        this.form.model.poid = ''
        this.form.model.reIP = ''
        this.form.model.remark = ''
        if (String(e) === '3') {
          this.kafkaShow = true
          this.form.model.port = '9092'
        } else {
          this.kafkaShow = false
        }
      }
    },
    // 关闭当前dialog
    clickCancelDialog() {
      this.$nextTick(() => {
        if (this.$refs.formTemplate) this.$refs.formTemplate.resetFields()
      })
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            const formModel = Object.assign({}, this.form.model)
            // 给父级调用数据
            this.$emit('on-submit', formModel, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
  },
}
</script>
