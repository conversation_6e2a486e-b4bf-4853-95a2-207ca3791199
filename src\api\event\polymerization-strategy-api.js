import request from '@util/request'

export function queryStrategyTableData(obj) {
  return request({
    url: '/strategy/aggregated/strategies',
    method: 'get',
    params: obj || {},
  })
}

export function queryRulesData(obj) {
  return request({
    url: '/strategy/aggregated/combo/rules',
    method: 'get',
    params: obj || {},
  })
}

export function querySystemData(obj) {
  return request({
    url: '/strategy/aggregated/combo/forward',
    method: 'get',
    params: obj || {},
  })
}

export function updateStrategyTableData(obj) {
  return request({
    url: '/strategy/aggregated/strategy',
    method: 'put',
    data: obj || {},
  })
}

// 导入关联策略
export function uploadParsePackage(obj) {
  return request(
    {
      url: `/strategy/aggregated/strategy/upload`,
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}
