<!--
 * @Description: 登录首页 - 左侧菜单
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <menu ref="menuContainer" :style="{ width: width + 'px' }">
    <el-menu :collapse="isCollapse" :unique-opened="false" :default-active="defaultMenu" :collapse-transition="false">
      <sub-menu :menu-data="menuData" @on-menu="routerLink"></sub-menu>
    </el-menu>
    <footer ref="menuFooter">
      <i ref="shrinkButton" class="soc-icon-scroller-left" @click="clickShrinkMenu($event)"></i>
      <i ref="stretchButton" class="soc-icon-scroller-right" @click="clickStretchMenu($event)"></i>
    </footer>
  </menu>
</template>

<script>
import SubMenu from './TheSubMenu.vue'

export default {
  components: {
    SubMenu,
  },
  props: {
    menuData: {
      required: true,
      type: Array,
      default() {
        return []
      },
    },
    defaultMenu: {
      required: false,
      type: String || Number,
      default: '',
    },
    width: {
      required: false,
      type: Number,
      default: 240,
    },
    height: {
      required: false,
      type: String || Number,
      default: 'calc(100% - 70px)',
    },
  },
  data() {
    return {
      isCollapse: false,
      location: '',
      shrinkWidth: 74,
      stretchWidth: 180,
      menuWidth: 0,
    }
  },
  watch: {
    menuWidth(nVal) {
      this.$refs.menuContainer.css({
        width: `${nVal}px`,
        transition: '0.4s all',
      })
    },
    height() {
      this.$refs.menuContainer.css({
        height: this.height,
      })
    },
  },
  mounted() {
    this.setStyle()
  },
  methods: {
    setStyle() {
      this.$refs.menuContainer.css({
        height: this.height,
      })
    },
    routerLink(menu) {
      this.$router.push({
        path: menu.menuLocation,
      })
      this.$emit('get-action', menu)
    },
    clickShrinkMenu(evt) {
      this.isCollapse = true
      evt.target.hide()
      this.$refs.stretchButton.show()
      this.menuWidth = this.shrinkWidth
      this.$emit('scale-menu', this.menuWidth)
    },
    clickStretchMenu(evt) {
      this.isCollapse = false
      evt.target.hide()
      this.$refs.shrinkButton.show()
      this.menuWidth = this.stretchWidth
      this.$emit('scale-menu', this.menuWidth)
    },
  },
}
</script>

<style lang="scss" scoped>
menu {
  position: relative;
  width: 220px;
  @include theme('background-color', main-menu-bg-color);

  & > .el-menu[role='menubar'] {
    border: none;

    ::v-deep > li {
      & > div.el-submenu__title {
        @include theme('background-color', main-menu-bar-color);

        &:hover {
          @include theme('color', main-menu-hover-color);
          @include theme('background-color', main-menu-bg-active-color);

          & > i {
            @include theme('color', main-menu-hover-color);
          }
        }
      }

      &.is-active {
        & > div.el-submenu__title {
          @include theme('color', main-menu-hover-color);
          @include theme('background-color', main-menu-bg-active-color);

          & > i {
            @include theme('color', main-menu-hover-color);
          }
        }
      }
    }
  }

  ::v-deep ul.el-menu[role='menu'] {
    i,
    span {
      font-size: 12px;
    }
  }

  ::v-deep .el-submenu {
    .el-menu-item {
      min-width: 160px;
    }
  }

  ::v-deep .el-menu-item {
    &:active,
    &:hover,
    &.is-active {
      color: #fff !important;
      background-color: #0050ad !important;
    }
  }

  ::v-deep .el-menu-item,
  ::v-deep .el-submenu__title {
    height: 36px;
    line-height: 36px;
    font-size: 12px;
    display: flex;
    align-items: center;
    & > i {
      font-size: 14px;
      margin-right: 0px;
    }
    & > span {
      flex-basis: 136px;
      flex-grow: 0;
      flex-shrink: 0;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .el-menu-button {
    position: absolute;
    bottom: 0;
    width: 100%;
  }

  footer {
    height: 56px;
    line-height: 56px;
    text-align: center;

    i.soc-icon-scroller-left,
    i.soc-icon-scroller-right {
      padding: 10px;
      font-size: 20px;
      cursor: pointer;
      @include theme('color', text-color);

      &:hover {
        animation: flash 1s;
      }
    }

    i.soc-icon-scroller-right {
      display: none;
    }
  }
}
</style>
