import request from '@util/request'

// 查询安全事件列表
export function querySecurityEventTableData(obj) {
  return request(
    {
      url: '/event/security/events',
      method: 'get',
      params: obj || {},
    },
    'default',
    '180000'
  )
}

// 查询发生源设备
export function queryFromDeviceData(obj) {
  return request({
    url: '/event/security/combo/asset-types',
    method: 'get',
    params: obj || {},
  })
}

// 查询事件分类
export function queryEventCategoryData(obj) {
  return request({
    url: '/event/security/combo/event-categories',
    method: 'get',
    params: obj || {},
  })
}

// 下载列表数据
export function downloadSecurityEventTableData(obj) {
  return request(
    {
      url: '/event/security/download',
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

// 查询原始日志
export function queryOriginalLog(obj) {
  return request({
    url: '/event/security/original/events',
    method: 'get',
    params: obj || {},
  })
}

// 查询原始日志
export function queryTotal(obj) {
  return request({
    url: `/event/security/events/total`,
    method: 'get',
    params: obj || {},
  })
}

// 查询安全事件详情
export function querySecurityEventDetail(id, time) {
  return request({
    url: `/event/security/event/${id}/${time}`,
    method: 'get',
  })
}

// 查询自定义列
export function queryColumnsData(obj) {
  return request({
    url: '/event/security/columns',
    method: 'get',
    params: obj || {},
  })
}

// 修改自定义列
export function updateColumnsData(obj) {
  return request({
    url: '/event/security/columns',
    method: 'put',
    data: obj || {},
  })
}

// 查询事件下拉框
export function queryEventNames() {
  return request({
    url: '/event/security/combo/event-types',
    method: 'get',
  })
}

// 查询安全事件原始日志总数
export function queryOriginalTotal(obj) {
  return request({
    url: '/event/security/original/events/total',
    method: 'get',
    params: obj || {},
  })
}
