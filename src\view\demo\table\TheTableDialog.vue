<template>
  <!--自定义dialog-->
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="title"
    :width="width"
    :action="action"
    @on-close="clickCancelDialog"
    @on-submit="clickSubmitForm"
  >
    <!--主表单-->
    <el-form ref="formTemplate" :model="form.model" :rules="rules" label-width="25%">
      <template v-if="readonly">
        <el-form-item v-for="(item, index) in form.info" :key="index" :label="item.label" :prop="item.key">
          {{ form.model[item.key] }}
        </el-form-item>
      </template>
      <template v-else>
        <el-form-item v-for="(item, index) in form.info" :key="index" :label="item.label" :prop="item.key">
          <el-input v-model="form.model[item.key]" class="width-small"></el-input>
        </el-form-item>
      </template>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  name: 'DemoQuery',
  components: {
    CustomDialog,
  },
  props: {
    // dialog是否为展示状态
    visible: {
      required: true,
      type: Boolean,
    },
    // dialog展示标题
    title: {
      required: true,
      type: String,
    },
    // dialog展示标题
    width: {
      type: String,
      default: '600',
    },
    // dialog底部功能是否存在
    action: {
      type: Boolean,
      default: true,
    },
    // dialog是否只读
    readonly: {
      type: Boolean,
      default: false,
    },
    // dialog表单信息
    form: {
      required: true,
      type: Object,
    },
    // dialog表单是否需要校验
    validate: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
    }
  },
  computed: {
    rules() {
      return this.validate ? this.form.rules : null
    },
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    // 关闭当前dialog
    clickCancelDialog() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    // 点击提交表单
    clickSubmitForm() {
      this.$refs.formTemplate.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.handleFormInfo()
            // 给父级调用数据
            this.$emit('on-submit', this.form.model, this.form.info)
            this.clickCancelDialog()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogTemplate.end()
    },
    // 将绑定信息同步到formInfo中，供以后查询组件使用
    handleFormInfo() {
      Object.getOwnPropertyNames(this.form.model).forEach((modelKey) => {
        Object.getOwnPropertyNames(this.form.info).forEach((item) => {
          if (this.form.info[item]['key'] === modelKey) {
            this.form.info[item]['value'] = this.form.model[modelKey]
          }
        })
      })
    },
  },
}
</script>
