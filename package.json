{"name": "magica", "version": "2.0.0", "description": "", "author": "UD", "private": true, "license": "MIT", "scripts": {"dev": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service serve --open", "build:prod": "set NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src mock template", "prettier": "prettier -c --write \"**/**.{js,jsx,tsx,ts,less,md,json,vue,scss,css}\""}, "dependencies": {"animated-number-vue": "^1.0.0", "axios": "^0.19.2", "core-js": "^3.6.5", "echarts": "^5.1.1", "echarts-gl": "^2.0.4", "element-ui": "2.13.2", "font-awesome": "^4.7.0", "js-cookie": "2.2.0", "jsencrypt": "^3.0.0-rc.1", "nprogress": "^0.2.0", "simplemde": "^1.11.2", "v-scale-screen": "^1.0.0", "vue": "^2.6.11", "vue-fragment": "^1.5.1", "vue-i18n": "^8.18.2", "vue-router": "3.0.2", "vue2-ace-editor": "^0.0.15", "vuedraggable": "^2.23.2", "vuex": "^3.4.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-plugin-unit-jest": "~4.4.0", "@vue/cli-plugin-vuex": "~4.4.0", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/test-utils": "^1.0.3", "babel-eslint": "10.1.0", "babel-jest": "23.6.0", "babel-plugin-dynamic-import-node": "2.3.3", "body-parser": "^1.19.0", "chalk": "2.4.2", "chokidar": "^3.4.1", "connect": "3.6.6", "crypto": "^1.0.1", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "filemanager-webpack-plugin": "^2.0.5", "html-webpack-plugin": "3.2.0", "mockjs": "^1.1.0", "plop": "2.3.0", "prettier": "^1.19.1", "runjs": "4.3.2", "sass": "1.77.6", "sass-loader": "^8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "serve-static": "1.13.2", "vue-template-compiler": "^2.7.14"}, "engines": {"node": ">= 14.17.0", "npm": ">= 6.14.13"}, "browserslist": ["> 1%", "last 2 versions"]}