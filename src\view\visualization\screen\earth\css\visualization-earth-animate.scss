@mixin animate($name, $delay: 0s, $duration: 30s, $mode: linear, $iteration: infinite) {
  animation: $name $duration $mode $delay $iteration;
}

.earth-inner-ring,
.earth-outer-ring {
  transform-origin: 50% calc(50% - 66px);
}

.earth-inner-ring {
  @include animate(earth-inner-ring-rotate);
}

.earth-outer-ring {
  @include animate(earth-outer-ring-rotate);
}

.line-chart-title,
.bar-chart-title,
.radar-chart-title {
  @include animate(title-fade, 1s);
}

.line-chart-bg {
  @include animate(line-bg-width, 1s);
}

.line-chart-canvas {
  @include animate(line-canvas-fade, 2s);
}

.pie-chart {
  @include animate(pie-fade, 1s);
}

.pie-chart-bg {
  @include animate(pie-bg-width, 1s);
}

.pie-chart-first,
.pie-chart-second,
.pie-chart-third {
  @include animate(pie-fade, 2s);
}

.bar-chart-decorate {
  @include animate(bar-decorate-rotate);
}

.bar-chart-bg {
  @include animate(bar-bg-width, 1s);
}

.bar-chart-canvas {
  @include animate(bar-canvas-fade, 2s);
}

.radar-chart-canvas {
  @include animate(radar-canvas-fade, 2s);
}

.radar-chart-canvas-bg {
  @include animate(radar-canvas-bg-rotate, 2s);
}

.radar-chart-decorate {
  @include animate(radar-decorate-rotate);
}

.radar-chart-bg {
  @include animate(radar-bg-size, 1s);
  animation-fill-mode: forwards;
}

@keyframes earth-inner-ring-rotate {
  0%,
  86.67% {
    transform: rotate(0deg);
  }
  91.67%,
  95% {
    transform: rotate(120deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes earth-outer-ring-rotate {
  0%,
  10%,
  20%,
  40%,
  60%,
  80%,
  100% {
    opacity: 1;
  }
  30%,
  50%,
  70% {
    opacity: 0;
  }

  0%,
  86.67% {
    transform: rotate(0deg);
  }
  91.67%,
  95% {
    transform: rotate(-120deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes title-fade {
  0%,
  86.67% {
    opacity: 1;
  }
  91.67%,
  95% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes line-bg-width {
  0%,
  86.67% {
    width: 119px;
  }
  91.67%,
  95% {
    width: 0;
  }
  100% {
    width: 119px;
  }
}

@keyframes line-canvas-fade {
  0%,
  86.67% {
    left: 40px;
    opacity: 1;
  }
  91.67%,
  95% {
    left: -100px;
    opacity: 0;
  }
  100% {
    left: 40px;
    opacity: 1;
  }
}

@keyframes pie-bg-width {
  0%,
  86.67% {
    width: 160px;
  }
  91.67%,
  95% {
    width: 0;
  }
  100% {
    width: 160px;
  }
}

@keyframes pie-fade {
  0%,
  86.67% {
    opacity: 1;
  }
  91.67%,
  95% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes bar-decorate-rotate {
  0%,
  86.67% {
    transform: rotate(0deg);
  }
  91.67%,
  95% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes bar-bg-width {
  0%,
  86.67% {
    width: 407px;
  }
  91.67%,
  95% {
    width: 0;
  }
  100% {
    width: 407px;
  }
}

@keyframes bar-canvas-fade {
  0%,
  86.67% {
    left: 140px;
    opacity: 1;
  }
  91.67%,
  95% {
    left: -100px;
    opacity: 0;
  }
  100% {
    left: 140px;
    opacity: 1;
  }
}

@keyframes radar-canvas-fade {
  0%,
  86.67% {
    opacity: 1;
  }
  91.67%,
  95% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes radar-canvas-bg-rotate {
  0%,
  86.67% {
    transform: rotate(0deg);
  }
  91.67%,
  95% {
    transform: rotate(120deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes radar-decorate-rotate {
  0%,
  86.67% {
    transform: rotate(0deg);
  }
  91.67%,
  95% {
    transform: rotate(360deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

@keyframes radar-bg-size {
  0%,
  86.67% {
    width: 111px;
    height: 114px;
  }
  90% {
    width: 100px;
    height: 60px;
  }
  92% {
    width: 10px;
    height: 60px;
  }
  98% {
    width: 10px;
    height: 0;
  }
  100% {
    width: 111px;
    height: 114px;
  }
}
