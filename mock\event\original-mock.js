const { createMockTable } = require('../util')
const { getURLLastPath } = require('../util')

let columnList = ['eventName', 'targetIp', 'fromIp', 'level', 'levelName', 'sourceIp']

const detail = {
  id: '@GUID',
  eventName: '@CTITLE',
  'eventCategory|1': ['0', '1', '2', '3', '4'],
  'eventCategoryName|1': ['事件类别0', '事件类别1', '事件类别2', '事件类别3', '事件类别4'],
  'eventType|1': ['事件类型0', '事件类型1', '事件类型2', '事件类型3', '事件类型4'],
  'level|1': ['0', '1', '2', '3', '4'],
  'levelName|1': ['严重', '高级', '中级', '低级', '一般'],
  'deviceCategory|1': ['0', '1', '2', '3', '4'],
  'deviceCategoryName|1': ['设备类别0', '设备类别1', '设备类别2', '设备类别3', '设备类别4'],
  'deviceType|1': ['0', '1', '2', '3', '4'],
  'deviceTypeName|1': ['设备类型0', '设备类型1', '设备类型2', '设备类型3', '设备类型4'],
  timestamp: "@DATETIME('T')",
  time: '@DATETIME',
  logTime: '@DATETIME',
  'code|1': ['特征值0', '特征值1', '特征值2', '特征值3', '特征值4'],
  sourceIp: '@IP',
  sourceMac: '@IP',
  'sourcePort|1024-65535': 1024,
  sourceAddress: '@CITY(true)',
  targetIp: '@IP',
  targetMac: '@IP',
  'targetPort|1024-65535': 1024,
  targetAddress: '@CITY(true)',
  fromIp: '@IP',
  sourceCountryId: '@GUID',
  'sourceCountryName|1': ['中国', '美国', '德国', '法国', '日本', '朝鲜', '新加坡', '韩国'],
  'sourceCountryLongitude|0-180.2': 1,
  'sourceCountryLatitude|0-180.2': 1,
  sourceAreaId: '@GUID',
  sourceAreaName: '@CITY(true)',
  'sourceAreaLongitude|0-180.2': 1,
  'sourceAreaLatitude|0-180.2': 1,
  targetCountryId: '@GUID',
  'targetCountryName|1': ['中国', '美国', '德国', '法国', '日本', '朝鲜', '新加坡', '韩国'],
  'targetCountryLongitude|0-180.2': 1,
  'targetCountryLatitude|0-180.2': 1,
  targetAreaId: '@GUID',
  targetAreaName: '@CITY(true)',
  'targetAreaLongitude|0-180.2': 1,
  'targetAreaLatitude|0-180.2': 1,
  protocol: '@PROTOCOL',
  receiveProtocol: '@PROTOCOL',
  year: "@DATETIME('yyyy')",
  month: "@DATETIME('MM')",
  day: "@DATETIME('dd')",
  hour: "@DATETIME('HH')",
  minute: "@DATETIME('mm')",
  second: "@DATETIME('ss')",
  otherIp: '@IP',
  'otherPort|1024-65535': 1024,
  raw: '@SENTENCE',
}

const tempList = () => {
  return createMockTable(detail, 20)
}

const attrs = () => {
  return createMockTable(
    {
      'label|1': '@CTITLE',
      'value|1': '@GUID',
      type: null,
    },
    10
  )
}

const fields = [
  {
    value: 'logName',
    label: 'logName(原始日志名称)',
    type: null,
  },
  {
    value: 'logName.keyword',
    label: 'logName.keyword(原始日志名称)',
    type: null,
  },
  {
    value: 'LEVEL',
    label: 'LEVEL(等级)',
    type: null,
  },
  {
    value: 'LEVEL.keyword',
    label: 'LEVEL.keyword(等级)',
    type: null,
  },
  {
    value: 'dstIP',
    label: 'dstIP(发生源IP)',
    type: null,
  },
  {
    value: 'dstIP.keyword',
    label: 'dstIP.keyword(发生源IP)',
    type: null,
  },
  {
    value: 'srcIP',
    label: 'srcIP(目的IP)',
    type: null,
  },
  {
    value: 'srcIP.keyword',
    label: 'srcIP.keyword(目的IP)',
    type: null,
  },
  {
    value: 'fromIP',
    label: 'fromIP(事件设备IP)',
    type: null,
  },
  {
    value: 'fromIP.keyword',
    label: 'fromIP.keyword(事件设备IP)',
    type: null,
  },
  {
    value: 'deviceName',
    label: 'deviceName(日志源设备)',
    type: null,
  },
  {
    value: 'deviceName.keyword',
    label: 'deviceName.keyword(日志源设备)',
    type: null,
  },
  {
    value: 'receiveTime',
    label: 'receiveTime(时间)',
    type: null,
  },
  {
    value: 'receiveTime.keyword',
    label: 'receiveTime.keyword(时间)',
    type: null,
  },
]

const expressions = createMockTable(
  {
    value: '@ID',
    label: '@CWORD(2, 4)',
    'type|1': [
      'countryIp1.keyword:中国',
      'countryIp2.keyword:美国',
      'countryIp3.keyword:法国',
      'countryIp4.keyword:德国',
      'countryIp5.keyword:英国',
      'countryIp6.keyword:俄国',
      'countryIp7.keyword:意大利',
      'countryIp8.keyword:日本',
      'countryIp9.keyword:韩国',
      'countryIp10.keyword:泰国',
      'countryIp11.keyword:印度',
      'countryIp12.keyword:伊朗',
      'countryIp13.keyword:荷兰',
      'countryIp14.keyword:比利时',
      'countryIp15.keyword:葡萄牙',
    ],
  },
  10
)

const assetType = createMockTable(
  {
    'value|1': ['10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24'],
    'label|1': [
      'IDS/IPS',
      '操作系统',
      '防病毒',
      '防火墙/VPN',
      '隔离设备',
      '加密设备',
      '审计设备',
      '网络设备',
      '应用系统',
      '综合管理',
      '漏洞扫描',
      '流量监控',
      '网页监控',
      '邮件检查',
      '其它',
    ],
    'children|1-10': [
      {
        'type|1': ['10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24'],
        label: '@CWORD(2,6)',
        value: '@ID',
        children: null,
      },
    ],
  },
  10
)

const eventType = [
  { value: '1', label: '事件类型1' },
  { value: '2', label: '事件类型2' },
  { value: '3', label: '事件类型3' },
]

const forwardServer = [
  { value: '1', label: '服务1' },
  { value: '2', label: '服务2' },
  { value: '3', label: '服务3' },
]

// 策略信息
let logForward = {
  id: '1',
  status: '1',
  logFormat: '|sourceIp|?22|targetIp|=89|sourcePort|8090',
  forwardId: '1',
}

const expressionParams = createMockTable(
  {
    value: 't.@WORD(2, 5)',
    label: '@CTITLE',
    symbols: `@SHUFFLE([{"value":"=","label":"="},{"value":"!=","label":"!="},{"value":"like","label":"like"},{"value":"match","label":"match"},{"value":">","label":">"},{"value":">=","label":">="},{"value":"<","label":"<"},{"value":"<=","label":"<="}], 3, 8)`,
    'valueRange|7-13': [
      {
        value: '@INTEGER(1, 100)',
        label: '@CTITLE',
      },
    ],
  },
  20
)

module.exports = [
  {
    url: '/event/original/columns',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: columnList,
      }
    },
  },
  {
    url: '/event/original/columns',
    type: 'put',
    response: (option) => {
      columnList = option.body
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: '/event/original/events/[A-Za-z0-9]',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: detail,
      }
    },
  },
  {
    url: '/event/original/events',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: tempList(),
      }
    },
  },
  {
    url: '/event/original/total',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: /\d{1,7}/,
      }
    },
  },
  {
    url: '/event/original/attributes',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: fields,
      }
    },
  },
  {
    url: '/event/original/attribute',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: attrs(),
      }
    },
  },
  {
    url: '/event/original/expression',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: expressionParams,
      }
    },
  },
  {
    url: '/event/original/expression/[A-Za-z0-9]',
    type: 'delete',
    response: (option) => {
      const id = getURLLastPath(option.url)
      expressions.forEach((item, index) => {
        if (item.value === id) {
          expressions.splice(index, 1)
        }
      })
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/event/original/expression',
    type: 'put',
    response: (option) => {
      const param = option.body
      const temp = {}
      expressions.forEach((item) => {
        if (item.label === param.name) {
          item.type = param.expression
        } else {
          temp.value = new Date().getTime().toString()
          temp.label = param.name
          temp.type = param.expression
        }
      })
      expressions.push(temp)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/event/original/combo/asset-types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: assetType,
      }
    },
  },
  {
    url: '/event/original/combo/event-types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: eventType,
      }
    },
  },
  {
    url: '/event/original/combo/forward-strategies',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: forwardServer,
      }
    },
  },
  {
    url: '/event/original/queryOrilogForwardPolicyById',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: logForward,
      }
    },
  },
  {
    url: '/event/original/updateOrilogForwardPolicy',
    type: 'put',
    response: (option) => {
      logForward = option.body
      return {
        code: 200,
        data: 'success',
      }
    },
  },
  {
    url: '/event/original/consumingTime',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: Math.floor(Math.random() * 1000),
      }
    },
  },
]
