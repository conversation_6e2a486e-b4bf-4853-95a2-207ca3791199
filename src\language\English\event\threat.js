export default {
  threat: {
    title: 'Threat Event',
    domainName: 'Website Name',
    eventIpv: 'Source IP',
    eventTime: 'Alert Time',
    receiveTime: 'Receive Time',
    eventType: 'Event Type',
    eventLevel: 'Event Level',
    eventDesc: 'Event Description',
    startTime: 'Start Alert Time',
    endTime: 'End Alert Time',
    strategy: {
      title: 'Threat Intelligence Strategy Configuration',
      strategySwitch: 'Strategy Enabled',
      forwardService: 'Forward Service',
    },
    detailColumns: {
      type2Name: 'Original Event Name',
      eventName: 'Event Type',
      eventCategoryName: 'Event Category',
      level: 'Event Level',
      srcIp: 'Source IP',
      dstIp: 'Destination IP',
      dateTime: 'Time',
      raw: 'Log Original Text',
    },
    panel: {
      original: 'Original Log Query',
      detail: 'Detail Information',
    },
  },
}
