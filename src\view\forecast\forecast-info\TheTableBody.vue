<!--
 * @Description: 预测信息 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-26
 * @Editor:
 * @EditDate: 2021-10-26
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ tableTitle }}
      </h2>
    </header>
    <main class="table-body-main">
      <el-table
        v-loading="tableLoading"
        :data="tableData"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
      >
        <el-table-column
          v-for="(item, key) in columns"
          :key="key"
          :prop="item"
          :label="$t(`forecast.forecastInfo.label.${item}`)"
          show-overflow-tooltip
        >
          <template slot-scope="scope">
            <p v-if="item === 'type'">
              {{ transformType(scope.row[item]) }}
            </p>
            <p v-else>
              {{ scope.row[item] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column fixed="right" width="80">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetailButton(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
export default {
  props: {
    tableTitle: {
      required: true,
      type: String,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      columns: ['type', 'lableName', 'updateTime'],
    }
  },
  computed: {
    transformType() {
      return (type) => {
        let label = ''
        switch (type) {
          case 'srcIp':
            label = '源IP'
            break
          case 'dstIp':
            label = '目的IP'
            break
          case 'eventType':
            label = '事件类型'
            break
          case 'fromIp':
            label = '采集器IP'
            break
          default:
            label = '总体'
        }
        return label
      }
    },
  },
  methods: {
    clickDetailButton(row) {
      this.$emit('on-detail', row)
    },
  },
}
</script>

<style scoped></style>
