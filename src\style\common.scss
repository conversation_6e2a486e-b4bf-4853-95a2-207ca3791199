/*消除联想输入框背景的黄色*/
input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-transition-delay: 99999s;
  -webkit-transition: color 99999s ease-out, background-color 99999s ease-out, box-shadow 99999s ease-out;
}

.soc-password-icon-style {
  height: 100%;
  font-size: 20px;
  line-height: 100%;
  cursor: pointer;
}

.soc-form-error-text {
  display: inline-block;
  padding-left: 5px;
  font-size: 12px;
  color: #f56c6c;
}

table {
  border: none;

  td {
    border: none;
  }

  th {
    border: none;
  }
}

input,
button {
  background-color: transparent;
}

.router-wrap-table {
  position: relative;
  display: flex;
  flex-direction: column;
  height: 100%;

  .table-header {
    position: relative;
    padding: 5px 0;
    min-height: 50px;
    @include theme('background-color', table-header-bg-color);

    &-main {
      position: relative;
      display: flex;
      justify-content: space-between;

      .table-header-search {
        display: flex;

        &-input {
          padding: 5px 10px 5px 0;
          margin-right: 10px;
          max-height: 32px;
          box-sizing: border-box;
          border-radius: 4px;
          border: 1px solid;
          @include theme('border-color', border-color);
          @include theme('background-color', table-header-search-bg-color);

          .el-input {
            .el-input__prefix,
            el-input__suffix {
              cursor: pointer;
            }

            .el-input__inner {
              width: 300px;
              height: 22px;
              margin-left: 30px;
              padding-left: 10px;
              border: none;
              @include theme('background-color', table-header-bg-color);
            }

            .el-input__icon,
            .el-input__inner {
              line-height: 22px;
              @include theme('color', table-header-search-color);
            }
          }
        }
      }

      .table-header-button {
        .header-button-upload {
          display: inline-block;
          margin-right: 10px;
        }
      }
    }

    &-extend {
      margin: 5px 0 0 0;
      overflow: hidden;

      .el-row {
        @include seniorQuery;
        .el-select,
        .el-date-editor,
        .el-cascader,
        .el-input-number,
        .range-picker,
        .el-input,
        .el-autocomplete {
          width: 100%;
        }
        .el-input__inner {
          background-color: #fff;
        }
      }
    }
  }

  .table-body {
    display: flex;
    flex-direction: column;
    overflow: auto;
    flex: 1;

    &-header {
      display: flex;
      padding: 10px 0;
      height: 50px;
      justify-content: space-between;
      box-sizing: border-box;
      border-bottom: 1px solid;
      @include theme('border-color', table-body-title-border-color);

      .table-body-title {
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        font-weight: 600;
        @include theme('color', table-body-title-color);
      }

      .table-body-button {
        .header-button-upload {
          display: inline-block;
          margin-right: 10px;
        }
      }

      & > b {
        height: 30px;
        line-height: 30px;
        padding-left: 10px;
        font-weight: 600;
        @include theme('color', table-body-title-color);
      }
    }

    &-main {
      height: calc(100% - 54px);
    }

    .col-inline-block {
      display: inline-block;
    }
  }

  .table-footer {
    margin-top: 2px;
    min-height: 30px;
    overflow: auto;

    &.infinite-scroll {
      display: flex;
      margin-right: 30px;
      @include theme('color', table-body-title-color);

      .infinite-scroll-nomore {
        flex: 1;
        text-align: center;
      }

      .infinite-scroll-total {
        min-width: 50px;
      }
    }
  }
}

.router-wrap-columns {
  display: flex;
  height: 100%;

  .router-wrap-columns-left {
    width: 290px;
    height: 100%;
    overflow: auto;
    margin-right: 10px;
  }

  .router-wrap-columns-right {
    flex: 1;
    width: calc(100% - 300px);
    height: 100%;
  }
}

.width-max {
  width: 100% !important;
}

.width-small {
  width: 90% !important;

  .el-input__inner {
    width: 100% !important;
  }
}

.width-mini {
  width: 80% !important;
}

.width-half {
  width: 50% !important;
}

.context-center {
  text-align: center;
}

.soc-icon-search {
  cursor: pointer;
}

.el-select-nopop {
  display: none;
}
