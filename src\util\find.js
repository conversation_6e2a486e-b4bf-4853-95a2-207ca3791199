/**
 * @func 根据某个key的值获取该树形结构的路径
 * @param {string} value - 树形结构已知的值
 * @param {string} key - 树形结构已知值的key
 * @param {array} arr - 树形结构数据
 * @param {boolean} isPath - 返回值是否是所有路径，还是当前key的路径
 * @param {string} configKey - 返回当前设置的retKey的值
 * @return {array}
 * <AUTHOR> @date 2019/08/10
 */
export function getTreePath(value, key, arr, isPath = false, configKey) {
  const recursiveAllPath = []
  try {
    const getNodePath = (node) => {
      recursiveAllPath.push(node)
      if (node[key] === value) {
        throw recursiveAllPath
      }

      if (node.children && node.children.length > 0) {
        node.children.forEach((itemChild) => {
          getNodePath(itemChild)
        })
        // 当前节点的子节点遍历完依旧没找到，则删除路径中的该节点
        recursiveAllPath.pop()
      } else {
        // 找到叶子节点时，删除路径当中的该叶子节点
        recursiveAllPath.pop()
      }
    }

    // 遍历查找
    arr.forEach((item) => {
      getNodePath(item)
    })
  } catch (data) {
    const recursivePath = []
    let retData = []
    data.forEach((item) => {
      configKey ? recursivePath.push(item[configKey]) : recursivePath.push(item[key])
    })
    isPath ? (retData = recursivePath) : (retData = data)
    return retData
  }
}

/**
 * @func 获取浏览器url最后一个"/"后面的值
 * @param {string} url - 传入的URL的值
 * @return {string}
 * <AUTHOR> @date 2020/4/28
 */
export function getURLLastPath(url) {
  const site = url.lastIndexOf('/')
  return url.substring(site + 1, url.length)
}

/**
 * @func 递归获取树形结构的第一个子节点
 * @param {array} tree - 树形数组
 * @return {array}
 * <AUTHOR> @date 2019/09/15
 */
export function getFirstNode(tree) {
  const temp = []
  const recursiveFindNode = (arr) => {
    if (arr && arr.length > 0) {
      temp.push(arr[0])
      if (arr[0].children) {
        recursiveFindNode(arr[0].children)
      }
    }
  }
  recursiveFindNode(tree)
  return temp
}

/**
 * @func 递归查找一个节点的所有父节点
 * @param {array} tree - 树形数组
 * @param {string} key - 通过该值查找所有父节点
 * @return {array}
 * <AUTHOR> @date 2019/09/15
 */
export function getClosestNode(tree, key) {
  const temp = []
  const recursiveFindNode = (arr, key) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.id === key) {
        temp.push(item)
        recursiveFindNode(tree, item.pid)
        break
      } else {
        if (item.children) {
          recursiveFindNode(item.children, key)
        }
      }
    }
  }
  recursiveFindNode(tree, key)
  return temp
}

/**
 * @func 合并并返回require.context函数带入的文件内容
 * @param {function} context - require.context函数的返回值
 * @return {object} 返回匹配文件夹内容的文件，key为文件名，value为文件内容
 * @example getFolderContext(require.context("./src", true, /\.js$/)) 该返回值为src下所有有关js的文件
 * <AUTHOR> @date 2020/7/17
 */
export function getFolderContext(context) {
  return context.keys().reduce((module, modulePath) => {
    const moduleName = modulePath.replace(/^\.\/(.*)\.\w+$/, '$1')
    const value = context(modulePath)
    if (value.default) {
      module[moduleName] = value.default
    }
    return module
  }, {})
}
