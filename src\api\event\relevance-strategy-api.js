import request from '@util/request'

export function getRelevanceTableData(obj) {
  return request({
    url: '/strategy/associated/strategies',
    method: 'get',
    params: obj || {},
  })
}

export function getRelationType() {
  return request({
    url: '/strategy/associated/tree/alarm-type',
    method: 'get',
  })
}

export function getDeviceClass() {
  return request({
    url: '/strategy/associated/combo/source-device-categories',
    method: 'get',
  })
}

export function getDeviceType() {
  return request({
    url: '/strategy/associated/combo/source-device-types',
    method: 'get',
  })
}

export function getEventClass() {
  return request({
    url: '/strategy/associated/combo/alarm-categories',
    method: 'get',
  })
}

export function getEventType() {
  return request({
    url: '/strategy/associated/combo/alarm-types',
    method: 'get',
  })
}

export function getKeys() {
  return request({
    url: '/strategy/associated/combo/keywords',
    method: 'get',
  })
}

export function getSystems() {
  return request({
    url: '/strategy/associated/combo/forward-relay-way',
    method: 'get',
  })
}

export function updateRelevanceData(obj) {
  return request({
    url: '/strategy/associated/strategy',
    method: 'put',
    data: obj || {},
  })
}

export function addRelevanceData(obj) {
  return request({
    url: '/strategy/associated/strategy',
    method: 'post',
    data: obj || {},
  })
}

export function deleteRelevance(id) {
  return request({
    url: `/strategy/associated/strategy/${id}`,
    method: 'delete',
  })
}

export function updateStrategyStatus(id, status) {
  return request({
    url: `/strategy/associated/strategy/${id}/${status}`,
    method: 'put',
  })
}

export function queryRelevanceStrategyDetail(obj) {
  return request({
    url: `/strategy/associated/strategies/details`,
    method: 'get',
    params: obj || {},
  })
}

// 下载关联策略导入模板
export function downloadRelevanceStrategy(obj) {
  return request(
    {
      url: `/strategy/associated/download/template`,
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

// 导入关联策略
export function uploadRelevanceStrategy(obj) {
  return request(
    {
      url: `/strategy/associated/upload`,
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

// 导出关联策略
export function exportRelevanceStrategy(obj) {
  return request(
    {
      url: `/strategy/associated/download`,
      method: 'get',
      params: obj || {},
    },
    'download'
  )
}

export function copyRelevanceStrategy(obj) {
  return request({
    url: '/strategy/associated/copy',
    method: 'post',
    data: obj || {},
  })
}
