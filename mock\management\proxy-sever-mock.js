const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    agentId: '@ID',
    ip: '@IP',
    'status|1': [0, 1],
    'operationState|1': [0, 1],
    updateTime: '@DATETIME',
    metrics: JSON.stringify({
      cpu: {
        cpuNum: 8, // 核心数
        sys: 1.72, // CPU系统使用率
        used: 1.55, // CPU用户使用率
        wait: 0.0, // CPU当前等待率
        free: 96.73, // CPU当前空闲率
      },
      mem: {
        total: 7.81, // 内存总量  GB
        used: 6.36, // 已用内存
        free: 1.45, // 剩余内存
        usage: 81.41, // 使用率
      },
      jvm: {
        total: 113.5,
        max: 1778.0,
        free: 84.73,
        version: '1.8.0_221',
        home: 'E:\\Eclipse\\EclipseForJDK8NGSOC\\jdk1.8.0_221\\jre',
        name: 'Java HotSpot(TM) 64-Bit Server VM',
        startTime: '2021-07-13 15:08:16',
        usage: 25.34,
        runTime: '0天0小时0分钟',
        used: 28.77,
      },
      sys: {
        computerName: 'NS21103037870A', // 服务器名称
        computerIp: '***********',
        userDir: 'E:\\1-Reload\\Log_Probe_3.0\\soc.monitor.agent',
        osName: 'Windows 10', // 操作系统
        osArch: 'amd64', // 系统架构
        agentVersion: '1.1.0', //版本号
      },
      sysFiles: [
        {
          dirName: 'C:\\', // 盘符路径
          sysTypeName: 'NTFS', // 盘符类型
          typeName: '本地固定磁盘 (C:)', // 文件类型
          total: '99.3 GB', // 总大小
          free: '39.5 GB', // 剩余大小
          used: '59.9 GB', // 已经使用量
          usage: 60.27, // 使用率
        },
        {
          dirName: 'D:\\',
          sysTypeName: 'NTFS',
          typeName: '本地固定磁盘 (D:)',
          total: '183.0 GB',
          free: '13.0 GB',
          used: '170.0 GB',
          usage: 92.9,
        },
        {
          dirName: 'E:\\',
          sysTypeName: 'NTFS',
          typeName: '本地固定磁盘 (E:)',
          total: '182.3 GB',
          free: '65.2 GB',
          used: '117.1 GB',
          usage: 64.23,
        },
      ],
    }),
  },
})

const historyDetectTable = new TableMock({
  total: 200,
  template: {
    agentId: '@ID',
    ip: '@IP',
    'status|1': [0, 1],
    updateTime: '@DATETIME',
    metrics: JSON.stringify({
      cpu: {
        cpuNum: 8, // 核心数
        sys: 1.72, // CPU系统使用率
        used: 1.55, // CPU用户使用率
        wait: 0.0, // CPU当前等待率
        free: 96.73, // CPU当前空闲率
      },
      mem: {
        total: 7.81, // 内存总量  GB
        used: 6.36, // 已用内存
        free: 1.45, // 剩余内存
        usage: 81.41, // 使用率
      },
      jvm: {
        total: 113.5,
        max: 1778.0,
        free: 84.73,
        version: '1.8.0_221',
        home: 'E:\\Eclipse\\EclipseForJDK8NGSOC\\jdk1.8.0_221\\jre',
        name: 'Java HotSpot(TM) 64-Bit Server VM',
        startTime: '2021-07-13 15:08:16',
        usage: 25.34,
        runTime: '0天0小时0分钟',
        used: 28.77,
      },
      sys: {
        computerName: 'NS21103037870A', // 服务器名称
        computerIp: '***********',
        userDir: 'E:\\1-Reload\\Log_Probe_3.0\\soc.monitor.agent',
        osName: 'Windows 10', // 操作系统
        osArch: 'amd64', // 系统架构
      },
      sysFiles: [
        {
          dirName: 'C:\\', // 盘符路径
          sysTypeName: 'NTFS', // 盘符类型
          typeName: '本地固定磁盘 (C:)', // 文件类型
          total: '99.3 GB', // 总大小
          free: '39.5 GB', // 剩余大小
          used: '59.9 GB', // 已经使用量
          usage: 60.27, // 使用率
        },
        {
          dirName: 'D:\\',
          sysTypeName: 'NTFS',
          typeName: '本地固定磁盘 (D:)',
          total: '183.0 GB',
          free: '13.0 GB',
          used: '170.0 GB',
          usage: 92.9,
        },
        {
          dirName: 'E:\\',
          sysTypeName: 'NTFS',
          typeName: '本地固定磁盘 (E:)',
          total: '182.3 GB',
          free: '65.2 GB',
          used: '117.1 GB',
          usage: 64.23,
        },
      ],
    }),
  },
})

// 网络相关
const networkData = {
  networkConfig: [
    {
      id: 'ens192',
      ipAddress: '************',
      maskCode: '*************',
      bits: 21,
      name: 'LAN1(管理口)',
      token: false,
      dns1: '',
      dns2: '',
      gateWay: '*************',
    },
  ],
}

const panelData = [
  {
    id: 'ens192',
    name: 'LAN1',
    state: 1,
    row: 192,
    column: null,
  },
]

module.exports = [
  {
    url: '/agentmanagement/agents',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/collector/management/oneKeyDetection/history',
    type: 'get',
    response: (option) => {
      historyDetectTable.query(option)
      return {
        code: 200,
        data: historyDetectTable.getMockData(),
      }
    },
  },
  {
    url: `/agentmanagement/agents/[A-Za-z0-9]`,
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: 2,
      }
    },
  },
  {
    url: `/agentmanagement/configuration/[A-Za-z0-9]`,
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: networkData,
      }
    },
  },
  {
    url: `/agentmanagement/panel/[A-Za-z0-9]`,
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: panelData,
      }
    },
  },
]
