<!--
 * @Description: 监控器
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-26
 * @Editor:
 * @EditDate: 2021-07-26
-->
<template>
  <div class="router-wrap-table">
    <table-header
      :condition.sync="query"
      :monitor-type-option="monitorTypeOption"
      @on-change="changeQueryTable"
      @on-add="clickAddButton"
      @on-command="handleCommand"
    ></table-header>
    <table-body
      ref="monitorTable"
      :title="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :monitor-type-option="monitorTypeOption"
      @on-node="handleNodeClick"
      @on-select="selectsChange"
      @on-detail="clickDetailButton"
      @on-update="clickUpdateButton"
      @on-delete="clickDeleteButton"
      @toggle-status="toggleMontorStatus"
      @on-view="clickViewButton"
    ></table-body>
    <table-footer :pagination.sync="pagination" @on-change-size="tableSizeChange" @on-current="tableCurrentChange"></table-footer>
    <add-dialog :visible.sync="dialog.add.visible" :title-name="title" @on-submit="addSubmit"></add-dialog>
    <upd-dialog :visible.sync="dialog.upd.visible" :title-name="title" :model="dialog.upd.model" @on-submit="updSubmit"></upd-dialog>
    <detail-dialog ref="detailRef" :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model"></detail-dialog>
    <view-dialog :visible.sync="dialog.view.visible" :title-name="title" :model="dialog.view.model"></view-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import AddDialog from './TheAddDialog'
import UpdDialog from './TheUpdateDialog'
import DetailDialog from './TheDetailDialog'
import ViewDialog from './TheViewDialog'
import { prompt } from '@util/prompt'
import { queryMonitorType, queryMonitor, deleteMonitor, stopMonitor, startMonitor, addMonitor, updateMonitor } from '@api/monitor/management-api'
import { isEmpty } from '@util/common'

export default {
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    AddDialog,
    UpdDialog,
    DetailDialog,
    ViewDialog,
  },
  data() {
    return {
      title: this.$t('monitor.management.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          monitorName: '',
          agentId: '',
          monitorType: '',
          monitorEnabled: '',
          monitorClass: '',
          monitorId: '',
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      monitorTypeOption: [],
      dialog: {
        add: {
          visible: false,
        },
        upd: {
          visible: false,
          model: {},
        },
        detail: {
          visible: false,
          model: {},
        },
        view: {
          visible: false,
          model: {},
        },
      },
    }
  },
  watch: {
    $route: {
      handler(route) {
        this.pagination.pageNum = 1
        this.query.form.monitorId = route.query.drillKey
        if (!isEmpty(route.query.drillKey)) {
          const params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            monitorId: route.query.drillKey,
          }
          this.queryTableData(params)
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.initLoadData()
  },
  methods: {
    initLoadData() {
      this.getMonitorType()
      if (Object.keys(this.$route.query).length === 0 || this.$route.query.drillKey === '') {
        this.queryTableData()
      }
    },
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        let monitorType = this.query.form.monitorType
        if (Array.isArray(monitorType) && monitorType.length > 0) {
          monitorType = monitorType[1]
        } else {
          monitorType = ''
        }
        params = Object.assign(params, {
          monitorName: this.query.form.monitorName,
          agentId: this.query.form.agentId,
          monitorType: monitorType,
          monitorEnabled: this.query.form.monitorEnabled,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    selectsChange(sel) {
      this.table.selected = sel
    },
    handleNodeClick(data) {
      this.clearQuery()
      if (data.children) {
        this.query.form.monitorClass = data.value
      } else {
        this.query.form.monitorType = data.value
      }
      const params = Object.assign(
        {},
        {
          monitorClass: this.query.form.monitorClass,
          monitorType: this.query.form.monitorType,
          pageSize: this.pagination.pageSize,
          pageNum: 1,
        }
      )
      this.queryTableData(params)
    },
    clearQuery() {
      this.query.form = {
        fuzzyField: '',
        monitorName: '',
        agentId: '',
        monitorType: '',
        monitorEnabled: '',
        monitorClass: '',
      }
    },
    clickAddButton() {
      this.dialog.add.visible = true
    },
    addSubmit(formModel) {
      const params = Object.assign({}, formModel)
      this.addMonitor(params)
    },
    clickUpdateButton(row) {
      row.monitorTypeValue = [row.monitorClass, row.monitorType]
      row.assetId = [row.edDeviceClass, row.edDeviceType, row.edId]
      this.dialog.upd.model = Object.assign({}, row)
      this.dialog.upd.visible = true
    },
    updSubmit(formModel) {
      const params = Object.assign({}, formModel)
      this.updateMonitor(params)
    },
    clickDeleteButton(row) {
      if (row.monitorEnabled === '1') {
        prompt({
          i18nCode: 'tip.start.deletePromt',
          type: 'error',
        })
        return
      }
      this.deleteMonitor(row.monitorId)
    },
    clickDetailButton(row) {
      this.dialog.detail.model = Object.assign({}, row)
      this.dialog.detail.visible = true
    },
    handleCommand(command) {
      switch (command) {
        case 'stop':
          this.clickBatchStop()
          break
        case 'delete':
          this.clickBatchDelete()
          break
      }
    },
    clickBatchStop() {
      if (this.table.selected.length > 0) {
        const monitorEnabled = this.table.selected.map((item) => item.monitorEnabled).toString()
        if (monitorEnabled.indexOf('0') > -1) {
          prompt({
            i18nCode: 'tip.stop.existStop',
            type: 'error',
          })
          return
        }
        const ids = this.table.selected.map((item) => item.monitorId).toString()
        this.$confirm(this.$t('tip.confirm.batchStop'), this.$t('tip.confirm.tip'), {
          closeOnClickModal: false,
        }).then(() => {
          this.stopMonitor(ids)
        })
      } else {
        prompt({
          i18nCode: 'tip.stop.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    clickBatchDelete() {
      if (this.table.selected.length > 0) {
        const monitorEnabled = this.table.selected.map((item) => item.monitorEnabled).toString()
        if (monitorEnabled.indexOf('1') > -1) {
          prompt({
            i18nCode: 'tip.start.deletePromt',
            type: 'error',
          })
          return
        }
        const ids = this.table.selected.map((item) => item.monitorId).toString()
        this.deleteMonitor(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    toggleMontorStatus(row) {
      if (row.monitorEnabled === '1') {
        this.startMonitor(row.monitorId)
      } else {
        this.stopMonitor(row.monitorId)
      }
    },
    clickViewButton(row) {
      this.dialog.view.model = row
      this.dialog.view.visible = true
    },
    getMonitorType() {
      queryMonitorType().then((res) => {
        this.monitorTypeOption = res
      })
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.pagination.visible = false
      this.table.loading = true
      queryMonitor(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
        this.$nextTick(() => {
          this.$refs.monitorTable.doLayout()
        })
      })
    },
    deleteMonitor(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteMonitor(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.clearQuery()
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.table.data.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    stopMonitor(ids) {
      stopMonitor(ids).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.stop.success',
              type: 'success',
            },
            () => {
              const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
              if (idArray.length === this.table.data.length) {
                this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
              }
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.stop.error',
            type: 'error',
          })
        }
      })
    },
    startMonitor(ids) {
      startMonitor(ids).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.start.success',
              type: 'success',
            },
            () => {
              const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
              if (idArray.length === this.table.data.length) {
                this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
              }
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.start.error',
            type: 'error',
          })
        }
      })
    },
    addMonitor(obj) {
      addMonitor(obj).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else if (res === 'existName') {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else if (res === 'OverMonitorCount') {
          prompt({
            i18nCode: 'tip.add.licenseLimit',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    updateMonitor(obj) {
      updateMonitor(obj).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else if (res === 'existName') {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'warning',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
