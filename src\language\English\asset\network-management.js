export default {
  networkManagement: {
    name: 'Name',
    domainName: 'IP Type',
    access: 'Access Control',
    remark: 'Description',
    startIP: 'Start IP',
    endIP: 'End IP',
    allIpv: 'IP Range',
    net: 'Network Management',
    placeholder: {
      queryInput: 'Please enter keyword to search',
      name: 'Name',
      Ipv: 'IP Address',
      remark: 'Description',
    },
    update: {
      ipRepeat: 'IP address range overlaps, please re-enter',
      aroundError: 'IP range error, end IP should be greater than start IP',
      ipAround: 'IP range cannot exceed 120!',
      error: 'Update failed',
      nameRepeat: 'Name already exists',
    },
  },
}
