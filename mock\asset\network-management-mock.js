const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    allIpv: '***********~************',
    createTime: '2020-11-23 14:12:30',
    domainName: 'IPv4',
    endIp: '179320852',
    endIpv: '************',
    id: '@ID',
    name: 'test56.1',
    pageNum: 0,
    pageSize: 0,
    remark: '',
    startIp: '179320833',
    startIpv: '***********',
  },
})

module.exports = [
  {
    url: '/netmanagement/net',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/netmanagement/net/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/netmanagement/net',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/netmanagement/nets',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
]
