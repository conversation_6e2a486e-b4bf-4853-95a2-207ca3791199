export default {
  custom: {
    validate: {
      yes: '是',
      no: '否',
    },
    noDictionary: '无字典',
    custom: '自定义资产属性',
    attributeId: '属性ID',
    attributeName: '属性名称',
    assetClass: '资产类型',
    newDic: '新增字典',
    assetClassName: '资产一级分类',
    assetTypeName: '资产二级分类',
    remark: '描述',
    controlType: '控件类型',
    dictionary: '资产字典',
    attributeLength: '文本长度',
    checkType: '是否多选',
    reqType: '是否必填',
    gridType: '列表展示',
    dicName: '字典名称',
    placeholder: {
      inputVal: '属性名称/描述',
      attributeName: '属性名称',
      assetClass: '资产类型',
      controlType: '控件类型',
      attributeLength: '文本长度',
      reqType: '是否必填',
      checkType: '是否多选',
      remark: '描述',
      dicName: '字典名称',
    },
    control: {
      text: '文本控件',
      select: '下拉控件',
      timer: '时间控件',
      textarea: '文本域控件',
      radio: '单选控件',
      checkBox: '多选控件',
    },
  },
}
