<!--
 * @Description: 系统升级
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div
    v-loading.fullscreen="loading"
    :element-loading-text="$t('management.systemUpgrade.loadingText')"
    element-loading-spinner="el-icon-loading"
    element-loading-background="rgba(0, 0, 0, 0.8)"
    class="router-wrap-table"
  >
    <header class="header">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-tag effect="dark">{{ $t('management.systemUpgrade.edition') }}：{{ data.edition || $t('management.systemUpgrade.noEdition') }}</el-tag>
        </el-col>
      </el-row>
      <div class="second">
        <div class="label">{{ $t('management.systemUpgrade.bag') }}:</div>
        <div class="upload">
          <el-upload
            ref="upload"
            v-has="'upload'"
            class="header-button-upload"
            action="#"
            :headers="upload.header"
            auto-upload
            :show-file-list="true"
            :limit="1"
            accept=".zip"
            :file-list="upload.files"
            :on-exceed="handleExceed"
            :on-change="onUploadFileChange"
            :http-request="submitUploadFile"
            :on-remove="handleRemove"
            :before-upload="beforeUploadValidate"
            @click="clickUploadTable"
          >
            <el-input
              style="cursor: pointer"
              :placeholder="$t('management.systemUpgrade.bag')"
              suffix-icon="el-icon-folder-opened"
              disabled
            ></el-input>
          </el-upload>
        </div>
        <div class="up">
          <el-button v-has="'upload'" type="primary" @click="clickUp">
            {{ $t('management.systemUpgrade.upGrade') }}
          </el-button>
        </div>
        <div class="back">
          <el-button v-has="'update'" v-debounce="clickBack" type="primary">
            {{ $t('management.systemUpgrade.backOff') }}
          </el-button>
        </div>
      </div>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('management.systemUpgrade.upManagement') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="Table"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="TableRowChange"
        >
          <el-table-column prop="upgradeContent" sortable :label="$t('management.systemUpgrade.upContent')" show-overflow-tooltip></el-table-column>
          <el-table-column
            prop="previousVersion"
            sortable
            :label="$t('management.systemUpgrade.upOldEdition')"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column
            prop="currentVersion"
            sortable
            :label="$t('management.systemUpgrade.upNewEdition')"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="upgradeState" sortable :label="$t('management.systemUpgrade.upResult')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="createDate" sortable :label="$t('management.systemUpgrade.upTime')" show-overflow-tooltip></el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
  </div>
</template>
<script>
import { queryTableData, uploadData, queryNowData, backOff } from '@api/management/system-upgrade-api'
import { prompt } from '@util/prompt'

export default {
  name: 'UpGrade',
  components: {},
  data() {
    return {
      loading: false, // 系统升级时的loading
      uploadFile: {},
      upload: {
        header: {
          // 上传头部信息
          'Content-Type': 'multipart/form-data',
        },
        files: [], // 上传文件个数
      },
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        edition: '', // 当前版本
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
      },
    }
  },
  mounted() {
    // 查询列表
    this.getTableData()
    this.getNowData()
  },
  methods: {
    // 查询列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.data.loading = true
      queryTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.data.loading = false
      })
    },
    // 查询当前版本
    getNowData() {
      queryNowData().then((res) => {
        this.data.edition = res
      })
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.getTableData()
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.getTableData()
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 超出限制提示
    handleExceed(files, fileList) {
      const text = this.$t('asset.management.exceed', [files.length, files.length, fileList.length])
      this.$message.warning(text)
    },
    // 上传文件改变时调用
    onUploadFileChange(file) {
      this.upload.files.push(file)
    },
    // 移除上传的文件
    handleRemove(file, fileList) {
      this.upload.files.splice(0, 1)
    },
    // 提交上传
    submitUploadFile(param) {
      if (param.file && this.upload.files.length > 0) {
        const formData = new FormData()
        formData.append('name', 'upload')
        formData.append('file', param.file)
        this.uploadFile = formData
      }
    },
    // 点击升级
    clickUp() {
      if (this.uploadFile && this.upload.files.length > 0) {
        this.uploadTable(this.uploadFile)
      } else {
        prompt({
          i18nCode: 'validate.upload.empty',
          type: 'error',
        })
      }
    },
    // 上传之前的文件校验
    beforeUploadValidate(file) {
      if (this.upload.files.length > 0) {
        // 限制上传文件的类型
        const suffix = file.name.substring(file.name.lastIndexOf('.zip') + 1)
        const isRight = suffix === 'zip'
        if (!isRight) {
          prompt({
            i18nCode: 'tip.upload.typeError',
            type: 'warning',
          })
          return false
        }
      }
      if (this.upload.files.length > 0) {
        // 限制上传文件的大小
        const isLt = file.size / 1024 / 1024 / 4 <= 100
        if (!isLt) {
          const text = this.$t('management.systemUpgrade.fileSize')
          this.$message.error(text)
          return false
        }
      }
      if (this.upload.files.length > 0) {
        const reg = /^patch-\d{1,}to\d{1,}$/
        const fileName = file.name.replace('.zip', '')
        if (reg.test(fileName)) {
          const [firstName, lastName] = fileName.split('to')
          const replaceStr = firstName.replace(/^patch-/, '')
          if (replaceStr === lastName) {
            prompt({
              i18nCode: 'management.systemUpgrade.updatePackageSameError',
              type: 'warning',
            })
            return false
          } else if (replaceStr !== this.data.edition) {
            prompt({
              i18nCode: 'management.systemUpgrade.updatePackageCurrentError',
              type: 'warning',
            })
            return false
          } else if (parseInt(replaceStr) > parseInt(lastName)) {
            prompt({
              i18nCode: 'management.systemUpgrade.updatePackageNameError',
              type: 'warning',
            })
            return false
          }
        } else {
          prompt({
            i18nCode: 'management.systemUpgrade.updatePackageNameError',
            type: 'warning',
          })
          return false
        }
      }
      return true
    },
    // 点击上传文件
    clickUploadTable() {
      this.upload.files = []
      this.$refs.upload.submit()
    },
    // 上传信息
    async uploadTable(formData) {
      this.loading = true
      await uploadData(formData)
        .then((res) => {
          this.loading = false
          if (res === 1) {
            prompt({
              i18nCode: 'tip.upload.successUp',
              type: 'success',
            })
            this.uploadFile = {}
            this.handleRemove()
            this.getTableData()
            this.getNowData()
          } else if (res === 2) {
            prompt({
              i18nCode: 'tip.upload.running',
              type: 'error',
            })
          } else if (res === 3) {
            prompt({
              i18nCode: 'tip.upload.nameErr',
              type: 'error',
            })
          } else if (res === 4) {
            prompt({
              i18nCode: 'tip.upload.baseErr',
              type: 'error',
            })
          } else if (res === 5) {
            prompt({
              i18nCode: 'tip.upload.back.none',
              type: 'error',
            })
          } else if (res === 6) {
            prompt({
              i18nCode: 'tip.upload.contentErr',
              type: 'error',
            })
          } else if (res === -1) {
            prompt({
              i18nCode: 'tip.upload.fail',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
    // 点击回退
    clickBack() {
      this.loading = true
      backOff()
        .then((res) => {
          this.loading = false
          if (res === 1) {
            prompt({
              i18nCode: 'tip.upload.back.success',
              type: 'success',
            })
            this.uploadFile = {}
            this.handleRemove()
            this.getTableData()
            this.getNowData()
          } else if (res === 2) {
            prompt({
              i18nCode: 'tip.upload.running',
              type: 'error',
            })
          } else if (res === 3) {
            prompt({
              i18nCode: 'tip.upload.nameErr',
              type: 'error',
            })
          } else if (res === 4) {
            prompt({
              i18nCode: 'tip.upload.baseErr',
              type: 'error',
            })
          } else if (res === 5) {
            prompt({
              i18nCode: 'tip.upload.back.none',
              type: 'error',
            })
          } else if (res === -1) {
            prompt({
              i18nCode: 'tip.upload.back.error',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
  },
}
</script>
<style scoped lang="scss">
::v-deep .el-input.is-disabled .el-input__inner {
  cursor: pointer;
}

::v-deep .el-input.is-disabled .el-input__icon {
  cursor: pointer;
}

::v-deep .el-upload {
  width: 100%;
}

.header {
  padding: 10px 0 20px 28px;

  .second {
    padding: 10px 10px 10px 6px;
    display: flex;
    height: 60px;
    line-height: 30px;

    div {
      margin-right: 12px;
    }

    .upload {
      width: 300px;
    }
  }
}
</style>
