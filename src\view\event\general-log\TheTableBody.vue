<!--
 * @Description: 通用日志 - 主体列表
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-25
 * @Editor:
 * @EditDate: 2021-11-25
-->
<template>
  <main class="table-body">
    <header class="table-body-header">
      <h2 class="table-body-title">
        {{ titleName }}
      </h2>
      <el-button @click="clickCustomizeButton">
        {{ $t('button.th') }}
      </el-button>
    </header>
    <main class="table-body-main">
      <el-table
        v-if="tableShow"
        v-loading="tableLoading"
        v-el-table-scroll="scrollTable"
        :data="tableData"
        infinite-scroll-disabled="disableScroll"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
        highlight-current-row
        tooltip-effect="light"
        height="100%"
      >
        <el-table-column type="index" show-overflow-tooltip></el-table-column>
        <el-table-column v-for="(item, key) in columns" :key="key" :prop="item" :label="$t(`event.generalLog.label.${item}`)" show-overflow-tooltip>
          <template slot-scope="scope">
            <level-tag v-if="item === 'severity'" :level="scope.row[item]"></level-tag>
            <!--                        <p v-else-if="item==='message' || item==='logMessage'" class="col-white-space">
                            {{ scope.row[item] }}
                        </p>-->
            <p v-else>
              {{ scope.row[item] }}
            </p>
          </template>
        </el-table-column>
        <el-table-column width="120" fixed="right">
          <template slot-scope="scope">
            <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
              {{ $t('button.detail') }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
  </main>
</template>

<script>
import elTableScroll from '@/directive/el-table-scroll'
import LevelTag from '@comp/LevelTag'
export default {
  directives: {
    elTableScroll,
  },
  components: {
    LevelTag,
  },
  props: {
    titleName: {
      required: true,
      type: String,
    },
    tableShow: {
      required: true,
      type: Boolean,
    },
    tableLoading: {
      required: true,
      type: Boolean,
    },
    tableScroll: {
      required: true,
      type: Boolean,
    },
    tableData: {
      required: true,
      type: Array,
    },
    columns: {
      required: true,
      type: Array,
    },
  },
  computed: {
    disableScroll() {
      return this.tableScroll
    },
  },
  methods: {
    scrollTable() {
      this.$emit('on-scroll')
    },
    clickDetail(row) {
      this.$emit('on-detail', row)
    },
    clickCustomizeButton() {
      this.$emit('on-custom')
    },
  },
}
</script>
