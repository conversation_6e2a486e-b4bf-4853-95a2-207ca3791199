import request from '@util/request'

// 上传升级
export function uploadData(obj) {
  return request(
    {
      url: `/system/upgrade`,
      method: 'post',
      data: obj || {},
    },
    'upload',
    '180000'
  )
}

// 查询列表
export function queryTableData(obj) {
  return request({
    url: '/system/upgrade/records',
    method: 'get',
    params: obj || {},
  })
}

// 查询当前版本
export function queryNowData(obj) {
  return request({
    url: '/system/upgrade/version',
    method: 'get',
    params: obj || {},
  })
}

// 回退
export function backOff(obj) {
  return request({
    url: `/system/rollback`,
    method: 'put',
    params: obj || {},
  })
}
