import i18n from '@/language'
import { Message } from 'element-ui'

/**
 * @func 封装提示信息
 * @param {object} option - 弹窗的配置信息
 * @param {function} callbackFn - 回调函数
 * @example
 * <AUTHOR> @date 2020/1/19
 */
export function prompt(option = {}, callbackFn) {
  const defaultOption = {
    i18nCode: 'tip.confirm.tip', // 国际化码  默认为提示
    i18nParam: [], // 国际化码参数
    type: 'info', // 弹窗类型  默认信息框
    center: true, // 是否居中  默认不居中
    duration: 1500, // 弹窗保留时间 默认1.5s
    popup: true, // 是否弹出信息 默认弹出信息
    print: false, // 是否打印信息 默认不打印
    error: '', // 是否打印抛出的异常 默认不打印
  }
  const liveOption = Object.assign(defaultOption, option)

  if (liveOption.popup) {
    const message = liveOption.i18nParam.length > 0 ? i18n.t(liveOption.i18nCode, liveOption.i18nParam) : i18n.t(liveOption.i18nCode)
    Message({
      message: message,
      type: liveOption.type,
      center: liveOption.center,
      duration: liveOption.duration,
    })
  }

  if (liveOption.print) {
    switch (liveOption.type) {
      case 'info':
        console.info(`%c${i18n.t(liveOption.i18nCode)}`, 'color: #909399')
        break
      case 'success':
        console.log(`%c${i18n.t(liveOption.i18nCode)}`, 'color: #67c23a')
        break
      case 'warning':
        console.warn(`%c${i18n.t(liveOption.i18nCode)}`, 'color: #e6a23c')
        break
      case 'error':
        console.error(`%c${i18n.t(liveOption.i18nCode)}`, 'color: #f56c6c')
        break
      default:
        break
    }
  }

  if (liveOption.error !== '') {
    console.error(liveOption.error)
  }

  if (callbackFn) {
    callbackFn()
  }
}

export class Prompt {
  constructor(option, callback) {
    this.i18nCode = typeof option.i18nCode === 'string' ? option.i18nCode : 'tip.confirm.tip'
    this.type = typeof option.type === 'string' ? option.type : 'info'
    this.center = typeof option.center === 'boolean' ? option.center : true
    this.duration = typeof option.duration === 'number' ? option.duration : 1500
    this.popup = typeof option.popup === 'boolean' || typeof option.popup === 'string' ? option.popup : true
    this.print = typeof option.print === 'boolean' ? option.print : false
    this.error = typeof option.error === 'string' ? option.error : ''

    if (callback) {
      callback()
    }

    this.init()
  }

  init() {
    this.showPopup()
    this.printError()
    this.consoleError()
  }

  showPopup() {
    const message = (i18nCode = this.i18nCode, type = this.type, center = this.center, duration = this.duration) => {
      if (document.getElementsByClassName('el-message').length === 0) {
        Message({
          message: i18n.t(i18nCode),
          type: type,
          center: center,
          duration: duration,
        })
      }
    }

    if (this.popup.constructor === Boolean) {
      message()
    }

    if (this.popup.constructor === String) {
      switch (this.popup) {
        case 'interaction':
          message('ajax.interaction.error', 'error')
          this.print = true
          break
        case 'form':
          message('validate.form.warning', 'warning')
          this.print = true
          break
        case 'confirm':
          message('tip.confirm.error', 'warning')
          this.print = true
          this.popup = false
          break
        default:
          break
      }
    }
  }

  printError() {
    if (this.print) {
      switch (this.type) {
        case 'info':
          console.info(`%c${i18n.t(this.i18nCode)}`, 'color: #909399')
          break
        case 'success':
          console.log(`%c${i18n.t(this.i18nCode)}`, 'color: #67c23a')
          break
        case 'warning':
          console.warn(`%c${i18n.t(this.i18nCode)}`, 'color: #e6a23c')
          break
        case 'error':
          console.error(`%c${i18n.t(this.i18nCode)}`, 'color: #f56c6c')
          break
        default:
          break
      }
    }
  }

  consoleError() {
    if (this.error !== '') {
      console.error(this.error)
    }
  }
}

export function unprompt(type, error, fn) {
  switch (type) {
    case 'api':
      new Prompt({
        i18nCode: 'ajax.interaction.error',
        type: 'error',
        duration: 10000,
        center: true,
        print: true,
        error: error || '',
      })
      break
    case 'valid':
      new Prompt(
        {
          i18nCode: 'validate.form.warning',
          type: 'warning',
          center: true,
          print: true,
        },
        fn
      )
      break
    case 'confirm':
      new Prompt({
        i18nCode: 'tip.confirm.cancel',
        type: 'warning',
        popup: false,
        print: true,
        error: error || '',
      })
      break
    default:
      break
  }
}
