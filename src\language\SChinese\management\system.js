export default {
  system: {
    title: {
      email: '邮件服务',
      timing: '校时',
      databaseConfig: '数据库维护配置',
      databaseTable: '数据库维护列表',
      dataBackupTable: '数据备份列表',
      snapBackupConfig: '快照备份设置',
      snapshotBackupTable: '快照备份列表',
    },
    tab: {
      system: '系统配置',
      basic: '基本信息',
      diskSpace: '磁盘空间设置',
      database: '数据库维护',
      license: 'License管理',
      backup: '数据备份',
      snapshot: '系统快照',
      threat: '情报库设置',
      systemAlarm: '系统告警通知',
      webcert: 'WEB证书',
      imageFlow: '镜像流量审计配置',
      desensitization: '原始日志脱敏配置',
      flowGather: '流量采集',
    },
    label: {
      localeLanguage: '系统语言',
      productName: '产品名称',
      productVersion: '产品版本',
      buildVersion: 'build号',
      productModel: '产品型号',
      systemName: '系统名称',
      sshService: 'SSH服务',
      email: '管理员邮件',
      defaultPassword: '默认密码',
      captcha: '启动验证码',
      accountLockEnable: '账号锁定策略',
      accountLockTime: '账号锁定监听时间周期',
      accountLockCnt: '账号锁定监听次数',
      accountAutoUnlockEnable: '账号自动解锁策略',
      accountAutoUnlockTime: '账号自动解锁时间周期',
      systemTimeout: '系统超时时间',
      accountValidEnable: '账号有效期策略',
      passwordOverdueEnable: '密码过期策略',
      passwordOverdueTime: '密码过期时间',
      sendMailServer: '发送邮件服务器',
      receiveMailServer: '接收邮件服务器',
      port: '端口',
      senderAddress: '发件人地址',
      serverAuth: '服务器身份认证',
      user: '用户名',
      password: '密码',
      previousTime: '上次校时时间',
      previousMode: '上次校时方式',
      centerSeverTime: '中心服务器时间',
      configCenterSeverTime: '设置中心服务器时间',
      ntpSeverConfig: 'NTP服务器设置',
      autoValidate: '自动校验功能',
      settingCycle: '设定周期',
      settingTime: '设定时间',
      nextTime: '下次校时时间',
      systemCertification: '系统KEY认证',
      encryption: '加密方式',
      manualTiming: '手动校时',
      autoTiming: '自动校时',
      cleanHistory: '日志磁盘空间清理历史',
      cleanSpace: '清理磁盘空间',
      spaceSurpass: '空间超过',
      used: '已用',
      cleanTime: '清理时间，保留',
      warning: '时发布警告',
      monthData: '月的数据',
      safeguard: '时自动维护',
      dataRetainTime: '数据保留时间',
      time: '时间',
      description: '描述',
      result: '结果',
      license: '上传License文件',
      machineCode: '机器码',
      backupStrategy: '备份策略',
      backupMode: '备份方式',
      ftpBackup: 'FTP备份',
      ip: 'ip地址',
      account: '账号',
      path: '路径',
      defaultBackup: '默认（每天备份）',
      customBackup: '自定义',
      immediateBackup: '立即备份',
      noBackup: '不备份',
      backupWay: '备份方式',
      backupCycle: '备份周期',
      backupTime: '备份时间',
      backupRange: '备份范围',
      timeRange: '时间范围',
      all: '全部',
      passwordComplexity: '密码复杂度',
      upperCase: '大写字母',
      lowerCase: '小写字母',
      number: '数字',
      special: '特殊字符',
      minPasswordLength: '最小密码长度',
      chinese: '中文',
      english: '英文',
      systemLogo: '系统Logo',
      syslogPort: 'syslog端口',
      webPort: 'web端口',
      flowGather: '是否开启流量采集',
      flowGatherNetworkCard: '镜像流量绑定网卡',
      desensitizationSetting: '原始日志脱敏配置',
      desensitizeStr: '敏感字符',
      desensitizeNewStr: '脱敏结果',
      desensitizeStatus: '是否启用',
      desensitizeDesc: '描述',
    },
    tip: {
      localeLanguage: '请选择系统语言',
      systemName: '可对系统名称修改',
      email: '请输入管理员邮箱',
      defaultPassword: '请输入默认密码',
      captcha: '验证码功能开启和关闭',
      test: '您还未通过测试，确定要提交吗？',
      accountLockEnable: '账号锁定功能开启和关闭',
      accountLockTime: '请输入监听时间周期单位为秒',
      accountLockCnt: '请输入监听次数单位为次',
      accountAutoUnlockEnable: '账号自动解锁功能开启和关闭',
      accountAutoUnlockTime: '请输入账号自动解锁时间周期单位为分钟',
      systemTimeoutLockTime: '请输入系统超时锁定时间单位为分钟',
      accountValidEnable: '账号有效期功能开启和关闭',
      passwordOverdueEnable: '密码过期策略功能开启和关闭',
      passwordOverdueTime: '请输入密码过期时间单位为天',
      cleanHistoryMonth: '请填写过去几个月的历史日志数据',
      cleanHistoryDay: '请填写过去几日的历史日志数据',
      cleanSpace: '请填写当磁盘占用率到达多少百分比',
      largeData: '(数据量较大时，不建议使用)',
      manualTime: '请手动设置校验时间',
      autoTime: '请选择自动校时时间',
      passwordComplexity: '请设置密码复杂度',
      minPasswordLength: '请输入最小密码长度',
      flowGather: '是否开启镜像流量审计',
      flowGatherNetworkCard: '请选择镜像流量绑定网卡',
      desensitizeStr: '请输入敏感字符',
      desensitizeNewStr: '请输入脱敏结果',
      desensitizeDesc: '请输入描述',
      webcertP12: '只能上传p12文件',
      webcertPassword: '请输入证书密码',
    },
    button: {
      testEmail: '测试邮件服务',
      testTiming: '测试校时',
    },
    threat: {
      emptyError: '上传错误这是空文件',
      structureError: '上传错误结构非法',
      typeError: '上传错误类型非法',
      nameError: '上传错误名称非法',
      backupCycle: '同步周期',
      backupTime: '同步时间开启',
      cuttingTitle: '情报库升级',
      upload: {
        backupTimeText: '每天零点五分自动更新',
        successUpload: '上传成功',
        Submit: '提交',
        choice: '请选择上传文件',
        title: '上传情报库',
        titleInput: '上传情报库文件',
        uploadText: '正在上传请耐心等待',
      },
    },
    systemAlarm: {
      title: '通知方式',
      isMail: '邮件',
      mailTo: '邮件地址',
      snmpForward: '通知服务器',
      recipientAddress: '收件人地址',
      isSound: '声、光、电',
      isSms: '短信',
      mobileUrl: '网关地址',
      mobileEcName: '企业名称',
      mobileApId: '接口账号',
      mobileSecretKey: '密码',
      mobileMobiles: '手机号码',
      mobileSign: '签名编码',
      mobileAddSerial: '扩展码',
    },
    upload: {
      title: '数据恢复',
      downLoad: '下载模板',
      chooseFile: '请选择文件',
      exceed: '当前限制选择 1 个文件，请删除后再上传',
      remindTip: '当导入资产名称与系统资产名称相同时，以导入数据为准',
      talkTip: '当导入资产名称与系统资产名称相同时，以系统数据为准',
      successUpload: '恢复操作完成，数据恢复后会新增记录提示!',
    },
  },
}
