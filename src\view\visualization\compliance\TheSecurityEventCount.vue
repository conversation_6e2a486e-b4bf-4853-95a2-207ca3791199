<!--
 * @Description: 安全事件类别数量统计
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023/11/14
 * @Editor:
 * @EditDate: 2023/11/14
-->
<template>
  <el-container class="widget" :style="{ height: height + 'px' }">
    <el-header class="widget-header" height="30px">
      <el-col :span="10" class="widget-header-title">
        {{ convert === true ? $t('visualization.compliance.title.securityEventCountTop') : $t('visualization.compliance.title.securityEventCount') }}
      </el-col>
      <el-col align="right">
        <el-select v-model="cycleValue" size="mini" style="width: 116px; margin-right: 8px;" @change="changeCycle">
          <el-option v-for="item in options.cycle" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-link type="primary" :underline="false" @click="clickConvert">
          ･･･
        </el-link>
      </el-col>
    </el-header>
    <el-main v-if="convert === true" class="widget-main">
      <bar-chart ref="barChartDom" :option="option"></bar-chart>
    </el-main>
    <section v-else style="height: 100%;">
      <el-table
        v-loading="table.loading"
        :data="table.data"
        height="98%"
        :row-style="{ height: '0' }"
        :cell-style="{ padding: '5px 0' }"
        element-loading-background="rgba(0, 0, 0, 0.3)"
        size="mini"
      >
        <el-table-column
          align="left"
          prop="label"
          :label="$t(`visualization.compliance.label.logSourceName`)"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column align="right" prop="value" :label="$t(`visualization.compliance.label.count`)" show-overflow-tooltip></el-table-column>
      </el-table>
    </section>
  </el-container>
</template>

<script>
import BarChart from '@comp/ChartFactory/forecast/BarChart'
import elTableScroll from '@/directive/el-table-scroll'
import { querySecurityEventCount, querySecurityEventTable } from '@api/visualization/compliance-api'

export default {
  components: {
    BarChart,
  },
  directives: {
    elTableScroll,
  },
  props: {
    height: {
      type: [Number, String],
      default: '250',
    },
  },
  data() {
    return {
      countData: [],
      cycleValue: 3,
      convert: true,
      table: {
        loading: false,
        data: [],
      },
      options: {
        cycle: [
          { value: 1, label: this.$t('visualization.compliance.cycle.lastDay') },
          { value: 2, label: this.$t('visualization.compliance.cycle.lastWeek') },
          { value: 3, label: this.$t('visualization.compliance.cycle.lastMonth') },
          { value: 4, label: this.$t('visualization.compliance.cycle.lastHalfYear') },
          { value: 5, label: this.$t('visualization.compliance.cycle.lastYear') },
        ],
      },
    }
  },
  computed: {
    option() {
      return {
        type: 'bar',
        axis: 'y',
        data: this.countData,
        axisDefine: {
          xAxis: {
            axisLabel: {
              show: false,
            },
            axisTick: {
              show: false,
            },
          },
        },
      }
    },
  },
  mounted() {
    this.loadData()
  },
  methods: {
    loadData() {
      this.getSecurityEventCount()
      this.getSecurityEventTable()
    },
    changeCycle(value) {
      this.cycleValue = value
      this.getSecurityEventCount()
      this.getSecurityEventTable()
    },
    clickConvert() {
      this.convert = !this.convert
    },
    getSecurityEventCount(
      params = {
        type: this.cycleValue,
      }
    ) {
      querySecurityEventCount(params).then((res) => {
        this.countData = res
      })
    },
    getSecurityEventTable(
      params = {
        type: this.cycleValue,
      }
    ) {
      this.table.loading = true
      querySecurityEventTable(params).then((res) => {
        this.table.data = res
        this.table.loading = false
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.widget {
  &-header {
    .el-link:hover {
      color: #06699c;
    }
    &-title {
      padding-left: 2px !important;
    }
  }
  &-main {
    width: 100%;
    height: calc(100% - 30px);
    padding: 10px;
  }
  ::v-deep .el-table__header-wrapper {
    width: 99%;
  }
}
</style>
