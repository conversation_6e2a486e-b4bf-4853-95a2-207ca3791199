<!--
 * @Description: 日志源设备 - 添加弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2023-02-21
 * @Editor:
 * @EditDate: 2023-02-21
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.add', [title])"
    width="30%"
    @on-close="clickCancel"
    @on-submit="clickSubmit"
  >
    <el-form ref="formDom" :model="model" :rules="rules" label-width="110px">
      <el-form-item prop="typeName" :label="$t('collector.logSource.label.typeName')">
        <el-input v-model="model.typeName" maxlength="32"></el-input>
      </el-form-item>
      <el-form-item prop="categoryId" :label="$t('collector.logSource.label.categoryName')">
        <el-select v-model="model.categoryId" clearable :placeholder="$t('collector.logSource.placeholder.categoryName')">
          <el-option v-for="item in options.category" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="manufact" :label="$t('collector.logSource.label.manufact')">
        <el-select
          v-model="model.manufact"
          filterable
          allow-create
          default-first-option
          :placeholder="$t('collector.logSource.placeholder.manufact')"
        >
          <el-option v-for="item in options.manufact" :key="item.value" :label="item.label" :value="item.label"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="desc" :label="$t('collector.logSource.label.desc')">
        <el-input v-model="model.desc" type="textarea" :rows="4" maxlength="1024"></el-input>
      </el-form-item>
    </el-form>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import { prompt } from '@util/prompt'

export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    model: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      title: this.$t('collector.logSource.title'),
      dialogVisible: this.visible,
      rules: {
        manufact: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        categoryId: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
        typeName: [
          {
            required: true,
            message: this.$t('validate.empty'),
            trigger: 'blur',
          },
        ],
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
    clickSubmit() {
      this.$refs.formDom.validate((valid) => {
        if (valid) {
          this.$confirm(this.$t('tip.confirm.submit'), this.$t('tip.confirm.tip'), {
            closeOnClickModal: false,
          }).then(() => {
            this.model.manufact = this.model.manufact.toString()
            this.$emit('on-submit', this.model)
            this.clickCancel()
          })
        } else {
          prompt(
            {
              i18nCode: 'validate.form.warning',
              type: 'warning',
              print: true,
            },
            () => {
              return false
            }
          )
        }
      })
      this.$refs.dialogDom.end()
    },
  },
}
</script>
