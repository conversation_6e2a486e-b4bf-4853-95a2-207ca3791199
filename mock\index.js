// 参考文档： https://github.com/nuysoft/Mock/wiki
const path = require('path')
const fs = require('fs')
const Mock = require('mockjs')
const { paramToObj, getFolderContext } = require('./util')

if (!require.context) {
  /**
   * @func 创建自己要引入的文件内容
   * @param {string} directory - 要搜索的目录
   * @param {boolean} scanSubDirectories - 是否还搜索其子目录
   * @param {regexp} regExp - 匹配文件的正则表达式
   * @return {function} 写入要引入的文件路径
   * @note - 解决mock不存在webpack中require.context方法
   * @example require.context('./test', false, /\.test\.js$/);
   * <AUTHOR> @date 2020/7/22
   */
  require.context = (directory = '.', scanSubDirectories = false, regExp = /\.[jt]s$/) => {
    const files = {}
    const Module = (file) => require(file)
    const readDirectory = (directory) => {
      fs.readdirSync(directory).forEach((file) => {
        const fullPath = path.resolve(directory, file)
        if (fs.statSync(fullPath).isDirectory()) {
          if (scanSubDirectories) {
            readDirectory(fullPath)
          }
          return
        }
        if (!regExp.test(fullPath)) {
          return
        }
        files[fullPath] = true
      })
    }
    readDirectory(path.resolve(__dirname, directory))
    Module.keys = () => Object.keys(files)
    return Module
  }
}

const mocks = (() => {
  const context = getFolderContext(require.context('./', true, /-mock\.js$/))
  const mock = []
  Reflect.ownKeys(context).forEach((key) => {
    mock.push(...context[key])
  })
  return mock
})()

function mockXHR() {
  //  解决Mock 导致 Cookie 丢失的问题，只有在 XHR.open() 周期时，自定义的 withCredentials 会被挂载，
  //  此时检查是否是未被拦截的 xhr，并挂载自定义的 withCredentials ，无则默认为 false
  Mock.XHR.prototype.proxy_send = Mock.XHR.prototype.send
  Mock.XHR.prototype.send = function() {
    if (this.custom.xhr) {
      this.custom.xhr.withCredentials = this.withCredentials || false

      if (this.responseType) {
        this.custom.xhr.responseType = this.responseType
      }
    }
    this.proxy_send(...arguments)
  }
  const XHRToExpressReqWrap = (respond) => {
    return (options) => {
      let result = null
      if (respond instanceof Function) {
        const { body, type, url } = options
        result = respond({
          method: type,
          body: body instanceof FormData ? null : JSON.parse(body),
          query: paramToObj(url),
          url: url,
        })
      } else {
        result = respond
      }
      return Mock.mock(result)
    }
  }

  for (const api of mocks) {
    Mock.mock(new RegExp(api.url), api.type || 'get', XHRToExpressReqWrap(api.response))
  }
}

module.exports = {
  mocks,
  mockXHR,
}
