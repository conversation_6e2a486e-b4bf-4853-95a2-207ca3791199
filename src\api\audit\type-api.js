import request from '@util/request'

// 增加
export function addData(obj) {
  return request({
    url: '/audittype/type',
    method: 'post',
    data: obj || {},
  })
}

// 删除
export function deleteData(ids) {
  return request({
    url: `/audittype/type/${ids}`,
    method: 'delete',
  })
}

// 修改
export function updateData(obj) {
  return request({
    url: `/audittype/type`,
    method: 'put',
    data: obj || {},
  })
}

// 查询列表
export function queryTableData(obj) {
  return request({
    url: '/audittype/types',
    method: 'get',
    params: obj || {},
  })
}
