export default {
  strategy: {
    header: '采集过滤策略',
    table: {
      policyName: '策略名称',
      level: '日志级别',
      eventTypeName: '事件类型',
      srcIpv: '源IP',
      dstIpv: '目的IP',
      describe: '描述',
      handle: '操作',
    }, // 采集器列表语言国际化
    dialog: {
      title: {
        add: '采集过滤策略添加',
        update: '采集过滤策略修改',
      },
      columns: {
        policyName: '策略名称',
        level: '日志级别',
        eventType: '事件类型',
        srcIpv: '源IP',
        dstIpv: '目的IP',
        describe: '描述',
      },
    },
    placeholder: {
      input: '策略名称',
      policyName: '策略名称',
      level: '日志级别',
      eventType: '事件类型',
      srcStartIP: '源起始IP',
      srcEndIP: '源终止IP',
      dstStartIP: '目的起始IP',
      dstEndIP: '目的终止IP',
    },
    level: {
      none: '暂无风险等级',
    },
  },
}
