<template>
  <div :id="id" ref="lesMiserables" :class="className" :style="{ height: height, width: width }"></div>
</template>

<script>
import * as echarts from 'echarts'
import common from '../mixin/common'

export default {
  mixins: [common],
  props: {
    className: {
      type: String,
      default: 'chart-les',
    },
    id: {
      type: String,
      default: '',
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    mouseEvent: {
      type: Boolean,
      default: false,
    },
    proto: {
      type: Boolean,
      default: false,
    },
    graphData: {
      type: Object,
      default() {
        return {
          title: {
            left: '10px',
            textStyle: {
              fontSize: 12,
            },
            text: '',
          },
          series: [
            {
              nodes: [],
              lines: [],
              categories: [],
            },
          ],
        }
      },
    },
  },
  data() {
    return {
      chart: null,
    }
  },
  watch: {
    graphData: {
      handler(data) {
        this.configChart(data)
      },
      deep: true,
    },
  },
  mounted() {
    this.renderChart()
  },
  beforeDestroy() {
    this.disposeChart()
  },
  methods: {
    renderChart() {
      this.initChart()
      this.configChart()
    },
    initChart(data = this.graphData) {
      this.chart = echarts.init(this.$refs.lesMiserables, this.$store.getters.theme)
    },
    configChart(config = this.graphData) {
      this.chart.showLoading()
      config && Object.keys(config).length > 0 && config.nodes.length > 0 ? this.drawChart(config) : this.empty()
      this.chart.hideLoading()
    },
    drawChart(data) {
      if (this.proto) {
        this.chart.setOption(data, true)
      } else {
        const option = this.chartOptionConfig(data)
        this.chart.setOption(option, true)
        this.chart.on('click', (params) => {
          this.$emit('on-click', params)
        })
      }
    },
    chartOptionConfig(data) {
      const option = {
        backgroundColor: 'transparent',
        title: {
          left: '10px',
          textStyle: {
            fontSize: 12,
          },
          text: data.title ? data.title : '',
        },
        tooltip: {
          show: false,
        },
        legend: {
          data: data.categories || [],
          type: 'scroll',
          orient: 'vertical',
          right: 50,
          top: 10,
          pageButtonItemGap: 5,
        },
        series: [
          {
            name: 'Les Miserables',
            type: 'graph',
            layout: 'circular',
            circular: {
              rotateLabel: false,
            },
            edgeSymbol: ['circle', 'arrow'],
            edgeSymbolSize: [4, 10],
            edgeLabel: {
              fontSize: 20,
            },
            data: data.nodes || [],
            links: data.links || [],
            categories: data.categories || [],
            roam: false,
            label: {
              position: 'right',
              formatter: '{b}',
            },
            lineStyle: {
              color: 'source',
              curveness: 0.3,
            },
          },
        ],
      }

      if (this.mouseEvent) {
        this.chartHasMouseEvent(option)
      }

      return option
    },
    chartHasMouseEvent(option) {
      option.tooltip = Object.assign(option.tooltip, {
        show: true,
        trigger: 'item',
        formatter: (params) => {
          if (Object.keys(params.data).length > 3) {
            const value = params.data.value
            const num = params.data.num
            return `${this.$t('event.originalLes.srcNum')}：${value}` + '</br>' + `${this.$t('event.originalLes.dstNum')}：${num}`
          }
        },
      })
      option.series[0] = Object.assign(option.series[0], {
        roam: true,
        circular: {
          rotateLabel: true,
        },
      })
      return option
    },
    disposeChart() {
      if (!this.chart) {
        return
      }
      this.chart.dispose()
      this.chart = null
    },
  },
}
</script>
