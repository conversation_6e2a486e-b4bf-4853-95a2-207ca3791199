import router from '@/router'
import { getRegisterData, getLoginData, getLoginMailData } from '@api/login/login-api'
import { queryLogoutData } from '@api/layout/layout-api'

const state = {
  token: '',
  publicKey: '',
  aesKey: '',
  systemName: '',
  mode: 'offline',
  userID: '',
  defaultMenu: '',
  homePath: '',
  actions: [],
}

const mutations = {
  SET_TOKEN(state, token) {
    state.token = token
  },
  SET_PUBLIC_KEY(state, publicKey) {
    state.publicKey = publicKey
  },
  SET_AES_KEY(state, aesKey) {
    state.aesKey = aesKey
  },
  SET_SYSTEM_NAME(state, systemName) {
    state.systemName = systemName
  },
  SET_MODE(state, mode) {
    state.mode = mode
  },
  SET_USER_ID(state, id) {
    state.userID = id
  },
  SET_USER_MENU(state, menu) {
    state.defaultMenu = menu
  },
  SET_HOME_PATH: (state, path) => {
    state.homePath = path
  },
  SET_ACTION: (state, action) => {
    state.actions = action
  },
}

const actions = {
  saveMode({ commit }, mode) {
    commit('SET_MODE', mode)
  },
  saveUserID({ commit }, id) {
    commit('SET_USER_ID', id)
  },
  saveUserMenu({ commit }, menu) {
    commit('SET_USER_MENU', menu)
  },
  updatePath({ commit }, path) {
    commit('SET_HOME_PATH', path)
  },
  authAction({ commit }, action) {
    commit('SET_ACTION', action)
  },
  registry({ commit }) {
    return new Promise((resolve, reject) => {
      getRegisterData()
        .then((res) => {
          commit('SET_TOKEN', res.accessToken)
          commit('SET_PUBLIC_KEY', res.publicKey)
          commit('SET_AES_KEY', res.aesKey)
          commit('SET_SYSTEM_NAME', res.systemName)
          resolve(res)
        })
        .catch((error) => {
          reject(error)
        })
    })
  },
  login({ commit }, loginForm) {
    return new Promise((resolve, reject) => {
      getLoginData(loginForm)
        .then((res) => {
          resolve(res)
        })
        .catch((e) => {
          reject(e)
        })
    })
  },
  loginMail({ commit }, loginForm) {
    return new Promise((resolve, reject) => {
      getLoginMailData(loginForm)
        .then((res) => {
          resolve(res)
        })
        .catch((e) => {
          reject(e)
        })
    })
  },
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      queryLogoutData()
        .then((res) => {
          if (res) {
            commit('SET_TOKEN', '')
            commit('SET_PUBLIC_KEY', '')
            commit('SET_AES_KEY', '')
            commit('SET_SYSTEM_NAME', '')
            commit('SET_USER_ID', '')
            commit('SET_MODE', 'offline')
            commit('SET_USER_MENU', '')
            commit('SET_HOME_PATH', '')
            commit('SET_ACTION', '')
            router.replace({
              path: '/login',
            })
            resolve(res)
          }
        })
        .catch((e) => {
          reject(e)
        })
    })
  },
  reset({ commit }) {
    commit('SET_MODE', 'offline')
  },
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
}
