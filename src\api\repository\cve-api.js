import request from '@util/request'

export function queryCveTableData(obj) {
  return request({
    url: '/knowledge/vulnerability/cve',
    method: 'get',
    params: obj || {},
  })
}

export function queryCveDetail(id) {
  return request({
    url: `/knowledge/vulnerability/cve/${id}`,
    method: 'get',
  })
}

export function queryTotal(param) {
  return request({
    url: `/knowledge/vulnerability/cve/total`,
    method: 'get',
    params: param || {},
  })
}
