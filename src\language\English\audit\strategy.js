export default {
  strategy: {
    strategy: 'Audit Strategy',
    orderNumber: 'Sequence',
    safeEvent: 'Security Event',
    linkEvent: 'Related Event',
    threatEvent: 'Threat Event',
    day: 'Daily',
    week: 'Week',
    dater: 'Date',
    policyName: 'Strategy Name',
    outputEventRemark: 'Strategy Description',
    systemOwn: {
      title: 'System Built-in',
      own: 'Built-in',
      write: 'Custom',
    },
    state: 'Usage Status',
    states: {
      on: 'Enable',
      off: 'Disable',
    },
    date: 'Time Period',
    eventType: 'Event',
    dateType: 'Audit Valid Time Period',
    startTime: 'Start Time',
    startDate: 'Start Date',
    endDate: 'End Date',
    endTime: 'End Time',
    weekStart: 'Week Start',
    weekEnd: 'Week End',
    outputEventName: 'Audit Event Name',
    outputEventLevel: 'Audit Event Level',
    flag: 'Audit Event Type',
    outputEventType: 'Audit Type',
    auditUser: 'Auditor',
    forwardSystemId: 'Forward to External System',
    forwardAudit: 'Forward Content',
    forwardAuditEvent: 'Audit Event',
    placeholder: {
      placeholder: 'Please select',
      inputVal: 'Please enter keyword to search',
      policyName: 'Strategy Name',
      state: 'Usage Status',
      eventTypeList: 'Event',
      outputEventName: 'Audit Event Name',
      outputEventLevel: 'Event Level',
      outputEventType: 'Audit Type',
      auditUser: 'Auditor',
      outputEventRemark: 'Strategy Description',
      forwardSystemId: 'Forward to External System',
      time: 'Week Start Day',
    },
    weekList: {
      mon: 'Monday',
      tue: 'Tuesday',
      wed: 'Wednesday',
      thu: 'Thursday',
      fri: 'Friday',
      sat: 'Saturday',
      sun: 'Sunday',
    },
  },
}
