<!--
 * @Description: 聚合策略
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('event.polymerizationStrategy.table.alarmName')])"
              clearable
              prefix-icon="soc-icon-search"
              @keyup.enter.native="pageQuery('e')"
              @change="pageQuery('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="pageQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <!--                <section class="table-header-button">
                    <el-button
                        v-has="'upload'"
                        @click="clickParsePackageUpload">
                        {{ $t("button.packageUpload") }}
                    </el-button>
                </section>-->
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="4">
                <el-input
                  v-model.trim="query.seniorQuery.alarmName"
                  :placeholder="$t('event.polymerizationStrategy.table.alarmName')"
                  clearable
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="8" class="multi-item">
                <p>{{ this.$t('event.polymerizationStrategy.label.alarmTimeout') }}</p>
                <p>
                  <el-input-number
                    v-model="query.seniorQuery.alarmStartTimeout"
                    controls-position="right"
                    :min="0"
                    :max="2147483647"
                    @change="pageQuery('Timeout')"
                  ></el-input-number>
                </p>
                <p>—</p>
                <p>
                  <el-input-number
                    v-model="query.seniorQuery.alarmEndTimeout"
                    controls-position="right"
                    :min="0"
                    :max="2147483647"
                    @change="pageQuery('Timeout')"
                  ></el-input-number>
                </p>
              </el-col>
              <el-col :span="8" class="multi-item">
                <p>{{ this.$t('event.polymerizationStrategy.label.countThreshold') }}</p>
                <p>
                  <el-input-number
                    v-model="query.seniorQuery.countStartThreshold"
                    controls-position="right"
                    :min="0"
                    :max="2147483647"
                    @change="pageQuery('Threshold')"
                  ></el-input-number>
                </p>
                <p>—</p>
                <p>
                  <el-input-number
                    v-model="query.seniorQuery.countEndThreshold"
                    controls-position="right"
                    :min="0"
                    :max="2147483647"
                    @change="pageQuery('Threshold')"
                  ></el-input-number>
                </p>
              </el-col>
              <el-col align="right" :offset="0" :span="4">
                <el-button v-has="'query'" @click="pageQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQueryForm">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体内容-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('event.polymerizationStrategy.strategy') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
        >
          <el-table-column
            v-for="(item, index) in options.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`event.polymerizationStrategy.table.${item}`)"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="matchCriteriaList" :label="$t('event.polymerizationStrategy.table.matchCriteriaList')" show-overflow-tooltip>
            <template slot-scope="scope">
              {{ rules(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="80">
            <template slot-scope="scope">
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateButton(scope.row)">
                {{ $t('button.update') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <!--分页组件-->
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page.sync="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size.sync="pagination.pageSize"
        :total="pagination.total"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
    <!--修改框-->
    <update-dialog
      :visible.sync="dialog.updateDialog.visible"
      :title="dialog.updateDialog.title"
      :form="dialog.updateDialog.form"
      :rules-option="options.rulesOption"
      :forward-option="options.forwardOption"
      @on-submit="clickSubmitUpdate"
    ></update-dialog>
    <upload-dialog
      :visible.sync="dialog.upload.visible"
      :title="title"
      :form="dialog.upload"
      :width="'35%'"
      @on-submit="clickSubmitPackageUpload"
    ></upload-dialog>
  </div>
</template>

<script>
import updateDialog from './EventPolymerizationStrategyUpdateDialog'
import uploadDialog from './TheUploadDialog'
import { prompt } from '@util/prompt'
import { debounce } from '@/util/effect'
import {
  queryStrategyTableData,
  queryRulesData,
  querySystemData,
  updateStrategyTableData,
  uploadParsePackage,
} from '@api/event/polymerization-strategy-api'

export default {
  name: 'AggregationStrategy',
  components: {
    updateDialog,
    uploadDialog,
  },
  data() {
    return {
      title: this.$t('event.polymerizationStrategy.strategy'),
      data: {
        loading: false,
        table: [],
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
      },
      show: {
        seniorQueryShow: false,
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      query: {
        fuzzyField: '',
        seniorQuery: {
          alarmName: '',
          alarmStartTimeout: '',
          alarmEndTimeout: '',
          countStartThreshold: '',
          countEndThreshold: '',
        },
      },
      options: {
        rulesOption: [],
        forwardOption: [],
        columnOption: ['alarmName', 'alarmTimeout', 'countThreshold'],
      },
      dialog: {
        updateDialog: {
          visible: false,
          title: this.$t('event.polymerizationStrategy.title.update'),
          form: {
            model: {
              alarmId: '',
              alarmName: '',
              matchCriteriaList: '',
              alarmTimeout: '',
              countThreshold: '',
              forwardTypeList: [],
            },
            info: {
              alarmName: {
                key: 'alarmName',
                label: this.$t('event.polymerizationStrategy.table.alarmName'),
              },
              matchCriteriaList: {
                key: 'matchCriteriaList',
                label: this.$t('event.polymerizationStrategy.table.matchCriteriaList'),
              },
              alarmTimeout: {
                key: 'alarmTimeout',
                label: this.$t('event.polymerizationStrategy.label.alarmTimeout'),
              },
              countThreshold: {
                key: 'countThreshold',
                label: this.$t('event.polymerizationStrategy.label.countThreshold'),
              },
              forwardTypeList: {
                key: 'forwardTypeList',
                label: this.$t('event.polymerizationStrategy.label.forwardType'),
              },
            },
            rules: {
              alarmName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              matchCriteriaList: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              alarmTimeout: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              countThreshold: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
            },
          },
        },
        upload: {
          visible: false,
          loading: false,
          header: {
            'Content-Type': 'multipart/form-data',
          },
          files: [],
          templateName: 'script.jar', // 导入模板名称
          rules: {
            files: [
              {
                required: true,
                message: this.$t('validate.choose'),
                trigger: 'change',
              },
            ],
          },
        },
      },
    }
  },
  computed: {
    rules() {
      return (row) => {
        const ruleName = []
        const rulesOption = this.options.rulesOption
        for (const item of row.matchCriteriaList) {
          for (const items of rulesOption) {
            if (item === items['value']) {
              ruleName.push(items['label'])
            }
          }
        }
        return ruleName.toString()
      }
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    // 页面初始化方法
    init() {
      this.queryRulesOption()
      this.queryStrategyTable()
      this.querySystemOption()
      this.initDebounce()
    },
    // 初始化防抖函数
    initDebounce() {
      this.data.debounce.query = debounce(() => {
        let params = {}
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.seniorQuery, {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            alarmEndTimeout: this.query.seniorQuery.alarmEndTimeout === 0 ? undefined : this.query.seniorQuery.alarmEndTimeout,
            alarmStartTimeout: this.query.seniorQuery.alarmStartTimeout === 0 ? undefined : this.query.seniorQuery.alarmStartTimeout,
            countEndThreshold: this.query.seniorQuery.countEndThreshold === 0 ? undefined : this.query.seniorQuery.countEndThreshold,
            countStartThreshold: this.query.seniorQuery.countStartThreshold === 0 ? undefined : this.query.seniorQuery.countStartThreshold,
          })
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            inputVal: this.query.fuzzyField,
          }
        }
        this.queryStrategyTable(params)
      }, 500)
      // 重置查询防抖初始化
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.query.fuzzyField = ''
        this.query.seniorQuery = {
          alarmName: '',
          alarmStartTimeout: '',
          alarmEndTimeout: '',
          countStartThreshold: '',
          countEndThreshold: '',
        }
        this.pagination.pageNum = 1
        setTimeout(() => {
          this.queryStrategyTable()
        }, 150)
      }, 500)
    },
    pageQuery(flag) {
      if (flag) this.pagination.pageNum = 1
      if (this.show.seniorQueryShow) {
        let queryFlag = 0
        const { alarmEndTimeout, alarmStartTimeout, countEndThreshold, countStartThreshold } = this.query.seniorQuery
        if (alarmStartTimeout === 0 && alarmEndTimeout === 0 && countStartThreshold === 0 && countEndThreshold === 0) {
          this.data.debounce.query()
          queryFlag = 1
        }
        if (queryFlag === 0) {
          if (flag === 'Threshold') {
            if (countEndThreshold === undefined) {
              this.data.debounce.query()
            } else if (countEndThreshold === 0 && countStartThreshold === 0) {
              this.data.debounce.query()
            } else {
              if (countStartThreshold > countEndThreshold || countStartThreshold === countEndThreshold) {
                prompt({
                  i18nCode: 'event.polymerizationStrategy.tip.countThreshold',
                  type: 'warning',
                })
                return false
              } else {
                this.data.debounce.query()
              }
            }
          } else if (flag === 'Timeout') {
            if (alarmEndTimeout === undefined) {
              this.data.debounce.query()
            } else if (alarmStartTimeout === 0 && alarmEndTimeout === 0) {
              this.data.debounce.query()
            } else {
              if (alarmStartTimeout > alarmEndTimeout || alarmStartTimeout === alarmEndTimeout) {
                prompt({
                  i18nCode: 'event.polymerizationStrategy.tip.alarmTimeout',
                  type: 'warning',
                })
                return false
              } else {
                this.data.debounce.query()
              }
            }
          } else {
            this.data.debounce.query()
          }
        }
      } else {
        this.data.debounce.query()
      }
    },
    // 查询事件关联策略
    queryStrategyTable(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.data.loading = true
      this.pagination.visible = false
      queryStrategyTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 查询匹配规则
    queryRulesOption() {
      queryRulesData().then((res) => {
        this.options.rulesOption = res
      })
    },
    // 查询转发外系统
    querySystemOption() {
      querySystemData().then((res) => {
        this.options.forwardOption = res
      })
    },
    // 点击向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetQueryForm()
      this.initDebounce()
    },
    // 点击查询按钮
    clickQueryButton() {
      this.query.fuzzyField = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.resetQueryForm()
      this.initDebounce()
    },
    clickParsePackageUpload() {
      this.dialog.upload.files = []
      this.dialog.upload.visible = true
    },
    // 点击修改按钮
    clickUpdateButton(row) {
      this.dialog.updateDialog.visible = true
      queryStrategyTableData({ alarmId: row.alarmId }).then((res) => {
        this.dialog.updateDialog.form.model = res[0]
      })
    },
    // 提交修改
    clickSubmitUpdate(formModel) {
      const updateModel = this.dialog.updateDialog.form.model
      const param = Object.assign(
        {},
        {
          alarmId: updateModel.alarmId,
          alarmName: updateModel.alarmName,
          alarmTimeout: updateModel.alarmTimeout,
          countThreshold: updateModel.countThreshold,
          eventFrequency: updateModel.eventFrequency,
          matchCriteriaList: updateModel.matchCriteriaList,
          minLimit: updateModel.minLimit,
          minThreshold: updateModel.minThreshold,
          matchCriteria: formModel.matchCriteriaList.length !== 0 ? formModel.matchCriteriaList.toString() : '',
          forwardType: formModel.forwardTypeList.length !== 0 ? formModel.forwardTypeList.toString() : '',
        }
      )
      updateStrategyTableData(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.pageQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 修改size
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.data.debounce.query()
    },
    // 修改pageNumber
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.data.debounce.query()
    },
    // 重置查询条件
    resetQueryForm() {
      this.data.debounce.resetQueryDebounce()
    },
    clickSubmitPackageUpload(formData) {
      this.dialog.upload.loading = true
      uploadParsePackage(formData)
        .then((res) => {
          this.dialog.upload.loading = false
          if (res === 1) {
            prompt({
              i18nCode: 'event.polymerizationStrategy.upload.successUpload',
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.import.error',
              type: 'error',
            })
          }
        })
        .catch((e) => {
          console.error(e)
        })
    },
  },
}
</script>

<style lang="scss" scoped>
.multi-item {
  display: flex;
  direction: ltr;
  > p {
    margin-right: 10px;
    white-space: nowrap;
    line-height: 32px;
    text-align: center;
    @include theme('color', table-header-search-color);
  }
}
</style>
