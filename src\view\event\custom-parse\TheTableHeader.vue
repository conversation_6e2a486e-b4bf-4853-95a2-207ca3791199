<!--
 * @Description: 自定义解析 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-12-31
 * @Editor:
 * @EditDate: 2022-12-31
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('event.customParse.placeholder.fuzzyField')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'add'" @click="clickAdd">
          {{ $t('button.add') }}
        </el-button>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-input
                v-model.trim="filterCondition.form.patternValue"
                clearable
                :placeholder="$t('event.customParse.placeholder.patternValue')"
                @change="changeQueryCondition"
              ></el-input>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.patternKey"
                clearable
                multiple
                collapse-tags
                :placeholder="$t('event.customParse.placeholder.patternKey')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.multiGroup" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.status"
                clearable
                :placeholder="$t('event.customParse.placeholder.status')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.status" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.devType"
                clearable
                filterable
                :placeholder="$t('event.customParse.placeholder.devType')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.devType" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col align="right" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
    options: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
    }
  },
  watch: {
    condition(nVal) {
      this.filterCondition = nVal
    },
    filterCondition(nVal) {
      this.$emit('update:condition', nVal)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        patternValue: '',
        patternKey: '',
        status: '',
        devType: '',
      }
      this.changeQueryCondition()
    },
    clickAdd() {
      this.$emit('on-add')
    },
  },
}
</script>
