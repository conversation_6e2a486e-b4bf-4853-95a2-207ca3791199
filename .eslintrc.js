// 参考文档： https://eslint.org/docs/user-guide/configuring
module.exports = {
  root: true,
  parserOptions: {
    parser: 'babel-eslint',
    sourceType: 'module',
  },
  env: {
    browser: true,
    node: true,
    es6: true,
  },
  extends: ['plugin:vue/recommended', 'prettier', 'eslint:recommended'],
  /**
   * off/0：关闭规则
   * warn/1：开启规则，使用警告级别的错误
   * error/2：开启规则，使用错误级别的错误
   */
  rules: {
    'vue/no-unused-vars': 'off',
    'vue/no-v-text-v-html-on-component': 'off',
    'vue/require-directive': 'off',
    'vue/no-mutating-props': 'off',
    'no-prototype-builtins': 'off',
    'no-debugger': 'off',
    'no-irregular-whitespace': 'off',
    'sonarjs/no-duplicate-string': 'off',
    'vue/script-setup-uses-vars': 'off',
    'vue/no-reserved-component-names': 'off',
    'vue/no-template-shadow': 'off',
    'vue/custom-event-name-casing': 'off',
    'no-use-before-define': 'off',
    'no-unused-vars': 'off',
    'space-before-function-paren': 'off',
    'no-useless-escape': 0,
    'vue/attributes-order': 'off',
    'vue/one-component-per-file': 'off',
    'vue/html-closing-bracket-newline': 'off',
    'vue/max-attributes-per-line': 'off',
    'vue/multiline-html-element-content-newline': 'off',
    'vue/singleline-html-element-content-newline': 'off',
    'vue/attribute-hyphenation': 'off',
    'vue/require-default-prop': 'off',
    'vue/require-explicit-emits': 'off',
    'sonarjs/cognitive-complexity': 'off',
    'vue/html-self-closing': 'off',
    'vue/multi-word-component-names': 'off',
    'vue/no-v-html': 'off',
  },
  overrides: [
    {
      files: ['**/*.vue', '**/*.js', '**/*.css', '**/*.json'],
      rules: {
        indent: 'off',
      },
    },
  ],
}
