<!--
 * @Description: 代理服务器 - 详细弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-16
 * @Editor:
 * @EditDate: 2021-07-16
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane :label="$t('management.proxy.detail.basic.title')" name="current">
          <el-form :model="detailModel" label-width="120px">
            <el-row>
              <el-divider content-position="left">{{ $t('management.proxy.detail.basic.title') }}</el-divider>
              <el-col v-for="(item, index) in columnOption.basic" :key="index" :span="8">
                <el-form-item :prop="item" :label="$t(`management.proxy.label.${item}`)">
                  <span v-if="item === 'status'">
                    {{ detailModel[item] === 1 ? $t('code.status.on') : $t('code.status.off') }}
                  </span>
                  <span v-else-if="item === 'systemStatus'">
                    {{ detailModel[item] === 0 ? $t('code.state.current.normal') : $t('code.state.current.abnormal') }}
                  </span>
                  <span v-else>
                    {{ detailModel[item] }}
                  </span>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-form :model="cpuModel" label-width="120px">
            <el-row>
              <el-divider content-position="left">{{ $t('management.proxy.detail.cpu.title') }}</el-divider>
              <el-col v-for="(item, index) in columnOption.cpu" :key="index" :span="8">
                <el-form-item :prop="item" :label="$t(`management.proxy.detail.cpu.${item}`)">
                  {{ cpuModel[item] }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>

          <el-form :model="memModel" label-width="120px">
            <el-row>
              <el-divider content-position="left">{{ $t('management.proxy.detail.mem.title') }}</el-divider>
              <el-col v-for="(item, index) in columnOption.mem" :key="index" :span="8">
                <el-form-item :prop="item" :label="$t(`management.proxy.detail.mem.${item}`)">
                  {{ memModel[item] }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-form :model="sysModel" label-width="120px">
            <el-row>
              <el-divider content-position="left">{{ $t('management.proxy.detail.sys.title') }}</el-divider>
              <el-col v-for="(item, index) in columnOption.sys" :key="index" :span="8">
                <el-form-item :prop="item" :label="$t(`management.proxy.detail.sys.${item}`)">
                  {{ sysModel[item] }}
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-form label-width="120px">
            <el-row>
              <el-divider content-position="left">{{ $t('management.proxy.detail.sysFiles.title') }}</el-divider>
              <el-col :span="24">
                <el-table
                  :data="filesModel"
                  element-loading-background="rgba(0, 0, 0, 0.3)"
                  size="mini"
                  highlight-current-row
                  tooltip-effect="light"
                  style="width: 100%"
                >
                  <el-table-column
                    v-for="(item, key) in columnOption.sysFiles"
                    :key="key"
                    :prop="item"
                    :label="$t(`management.proxy.detail.sysFiles.${item}`)"
                    show-overflow-tooltip
                  ></el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>
        <el-tab-pane :label="$t('management.proxy.detail.history.title')" name="history">
          <main class="table-body-main">
            <el-table
              v-loading="table.loading"
              :data="table.data"
              element-loading-background="rgba(0, 0, 0, 0.3)"
              size="mini"
              highlight-current-row
              tooltip-effect="light"
              height="268"
            >
              <el-table-column
                v-for="(item, index) in tableColumns"
                :key="index"
                :prop="item"
                :label="$t(`management.proxy.label.${item}`)"
                show-overflow-tooltip
              >
                <template slot-scope="scope">
                  <p v-if="item === 'status'">
                    {{ scope.row[item] === 1 ? $t('code.status.on') : $t('code.status.off') }}
                  </p>
                  <span v-else-if="item === 'systemStatus'">
                    {{ scope.row[item] === 0 ? $t('code.state.current.normal') : $t('code.state.current.abnormal') }}
                  </span>
                  <p v-else>
                    {{ scope.row[item] }}
                  </p>
                </template>
              </el-table-column>
              <el-table-column fixed="right" width="120">
                <template slot-scope="scope">
                  <el-button v-has="'query'" class="el-button--blue" @click="clickDetail(scope.row)">
                    {{ $t('button.detail') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </main>
          <footer class="table-footer">
            <el-pagination
              v-if="pagination.visible"
              small
              background
              align="right"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="pagination.pageNum"
              :page-sizes="[10, 20, 50, 100]"
              :page-size="pagination.pageSize"
              :total="pagination.total"
              @size-change="tableSizeChange"
              @current-change="tablePageChange"
            ></el-pagination>
          </footer>
        </el-tab-pane>
      </el-tabs>
    </section>
    <history-detail-dialog
      :visible.sync="historyDetailDialog.visible"
      :title-name="title"
      :detail-model="historyDetailDialog.model"
    ></history-detail-dialog>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import HistoryDetailDialog from './TheHistoryDetailDialog'
import { queryHistoryHealthy } from '@api/management/proxy-server-api'
export default {
  components: {
    CustomDialog,
    HistoryDetailDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    detailModel: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      title: this.$t('management.proxy.detail.history.title'),
      dialogVisible: this.visible,
      cpuModel: {},
      memModel: {},
      sysModel: {},
      filesModel: [],
      columnOption: {
        basic: ['ip', 'status', 'updateTime', 'systemStatus', 'description'],
        cpu: ['cpuNum', 'sys', 'used', 'wait', 'free'],
        mem: ['total', 'used', 'free', 'usage'],
        sys: ['computerName', 'osName', 'osArch', 'agentVersion'],
        sysFiles: ['dirName', 'sysTypeName', 'typeName', 'total', 'free', 'used', 'usage'],
      },
      activeName: 'current',
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      tableColumns: ['ip', 'status', 'systemStatus', 'updateTime'],
      historyDetailDialog: {
        visible: false,
        model: {},
      },
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.activeName = 'current'
        this.bindingDetail()
        this.changeQueryTable()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    bindingDetail() {
      const metrics = JSON.parse(this.detailModel.metrics)
      this.cpuModel = metrics.cpu
      this.memModel = metrics.mem
      this.sysModel = metrics.sys
      this.filesModel = metrics.sysFiles
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    changeQueryTable(flag) {
      if (flag !== 'turn-page') this.pagination.pageNum = 1
      const params = {
        agentId: this.detailModel.agentId,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      this.queryHistoryTable(params)
    },
    clickDetail(row) {
      this.historyDetailDialog.visible = true
      this.historyDetailDialog.model = row
    },
    queryHistoryTable(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryHistoryHealthy(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}
</style>
