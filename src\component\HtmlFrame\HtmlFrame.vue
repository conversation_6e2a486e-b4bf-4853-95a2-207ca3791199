<template>
  <el-container v-loading="loading" class="iframe-wrapper">
    <iframe ref="frameDOM" :src="frameUrl" :style="{ width: width, height: height }" frameborder="0"></iframe>
  </el-container>
</template>

<script>
export default {
  props: {
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '100%',
    },
    frameUrl: {
      required: true,
      type: String,
    },
  },
  data() {
    return {
      loading: true,
    }
  },
  mounted() {
    this.onload()
  },
  methods: {
    onload() {
      const iframe = this.$refs.frameDOM
      if (iframe.attachEvent) {
        iframe.attachEvent('onload', () => {
          this.loading = false
        })
      } else {
        iframe.onload = () => {
          this.loading = false
        }
      }
    },
    reload() {
      this.$refs.frameDOM.contentWindow.location.reload(true)
    },
  },
}
</script>

<style lang="scss" scoped>
.iframe-wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;

  iframe {
    width: 100%;
    height: 100%;
  }
}
</style>
