<template>
  <div class="scroll-table-wrapper">
    <ul class="table-head">
      <li v-for="item in head" :style="`flex-basis: ${item.width};`">{{ item.title }}</li>
    </ul>
    <ul class="table-body-skelon">
      <li v-for="n in rows"></li>
    </ul>
    <vue-seamless-scroll :class-option="defaultOption" :data="data" class="warp">
      <ul class="item">
        <li v-for="(item, index) in data" :key="index">
          <span v-for="ho in head" v-html="transformData(item, ho.key, index + 1)" :style="`flex-basis: ${ho.width};`"></span>
        </li>
      </ul>
    </vue-seamless-scroll>
  </div>
</template>
<script>
import vueSeamlessScroll from 'vue-seamless-scroll'
export default {
  components: {
    vueSeamlessScroll,
  },
  props: {
    rows: {
      type: Number,
      default() {
        return 8
      },
    },
    head: {
      type: Array,
      default() {
        return []
      },
    },
    data: {
      type: Array,
      default() {
        return []
      },
    },
  },
  data() {
    return {}
  },
  computed: {
    defaultOption() {
      return {
        singleHeight: 40,
        waitTime: 3000,
      }
    },
  },
  mounted() {},
  methods: {
    transformData(item, key, index) {
      if (key === 'index') return this.transformIndex(index)
      if (key === 'riskLevel') return this.transformLevel(item[key])
      return item[key]
    },
    transformIndex(index) {
      switch (index) {
        case 1:
          return '<span class="high-risk">1</span>'
        case 2:
          return '<span class="middle-risk">2</span>'
        case 3:
          return '<span class="low-risk">3</span>'
        default:
          return index
      }
    },
    transformLevel(level) {
      switch (level) {
        case 5:
          return '<span class="lost-risk">失陷</span>'
        case 4:
          return '<span class="high-risk">高危</span>'
        case 3:
          return '<span class="middle-risk">中危</span>'
        case 2:
          return '<span class="low-risk">低危</span>'
        default:
          return '<span class="normal-risk">正常</span>'
      }
    },
  },
}
</script>
<style lang="scss" scoped>
.scroll-table-wrapper {
  height: 100%;
  position: relative;
  overflow: hidden;
  .table-head {
    width: 100%;
    height: 48px;
    display: flex;
    justify-content: space-between;
    font-size: 13px;
    align-items: center;
    color: #78ddff;
    padding: 0 12px;
    li {
      position: relative;
      display: block;
    }
  }
  .table-body-skelon {
    padding-top: 5px;
    position: absolute;
    width: 100%;
    overflow: hidden;
    li {
      font-size: 13px;
      color: #78ddff;
      display: flex;
      justify-content: space-between;
      height: 40px;
      background-image: url('@asset/image/visualization/threatrisk/rowbg.png');
      background-size: contain;
      background-repeat: no-repeat;
      margin: 0px;
    }
  }
  .warp {
    padding-top: 5px;
    height: calc(100% - 40px);
    width: 100%;
    overflow: hidden;
    padding: 0 12px;
    ul {
      list-style: none;
      padding: 0;
      margin: 0 auto;
      li {
        font-size: 13px;
        color: #78ddff;
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 40px;
        padding: 0px;
        span {
          display: block;
        }
      }
    }
  }
  .lost-risk {
    color: #8a44f5;
  }
  .high-risk {
    color: #ff5a5f;
  }

  .middle-risk {
    color: #ffca5f;
  }

  .low-risk {
    color: #5fff9f;
  }

  .normal-risk {
    color: #78ddff;
  }
}
</style>
