<!--
 * @Description: 威胁情报库 - 底部翻页
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-09-01
 * @Editor:
 * @EditDate: 2021-09-01
-->
<template>
  <section class="table-footer infinite-scroll">
    <section class="infinite-scroll-nomore">
      <span v-show="nomore">{{ $t('validate.data.nomore') }}</span>
    </section>
    <section class="infinite-scroll-total">
      <b>{{ $t('tip.total') + ':' }}</b>
      <i v-if="loading" class="el-icon-loading"></i>
      <span v-else>
        {{ total }}
      </span>
    </section>
  </section>
</template>

<script>
export default {
  props: {
    loading: {
      type: Boolean,
      default: false,
    },
    total: {
      required: true,
      type: [String, Number],
    },
    nomore: {
      required: true,
      type: Boolean,
    },
  },
}
</script>

<style scoped></style>
