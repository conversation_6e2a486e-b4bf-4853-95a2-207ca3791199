<!--
 * @Description: 行为分析策略
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-13
 * @Editor:
 * @EditDate: 2021-10-13
-->
<template>
  <div class="router-wrap-table">
    <table-header
      :condition.sync="query"
      :options="options"
      @on-change="changeQueryTable"
      @on-add="clickAdd"
      @on-batch-handle="clickBatchHandle"
    ></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-select="clickSelectRows"
      @on-toggle-status="toggleStatus"
      @on-detail="clickDetail"
      @on-update="clickUpdate"
      @on-delete="clickDelete"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <add-dialog
      :visible.sync="dialog.add.visible"
      :title-name="title"
      :model="dialog.add.model"
      :options="options"
      @on-submit="addSubmit"
    ></add-dialog>
    <update-dialog
      :visible.sync="dialog.update.visible"
      :title-name="title"
      :model="dialog.update.model"
      :options="options"
      @on-submit="updSubmit"
    ></update-dialog>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import AddDialog from './TheAddDialog'
import UpdateDialog from './TheUpdateDialog'
import DetailDialog from './TheDetailDialog'
import { executeStatus } from '@asset/js/code/option'
import { prompt } from '@util/prompt'
import { parseTime } from '@util/format'
import { validateIp } from '@util/validate'
import { isEmpty } from '@util/common'

import {
  queryBehaviorStrategyTable,
  addBehaviorStrategy,
  deleteBehaviorStrategy,
  updateBehaviorStrategy,
  startStrategyStatus,
  stopStrategyStatus,
  queryEventTypeCombo,
  queryExternalSystemCombo,
} from '@api/audit/behavior-strategy-api'

export default {
  name: 'BehaviorStrategy',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    AddDialog,
    UpdateDialog,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('audit.behaviorStrategy.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          systemName: '',
          systemIp: '',
          status: '',
          abnormalAction: '',
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        eventType: [],
        status: executeStatus,
        externalSystem: [],
      },
      dialog: {
        add: {
          visible: false,
          model: {},
        },
        update: {
          visible: false,
          model: {},
        },
        detail: {
          visible: false,
          model: {},
        },
      },
    }
  },
  mounted() {
    this.initOptions()
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (this.validatorIp()) {
        if (flag !== 'turn-page') {
          this.pagination.pageNum = 1
        }
        const params = this.handleQueryParams()
        this.queryTableData(params)
      }
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          systemName: this.query.form.systemName,
          systemIp: this.query.form.systemIp,
          status: this.query.form.status,
          abnormalAction: this.query.form.abnormalAction.toString(),
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickSelectRows(sel) {
      this.table.selected = sel
    },
    clickAdd() {
      this.dialog.add.visible = true
      this.dialog.add.model = {
        systemName: '',
        systemIp: '',
        legalIp: '',
        abnormalAction: [],
        startTime: '',
        endTime: '',
        timeRange: this.initTimeRange(),
        externalSystem: [],
        externalMail: '',
      }
    },
    initTimeRange() {
      return [parseTime(new Date(0, 0, 0, 0, 0, 0).getTime(), '{h}:{i}:{s}'), parseTime(new Date(0, 0, 0, 23, 59, 59).getTime(), '{h}:{i}:{s}')]
    },
    addSubmit(obj) {
      const params = this.handleFormParams(obj)
      this.addBehaviorStrategy(params)
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    clickUpdate(row) {
      this.dialog.update.model = {
        id: row.id,
        systemName: row.systemName,
        systemIp: row.systemIp,
        legalIp: row.legalIp.split(','),
        abnormalAction: JSON.parse(row.abnormalAction),
        startTime: row.startTime,
        endTime: row.endTime,
        timeRange: [row.startTime, row.endTime],
        externalSystem: isEmpty(row.externalSystem) ? [] : row.externalSystem.split(','),
        externalMail: row.externalMail,
      }
      this.dialog.update.visible = true
    },
    updSubmit(formModel) {
      const params = this.handleFormParams(formModel)
      this.updateBehaviorStrategy(params)
    },
    handleFormParams(obj) {
      obj.timeRange = obj.timeRange || ['', '']
      const params = Object.assign(
        {},
        {
          id: obj.id,
          systemName: obj.systemName,
          systemIp: obj.systemIp,
          legalIp: obj.legalIp.join(','),
          abnormalAction: JSON.stringify(obj.abnormalAction),
          startTime: obj.timeRange[0],
          endTime: obj.timeRange[1],
          externalSystem: obj.externalSystem.toString(),
          externalMail: obj.externalMail,
        }
      )
      return params
    },
    clickDelete(row) {
      this.deleteBehaviorStrategy(row.id)
    },
    toggleStatus(row) {
      if (row.status === '1') {
        this.startStatus(row.id)
      } else {
        this.stopStatus(row.id)
      }
    },
    clickBatchHandle(command) {
      switch (command) {
        case 'run':
          this.batchRun()
          break
        case 'stop':
          this.batchStop()
          break
      }
    },
    batchRun() {
      const arr = []
      const status = this.table.selected.map((item) => item.status).toString()
      if (status.indexOf('1') > -1) {
        prompt({
          i18nCode: 'tip.start.existStart',
          type: 'error',
        })
        return
      }
      this.table.selected.map((item) => arr.push(item.id))
      if (arr.length === 0) {
        prompt({
          i18nCode: 'tip.start.selectRow',
          type: 'info',
        })
      } else {
        this.startStatus(arr.toString())
      }
    },
    // 批量停止
    batchStop() {
      const arr = []
      const status = this.table.selected.map((item) => item.status).toString()
      if (status.indexOf('0') > -1) {
        prompt({
          i18nCode: 'tip.stop.existStop',
          type: 'error',
        })
        return
      }
      this.table.selected.map((item) => arr.push(item.id))
      if (arr.length === 0) {
        prompt({
          i18nCode: 'tip.stop.selectRow',
          type: 'info',
        })
      } else {
        this.stopStatus(arr.toString())
      }
    },
    validatorIp() {
      const ip = this.query.form.systemIp || ''
      if (!isEmpty(ip) && !validateIp(ip)) {
        prompt({
          i18nCode: 'validate.ip.incorrect',
          type: 'error',
        })
        return false
      } else {
        return true
      }
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryBehaviorStrategyTable(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
    addBehaviorStrategy(obj) {
      addBehaviorStrategy(obj).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else if (res === 'existName') {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    updateBehaviorStrategy(obj) {
      updateBehaviorStrategy(obj).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else if (res === 'existName') {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    deleteBehaviorStrategy(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteBehaviorStrategy(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.query.form = {
                  fuzzyField: '',
                  systemName: '',
                  systemIp: '',
                  abnormalAction: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.table.data.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    startStatus(id) {
      startStrategyStatus(id).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.enable.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.enable.error',
            type: 'error',
          })
        }
      })
    },
    stopStatus(id) {
      stopStrategyStatus(id).then((res) => {
        if (res) {
          prompt(
            {
              i18nCode: 'tip.disable.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else {
          prompt({
            i18nCode: 'tip.disable.error',
            type: 'error',
          })
        }
      })
    },
    initOptions() {
      queryEventTypeCombo().then((res) => {
        this.options.eventType = res
      })
      queryExternalSystemCombo().then((res) => {
        this.options.externalSystem = res
      })
    },
  },
}
</script>
