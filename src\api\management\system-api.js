import request from '@util/request'

export function queryBasicInfoData() {
  return request({
    url: '/systemmanagement/basic',
    method: 'get',
  })
}

export function querySSHStatus() {
  return request({
    url: '/systemmanagement/querySshdStatus',
    method: 'get',
  })
}

export function startSSHStatus() {
  return request({
    url: '/systemmanagement/startSshd',
    method: 'put',
  })
}

export function stopSSHStatus() {
  return request({
    url: '/systemmanagement/stopSshd',
    method: 'put',
  })
}

export function queryRestartDeviceData() {
  return request({
    url: '/systemmanagement/device/restart',
    method: 'get',
  })
}

export function queryShutdownDeviceData() {
  return request({
    url: '/systemmanagement/device/shutdown',
    method: 'get',
  })
}

export function queryRestoreDeviceData() {
  return request({
    url: '/systemmanagement/device/restore',
    method: 'get',
  })
}

export function querySystemConfigData() {
  return request({
    url: '/systemmanagement/properties',
    method: 'get',
  })
}

export function resetSystemConfigData() {
  return request({
    url: '/systemmanagement/properties',
    method: 'put',
  })
}

export function saveSystemConfigData(obj) {
  return request({
    url: '/systemmanagement/properties',
    method: 'post',
    data: obj || {},
  })
}

export function queryEmailServeConfigData() {
  return request({
    url: '/systemmanagement/mail-server',
    method: 'get',
  })
}

export function resetEmailServeConfigData() {
  return request({
    url: '/systemmanagement/mail-server/reset',
    method: 'put',
  })
}

export function saveEmailServeConfigData(obj) {
  return request({
    url: '/systemmanagement/mail-server',
    method: 'put',
    data: obj || {},
  })
}

export function testEmailServeConfigData(obj) {
  return request(
    {
      url: '/systemmanagement/mail-server/check',
      method: 'put',
      data: obj || {},
    },
    'default',
    '20000'
  )
}

export function queryReviseTimeConfigData() {
  return request({
    url: '/systemmanagement/time-server',
    method: 'get',
  })
}

export function resetReviseTimeConfigData() {
  return request({
    url: '/systemmanagement/time-server/reset',
    method: 'put',
  })
}

export function saveReviseTimeConfigData(obj) {
  return request({
    url: '/systemmanagement/time-server',
    method: 'put',
    data: obj || {},
  })
}

export function testReviseTimeConfigData(obj) {
  return request(
    {
      url: '/systemmanagement/time-server/check',
      method: 'put',
      data: obj || {},
    },
    'default',
    '60000'
  )
}

export function queryEncryptionOptionData() {
  return request({
    url: '/systemmanagement/combo/encryption-types',
    method: 'get',
  })
}

export function queryDiskSpaceConfigData() {
  return request({
    url: '/systemmanagement/diskspace',
    method: 'get',
  })
}

export function resetDiskSpaceConfigData() {
  return request({
    url: '/systemmanagement/diskspace/init',
    method: 'put',
  })
}

export function saveDiskSpaceConfigData(obj) {
  return request({
    url: '/systemmanagement/diskspace',
    method: 'put',
    data: obj || {},
  })
}

export function queryDatabaseSafeguardData() {
  return request({
    url: '/systemmanagement/data-cleanup/properties',
    method: 'get',
  })
}

export function saveDatabaseSafeguardData(obj) {
  return request({
    url: '/systemmanagement/data-cleanup/properties',
    method: 'put',
    data: obj || {},
  })
}

export function queryDatabaseSafeguardTableData() {
  return request({
    url: '/systemmanagement/data-cleanup/records',
    method: 'get',
  })
}

export function queryLicenseListData() {
  return request({
    url: '/systemmanagement/license',
    method: 'get',
  })
}

export function uploadLicenseData(obj) {
  return request(
    {
      url: '/systemmanagement/license/upload',
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

export function downloadLicense() {
  return request(
    {
      url: `/systemmanagement/license/download`,
      method: 'get',
    },
    'download'
  )
}

export function queryDataBackupData() {
  return request({
    url: '/systemmanagement/data-backup/properties',
    method: 'get',
  })
}

export function resetDataBackupData() {
  return request({
    url: '/systemmanagement/data-backup/reset',
    method: 'put',
  })
}

export function saveDataBackupData(obj) {
  return request({
    url: '/systemmanagement/data-backup/properties',
    method: 'put',
    data: obj || {},
  })
}

export function queryDataBackupTableData() {
  return request({
    url: '/systemmanagement/data-backup/records',
    method: 'get',
  })
}

export function executeBackupData(obj) {
  return request({
    url: '/systemmanagement/data-backup/create',
    method: 'post',
  })
}

export function querySnapshotTaskData() {
  return request({
    url: '/systemmanagement/SystemConfigSnapshot/properties',
    method: 'get',
  })
}

export function resetSnapshotTaskData() {
  return request({
    url: '/systemmanagement/SystemConfigSnapshot/reset',
    method: 'put',
  })
}

export function saveSnapshotTaskData(obj) {
  return request({
    url: '/systemmanagement/SystemConfigSnapshot/properties',
    method: 'put',
    data: obj || {},
  })
}

export function addSnapshotData(obj) {
  return request({
    url: '/systemmanagement/SystemConfigSnapshot/create',
    method: 'post',
  })
}

export function uploadSnapshot(obj) {
  return request(
    {
      url: '/systemmanagement/SystemConfigSnapshot/upload',
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}

export function downloadSnapshot(id) {
  return request(
    {
      url: `/systemmanagement/SystemConfigSnapshot/download/${id}`,
      method: 'get',
    },
    'download'
  )
}
export function delSnapshot(id) {
  return request({
    url: `/systemmanagement/SystemConfigSnapshot/delete/${id}`,
    method: 'delete',
  })
}

export function recoverySnapshot(id) {
  return request({
    url: `/systemmanagement/SystemConfigSnapshot/recovery/${id}`,
    method: 'put',
  })
}

export function querySnapshotTableData() {
  return request({
    url: '/systemmanagement/SystemConfigSnapshot/records',
    method: 'get',
  })
}

export function saveDataThreatData(obj) {
  return request({
    url: '/systemmanagement/task',
    method: 'put',
    data: obj || {},
  })
}

export function resetDataThreatData() {
  return request({
    url: '/systemmanagement/task/reset',
    method: 'put',
  })
}

export function queryDataThreatData() {
  return request({
    url: '/systemmanagement/task/echoed',
    method: 'get',
  })
}

// 上传
export function uploadThreatData(obj) {
  return request(
    {
      url: `/systemmanagement/upgrade`,
      method: 'post',
      data: obj || {},
    },
    'upload',
    '180000'
  )
}

export function querySnmpForward() {
  return request({
    url: '/systemmanagement/combo/forward-relay-way',
    method: 'get',
  })
}

export function querySysAlarmNotice() {
  return request({
    url: '/systemmanagement/find-system-alarm-notice',
    method: 'get',
  })
}

export function saveSysAlarmNotice(obj) {
  return request({
    url: '/systemmanagement/system-alarm-notice',
    method: 'put',
    data: obj || {},
  })
}

export function uploadBackupData(obj) {
  return request(
    {
      url: `/systemmanagement/data-backup/upload`,
      method: 'post',
      data: obj || {},
    },
    'upload',
    '180000'
  )
}

export function getZeekConfigData() {
  return request({
    url: '/zeekmanagement/zeek/config',
    method: 'get',
  })
}

export function saveZeekConfigData(obj) {
  return request({
    url: '/zeekmanagement/zeek/updateConfig',
    method: 'post',
    data: obj || {},
  })
}

export function getDesensitizationList(obj) {
  return request({
    url: '/systemmanagement/list',
    method: 'get',
    params: obj || {},
  })
}

export function addDesensitization(obj) {
  return request({
    url: '/systemmanagement/addDesensitize',
    method: 'post',
    data: obj || {},
  })
}

export function updateDesensitization(obj) {
  return request({
    url: '/systemmanagement/updDesensitize',
    method: 'post',
    data: obj || {},
  })
}

export function deleteDesensitization(ids) {
  return request({
    url: `/systemmanagement/delDesensitize/${ids}`,
    method: 'post',
  })
}

export function toggleDesensitization(ids, status) {
  return request({
    url: `/systemmanagement/updDesensitize/${status}/${ids}`,
    method: 'post',
  })
}

export function getFlowGatherData() {
  return request({
    url: '/flowswitch/config',
    method: 'get',
  })
}

export function saveFlowGatherData(obj) {
  return request({
    url: '/flowswitch/updateConfig',
    method: 'post',
    data: obj || {},
  })
}
export function getTcpdumpNetcard() {
  return request({
    url: '/systemmanagement/qamtool/tcpdumpCards',
    method: 'get',
  })
}
export function getTcpdumpRecords() {
  return request({
    url: '/systemmanagement/qamtool/querytcpdumpRecords',
    method: 'get',
  })
}
export function execTcpdump(obj) {
  return request({
    url: '/systemmanagement/qamtool/exectcpdump',
    method: 'post',
    data: obj || {},
  })
}
export function downloadTcpdump(fileName) {
  return request(
    {
      url: `/systemmanagement/qamtool/downloadtcpdump/${fileName}`,
      method: 'get',
    },
    'download'
  )
}

export function execCommonCmd(obj) {
  return request({
    url: '/systemmanagement/qamtool/command',
    method: 'post',
    data: obj || {},
  })
}

export function uploadWebCert(obj) {
  return request(
    {
      url: '/systemmanagement/webcert/upload',
      method: 'post',
      data: obj || {},
    },
    'upload'
  )
}
