<template>
  <el-form ref="serviceForm" :model="form.model" :rules="form.rules" label-width="120px">
    <el-row>
      <el-col :span="12">
        <el-form-item prop="centerIp" :label="$t('management.proxy.network.centerIp')">
          <el-input v-model.trim="form.model.centerIp" maxlength="128"></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-button v-has="'handle'" @click="setCenterIp">
          {{ $t('button.save') }}
        </el-button>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item prop="sshEnable" :label="$t('management.system.label.sshService')">
          <el-switch v-model="form.model.sshEnable" active-value="1" inactive-value="0" @change="toggleSSHStatus"></el-switch>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <section class="form-button">
          <el-button v-has="'restart'" @click="clickRestartDevice">
            {{ $t('button.restart') }}
          </el-button>
          <el-button v-has="'shutdown'" @click="clickShutdownDevice">
            {{ $t('button.shutdown') }}
          </el-button>
        </section>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { validateIpv4 } from '@util/validate'
import { prompt } from '@util/prompt'
import {
  querySSHStatus,
  startSSHStatus,
  stopSSHStatus,
  queryRestartDeviceData,
  queryShutdownDeviceData,
  queryCenterIp,
  updateCenterIp,
} from '@api/management/proxy-server-api'

export default {
  props: {
    agentId: {
      type: String,
      default: '',
    },
  },
  data() {
    const validatorIp = (rule, value, callback) => {
      if (!value) {
        callback(new Error(this.$t('validate.ip.empty')))
      } else if (!validateIpv4(value)) {
        callback(new Error(this.$t('validate.ip.incorrect')))
      } else {
        callback()
      }
    }

    return {
      form: {
        model: {
          centerIp: '',
        },
        rules: {
          centerIp: [
            {
              required: true,
              validator: validatorIp,
              trigger: 'blur',
            },
          ],
        },
      },
    }
  },
  mounted() {
    this.getSSHStatus()
    this.getCenterIp()
  },
  methods: {
    toggleSSHStatus(val) {
      this.form.model.sshEnable = val === '1' ? '0' : '1'
      if (val === '0') {
        this.stopSshService()
      } else {
        this.startSshService()
      }
    },
    clickRestartDevice() {
      this.$confirm(this.$t('tip.confirm.restart'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.restartDevice()
      })
    },
    clickShutdownDevice() {
      this.$confirm(this.$t('tip.confirm.shutdown'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        this.shutdownDevice()
      })
    },
    getCenterIp() {
      queryCenterIp(this.agentId).then((res) => {
        this.form.model.centerIp = res
      })
    },
    setCenterIp() {
      updateCenterIp(this.agentId, this.form.model.centerIp).then((res) => {
        if (res === '0') {
          this.$emit('on-save')
          prompt({
            i18nCode: 'tip.update.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    getSSHStatus() {
      querySSHStatus(this.agentId).then((res) => {
        this.form.model.sshEnable = res
      })
    },
    stopSshService() {
      this.$confirm(this.$t('tip.confirm.sslStop'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        stopSSHStatus(this.agentId).then((res) => {
          if (res === '0') {
            this.getSSHStatus()
            prompt({
              i18nCode: 'tip.disable.success',
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.disable.error',
              type: 'error',
            })
          }
        })
      })
    },
    startSshService() {
      this.$confirm(this.$t('tip.confirm.sslStart'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        startSSHStatus(this.agentId).then((res) => {
          if (res === '0') {
            this.getSSHStatus()
            prompt({
              i18nCode: 'tip.enable.success',
              type: 'success',
            })
          } else {
            prompt({
              i18nCode: 'tip.enable.error',
              type: 'error',
            })
          }
        })
      })
    },
    restartDevice() {
      queryRestartDeviceData(this.agentId).then((res) => {
        if (res) {
          this.$emit('on-save')
          prompt({
            i18nCode: 'tip.restart.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.restart.error',
            type: 'error',
          })
        }
      })
    },
    shutdownDevice() {
      queryShutdownDeviceData(this.agentId).then((res) => {
        if (res) {
          this.$emit('on-save')
          prompt({
            i18nCode: 'tip.shutdown.success',
            type: 'success',
          })
        } else {
          prompt({
            i18nCode: 'tip.shutdown.error',
            type: 'error',
          })
        }
      })
    },
  },
}
</script>
