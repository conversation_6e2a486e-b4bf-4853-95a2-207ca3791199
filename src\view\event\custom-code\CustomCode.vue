<!--
 * @Description: 事件特征值
 * @Version: 1.0.0
 * @Author:
 * @Date: 2022-01-06
 * @Editor:
 * @EditDate: 2022-01-06
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" :options="options" @on-change="changeQueryTable" @on-add="clickAdd"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      :options="options"
      @on-update="clickUpdate"
      @on-delete="clickDelete"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <add-dialog
      :visible.sync="dialog.add.visible"
      :title-name="title"
      :model="dialog.add.model"
      :options="options"
      @on-submit="addSubmit"
    ></add-dialog>
    <update-dialog
      :visible.sync="dialog.update.visible"
      :title-name="title"
      :model="dialog.update.model"
      :options="options"
      @on-submit="updSubmit"
    ></update-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import AddDialog from './TheAddDialog'
import UpdateDialog from './TheUpdateDialog'
import { prompt } from '@util/prompt'
import {
  queryCustomCode,
  addCustomCode,
  deleteCustomCode,
  updateCustomCode,
  queryDevTypeCombo,
  queryEventTypeCombo,
} from '@api/event/custom-code-api'

export default {
  name: 'CustomCode',
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    AddDialog,
    UpdateDialog,
  },
  data() {
    return {
      title: this.$t('event.customCode.title'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          code: '',
          devType: '',
          eventType: '',
        },
      },
      table: {
        loading: false,
        data: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      options: {
        devType: [],
        eventType: [],
      },
      dialog: {
        add: {
          visible: false,
          model: {},
        },
        update: {
          visible: false,
          model: {},
        },
      },
    }
  },
  mounted() {
    this.initOptions()
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') {
        this.pagination.pageNum = 1
      }
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
      if (this.query.senior) {
        params = Object.assign(params, {
          code: this.query.form.code,
          devType: this.query.form.devType,
          eventType: this.query.form.eventType,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickAdd() {
      this.dialog.add.visible = true
      this.dialog.add.model = {
        devType: '',
        code: '',
        eventType: '',
      }
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    clickUpdate(row) {
      this.dialog.update.model = row
      this.dialog.update.model = {
        id: row.id,
        code: row.code,
        devType: row.devType,
        eventType: row.eventType,
      }
      this.dialog.update.visible = true
    },
    clickDelete(row) {
      this.deleteCustomCode(row.id)
    },
    addSubmit(obj) {
      const params = this.handleFormParams(obj)
      this.addCustomCode(params)
    },
    updSubmit(formModel) {
      const params = this.handleFormParams(formModel)
      this.updateCustomCode(params)
    },
    handleFormParams(obj) {
      const params = Object.assign(
        {},
        {
          id: obj.id,
          code: obj.code,
          devType: obj.devType,
          eventType: obj.eventType,
        }
      )
      return params
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.table.loading = true
      this.pagination.visible = false
      queryCustomCode(params).then((res) => {
        this.table.data = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.pagination.visible = true
        this.table.loading = false
      })
    },
    addCustomCode(obj) {
      addCustomCode(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    updateCustomCode(obj) {
      updateCustomCode(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    deleteCustomCode(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteCustomCode(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.query.form = {
                  fuzzyField: '',
                  code: '',
                  devType: '',
                  eventType: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.table.data.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else if (res === 4) {
            prompt({
              i18nCode: 'tip.delete.use',
              type: 'info',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    initOptions() {
      queryDevTypeCombo().then((res) => {
        this.options.devType = res
      })
      queryEventTypeCombo().then((res) => {
        this.options.eventType = res
      })
    },
  },
}
</script>
