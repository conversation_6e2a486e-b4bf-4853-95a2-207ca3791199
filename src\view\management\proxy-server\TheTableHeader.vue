<!--
 * @Description: 代理服务器 - 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-07-16
 * @Editor:
 * @EditDate: 2021-07-16
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model="filterCondition.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('management.proxy.label.ip')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'handle'" @click="clickConfigCenterIp">
          {{ $t('button.centerIp') }}
        </el-button>
        <el-button v-has="'add'" @click="clickDetect">
          {{ $t('button.detect') }}
        </el-button>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select v-model="filterCondition.status" clearable :placeholder="$t('management.proxy.label.status')" @change="changeQueryCondition">
                <el-option v-for="item in statusOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col align="right" :offset="15" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      debounce: null,
      statusOption: [
        { value: 1, label: this.$t('code.status.on') },
        { value: 0, label: this.$t('code.status.off') },
      ],
    }
  },
  watch: {
    condition(nVal) {
      this.filterCondition = nVal
    },
    filterCondition(nVal) {
      this.$emit('update:condition', nVal)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.fuzzyField = ''
      this.filterCondition.status = ''
      this.changeQueryCondition()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    clickDetect() {
      this.$emit('on-detect')
    },
    clickConfigCenterIp() {
      this.$emit('on-config-ip')
    },
  },
}
</script>

<style scoped></style>
