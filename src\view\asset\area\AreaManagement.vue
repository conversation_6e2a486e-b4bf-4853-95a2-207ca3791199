<!--
 * @Description: 区域管理
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-06-29
 * @Editor:
 * @EditDate: 2021-06-29
-->
<template>
  <div class="router-wrap-table">
    <table-header :condition.sync="query" @on-change="changeQueryTable" @on-add="clickAdd" @on-batch-delete="clickBatchDelete"></table-header>
    <table-body
      :title-name="title"
      :table-loading="table.loading"
      :table-data="table.data"
      @on-select="clickSelectRows"
      @on-detail="clickDetail"
      @on-update="clickUpdate"
      @on-delete="clickDelete"
    ></table-body>
    <table-footer :pagination.sync="pagination" @size-change="tableSizeChange" @page-change="tablePageChange"></table-footer>
    <add-dialog :visible.sync="dialog.add.visible" :title-name="title" :model="dialog.add.model" @on-submit="addSubmit"></add-dialog>
    <upd-dialog :visible.sync="dialog.update.visible" :title-name="title" :model="dialog.update.model" @on-submit="updSubmit"></upd-dialog>
    <detail-dialog :visible.sync="dialog.detail.visible" :title-name="title" :model="dialog.detail.model"></detail-dialog>
  </div>
</template>

<script>
import TableHeader from './TheTableHeader'
import TableBody from './TheTableBody'
import TableFooter from './TheTableFooter'
import AddDialog from './TheAddDialog'
import UpdDialog from './TheUpdDialog'
import DetailDialog from './TheDetailDialog'
import { prompt } from '@util/prompt'
import { queryAreaTable, addArea, updateArea, deleteArea } from '@api/asset/area-api'

export default {
  components: {
    TableHeader,
    TableBody,
    TableFooter,
    AddDialog,
    UpdDialog,
    DetailDialog,
  },
  data() {
    return {
      title: this.$t('asset.area.header'),
      query: {
        senior: false,
        form: {
          fuzzyField: '',
          domaName: '',
          domaAbbreviation: '',
        },
      },
      table: {
        loading: false,
        data: [],
        selected: [],
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      dialog: {
        add: {
          visible: false,
          model: {},
        },
        update: {
          visible: false,
          model: {
            domaName: '',
            domaAbbreviation: '',
            officeTel: '',
            email: '',
            domaAddress: '',
            domaFunc: '',
            domaMemo: '',
          },
        },
        detail: {
          visible: false,
          model: {},
        },
      },
    }
  },
  mounted() {
    this.queryTableData()
  },
  methods: {
    changeQueryTable(flag) {
      if (flag !== 'turn-page') {
        this.pagination.pageNum = 1
      }
      const params = this.handleQueryParams()
      this.queryTableData(params)
    },
    handleQueryParams() {
      let params = {}
      if (this.query.senior) {
        params = Object.assign(params, {
          domaName: this.query.form.domaName,
          domaAbbreviation: this.query.form.domaAbbreviation,
        })
      } else {
        params = Object.assign(params, {
          fuzzyField: this.query.form.fuzzyField,
        })
      }
      return params
    },
    clickSelectRows(select) {
      this.table.selected = select
    },
    clickAdd() {
      this.dialog.add.visible = true
      this.dialog.add.model = {
        domaName: '',
        domaAbbreviation: '',
        officeTel: '',
        email: '',
        domaAddress: '',
        domaFunc: '',
        domaMemo: '',
      }
    },
    clickDetail(row) {
      this.dialog.detail.visible = true
      this.dialog.detail.model = row
    },
    clickUpdate(row) {
      this.dialog.update.model = {
        domaId: row.domaId,
        domaName: row.domaName,
        domaAbbreviation: row.domaAbbreviation,
        officeTel: row.officeTel,
        email: row.email,
        domaAddress: row.domaAddress,
        domaFunc: row.domaFunc,
        domaMemo: row.domaMemo,
      }
      this.dialog.update.visible = true
    },
    clickDelete({ domaId: id }) {
      this.deleteArea(id)
    },
    clickBatchDelete() {
      if (this.table.selected.length > 0) {
        const ids = this.table.selected.map((item) => item.domaId).toString()
        this.deleteArea(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    addSubmit(formModel) {
      const params = Object.assign({}, formModel)
      this.addArea(params)
    },
    updSubmit(formModel) {
      const params = Object.assign({}, formModel)
      this.updateArea(params)
    },
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.changeQueryTable()
    },
    tablePageChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.changeQueryTable('turn-page')
    },
    queryTableData(params) {
      params = Object.assign({}, params, {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      })
      this.table.loading = true
      this.pagination.visible = false
      queryAreaTable(params).then((res) => {
        if (res) {
          this.table.data = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.table.loading = false
        this.pagination.visible = true
      })
    },
    addArea(obj) {
      addArea(obj).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else if (res === 'existName') {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    updateArea(obj) {
      updateArea(obj).then((res) => {
        if (res === 'success') {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.changeQueryTable()
            }
          )
        } else if (res === 'existName') {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'warning',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    deleteArea(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteArea(ids).then((res) => {
          if (res === 'success') {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.query.form = {
                  fuzzyField: '',
                  domaName: '',
                  domaAbbreviation: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.table.data.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else if (res === 'inUse') {
            prompt({
              i18nCode: 'tip.delete.use',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
  },
}
</script>
