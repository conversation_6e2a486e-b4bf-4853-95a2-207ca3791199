<!--
 * @Description: 聚合策略 - 自定义弹框
 * @Version: 1.0.0
 * @Author: 
 * @Date: 2020-09-01
 * @Editor: 
 * @EditDate: 2020-09-01
-->
<template>
  <el-dialog
    ref="elDialog"
    v-el-dialog-drag
    v-loading="loading"
    append-to-body
    :title="title"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    :width="dialogWidth"
    :data-id="id"
    class="custom-dialog-container"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <section v-if="show" class="custom-dialog-body">
      <section v-if="readonly" class="dialog-mask"></section>
      <slot></slot>
    </section>
    <footer v-if="action" slot="footer" class="custom-dialog-footer" :element-loading-text="loadingText">
      <slot name="action">
        <el-button @click="cancel">
          {{ $t('button.cancel') }}
        </el-button>
        <el-button @click="submit">
          {{ $t('button.determine') }}
        </el-button>
      </slot>
    </footer>
  </el-dialog>
</template>

<script>
import elDialogDrag from '@/directive/el-dialog-drag'

export default {
  directives: { elDialogDrag },
  inheritAttrs: false,
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    loadingText: {
      type: String,
      default: 'loading...',
    },
    title: {
      type: String,
      default: 'dialog title',
    },
    width: {
      type: String,
      default: '600',
    },
    embed: {
      type: Boolean,
      default: true,
    },
    action: {
      type: Boolean,
      default: true,
    },
    readonly: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      loading: false,
      dialogVisible: this.visible,
      id: 'dialog_' + new Date().getTime(),
      showBody: false,
      window: {
        width: '',
        height: '',
      },
    }
  },
  computed: {
    show() {
      return this.embed ? this.showBody : true
    },
    dialogWidth() {
      if (this.width.indexOf('px') > -1 || this.width.indexOf('%') > -1) {
        return this.width
      } else {
        return `${this.width}px`
      }
    },
  },
  watch: {
    dialogVisible(nVal) {
      if (!nVal) {
        this.loading = false
        this.$emit('on-close')
        setTimeout(() => {
          this.showBody = false
        }, 300)
      } else {
        this.showBody = true
      }
    },
    visible(nVal) {
      this.dialogVisible = nVal
    },
  },
  mounted() {
    this.windowResize()
  },
  methods: {
    windowResize() {
      this.window.width = document.body.clientWidth
      this.window.height = document.body.clientHeight

      window.addEventListener('resize', () => {
        this.window.width = document.body.clientWidth
        this.window.height = document.body.clientHeight
      })
    },
    cancel() {
      this.dialogVisible = false
    },
    submit() {
      this.loading = true
      this.$emit('on-submit')
    },
    end() {
      this.loading = false
    },
  },
}
</script>

<style lang="scss" scoped>
.dialog-mask {
  position: absolute;
  top: 55px;
  left: 0;
  z-index: 2000;
  width: calc(100% - 10px);
  height: calc(100% - 118px);
}
</style>
