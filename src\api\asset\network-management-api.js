import request from '@util/request'

// 网络管理添加
export function addData(obj) {
  return request({
    url: '/netmanagement/net',
    method: 'post',
    data: obj || {},
  })
}

// 网络管理删除
export function deleteData(ids) {
  return request({
    url: `/netmanagement/net/${ids}`,
    method: 'delete',
  })
}

// 网络管理修改
export function updateData(obj) {
  return request({
    url: '/netmanagement/net',
    method: 'put',
    data: obj || {},
  })
}

// 网络管理查询列表
export function queryTableData(obj) {
  return request({
    url: '/netmanagement/nets',
    method: 'get',
    params: obj || {},
  })
}
