const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    accountName: '@NAME',
    remark: '@ZIP',
    typeId: '1',
    groupId: '1',
  },
})
const groupSelect = [
  {
    label: '审计组一',
    value: '1',
  },
  {
    label: '审计组二',
    value: '2',
  },
]
const typeSelect = [
  {
    label: '类型一',
    value: '1',
  },
  {
    label: '类型二',
    value: '2',
  },
]
const groupTree = [
  {
    value: 1,
    label: '审计组1',
    children: [
      {
        value: 4,
        label: '审计员1',
      },
      {
        value: 10,
        label: '审计员2',
      },
    ],
    level: 1,
    remark: '描述1',
  },
  {
    value: 2,
    label: '审计组2',
    children: [
      {
        value: 5,
        label: '审计员3',
      },
      {
        value: 6,
        label: '审计员4',
      },
    ],
    level: 1,
    remark: '描述2',
  },
  {
    value: 3,
    label: '审计组3',
    children: [
      {
        value: 7,
        label: '审计员5',
      },
      {
        value: 8,
        label: '审计员6',
      },
    ],
    level: 1,
    remark: '描述3',
  },
]
module.exports = [
  {
    url: '/audituser/user',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/audituser/user/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/audituser/user',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/audituser/users',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/audituser/group',
    type: 'post',
    response: () => {
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/audituser/group/[A-Za-z0-9]`,
    type: 'delete',
    response: () => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/audituser/group',
    type: 'put',
    response: (option) => {
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/audituser/groups',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: groupTree,
      }
    },
  },
  {
    url: '/audituser/combo/groups',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: groupSelect,
      }
    },
  },
  {
    url: '/audituser/combo/audit-types',
    type: 'get',
    response: () => {
      return {
        code: 200,
        data: typeSelect,
      }
    },
  },
]
