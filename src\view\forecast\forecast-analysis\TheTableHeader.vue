<!--
 * @Description: 预测分析- 顶部查询
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-11-11
 * @Editor:
 * @EditDate: 2021-11-11
-->
<template>
  <header class="table-header">
    <section class="table-header-main">
      <section class="table-header-search">
        <section v-show="!filterCondition.senior" class="table-header-search-input">
          <el-input
            v-model.trim="filterCondition.form.fuzzyField"
            prefix-icon="soc-icon-search"
            clearable
            :placeholder="$t('tip.placeholder.query', [$t('forecast.forecastAnalysis.placeholder.infoItem')])"
            @change="changeQueryCondition"
          ></el-input>
        </section>
        <section class="table-header-search-button">
          <el-button v-if="!filterCondition.senior" v-has="'query'" @click="changeQueryCondition">
            {{ $t('button.query') }}
          </el-button>
          <el-button v-has="'query'" @click="clickExactQuery">
            {{ $t('button.search.exact') }}
            <i :class="filterCondition.senior ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
          </el-button>
        </section>
      </section>
      <section class="table-header-button">
        <el-button v-has="'update'" @click="clickStrategyConfig">
          {{ $t('button.strategyConfig') }}
        </el-button>
      </section>
    </section>
    <section class="table-header-extend">
      <el-collapse-transition>
        <div v-show="filterCondition.senior" class="table-header-query">
          <el-row :gutter="20">
            <el-col :span="5">
              <el-select v-model="filterCondition.form.type" clearable :placeholder="$t('forecast.forecastAnalysis.label.type')" @change="changeType">
                <el-option v-for="item in forecastTypeOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col :span="5">
              <el-select
                v-model="filterCondition.form.infoItem"
                filterable
                clearable
                :placeholder="$t('forecast.forecastAnalysis.label.infoItem')"
                @change="changeQueryCondition"
              >
                <el-option v-for="item in options.infoItem" :key="item.value" :label="item.label" :value="item.value"></el-option>
              </el-select>
            </el-col>
            <el-col align="right" :offset="10" :span="4">
              <el-button v-has="'query'" @click="changeQueryCondition">
                {{ $t('button.query') }}
              </el-button>
              <el-button v-has="'query'" @click="resetQuery">
                {{ $t('button.reset.default') }}
              </el-button>
              <el-button ref="shrinkButton" @click="clickUpButton">
                <i class="soc-icon-scroller-top-all"></i>
              </el-button>
            </el-col>
          </el-row>
        </div>
      </el-collapse-transition>
    </section>
  </header>
</template>

<script>
import { debounce } from '@util/effect'
import { isEmpty } from '@util/common'
import { queryInfoItemCombo } from '@api/forecast/forecast-analysis-api'
export default {
  props: {
    condition: {
      required: true,
      type: Object,
    },
    forecastTypeOption: {
      required: true,
      type: Array,
    },
  },
  data() {
    return {
      filterCondition: this.condition,
      options: {
        infoItem: [],
      },
    }
  },
  watch: {
    condition(nVal) {
      this.filterCondition = nVal
    },
    filterCondition(nVal) {
      this.$emit('update:condition', nVal)
    },
  },
  mounted() {
    this.initDebounceQuery()
  },
  methods: {
    initDebounceQuery() {
      this.debounce = debounce(() => {
        this.$emit('on-change')
      }, 400)
    },
    changeQueryCondition() {
      this.debounce()
    },
    clickExactQuery() {
      this.filterCondition.senior = !this.filterCondition.senior
      this.resetQuery()
    },
    resetQuery() {
      this.filterCondition.form = {
        fuzzyField: '',
        type: '',
        infoItem: '',
      }
      this.options.infoItem = []
      this.changeQueryCondition()
    },
    clickUpButton() {
      this.filterCondition.senior = false
      this.resetQuery()
    },
    async changeType(type) {
      await this.getInfoItemCombo(type)
      this.changeQueryCondition()
    },
    getInfoItemCombo(type) {
      this.filterCondition.form.infoItem = ''
      this.options.infoItem = []
      if (!isEmpty(type)) {
        queryInfoItemCombo(type).then((res) => {
          this.options.infoItem = res
        })
      }
    },
    clickStrategyConfig() {
      this.$emit('on-strategy-config')
    },
  },
}
</script>

<style lang="scss" scoped>
.table-header-button {
  .el-switch {
    padding-top: 10px;
  }
}
</style>
