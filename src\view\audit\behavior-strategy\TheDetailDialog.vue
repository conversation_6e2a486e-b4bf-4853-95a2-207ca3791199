<!--
 * @Description: 行为分析策略 - 详情弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-10-25
 * @Editor:
 * @EditDate: 2021-10-25
-->
<template>
  <custom-dialog
    ref="dialogDom"
    :visible="visible"
    :title="$t('dialog.title.detail', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-form :model="model" label-width="120px">
        <el-row>
          <el-col v-for="(item, index) in columnOption" :key="index" :span="12">
            <el-form-item :prop="item.key" :label="item.label">
              {{ model[item.key] }}
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('audit.behaviorStrategy.label.abnormalActionStr')">
              <el-tag v-for="tag in abnormalActionStr" :key="tag" type="info">
                {{ tag }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="$t('audit.behaviorStrategy.label.legalIp')">
              <el-tag v-for="tag in legalIp" :key="tag" type="info">
                {{ tag }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
export default {
  components: {
    CustomDialog,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      type: String,
      default: '',
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      columnOption: [
        { key: 'systemName', label: this.$t('audit.behaviorStrategy.label.systemName') },
        { key: 'systemIp', label: this.$t('audit.behaviorStrategy.label.systemIp') },
        { key: 'startTime', label: this.$t('audit.behaviorStrategy.label.startTime') },
        { key: 'endTime', label: this.$t('audit.behaviorStrategy.label.endTime') },
      ],
      legalIp: [],
      abnormalActionStr: [],
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.legalIp = this.model.legalIp.split(',')
        this.abnormalActionStr = this.model.abnormalActionStr.split(',')
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    clickCancel() {
      this.$refs.dialogDom.end()
      this.dialogVisible = false
    },
  },
}
</script>

<style lang="scss" scoped>
::v-deep .el-tag.el-tag--info {
  margin-right: 5px;
}
</style>
