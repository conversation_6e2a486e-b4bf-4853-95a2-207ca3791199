export default {
  proxy: {
    title: 'Proxy Server',
    label: {
      ip: 'IP',
      status: 'Status',
      systemStatus: 'System Status',
      description: 'Description',
      updateTime: 'Update Time',
      operationstatus: 'Uninstall Recovery',
    },
    detail: {
      basic: {
        title: 'Current Status',
        status: 'Status',
        updateTime: 'Update Time',
      },
      history: {
        title: 'Historical Status',
      },
      cpu: {
        title: 'CPU Information',
        cpuNum: 'Core Count',
        sys: 'CPU System Usage',
        used: 'CPU User Usage',
        wait: 'CPU Current Wait Rate',
        free: 'CPU Current Idle Rate',
      },
      mem: {
        title: 'Memory Information',
        total: 'Total Memory (GB)',
        used: 'Used Memory',
        free: 'Free Memory',
        usage: 'Usage Rate',
      },
      sys: {
        title: 'System Information',
        computerName: 'Server Name',
        osName: 'Operating System',
        osArch: 'System Architecture',
        agentVersion: 'Agent Version',
      },
      sysFiles: {
        title: 'System File Information',
        dirName: 'Drive Path',
        sysTypeName: 'Drive Type',
        typeName: 'File Type',
        total: 'Total Size',
        free: 'Free Size',
        used: 'Used Amount',
        usage: 'Usage Rate',
      },
    },
    existEquipment: 'There are enabled monitors and collectors under this agent, deletion is not allowed.',
    network: {
      networkConfig: 'Network Configuration',
      service: 'Service Configuration',
      centerIp: 'Set Center IP',
    },
  },
}
