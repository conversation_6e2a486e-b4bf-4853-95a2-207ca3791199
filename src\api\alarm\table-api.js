import request from '@util/request'

export function queryAlarmEventData(obj) {
  return request({
    url: '/event/alarm/events',
    method: 'get',
    params: obj || {},
  })
}

export function queryRawLog(obj) {
  return request({
    url: '/event/alarm/events/original',
    method: 'get',
    params: obj || {},
  })
}

export function querySafeDetailData(obj) {
  return request({
    url: '/event/alarm/events/security',
    method: 'get',
    params: obj || {},
  })
}

export function queryRelevanceDetailData(obj) {
  return request({
    url: '/event/alarm/events/associated',
    method: 'get',
    params: obj || {},
  })
}

export function queryThreatDetailData(obj) {
  return request({
    url: '/event/alarm/events/apt',
    method: 'get',
    params: obj || {},
  })
}

export function queryAuditUserOption(obj) {
  return request({
    url: '/event/alarm/combo/audit-user',
    method: 'get',
    params: obj || {},
  })
}

export function queryAuditTypeOption(obj) {
  return request({
    url: '/event/alarm/combo/audit-types',
    method: 'get',
    params: obj || {},
  })
}

export function queryAlarmStrategyOption(obj) {
  return request({
    url: '/event/alarm/combo/alarm-strategies',
    method: 'get',
    params: obj || {},
  })
}

export function queryColumnsData(obj) {
  return request({
    url: '/event/alarm/columns',
    method: 'get',
    params: obj || {},
  })
}

// 查询告警列表详情
export function queryAlarmEventDetail(id, time) {
  return request({
    url: `/event/alarm/event/${id}/${time}`,
    method: 'get',
  })
}

// 查询告警列表总数
export function queryAlarmEventTotal(obj) {
  return request({
    url: `/event/alarm/events/total`,
    method: 'get',
    params: obj || {},
  })
}

export function updateColumnsData(obj) {
  return request({
    url: '/event/alarm/columns',
    method: 'put',
    data: obj || {},
  })
}

export function updateAlaramState(obj) {
  return request({
    url: '/event/alarm/state',
    method: 'put',
    data: obj || {},
  })
}

// 查询安全事件总数
export function querySecurityTotal(obj) {
  return request({
    url: '/event/alarm/events/security/total',
    method: 'get',
    params: obj || {},
  })
}

// 查询关联事件总数
export function queryRelevanceTotal(obj) {
  return request({
    url: '/event/alarm/events/associated/total',
    method: 'get',
    params: obj || {},
  })
}

// 查询威胁事件总数
export function queryThreatTotal(obj) {
  return request({
    url: '/event/alarm/events/apt/total',
    method: 'get',
    params: obj || {},
  })
}

// 查询原始日志总数
export function queryRawLogTotal(obj) {
  return request({
    url: '/event/alarm/events/original/total',
    method: 'get',
    params: obj || {},
  })
}

// 全部确认
export function updateAlarmConfirmAll(obj) {
  return request({
    url: '/event/alarm/confirmAll',
    method: 'put',
    data: obj || {},
  })
}
