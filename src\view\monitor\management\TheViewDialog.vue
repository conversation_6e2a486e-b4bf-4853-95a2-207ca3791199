<!--
 * @Description: 监控器 - 展示弹框
 * @Version: 1.0.0
 * @Author:
 * @Date: 2021-08-09
 * @Editor:
 * @EditDate: 2021-08-09
-->
<template>
  <custom-dialog
    ref="dialogTemplate"
    :visible="visible"
    :title="$t('dialog.title.show', [titleName])"
    width="60%"
    :action="false"
    @on-close="clickCancel"
  >
    <section>
      <el-divider style="padding-top: -20px;" content-position="left">
        <el-date-picker
          v-model="timeRange"
          type="datetimerange"
          value-format="yyyy-MM-dd HH:mm:ss"
          :clearable="false"
          :popper-class="'no-clearable'"
          start-placeholder="$t('monitor.management.placeholder.startTime')"
          end-placeholder="$t('monitor.management.placeholder.endTime')"
          :picker-options="limitTimeOption"
          @change="changeTimeRange"
        ></el-date-picker>
      </el-divider>
      <section>
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane :label="$t('monitor.management.view.basic.title')" name="first">
            <basic-info :params="queryParams" :basic-model="model" :comp-info="infoItems"></basic-info>
            <template v-if="infoItems.indexOf('cpu') > -1">
              <cpu-info :params="queryParams"></cpu-info>
            </template>
            <template v-if="infoItems.indexOf('memory') > -1">
              <memory-info :params="queryParams"></memory-info>
            </template>
            <template v-if="infoItems.indexOf('disk') > -1">
              <disk-info :params="queryParams"></disk-info>
            </template>
          </el-tab-pane>
          <el-tab-pane :label="$t('monitor.management.view.perf.title')" name="second">
            <perf-info :params="queryParams"></perf-info>
          </el-tab-pane>
          <el-tab-pane :label="$t('monitor.management.view.fault.title')" name="third">
            <fault-info :params="queryParams"></fault-info>
          </el-tab-pane>
        </el-tabs>
      </section>
    </section>
  </custom-dialog>
</template>

<script>
import CustomDialog from '@comp/CustomDialog'
import BasicInfo from './comp/view/BasicInfo'
import CpuInfo from './comp/view/CpuInfo'
import MemoryInfo from './comp/view/MemoryInfo'
import DiskInfo from './comp/view/DiskInfo'
import PerfInfo from './comp/view/PerfInfo'
import FaultInfo from './comp/view/FaultInfo'
import { parseTime } from '@util/format'
import { queryMonitorComp } from '@api/monitor/management-api'

export default {
  components: {
    CustomDialog,
    BasicInfo,
    CpuInfo,
    MemoryInfo,
    DiskInfo,
    PerfInfo,
    FaultInfo,
  },
  props: {
    visible: {
      required: true,
      type: Boolean,
    },
    titleName: {
      required: true,
      type: String,
    },
    model: {
      required: true,
      type: Object,
    },
  },
  data() {
    return {
      dialogVisible: this.visible,
      limitTimeOption: {
        disabledDate(date) {
          return date.getTime() > Date.now()
        },
      },
      timeRange: [],
      activeName: 'first',
      queryParams: {},
      infoItems: '',
    }
  },
  watch: {
    visible(nVal) {
      this.dialogVisible = nVal
      if (nVal) {
        this.initLoadData()
      }
    },
    dialogVisible(nVal) {
      this.$emit('update:visible', nVal)
    },
  },
  methods: {
    initLoadData() {
      this.activeName = 'first'
      this.initTimeRange()
      this.handleQueryParams()
      this.getMonitorComp()
    },
    initTimeRange() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 3)
      this.timeRange = [parseTime(start), parseTime(end)]
    },
    changeTimeRange(val) {
      this.timeRange = val
      this.handleQueryParams()
    },
    clickCancel() {
      this.$refs.dialogTemplate.end()
      this.dialogVisible = false
    },
    handleQueryParams() {
      this.queryParams = {
        monitorId: this.model.monitorId,
        edId: this.model.edId,
        startTime: this.timeRange ? this.timeRange[0] || '' : '',
        endTime: this.timeRange ? this.timeRange[1] || '' : '',
      }
    },
    getMonitorComp(params = this.model.monitorType) {
      queryMonitorComp(params).then((res) => {
        this.infoItems = res
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.el-divider--horizontal {
  margin: 5px 0 24px 0;
}
</style>
