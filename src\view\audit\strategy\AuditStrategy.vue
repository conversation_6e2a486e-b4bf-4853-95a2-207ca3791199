<!--
 * @Description: 审计策略
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <header class="table-header">
      <section class="table-header-main">
        <section class="table-header-search">
          <section v-show="!isShow" class="table-header-search-input">
            <el-input
              v-model.trim="queryInput.inputVal"
              :placeholder="$t('tip.placeholder.query', [$t('audit.strategy.policyName')])"
              clearable
              prefix-icon="soc-icon-search"
              @change="inputQueryEvent('e')"
            ></el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!isShow" v-has="'query'" @click="inputQueryEvent('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="seniorQuery">
              {{ $t('button.search.exact') }}
              <i :class="isShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAdd">
            {{ $t('button.add') }}
          </el-button>
        </section>
      </section>
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="isShow">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="queryInput.policyName"
                  class="width-max"
                  clearable
                  :placeholder="$t('audit.strategy.placeholder.policyName')"
                  @change="inputQueryEvent('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="queryInput.state"
                  :placeholder="$t('audit.strategy.placeholder.state')"
                  clearable
                  class="width-max"
                  @change="inputQueryEvent('e')"
                >
                  <el-option :label="$t('audit.strategy.states.on')" :value="1"></el-option>
                  <el-option :label="$t('audit.strategy.states.off')" :value="0"></el-option>
                </el-select>
              </el-col>
              <el-col :span="4" :offset="10" align="right">
                <el-button v-has="'query'" @click="inputQueryEvent('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button icon="soc-icon-scroller-top-all" @click="seniorQuery"></el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('audit.strategy.strategy') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="Table"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @current-change="TableRowChange"
          @selection-change="TableSelectsChange"
        >
          <el-table-column type="index" :index="indexMethod" :label="$t('audit.strategy.orderNumber')" width="60"></el-table-column>
          <el-table-column prop="policyName" :label="$t('audit.strategy.policyName')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="outputEventRemark" :label="$t('audit.strategy.outputEventRemark')" show-overflow-tooltip></el-table-column>
          <el-table-column prop="enable" :label="$t('audit.strategy.systemOwn.title')" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-tag :type="scope.row.enable === '1' ? '' : 'success'">
                {{ [$t('audit.strategy.systemOwn.write'), $t('audit.strategy.systemOwn.own')][scope.row.enable] }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="state" :label="$t('audit.strategy.state')" show-overflow-tooltip>
            <template slot-scope="scope">
              <el-switch v-model="scope.row.state" active-value="1" inactive-value="0" @change="changeState(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="360">
            <template slot-scope="scope">
              <el-button class="el-button--blue" @click="clickMove(scope.row, 'up')">
                {{ $t('button.move.up') }}
              </el-button>
              <el-button class="el-button--blue" @click="clickMove(scope.row, 'down')">
                {{ $t('button.move.down') }}
              </el-button>
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdate(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" :disabled="scope.row.enable !== '0'" class="el-button--red" @click="clickDelete(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="TableSizeChange"
        @current-change="TableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加弹窗-->
    <Au-dialog
      :visible.sync="dialog.visible.add"
      :title="dialog.title.add"
      :form="dialog.form"
      :width="'60%'"
      @on-submit="clickSubmitAdd"
    ></Au-dialog>
    <!--修改弹窗-->
    <Au-dialog
      :visible.sync="dialog.visible.update"
      :title="dialog.title.update"
      :form="dialog.form"
      :loading="dialog.form.dialogLoading"
      :width="'60%'"
      @on-submit="clickSubmitUpdate"
    ></Au-dialog>
  </div>
</template>
<script>
import AuDialog from './AuditStrategyAu'
import { prompt } from '@util/prompt'
import {
  addData,
  updateData,
  changeState,
  queryTableData,
  upAndDown,
  queryEventTypeList,
  queryOutputEventTypeList,
  queryIsForwardList,
  deleteData,
  queryDetails,
} from '@api/audit/strategy-api'
import { debounce } from '@util/effect'

export default {
  name: 'AuditStrategy',
  components: {
    AuDialog,
  },
  data() {
    return {
      isShow: false,
      state: true,
      stateRow: {},
      queryInput: {
        policyName: '',
        state: '',
        inputVal: '',
      }, // 搜索框内容
      data: {
        loading: false, // 数据加载时loading
        table: [], // 列表载入的整体数据,
        selected: [], // 选中的列表的数据
      },
      pagination: {
        pageSize: this.$store.getters.pageSize, // 列表默认展示的行数
        pageNum: 1, // 列表载入所在的分页
        total: 0, // 一共存在的页数
        currentRow: {}, // 被选中的当前行
        visible: true,
      },
      dialog: {
        title: {
          // 控制弹窗的标题
          add: this.$t('dialog.title.add', [this.$t('audit.strategy.strategy')]),
          update: this.$t('dialog.title.update', [this.$t('audit.strategy.strategy')]),
        },
        visible: {
          // 控制弹窗显示隐藏
          add: false,
          update: false,
        },
        form: {
          dialogLoading: false,
          eventTypeList: [], // 事件类型
          outputEventTypeList: [], // 审计事件类型
          isForwardList: [], // 转发外系统
          model: {
            // 弹出表单绑定值
            orderNumber: '',
            policyName: '',
            outputEventRemark: '',
            eventTypeList: '',
            dateType: 0,
            flag: 0,
            startTime: '',
            endTime: '',
            weekStart: '',
            startWeek: '',
            weekEnd: '',
            endWeek: '',
            outputEventName: '',
            outputEventLevel: '',
            outputEventType: '',
            forwardSystemId: [],
            date: '',
          },
          info: {
            // 弹出表单信息
            orderNumber: {
              key: 'orderNumber',
              label: this.$t('audit.strategy.orderNumber'),
              value: '',
            },
            policyName: {
              key: 'policyName',
              label: this.$t('audit.strategy.policyName'),
              value: '',
            },
            outputEventRemark: {
              key: 'outputEventRemark',
              label: this.$t('audit.strategy.outputEventRemark'),
              value: '',
            },
            eventTypeList: {
              key: 'eventTypeList',
              label: this.$t('audit.strategy.eventType'),
              value: '',
            },
            dateType: {
              key: 'dateType',
              label: this.$t('audit.strategy.dateType'),
              value: '',
            },
            flag: {
              key: 'flag',
              label: this.$t('audit.strategy.flag'),
            },
            date: {
              key: 'date',
              label: this.$t('audit.strategy.date'),
              value: '',
            },
            weekStart: {
              key: 'weekStart',
              label: this.$t('audit.strategy.weekStart'),
              value: '',
            },
            weekEnd: {
              key: 'weekEnd',
              label: this.$t('audit.strategy.weekEnd'),
              value: '',
            },
            outputEventName: {
              key: 'outputEventName',
              label: this.$t('audit.strategy.outputEventName'),
              value: '',
            },
            outputEventLevel: {
              key: 'outputEventLevel',
              label: this.$t('audit.strategy.outputEventLevel'),
              value: '',
            },
            outputEventType: {
              key: 'outputEventType',
              label: this.$t('audit.strategy.outputEventType'),
              value: '',
            },
            forwardSystemId: {
              key: 'forwardSystemId',
              label: this.$t('audit.strategy.forwardSystemId'),
              value: '',
            },
          },
          week: [
            {
              label: this.$t('audit.strategy.weekList.mon'),
              value: this.$t('audit.strategy.weekList.mon'),
              key: 1,
            },
            {
              label: this.$t('audit.strategy.weekList.tue'),
              value: this.$t('audit.strategy.weekList.tue'),
              key: 2,
            },
            {
              label: this.$t('audit.strategy.weekList.wed'),
              value: this.$t('audit.strategy.weekList.wed'),
              key: 3,
            },
            {
              label: this.$t('audit.strategy.weekList.thu'),
              value: this.$t('audit.strategy.weekList.thu'),
              key: 4,
            },
            {
              label: this.$t('audit.strategy.weekList.fri'),
              value: this.$t('audit.strategy.weekList.fri'),
              key: 5,
            },
            {
              label: this.$t('audit.strategy.weekList.sat'),
              value: this.$t('audit.strategy.weekList.sat'),
              key: 6,
            },
            {
              label: this.$t('audit.strategy.weekList.sun'),
              value: this.$t('audit.strategy.weekList.sun'),
              key: 7,
            },
          ],
          rules: {
            // 弹出表单的校验规则
            orderNumber: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            policyName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            eventTypeList: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            dateType: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            flag: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            date: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            outputEventName: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'blur',
              },
            ],
            outputEventLevel: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
            outputEventType: [
              {
                required: true,
                message: this.$t('validate.empty'),
                trigger: 'change',
              },
            ],
          },
        },
      },
      queryDebounce: null,
    }
  },
  mounted() {
    // 查询列表
    this.getTableData()
    this.getSelect()
    this.initDebounce()
  },
  methods: {
    initDebounce() {
      this.queryDebounce = debounce(() => {
        const params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          policyName: this.queryInput.policyName,
          state: this.queryInput.state,
          inputVal: this.queryInput.inputVal,
        }
        this.getTableData(params)
      }, 500)
    },
    // 查询列表
    getTableData(params = { pageSize: this.pagination.pageSize, pageNum: this.pagination.pageNum }) {
      this.pagination.visible = false
      this.data.loading = true
      queryTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    getEventType(type) {
      queryEventTypeList({
        type,
      }).then((res) => {
        if (res) {
          if (type === '1') {
            this.dialog.form.eventTypeList = res[0].children
          } else if (type === '2') {
            this.dialog.form.eventTypeList = { value: 'ip', label: 'ip' }
          } else {
            this.dialog.form.eventTypeList = res
          }
        }
      })
    },
    // 查询下拉框
    getSelect() {
      queryOutputEventTypeList().then((res) => {
        this.dialog.form.outputEventTypeList = res
      })
      queryIsForwardList().then((res) => {
        this.dialog.form.isForwardList = res
      })
    },
    // 调用添加接口
    add(obj) {
      const params = {
        ...obj,
        startTime: obj.date[0],
        endTime: obj.date[1],
        forwardSystemId: obj.forwardSystemId.join(','),
      }
      addData(params).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.clearQuery()
              this.getTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 调用修改接口
    update(obj) {
      const params = {
        ...obj,
        startTime: obj.date[0],
        endTime: obj.date[1],
        forwardSystemId: obj.forwardSystemId.join(','),
      }
      updateData(params).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.inputQueryEvent()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 清空表单数据
    clearDialogFormModel() {
      this.dialog.form.model = {
        orderNumber: '',
        policyName: '',
        outputEventRemark: '',
        eventTypeList: '',
        dateType: 0,
        flag: 0,
        startTime: '',
        endTime: '',
        weekStart: '',
        startWeek: '',
        weekEnd: '',
        endWeek: '',
        outputEventName: '',
        outputEventLevel: '',
        outputEventType: '',
        forwardSystemId: [],
        date: '',
      }
    },
    // 点击添加操作并弹出对话框操作
    clickAdd() {
      this.clearDialogFormModel()
      this.getEventType('0')
      this.dialog.visible.add = true
    },
    // 点击删除操作
    clickDelete(row) {
      this.delete(row)
    },
    // 删除
    delete(row) {
      this.$confirm(this.$t('tip.confirm.delete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        const ids = row.policyId
        deleteData(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.inputQueryEvent()
              }
            )
          } else if (res === 2) {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          } else if (res === 4) {
            prompt({
              i18nCode: 'tip.delete.running',
              type: 'error',
            })
          }
        })
      })
    },
    // 提交添加表单操作
    clickSubmitAdd(formModel) {
      this.add(formModel)
    },
    // 时间转化时间戳
    formatTime(time) {
      let init = 0
      const hour = time.split(':')[0]
      const min = time.split(':')[1]
      const sec = time.split(':')[2]
      init = Number(hour * 3600) + Number(min * 60) + Number(sec)
      return init
    },
    // 点击修改并弹出对话框操作
    clickUpdate(detail) {
      const params = {
        policyId: detail.policyId,
      }
      this.dialog.visible.update = true
      this.dialog.form.dialogLoading = true
      queryDetails(params).then((res) => {
        if (res) {
          const row = res
          this.TableRowChange(row)
          this.clearDialogFormModel()
          this.dialog.form.dialogLoading = false
          let rowData = {}
          rowData = Object.assign({}, row)
          let start = ''
          let end = ''
          this.dialog.form.week.map((item) => {
            if (item.value === rowData.weekStart) {
              start = item.key
            }
            if (item.value === rowData.weekEnd) {
              end = item.key
            }
          })
          rowData.startWeek = start
          rowData.endWeek = end
          if (rowData.forwardSystemId && typeof rowData.forwardSystemId === 'string') {
            rowData.forwardSystemId = rowData.forwardSystemId.split(',')
          } else {
            rowData.forwardSystemId = []
          }
          if (rowData.flag === 1 || rowData.flag === 2) {
            const arr = []
            let Arr = []
            Arr = [...rowData.eventTypeList]
            Arr.map((item) => {
              arr.push(item[1])
            })
            queryEventTypeList({
              type: String(rowData.flag),
            }).then((res) => {
              if (res) {
                this.dialog.form.eventTypeList = res[0].children
              }
              this.dialog.form.model = rowData
              this.dialog.form.model.eventTypeList = arr
            })
          } else {
            queryEventTypeList({
              type: '0',
            }).then((res) => {
              if (res) {
                this.dialog.form.eventTypeList = res
              }
              this.dialog.form.model = rowData
            })
          }
        }
      })
    },
    // 提交修改表单操作
    clickSubmitUpdate(formModel) {
      this.update(formModel)
    },
    // 更改每个页面显示的行数操作 pageSize 改变时会触发
    TableSizeChange(size) {
      this.pagination.pageSize = size
      this.inputQueryEvent('e')
    },
    // 查看当前页操作 pageNum 改变时会触发
    TableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQueryEvent()
    },
    // 列表多选改变
    TableSelectsChange(select) {
      this.data.selected = select
    },
    // 列表点击之后改变当前行
    TableRowChange(row) {
      this.pagination.currentRow = row
    },
    // 输入框搜索方法
    inputQueryEvent(e) {
      if (e) this.pagination.pageNum = 1
      this.queryDebounce()
    },
    // 切换高级查询
    seniorQuery() {
      this.isShow = !this.isShow
      this.resetQuery()
    },
    clearQuery() {
      // 清空搜索条件
      this.queryInput = {
        policyName: '',
        state: '',
        inputVal: '',
      }
      this.pagination.pageNum = 1
    },
    // 清空搜索条件
    resetQuery() {
      this.clearQuery()
      this.queryDebounce()
    },
    // 点击改变状态
    changeState(e) {
      const params = {
        state: e.state,
        policyId: e.policyId,
      }
      changeState(params).then((res) => {
        if (res) {
          if (e.state === '1') {
            prompt(
              {
                i18nCode: 'tip.enable.success',
                type: 'success',
              },
              () => {
                this.inputQueryEvent()
              }
            )
          } else {
            prompt(
              {
                i18nCode: 'tip.disable.success',
                type: 'success',
              },
              () => {
                this.inputQueryEvent()
              }
            )
          }
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 点击移动操作
    clickMove(row, type) {
      const params = {
        policyId: row.policyId,
        num: type,
        orderNumber: row.orderNumber,
      }
      upAndDown(params)
        .then((res) => {
          if (res) {
            this.queryInput = {
              policyName: '',
              state: '',
              inputVal: '',
            }
            this.getTableData()
          }
        })
        .catch(function(e) {
          console.log(e)
        })
    },
    indexMethod(index) {
      return (this.pagination.pageNum - 1) * this.pagination.pageSize + (index + 1)
    },
  },
}
</script>
<style lang="scss" scoped>
::v-deep.el-table {
  .cell {
    padding-right: 0px;
  }
}
</style>
