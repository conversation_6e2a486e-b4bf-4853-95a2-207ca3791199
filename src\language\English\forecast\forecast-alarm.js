export default {
  forecastAlarm: {
    title: 'Forecast Alarm',
    label: {
      type: 'Category',
      infoItem: 'Information Item',
      updateTime: 'Storage Time',
      actualVal: 'Actual Value',
      expectedVal: 'Predicted Value',
      abnormalModel: 'Anomaly Model',
      model: 'Model Name',
      accurTime: 'Occurrence Time',
      anomalyDesc: 'Anomaly Description',
    },
    placeholder: {
      infoItem: 'Information Item',
      startAccurTime: 'Start Occurrence Time',
      endAccurTime: 'End Occurrence Time',
    },
    detail: {
      actualVal: 'Actual Value',
      expectedVal: 'Predicted Value',
      key: 'Anomaly Model',
      name: 'Model Name',
      time: 'Occurrence Time',
    },
  },
}
