<!--
 * @Description: 告警策略
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section class="table-header-search-input">
            <el-input
              v-model.trim="query.fuzzyField"
              :placeholder="$t('tip.placeholder.query', [$t('alarm.strategy.table.strategyName')])"
              clearable
              @keyup.enter.native="inputQuery('e')"
              @change="inputQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="inputQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-has="'query'" @click="inputQuery('e')">
              {{ $t('button.query') }}
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddButton">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteButton">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend"></section>
    </header>
    <!--页面主体-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ title }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="selectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            v-for="(item, index) in options.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`alarm.strategy.table.${item}`)"
            show-overflow-tooltip
          ></el-table-column>
          <el-table-column prop="state" :label="$t('alarm.strategy.table.state')">
            <template slot-scope="scope">
              <el-switch v-model="scope.row.state" active-value="1" inactive-value="0" @change="toggleStatus(scope.row)"></el-switch>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="200">
            <template slot-scope="scope">
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateButton(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteButton(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <!--分页器-->
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        :current-page="pagination.pageNum"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加框-->
    <add-dialog
      :visible.sync="dialog.addDialog.visible"
      :title="title"
      :form="dialog.addDialog.form"
      :width="'60%'"
      :level-option="options.levelOption"
      :type-option="options.typeOption"
      :system-option="options.systemOption"
      :forward-option="options.forwardOption"
      @on-submit="clickSubmitAdd"
    ></add-dialog>
    <!--修改框-->
    <update-dialog
      :visible.sync="dialog.updateDialog.visible"
      :title="title"
      :form="dialog.updateDialog.form"
      :width="'60%'"
      :level-option="options.levelOption"
      :type-option="options.typeOption"
      :system-option="options.systemOption"
      :forward-option="options.forwardOption"
      @on-submit="clickSubmitUpdate"
    ></update-dialog>
  </div>
</template>

<script>
import AddDialog from './AlarmStrategyAddDialog'
import UpdateDialog from './AlarmStrategyUpdateDialog'
import { debounce } from '@/util/effect'
import { prompt } from '@util/prompt'
import { validateEmail } from '@util/validate'
import {
  queryStrategyTableData,
  queryAlarmTypeOption,
  querySystemsOption,
  queryStrategyTableDetail,
  deleteStrategy,
  addStrategy,
  updateStrategy,
  updateStrategyStatus,
  queryForwardCombo,
} from '@api/alarm/strategy-api'

export default {
  name: 'AlarmStrategy',
  components: {
    AddDialog,
    UpdateDialog,
  },
  data() {
    const validatorEmail = (rule, value, callback) => {
      if (value !== '' && !validateEmail(value)) {
        callback(new Error(this.$t('validate.email')))
      } else {
        callback()
      }
    }
    return {
      title: this.$t('alarm.strategy.title'),
      data: {
        loading: false,
        table: [],
        selected: [],
        debounce: {
          inputQueryDebounce: null,
        },
      },
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      },
      query: {
        fuzzyField: '',
      },
      dialog: {
        updateDialog: {
          visible: false,
          form: {
            model: {
              policyId: '',
              strategyName: '',
              alarmName: '',
              alarmType: '',
              alarmLevel: '',
              snmpForwardServer: [],
              mailTo: '',
              description: '',
            },
            info: {
              strategyName: {
                key: 'strategyName',
                label: this.$t('alarm.strategy.table.strategyName'),
              },
              alarmName: {
                key: 'alarmName',
                label: this.$t('alarm.strategy.label.alarmName'),
              },
              alarmType: {
                key: 'alarmType',
                label: this.$t('alarm.strategy.label.alarmType'),
              },
              alarmLevel: {
                key: 'alarmLevel',
                label: this.$t('alarm.strategy.label.alarmLevel'),
              },
              snmpForwardServer: {
                key: 'snmpForwardServer',
                label: this.$t('alarm.strategy.label.snmpForwardServer'),
              },
              mailTo: {
                key: 'mailTo',
                label: this.$t('alarm.strategy.label.mailTo'),
              },
              description: {
                key: 'description',
                label: this.$t('alarm.strategy.label.description'),
              },
            },
            rules: {
              strategyName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              alarmType: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              alarmLevel: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              mailTo: [
                {
                  trigger: 'blur',
                  validator: validatorEmail,
                },
              ],
            },
          },
        },
        addDialog: {
          visible: false,
          form: {
            model: {
              strategyName: '',
              alarmName: '',
              alarmType: '',
              alarmLevel: '',
              snmpForwardServer: [],
              mailTo: '',
              description: '',
            },
            info: {
              strategyName: {
                key: 'strategyName',
                label: this.$t('alarm.strategy.table.strategyName'),
              },
              alarmName: {
                key: 'alarmName',
                label: this.$t('alarm.strategy.label.alarmName'),
              },
              alarmType: {
                key: 'alarmType',
                label: this.$t('alarm.strategy.label.alarmType'),
              },
              alarmLevel: {
                key: 'alarmLevel',
                label: this.$t('alarm.strategy.label.alarmLevel'),
              },
              snmpForwardServer: {
                key: 'snmpForwardServer',
                label: this.$t('alarm.strategy.label.snmpForwardServer'),
              },
              mailTo: {
                key: 'mailTo',
                label: this.$t('alarm.strategy.label.mailTo'),
              },
              description: {
                key: 'description',
                label: this.$t('alarm.strategy.label.description'),
              },
            },
            rules: {
              strategyName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              alarmType: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              alarmLevel: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'change',
                },
              ],
              mailTo: [
                {
                  trigger: 'blur',
                  validator: validatorEmail,
                },
              ],
            },
          },
        },
      },
      options: {
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        typeOption: [],
        systemOption: [],
        columnOption: ['strategyName', 'description', 'createTime'],
        forwardOption: [],
      },
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    // 页面初始化方法
    init() {
      this.queryStrategyTable()
      this.queryAlarmType()
      this.querySystem()
      this.queryForwardCombo()
      this.initDebounce()
    },
    initDebounce() {
      this.data.debounce.inputQueryDebounce = debounce(() => {
        const params = {
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
          inputVal: this.query.fuzzyField,
        }
        this.queryStrategyTable(params)
      }, 500)
    },
    // 查询告警策略
    queryStrategyTable(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.pagination.visible = false
      this.data.loading = true
      queryStrategyTableData(params).then((res) => {
        this.data.table = res.rows
        this.pagination.total = res.total
        this.pagination.pageNum = res.pageNum
        this.pagination.pageSize = res.pageSize
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 删除方法
    delete(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteStrategy(ids).then((res) => {
          if (res) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryStrategyTable()
              }
            )
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 添加方法
    add(param) {
      param.snmpForwardServer = param.snmpForwardServer.join(',')
      addStrategy(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryStrategyTable()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 修改方法
    update(param) {
      param.snmpForwardServer = param.snmpForwardServer.join(',')
      updateStrategy(param).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.inputQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 模糊查询
    inputQuery(flag) {
      if (flag) this.pagination.pageNum = 1
      this.data.debounce.inputQueryDebounce()
    },
    // 查询告警类型Option
    queryAlarmType() {
      queryAlarmTypeOption().then((res) => {
        this.options.typeOption = res
      })
    },
    // 查询外系统的Option
    querySystem() {
      querySystemsOption().then((res) => {
        this.options.systemOption = res
      })
    },
    queryForwardCombo() {
      queryForwardCombo().then((res) => {
        this.options.forwardOption = res
      })
    },
    // 提交修改
    clickSubmitUpdate() {
      const param = Object.assign({}, this.dialog.updateDialog.form.model)
      this.update(param)
    },
    // 提交添加
    clickSubmitAdd() {
      const param = Object.assign({}, this.dialog.addDialog.form.model)
      this.add(param)
    },
    // 点击修改按钮
    clickUpdateButton(row) {
      this.dialog.updateDialog.visible = true
      queryStrategyTableDetail(row.id).then((res) => {
        if (res) {
          if (res.snmpForwardServer && typeof res.snmpForwardServer === 'string') {
            res.snmpForwardServer = res.snmpForwardServer.split(',')
          } else {
            res.snmpForwardServer = []
          }
          this.dialog.updateDialog.form.model = res
        }
      })
    },
    // 点击删除按钮
    clickDeleteButton(row) {
      this.delete(row.id)
    },
    // 点击批量删除按钮
    clickBatchDeleteButton() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.id).toString()
        this.delete(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击添加按钮
    clickAddButton() {
      this.dialog.addDialog.visible = true
      this.dialog.addDialog.form.model = {
        strategyName: '',
        alarmName: '',
        alarmType: '',
        alarmLevel: '',
        snmpForwardServer: [],
        mailTo: '',
        description: '',
      }
    },
    // 切换状态
    toggleStatus(row) {
      const params = {
        id: row.id,
        state: row.state,
      }
      updateStrategyStatus(params).then((res) => {
        if (res) {
          if (row.state === '1') {
            prompt(
              {
                i18nCode: 'tip.enable.success',
                type: 'success',
              },
              () => {
                this.inputQuery()
              }
            )
          } else {
            prompt(
              {
                i18nCode: 'tip.disable.success',
                type: 'success',
              },
              () => {
                this.inputQuery()
              }
            )
          }
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 修改pageSize
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.inputQuery()
    },
    // 修改pageNumber
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.inputQuery()
    },
    // 列表多选
    selectsChange(select) {
      this.data.selected = select
    },
  },
}
</script>
