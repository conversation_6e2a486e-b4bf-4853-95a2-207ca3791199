<!--
 * @Description: 采集器过滤策略
 * @Version: 1.0.0
 * @Author:
 * @Date: 2020-09-01
 * @Editor:
 * @EditDate: 2020-09-01
-->
<template>
  <div class="router-wrap-table">
    <!--页面头部-->
    <header class="table-header">
      <!--页面查询区-->
      <section class="table-header-main">
        <!--模糊查询输入框-->
        <section class="table-header-search">
          <section v-show="!show.seniorQueryShow" class="table-header-search-input">
            <el-input
              v-model.trim="query.inputVal"
              :placeholder="$t('tip.placeholder.query', [$t('collector.strategy.table.policyName')])"
              clearable
              @keyup.enter.native="pageQuery('e')"
              @change="pageQuery('e')"
            >
              <i slot="prefix" class="el-input__icon soc-icon-search" @click="pageQuery('e')"></i>
            </el-input>
          </section>
          <section class="table-header-search-button">
            <el-button v-if="!show.seniorQueryShow" v-has="'query'" @click="pageQuery('e')">
              {{ $t('button.query') }}
            </el-button>
            <el-button v-has="'query'" @click="clickQueryButton">
              {{ $t('button.search.exact') }}
              <i :class="show.seniorQueryShow ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" class="el-icon--right"></i>
            </el-button>
          </section>
        </section>
        <!--功能按钮-->
        <section class="table-header-button">
          <el-button v-has="'add'" @click="clickAddButton">
            {{ $t('button.add') }}
          </el-button>
          <el-button v-has="'delete'" @click="clickBatchDeleteButton">
            {{ $t('button.batch.delete') }}
          </el-button>
        </section>
      </section>
      <!--高级查询组件-->
      <section class="table-header-extend">
        <el-collapse-transition>
          <div v-show="show.seniorQueryShow" class="table-header-query">
            <el-row :gutter="20">
              <el-col :span="5">
                <el-input
                  v-model.trim="query.seniorQuery.policyName"
                  clearable
                  :placeholder="$t('collector.strategy.placeholder.policyName')"
                  @change="pageQuery('e')"
                ></el-input>
              </el-col>
              <el-col :span="5">
                <el-select
                  v-model="query.seniorQuery.level"
                  clearable
                  :placeholder="$t('collector.strategy.placeholder.level')"
                  @change="pageQuery('e')"
                >
                  <el-option v-for="item in option.levelOption" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
              </el-col>
              <el-col align="right" :offset="10" :span="4">
                <el-button v-has="'query'" @click="pageQuery('e')">
                  {{ $t('button.query') }}
                </el-button>
                <el-button v-has="'query'" @click="resetSeniorQuery">
                  {{ $t('button.reset.default') }}
                </el-button>
                <el-button ref="shrinkButton" @click="clickUpButton">
                  <i class="soc-icon-scroller-top-all"></i>
                </el-button>
              </el-col>
            </el-row>
          </div>
        </el-collapse-transition>
      </section>
    </header>
    <!--列表主体内容-->
    <main class="table-body">
      <header class="table-body-header">
        <h2 class="table-body-title">
          {{ $t('collector.strategy.header') }}
        </h2>
      </header>
      <main class="table-body-main">
        <el-table
          ref="CollectorTable"
          v-loading="data.loading"
          :data="data.table"
          element-loading-background="rgba(0, 0, 0, 0.3)"
          size="mini"
          highlight-current-row
          tooltip-effect="light"
          height="100%"
          @selection-change="selectsChange"
        >
          <el-table-column type="selection"></el-table-column>
          <el-table-column
            v-for="(item, index) in option.columnOption"
            :key="index"
            :prop="item"
            :label="$t(`collector.strategy.table.${item}`)"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <level-tag v-if="item === 'level'" :level="toArray(scope.row.level)"></level-tag>
              <p v-else>
                {{ scope.row[item] }}
              </p>
            </template>
          </el-table-column>
          <el-table-column fixed="right" width="200">
            <template slot-scope="scope">
              <el-button v-has="'update'" class="el-button--blue" @click="clickUpdateButton(scope.row)">
                {{ $t('button.update') }}
              </el-button>
              <el-button v-has="'delete'" class="el-button--red" @click="clickDeleteButton(scope.row)">
                {{ $t('button.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </main>
    </main>
    <!--分页组件-->
    <footer class="table-footer">
      <el-pagination
        v-if="pagination.visible"
        small
        background
        align="right"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 20, 50, 100]"
        :current-page="pagination.pageNum"
        :page-size="pagination.pageSize"
        :total="pagination.total"
        @size-change="tableSizeChange"
        @current-change="tableCurrentChange"
      ></el-pagination>
    </footer>
    <!--添加框-->
    <add-dialog
      :width="'40%'"
      :visible.sync="dialog.addDialog.visibility"
      :title="dialog.addDialog.title"
      :form="dialog.addDialog.form"
      :level-option="option.levelOption"
      :event-type-option="option.eventTypeOption"
      @on-submit="clickSubmitAdd"
    ></add-dialog>
    <!--修改框-->
    <update-dialog
      :width="'40%'"
      :visible.sync="dialog.updateDialog.visibility"
      :title="dialog.updateDialog.title"
      :form="dialog.updateDialog.form"
      :level-option="option.levelOption"
      :event-type-option="option.eventTypeOption"
      @on-submit="clickSubmitUpdate"
    ></update-dialog>
  </div>
</template>

<script>
import AddDialog from './CollectionFilterStrategyAddDialog'
import UpdateDialog from './CollectionFilterStrategyUpdateDialog'
import levelTag from '@comp/LevelTag'
import { debounce } from '@/util/effect'
import { prompt } from '@util/prompt'
import { isEmpty } from '@util/common'
import {
  queryStrategyTableData,
  deleteStrategyData,
  addStrategyData,
  updateStrategyData,
  queryComboEventType,
} from '@api/collector/collector-strategy-api'

export default {
  name: 'CollectorStrategy',
  components: {
    AddDialog,
    UpdateDialog,
    levelTag,
  },
  data() {
    return {
      data: {
        loading: false,
        table: [],
        selected: [],
        debounce: {
          query: null,
          resetQueryDebounce: null,
        },
      }, // 列表数据
      pagination: {
        pageSize: this.$store.getters.pageSize,
        pageNum: 1,
        total: 0,
        visible: true,
      }, // 分页信息
      dialog: {
        addDialog: {
          visibility: false,
          title: this.$t('collector.strategy.dialog.title.add'),
          form: {
            model: {
              policyName: '',
              levelList: '',
              eventTypeList: [],
              srcIpv: ['', ''],
              dstIpv: ['', ''],
              describe: '',
            },
            info: {
              policyName: {
                key: 'policyName',
                label: this.$t('collector.strategy.dialog.columns.policyName'),
              },
              describe: {
                key: 'describe',
                label: this.$t('collector.strategy.dialog.columns.describe'),
              },
              levelList: {
                key: 'levelList',
                label: this.$t('collector.strategy.dialog.columns.level'),
              },
              eventTypeList: {
                key: 'eventTypeList',
                label: this.$t('collector.strategy.dialog.columns.eventType'),
              },
              srcIpv: {
                key: 'srcIpv',
                label: this.$t('collector.strategy.dialog.columns.srcIpv'),
              },
              dstIpv: {
                key: 'dstIpv',
                label: this.$t('collector.strategy.dialog.columns.dstIpv'),
              },
            },
            rules: {
              policyName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              // ,
              // levelList: [{
              //     required: true,
              //     message: this.$t("validate.empty"),
              //     trigger: "change"
              // }]
            },
          },
        },
        updateDialog: {
          visibility: false,
          title: this.$t('collector.strategy.dialog.title.update'),
          form: {
            model: {
              policyId: '',
              policyName: '',
              levelList: '',
              eventTypeList: [],
              srcIpv: ['', ''],
              dstIpv: ['', ''],
              describe: '',
            },
            info: {
              policyName: {
                key: 'policyName',
                label: this.$t('collector.strategy.dialog.columns.policyName'),
              },
              describe: {
                key: 'describe',
                label: this.$t('collector.strategy.dialog.columns.describe'),
              },
              levelList: {
                key: 'levelList',
                label: this.$t('collector.strategy.dialog.columns.level'),
              },
              eventTypeList: {
                key: 'eventTypeList',
                label: this.$t('collector.strategy.dialog.columns.eventType'),
              },
              srcIpv: {
                key: 'srcIpv',
                label: this.$t('collector.strategy.dialog.columns.srcIpv'),
              },
              dstIpv: {
                key: 'dstIpv',
                label: this.$t('collector.strategy.dialog.columns.dstIpv'),
              },
            },
            rules: {
              policyName: [
                {
                  required: true,
                  message: this.$t('validate.empty'),
                  trigger: 'blur',
                },
              ],
              // ,levelList: [{
              //     required: true,
              //     message: this.$t("validate.empty"),
              //     trigger: "blur"
              // }]
            },
          },
        },
      }, // dialog信息
      option: {
        columnOption: ['policyName', 'level', 'eventTypeName', 'srcIpv', 'dstIpv', 'describe'],
        levelOption: [
          {
            label: this.$t('level.serious'),
            value: '0',
          },
          {
            label: this.$t('level.high'),
            value: '1',
          },
          {
            label: this.$t('level.middle'),
            value: '2',
          },
          {
            label: this.$t('level.low'),
            value: '3',
          },
          {
            label: this.$t('level.general'),
            value: '4',
          },
        ],
        eventTypeOption: [],
      },
      show: {
        seniorQueryShow: false,
      }, // 显示隐藏
      query: {
        inputVal: '',
        seniorQuery: {
          policyName: '',
          level: '',
        },
      }, // 页面模糊查询和高级查询
    }
  },
  computed: {
    toArray() {
      return (str) => {
        return str.split(',')
      }
    },
  },
  mounted() {
    this.init()
  },
  methods: {
    // 初始化函数
    init() {
      this.queryTableData()
      this.initDebounce()
      this.initOptions()
    },
    // 初始化防抖函数
    initDebounce() {
      this.data.debounce.query = debounce(() => {
        let params = {}
        if (this.show.seniorQueryShow) {
          params = Object.assign({}, this.query.seniorQuery, {
            level: this.query.seniorQuery.level.toString(),
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
          })
        } else {
          params = {
            pageSize: this.pagination.pageSize,
            pageNum: this.pagination.pageNum,
            inputVal: this.query.inputVal,
          }
        }
        this.queryTableData(params)
      }, 500)
      this.data.debounce.resetQueryDebounce = debounce(() => {
        this.query.seniorQuery = {
          policyName: '',
          level: '',
        }
        this.pagination.pageNum = 1
        setTimeout(() => {
          this.queryTableData()
        }, 150)
      }, 500)
    },
    initOptions() {
      queryComboEventType().then((res) => {
        this.option.eventTypeOption = res
      })
    },
    // 页面查询方法
    pageQuery(flag) {
      if (flag) this.pagination.pageNum = 1
      this.data.debounce.query()
    },
    // 查询采集策略
    queryTableData(
      params = {
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum,
      }
    ) {
      this.data.loading = true
      this.pagination.visible = false
      queryStrategyTableData(params).then((res) => {
        if (res) {
          this.data.table = res.rows
          this.pagination.total = res.total
          this.pagination.pageNum = res.pageNum
          this.pagination.pageSize = res.pageSize
        }
        this.data.loading = false
        this.pagination.visible = true
      })
    },
    // 添加采集策略方法API
    addStrategy(obj) {
      addStrategyData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.add.success',
              type: 'success',
            },
            () => {
              this.queryTableData()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.add.repeat',
            type: 'error',
          })
        } else {
          prompt({
            i18nCode: 'tip.add.error',
            type: 'error',
          })
        }
      })
    },
    // 修改采集策略方法API
    updateStrategy(obj) {
      updateStrategyData(obj).then((res) => {
        if (res === 1) {
          prompt(
            {
              i18nCode: 'tip.update.success',
              type: 'success',
            },
            () => {
              this.pageQuery()
            }
          )
        } else if (res === 2) {
          prompt({
            i18nCode: 'tip.update.repeat',
            type: 'warning',
          })
        } else {
          prompt({
            i18nCode: 'tip.update.error',
            type: 'error',
          })
        }
      })
    },
    // 删除采集策略方法API
    deleteStrategy(ids) {
      this.$confirm(this.$t('tip.confirm.batchDelete'), this.$t('tip.confirm.tip'), {
        closeOnClickModal: false,
      }).then(() => {
        deleteStrategyData(ids).then((res) => {
          if (res === 1) {
            prompt(
              {
                i18nCode: 'tip.delete.success',
                type: 'success',
              },
              () => {
                this.query.inputVal = ''
                this.query.seniorQuery = {
                  policyName: '',
                  level: '',
                }
                const [pageNum, idArray] = [this.pagination.pageNum, ids.split(',')]
                if (idArray.length === this.data.table.length) {
                  this.pagination.pageNum = pageNum === 1 ? 1 : pageNum - 1
                }
                this.queryTableData()
              }
            )
          } else if (res === 3) {
            prompt({
              i18nCode: 'tip.delete.running',
              type: 'error',
            })
          } else {
            prompt({
              i18nCode: 'tip.delete.error',
              type: 'error',
            })
          }
        })
      })
    },
    // 点击页面查询按钮
    clickQueryButton() {
      this.query.inputVal = ''
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initDebounce()
      this.resetSeniorQuery()
    },
    // 点击删除按钮
    clickDeleteButton({ policyId: id }) {
      this.deleteStrategy(id)
    },
    // 点击批量删除按钮
    clickBatchDeleteButton() {
      if (this.data.selected.length > 0) {
        const ids = this.data.selected.map((item) => item.policyId).toString()
        this.deleteStrategy(ids)
      } else {
        prompt({
          i18nCode: 'tip.delete.prompt',
          type: 'warning',
          print: true,
        })
      }
    },
    // 点击添加按钮
    clickAddButton() {
      this.dialog.addDialog.form.model = {
        policyName: '',
        level: '',
        eventTypeList: [],
        srcIpv: ['', ''],
        dstIpv: ['', ''],
        describe: '',
      }
      this.dialog.addDialog.visibility = true
    },
    // 提交添加信息
    clickSubmitAdd(formModel) {
      const params = Object.assign({}, formModel)
      this.addStrategy(params)
    },
    // 点击修改按钮
    clickUpdateButton(row) {
      this.dialog.updateDialog.form.model = {
        policyId: row.policyId,
        policyName: row.policyName,
        levelList: isEmpty(row.level) ? [] : row.level.split(','),
        eventTypeList: row.eventTypeList,
        srcIpv: row.srcIpv.split('-'),
        dstIpv: row.dstIpv.split('-'),
        describe: row.describe,
      }
      this.dialog.updateDialog.visibility = true
    },
    // 提交修改信息
    clickSubmitUpdate(formModel) {
      // const { levelList } = formModel;
      // levelList.length === 0 ? levelList.push("5") : levelList;
      const params = Object.assign({}, formModel)
      this.updateStrategy(params)
    },
    // 点击页面向上按钮
    clickUpButton() {
      this.show.seniorQueryShow = !this.show.seniorQueryShow
      this.initDebounce()
      this.resetSeniorQuery()
    },
    // 修改每页size
    tableSizeChange(size) {
      this.pagination.pageSize = size
      this.pagination.pageNum = 1
      this.pageQuery()
    },
    // 修改当前页码
    tableCurrentChange(pageNum) {
      this.pagination.pageNum = pageNum
      this.pageQuery()
    },
    // 页面重置按钮
    resetSeniorQuery() {
      this.data.debounce.resetQueryDebounce()
    },
    // 多选表格数据
    selectsChange(select) {
      this.data.selected = select
    },
  },
}
</script>
