import request from '@util/request'

export function queryPerfTable(obj) {
  return request({
    url: '/perfeventmanagement/queryPerformanceEvents',
    method: 'get',
    params: obj || {},
  })
}

export function queryPerfEventClass() {
  return request({
    url: '/perfeventmanagement/combo/perfclass',
    method: 'get',
  })
}

export function queryPerfDetails(obj) {
  return request({
    url: '/perfeventmanagement/queryPerformanceEventDetails',
    method: 'get',
    params: obj || {},
  })
}

export function queryPerfClassCombo() {
  return request({
    url: '/perfeventmanagement/combo/perfclass',
    method: 'get',
  })
}

export function queryPerfLevelCombo() {
  return request({
    url: '/perfeventmanagement/combo/perflevel',
    method: 'get',
  })
}
