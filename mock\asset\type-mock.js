const { TableMock } = require('../util')

const tableData = new TableMock({
  total: 200,
  template: {
    deviceClass: '1',
    deviceClassName: '网络设备',
    deviceType: '177730553351503872',
    deviceTypeName: 'sada',
    deviceTypeSort: '23',
  },
})

const assetList = [
  { value: '1', label: '网络设备', type: null },
  { value: '2', label: '安全设备', type: null },
  { value: '3', label: '服务器', type: null },
  { value: '4', label: '终端', type: null },
  { value: '5', label: '其他设备', type: null },
]

module.exports = [
  {
    url: '/assettype/type',
    type: 'post',
    response: (option) => {
      tableData.add(option)
      return {
        code: 200,
        data: 1,
      }
    },
  },
  {
    url: `/assettype/type/[A-Za-z0-9]`,
    type: 'delete',
    response: (option) => {
      tableData.delete(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/assettype/type',
    type: 'put',
    response: (option) => {
      tableData.update(option)
      return {
        code: 200,
        data: true,
      }
    },
  },
  {
    url: '/assettype/types',
    type: 'get',
    response: (option) => {
      tableData.query(option)
      return {
        code: 200,
        data: tableData.getMockData(),
      }
    },
  },
  {
    url: '/assettype/combo/categories',
    type: 'get',
    response: (option) => {
      return {
        code: 200,
        data: assetList,
      }
    },
  },
]
