import i18n from '@/language'

export default [
  {
    label: i18n.t('event.original.basic.type2Name'),
    value: '',
    key: 'type2Name',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.eventName'),
    value: '',
    key: 'eventName',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.eventCategoryName'),
    value: '',
    key: 'eventCategoryName',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.level'),
    value: '',
    key: 'level',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.deviceCategoryName'),
    value: '',
    key: 'deviceCategoryName',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.deviceTypeName'),
    value: '',
    key: 'deviceTypeName',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.time'),
    value: '',
    key: 'time',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.code'),
    value: '',
    key: 'code',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.username'),
    value: '',
    key: 'username',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.targetObject'),
    value: '',
    key: 'targetObject',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.logTime'),
    value: '',
    key: 'logTime',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.action'),
    value: '',
    key: 'action',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.resultName'),
    value: '',
    key: 'resultName',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.basic.eventDesc'),
    value: '',
    key: 'eventDesc',
    group: i18n.t('event.original.group.basic'),
    check: false,
  },
  {
    label: i18n.t('event.original.source.sourceIp'),
    value: '',
    key: 'sourceIp',
    group: i18n.t('event.original.group.source'),
    check: false,
  },
  {
    label: i18n.t('event.original.source.sourceAddress'),
    value: '',
    key: 'sourceAddress',
    group: i18n.t('event.original.group.source'),
    check: false,
  },
  {
    label: i18n.t('event.original.source.sourcePort'),
    value: '',
    key: 'sourcePort',
    group: i18n.t('event.original.group.source'),
    check: false,
  },
  {
    label: i18n.t('event.original.source.sourceAsset'),
    value: '',
    key: 'srcEdName',
    group: i18n.t('event.original.group.source'),
    check: false,
  },
  {
    label: i18n.t('event.original.source.sourceMac'),
    value: '',
    key: 'srcMac',
    group: i18n.t('event.original.group.source'),
    check: false,
  },
  {
    label: i18n.t('event.original.destination.targetIp'),
    value: '',
    key: 'targetIp',
    group: i18n.t('event.original.group.destination'),
    check: false,
  },
  {
    label: i18n.t('event.original.destination.targetAddress'),
    value: '',
    key: 'targetAddress',
    group: i18n.t('event.original.group.destination'),
    check: false,
  },
  {
    label: i18n.t('event.original.destination.targetPort'),
    value: '',
    key: 'targetPort',
    group: i18n.t('event.original.group.destination'),
    check: false,
  },
  {
    label: i18n.t('event.original.destination.targetAsset'),
    value: '',
    key: 'dstEdName',
    group: i18n.t('event.original.group.destination'),
    check: false,
  },
  {
    label: i18n.t('event.original.destination.targetMac'),
    value: '',
    key: 'dstMac',
    group: i18n.t('event.original.group.destination'),
    check: false,
  },
  {
    label: i18n.t('event.original.from.fromIp'),
    value: '',
    key: 'fromIp',
    group: i18n.t('event.original.group.from'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.sourceCountryName'),
    value: '',
    key: 'sourceCountryName',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.sourceCountryLongitude'),
    value: '',
    key: 'sourceCountryLongitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.sourceCountryLatitude'),
    value: '',
    key: 'sourceCountryLatitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.sourceAreaName'),
    value: '',
    key: 'sourceAreaName',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.sourceAreaLongitude'),
    value: '',
    key: 'sourceAreaLongitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.sourceAreaLatitude'),
    value: '',
    key: 'sourceAreaLatitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.targetCountryName'),
    value: '',
    key: 'targetCountryName',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.targetCountryLongitude'),
    value: '',
    key: 'targetCountryLongitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.targetCountryLatitude'),
    value: '',
    key: 'targetCountryLatitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.targetAreaName'),
    value: '',
    key: 'targetAreaName',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.targetAreaLongitude'),
    value: '',
    key: 'targetAreaLongitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.geo.targetAreaLatitude'),
    value: '',
    key: 'targetAreaLatitude',
    group: i18n.t('event.original.group.geo'),
    check: false,
  },
  {
    label: i18n.t('event.original.log.raw'),
    value: '',
    key: 'raw',
    group: i18n.t('event.original.group.log'),
    check: false,
  },
]
